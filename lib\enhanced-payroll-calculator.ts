// Enhanced Payroll Calculator
// Automatic salary calculation based on attendance data with allowances/deductions integration

import { db } from './neon';
import { allowancesManager } from './allowances-manager';
import { deductionsManager } from './deductions-manager';

export interface AttendanceSummary {
  user_id: string;
  month: string;
  year: number;
  total_working_days: number;
  days_present: number;
  days_absent: number;
  days_late: number;
  days_half_day: number;
  days_on_leave: number;
  total_hours_worked: number;
  regular_hours: number;
  overtime_hours: number;
  sessions_count: number;
}

export interface PayrollCalculation {
  user_id: string;
  employee_name: string;
  employee_type: string;
  base_salary: number;
  hourly_rate: number;
  
  // Attendance-based calculations
  attendance_summary: AttendanceSummary;
  regular_pay: number;
  overtime_pay: number;
  attendance_bonus: number;
  late_penalty: number;
  
  // Allowances
  total_allowances: number;
  allowance_breakdown: Array<{
    name: string;
    type: string;
    amount: number;
    is_taxable: boolean;
  }>;
  
  // Deductions
  total_deductions: number;
  deduction_breakdown: Array<{
    name: string;
    type: string;
    amount: number;
    reason: string;
  }>;
  
  // Final calculations
  gross_pay: number;
  taxable_income: number;
  income_tax: number;
  provident_fund: number;
  net_pay: number;
  
  // Metadata
  calculation_date: string;
  payroll_period: string;
  status: 'draft' | 'calculated' | 'approved' | 'processed';
}

export interface PayrollSettings {
  standard_working_hours_per_day: number;
  standard_working_days_per_month: number;
  overtime_multiplier: number;
  late_penalty_per_hour: number;
  attendance_bonus_threshold: number;
  attendance_bonus_amount: number;
  provident_fund_rate: number;
  income_tax_slabs: Array<{
    min_amount: number;
    max_amount: number;
    tax_rate: number;
  }>;
}

export class EnhancedPayrollCalculator {
  
  private defaultSettings: PayrollSettings = {
    standard_working_hours_per_day: 8,
    standard_working_days_per_month: 22,
    overtime_multiplier: 1.5,
    late_penalty_per_hour: 100,
    attendance_bonus_threshold: 0.95, // 95% attendance
    attendance_bonus_amount: 2000,
    provident_fund_rate: 0.10, // 10%
    income_tax_slabs: [
      { min_amount: 0, max_amount: 500000, tax_rate: 0.01 },
      { min_amount: 500000, max_amount: 700000, tax_rate: 0.10 },
      { min_amount: 700000, max_amount: 1000000, tax_rate: 0.20 },
      { min_amount: 1000000, max_amount: 2000000, tax_rate: 0.30 },
      { min_amount: 2000000, max_amount: Infinity, tax_rate: 0.36 }
    ]
  };

  // Get attendance summary for employee for a specific month
  async getAttendanceSummary(userId: string, year: number, month: number): Promise<AttendanceSummary> {
    try {
      // Get attendance records for the month
      const startDate = new Date(year, month - 1, 1);
      const endDate = new Date(year, month, 0);
      
      const attendanceRecords = await db.sql`
        SELECT 
          DATE(check_in_time) as attendance_date,
          COUNT(*) as sessions_count,
          SUM(CASE WHEN check_out_time IS NOT NULL THEN 
            EXTRACT(EPOCH FROM (check_out_time - check_in_time))/3600 
            ELSE 0 END) as daily_hours,
          MIN(check_in_time) as first_check_in,
          MAX(check_out_time) as last_check_out,
          status
        FROM attendance 
        WHERE user_id = ${userId} 
          AND DATE(check_in_time) BETWEEN ${startDate.toISOString().split('T')[0]} 
          AND ${endDate.toISOString().split('T')[0]}
        GROUP BY DATE(check_in_time), status
        ORDER BY attendance_date
      `;
      
      // Calculate working days in month (excluding weekends)
      const totalWorkingDays = this.calculateWorkingDaysInMonth(year, month);
      
      // Process attendance data
      let daysPresent = 0;
      let daysAbsent = 0;
      let daysLate = 0;
      let daysHalfDay = 0;
      let daysOnLeave = 0;
      let totalHours = 0;
      let regularHours = 0;
      let overtimeHours = 0;
      let totalSessions = 0;
      
      const attendanceDates = new Set();
      
      for (const record of attendanceRecords) {
        const date = record.attendance_date;
        attendanceDates.add(date);
        
        const dailyHours = parseFloat(record.daily_hours) || 0;
        const sessions = parseInt(record.sessions_count) || 0;
        
        totalHours += dailyHours;
        totalSessions += sessions;
        
        // Categorize the day based on status and hours
        switch (record.status) {
          case 'present':
            daysPresent++;
            if (dailyHours >= this.defaultSettings.standard_working_hours_per_day) {
              regularHours += this.defaultSettings.standard_working_hours_per_day;
              overtimeHours += dailyHours - this.defaultSettings.standard_working_hours_per_day;
            } else {
              regularHours += dailyHours;
            }
            break;
          case 'late':
            daysLate++;
            daysPresent++; // Late is still present
            if (dailyHours >= this.defaultSettings.standard_working_hours_per_day) {
              regularHours += this.defaultSettings.standard_working_hours_per_day;
              overtimeHours += dailyHours - this.defaultSettings.standard_working_hours_per_day;
            } else {
              regularHours += dailyHours;
            }
            break;
          case 'half_day':
            daysHalfDay++;
            regularHours += dailyHours;
            break;
          case 'on_leave':
            daysOnLeave++;
            regularHours += this.defaultSettings.standard_working_hours_per_day; // Paid leave
            break;
          default:
            break;
        }
      }
      
      // Calculate absent days
      daysAbsent = totalWorkingDays - attendanceDates.size;
      
      return {
        user_id: userId,
        month: month.toString().padStart(2, '0'),
        year: year,
        total_working_days: totalWorkingDays,
        days_present: daysPresent,
        days_absent: daysAbsent,
        days_late: daysLate,
        days_half_day: daysHalfDay,
        days_on_leave: daysOnLeave,
        total_hours_worked: totalHours,
        regular_hours: regularHours,
        overtime_hours: overtimeHours,
        sessions_count: totalSessions
      };
      
    } catch (error) {
      console.error('Error getting attendance summary:', error);
      throw new Error('Failed to get attendance summary');
    }
  }
  
  // Calculate payroll for an employee
  async calculateEmployeePayroll(
    userId: string, 
    year: number, 
    month: number
  ): Promise<PayrollCalculation> {
    try {
      // Get employee details
      const employee = await db.sql`
        SELECT id, full_name, employee_type, salary, department, position
        FROM users 
        WHERE id = ${userId}
      `;
      
      if (employee.length === 0) {
        throw new Error('Employee not found');
      }
      
      const emp = employee[0];
      const baseSalary = emp.salary || 0;
      const hourlyRate = baseSalary / (this.defaultSettings.standard_working_days_per_month * this.defaultSettings.standard_working_hours_per_day);
      
      // Get attendance summary
      const attendanceSummary = await this.getAttendanceSummary(userId, year, month);
      
      // Calculate basic pay components
      const regularPay = attendanceSummary.regular_hours * hourlyRate;
      const overtimePay = attendanceSummary.overtime_hours * hourlyRate * this.defaultSettings.overtime_multiplier;
      
      // Calculate attendance bonus
      const attendanceRate = attendanceSummary.days_present / attendanceSummary.total_working_days;
      const attendanceBonus = attendanceRate >= this.defaultSettings.attendance_bonus_threshold 
        ? this.defaultSettings.attendance_bonus_amount : 0;
      
      // Calculate late penalty
      const latePenalty = attendanceSummary.days_late * this.defaultSettings.late_penalty_per_hour;
      
      // Get allowances
      const allowances = await allowancesManager.getEmployeeAllowances(userId);
      let totalAllowances = 0;
      const allowanceBreakdown = [];
      
      for (const allowance of allowances) {
        let amount = 0;
        if (allowance.calculation_type === 'fixed') {
          amount = allowance.amount;
        } else if (allowance.calculation_type === 'percentage') {
          const base = allowance.percentage_base === 'base_salary' ? baseSalary : regularPay + overtimePay;
          amount = (base * allowance.percentage) / 100;
        }
        
        totalAllowances += amount;
        allowanceBreakdown.push({
          name: allowance.allowance_name,
          type: allowance.allowance_type,
          amount: amount,
          is_taxable: allowance.is_taxable
        });
      }
      
      // Get approved deductions
      const deductions = await db.sql`
        SELECT 
          da.deduction_amount,
          da.deduction_reason,
          pcm.name as component_name,
          pcm.code as component_code
        FROM deduction_approvals da
        JOIN payroll_components_master pcm ON da.component_id = pcm.id
        WHERE da.user_id = ${userId} 
          AND da.status = 'approved'
          AND da.effective_from <= CURRENT_DATE
          AND (da.effective_to IS NULL OR da.effective_to >= CURRENT_DATE)
      `;
      
      let totalDeductions = 0;
      const deductionBreakdown = [];
      
      for (const deduction of deductions) {
        totalDeductions += deduction.deduction_amount;
        deductionBreakdown.push({
          name: deduction.component_name,
          type: deduction.component_code,
          amount: deduction.deduction_amount,
          reason: deduction.deduction_reason
        });
      }
      
      // Calculate gross pay
      const grossPay = regularPay + overtimePay + attendanceBonus + totalAllowances - latePenalty;
      
      // Calculate taxable income (exclude non-taxable allowances)
      const taxableAllowances = allowanceBreakdown
        .filter(a => a.is_taxable)
        .reduce((sum, a) => sum + a.amount, 0);
      const taxableIncome = regularPay + overtimePay + attendanceBonus + taxableAllowances - latePenalty;
      
      // Calculate income tax
      const incomeTax = this.calculateIncomeTax(taxableIncome * 12); // Annual calculation
      const monthlyIncomeTax = incomeTax / 12;
      
      // Calculate provident fund
      const providentFund = baseSalary * this.defaultSettings.provident_fund_rate;
      
      // Calculate net pay
      const netPay = grossPay - totalDeductions - monthlyIncomeTax - providentFund;
      
      return {
        user_id: userId,
        employee_name: emp.full_name,
        employee_type: emp.employee_type || 'full_time',
        base_salary: baseSalary,
        hourly_rate: hourlyRate,
        attendance_summary: attendanceSummary,
        regular_pay: regularPay,
        overtime_pay: overtimePay,
        attendance_bonus: attendanceBonus,
        late_penalty: latePenalty,
        total_allowances: totalAllowances,
        allowance_breakdown: allowanceBreakdown,
        total_deductions: totalDeductions,
        deduction_breakdown: deductionBreakdown,
        gross_pay: grossPay,
        taxable_income: taxableIncome,
        income_tax: monthlyIncomeTax,
        provident_fund: providentFund,
        net_pay: netPay,
        calculation_date: new Date().toISOString(),
        payroll_period: `${year}-${month.toString().padStart(2, '0')}`,
        status: 'calculated'
      };
      
    } catch (error) {
      console.error('Error calculating employee payroll:', error);
      throw new Error('Failed to calculate employee payroll');
    }
  }
  
  // Calculate income tax based on Nepal tax slabs
  private calculateIncomeTax(annualIncome: number): number {
    let tax = 0;
    let remainingIncome = annualIncome;
    
    for (const slab of this.defaultSettings.income_tax_slabs) {
      if (remainingIncome <= 0) break;
      
      const taxableInThisSlab = Math.min(
        remainingIncome, 
        slab.max_amount - slab.min_amount
      );
      
      if (annualIncome > slab.min_amount) {
        tax += taxableInThisSlab * slab.tax_rate;
        remainingIncome -= taxableInThisSlab;
      }
    }
    
    return tax;
  }
  
  // Calculate working days in a month (excluding weekends)
  private calculateWorkingDaysInMonth(year: number, month: number): number {
    const startDate = new Date(year, month - 1, 1);
    const endDate = new Date(year, month, 0);
    let workingDays = 0;
    
    for (let date = new Date(startDate); date <= endDate; date.setDate(date.getDate() + 1)) {
      const dayOfWeek = date.getDay();
      if (dayOfWeek !== 0 && dayOfWeek !== 6) { // Not Sunday (0) or Saturday (6)
        workingDays++;
      }
    }
    
    return workingDays;
  }
  
  // Calculate payroll for all employees
  async calculateBulkPayroll(year: number, month: number): Promise<PayrollCalculation[]> {
    try {
      // Get all active employees
      const employees = await db.sql`
        SELECT id FROM users 
        WHERE role != 'admin' AND employment_status = 'active'
        ORDER BY full_name
      `;
      
      const payrollCalculations = [];
      
      for (const employee of employees) {
        try {
          const calculation = await this.calculateEmployeePayroll(employee.id, year, month);
          payrollCalculations.push(calculation);
        } catch (error) {
          console.error(`Error calculating payroll for employee ${employee.id}:`, error);
          // Continue with other employees
        }
      }
      
      return payrollCalculations;
      
    } catch (error) {
      console.error('Error calculating bulk payroll:', error);
      throw new Error('Failed to calculate bulk payroll');
    }
  }
  
  // Save payroll calculation to database
  async savePayrollCalculation(calculation: PayrollCalculation): Promise<string> {
    try {
      const result = await db.sql`
        INSERT INTO monthly_payroll_summary (
          user_id, fiscal_year, bs_month, ad_month_start, ad_month_end,
          total_working_days, days_present, days_absent, days_late, days_half_day, days_on_leave,
          total_hours_worked, regular_hours, overtime_hours,
          base_salary, overtime_pay, total_allowances, total_deductions,
          gross_pay, tax_deductions, provident_fund, net_pay,
          attendance_bonus, late_penalty,
          status, calculated_at, calculated_by
        ) VALUES (
          ${calculation.user_id}, ${calculation.payroll_period.split('-')[0]}, ${calculation.payroll_period},
          ${new Date(parseInt(calculation.payroll_period.split('-')[0]), parseInt(calculation.payroll_period.split('-')[1]) - 1, 1).toISOString().split('T')[0]},
          ${new Date(parseInt(calculation.payroll_period.split('-')[0]), parseInt(calculation.payroll_period.split('-')[1]), 0).toISOString().split('T')[0]},
          ${calculation.attendance_summary.total_working_days}, ${calculation.attendance_summary.days_present},
          ${calculation.attendance_summary.days_absent}, ${calculation.attendance_summary.days_late},
          ${calculation.attendance_summary.days_half_day}, ${calculation.attendance_summary.days_on_leave},
          ${calculation.attendance_summary.total_hours_worked}, ${calculation.attendance_summary.regular_hours},
          ${calculation.attendance_summary.overtime_hours}, ${calculation.base_salary}, ${calculation.overtime_pay},
          ${calculation.total_allowances}, ${calculation.total_deductions}, ${calculation.gross_pay},
          ${calculation.income_tax}, ${calculation.provident_fund}, ${calculation.net_pay},
          ${calculation.attendance_bonus}, ${calculation.late_penalty}, ${calculation.status},
          NOW(), 'system'
        ) RETURNING id
      `;
      
      return result[0].id;
      
    } catch (error) {
      console.error('Error saving payroll calculation:', error);
      throw new Error('Failed to save payroll calculation');
    }
  }
}

export const enhancedPayrollCalculator = new EnhancedPayrollCalculator();
