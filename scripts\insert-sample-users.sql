-- Insert comprehensive sample user data
-- Password hash for 'admin123' using bcrypt with salt rounds 12

-- Insert sample users with realistic data
INSERT INTO users (
    email, password_hash, full_name, role, employee_id, department, position,
    employment_type, employment_status, hire_date, salary, date_of_birth, gender,
    marital_status, phone, citizenship_number, pan_number,
    emergency_contact_name, emergency_contact_phone, emergency_contact_relationship,
    bank_name, bank_account_number, bank_branch, is_active, email_verified
) VALUES
-- Admin User
(
    '<EMAIL>',
    '$2b$12$LQv3c1yqBWVHxkd0LHAkCOYz6TtxMQJqhN8/LewdBPj/VcSAg/9S.',
    '<PERSON><PERSON>',
    'admin',
    'EMP001',
    'Information Technology',
    'System Administrator',
    'full_time',
    'active',
    '2020-01-15',
    120000.00,
    '1985-03-20',
    'male',
    'married',
    '+977-**********',
    '12-34-56-78901',
    '*********',
    '<PERSON><PERSON>',
    '+977-**********',
    'spouse',
    'Nepal Bank Limited',
    '*********0123456',
    'Kathmandu Main Branch',
    true,
    true
),
-- HR Manager
(
    '<EMAIL>',
    '$2b$12$LQv3c1yqBWVHxkd0LHAkCOYz6TtxMQJqhN8/LewdBPj/VcSAg/9S.',
    'Sunita Devi Thapa',
    'hr_manager',
    'EMP002',
    'Human Resources',
    'HR Manager',
    'full_time',
    'active',
    '2019-06-01',
    95000.00,
    '1988-07-15',
    'female',
    'married',
    '+977-**********',
    '23-45-67-89012',
    '*********',
    'Ram Thapa',
    '+977-**********',
    'spouse',
    'Rastriya Banijya Bank',
    '*********1234567',
    'Lalitpur Branch',
    true,
    true
),
-- Department Manager
(
    '<EMAIL>',
    '$2b$12$LQv3c1yqBWVHxkd0LHAkCOYz6TtxMQJqhN8/LewdBPj/VcSAg/9S.',
    'Prakash Bahadur Rana',
    'manager',
    'EMP003',
    'Operations',
    'Operations Manager',
    'full_time',
    'active',
    '2018-03-10',
    85000.00,
    '1982-11-25',
    'male',
    'married',
    '+977-**********',
    '34-56-78-90123',
    '*********',
    'Maya Rana',
    '+977-**********',
    'spouse',
    'Nepal Investment Bank',
    '*********2345678',
    'New Road Branch',
    true,
    true
),
-- Finance Staff
(
    '<EMAIL>',
    '$2b$12$LQv3c1yqBWVHxkd0LHAkCOYz6TtxMQJqhN8/LewdBPj/VcSAg/9S.',
    'Anita Kumari Shrestha',
    'staff',
    'EMP004',
    'Finance & Accounting',
    'Senior Accountant',
    'full_time',
    'active',
    '2021-08-15',
    65000.00,
    '1990-05-12',
    'female',
    'single',
    '+977-**********',
    '45-67-89-01234',
    '*********',
    'Kamala Shrestha',
    '+977-**********',
    'mother',
    'Standard Chartered Bank',
    '4567890*********',
    'Durbar Marg Branch',
    true,
    true
),
-- IT Staff
(
    '<EMAIL>',
    '$2b$12$LQv3c1yqBWVHxkd0LHAkCOYz6TtxMQJqhN8/LewdBPj/VcSAg/9S.',
    'Bikash Adhikari',
    'staff',
    'EMP005',
    'Information Technology',
    'Software Developer',
    'full_time',
    'active',
    '2022-02-01',
    75000.00,
    '1992-09-08',
    'male',
    'single',
    '+977-**********',
    '56-78-90-12345',
    '*********',
    'Bishnu Adhikari',
    '+977-**********',
    'father',
    'Himalayan Bank',
    '567890*********0',
    'Thamel Branch',
    true,
    true
),
-- Marketing Staff
(
    '<EMAIL>',
    '$2b$12$LQv3c1yqBWVHxkd0LHAkCOYz6TtxMQJqhN8/LewdBPj/VcSAg/9S.',
    'Priya Maharjan',
    'staff',
    'EMP006',
    'Marketing',
    'Marketing Executive',
    'full_time',
    'active',
    '2021-11-20',
    55000.00,
    '1993-12-03',
    'female',
    'single',
    '+977-**********',
    '67-89-01-23456',
    '*********',
    'Laxmi Maharjan',
    '+977-**********',
    'mother',
    'Nabil Bank',
    '67890*********01',
    'Patan Branch',
    true,
    true
),
-- Customer Service Staff
(
    '<EMAIL>',
    '$2b$12$LQv3c1yqBWVHxkd0LHAkCOYz6TtxMQJqhN8/LewdBPj/VcSAg/9S.',
    'Deepak Gurung',
    'staff',
    'EMP007',
    'Customer Service',
    'Customer Support Representative',
    'full_time',
    'active',
    '2023-01-10',
    45000.00,
    '1995-04-18',
    'male',
    'single',
    '+977-**********',
    '78-90-12-34567',
    '*********',
    'Dhan Bahadur Gurung',
    '+977-**********',
    'father',
    'Machhapuchchhre Bank',
    '7890*********012',
    'Pokhara Branch',
    true,
    true
),
-- Sales Staff
(
    '<EMAIL>',
    '$2b$12$LQv3c1yqBWVHxkd0LHAkCOYz6TtxMQJqhN8/LewdBPj/VcSAg/9S.',
    'Ramesh Khadka',
    'staff',
    'EMP008',
    'Sales',
    'Sales Executive',
    'full_time',
    'active',
    '2022-07-01',
    60000.00,
    '1991-02-14',
    'male',
    'married',
    '+977-**********',
    '89-01-23-45678',
    '*********',
    'Gita Khadka',
    '+977-**********',
    'spouse',
    'NIC Asia Bank',
    '****************',
    'Baneshwor Branch',
    true,
    true
);

-- Insert employee addresses
INSERT INTO employee_addresses (user_id, address_type, address_line_1, address_line_2, city, district, province, postal_code, is_primary)
SELECT 
    u.id,
    'permanent',
    CASE 
        WHEN u.employee_id = 'EMP001' THEN 'Gongabu-12'
        WHEN u.employee_id = 'EMP002' THEN 'Lalitpur-5'
        WHEN u.employee_id = 'EMP003' THEN 'Bhaktapur-8'
        WHEN u.employee_id = 'EMP004' THEN 'Kathmandu-15'
        WHEN u.employee_id = 'EMP005' THEN 'Kirtipur-3'
        WHEN u.employee_id = 'EMP006' THEN 'Patan-7'
        WHEN u.employee_id = 'EMP007' THEN 'Pokhara-12'
        WHEN u.employee_id = 'EMP008' THEN 'Baneshwor-10'
    END,
    CASE 
        WHEN u.employee_id = 'EMP001' THEN 'Near Gongabu Bus Park'
        WHEN u.employee_id = 'EMP002' THEN 'Jawalakhel Area'
        WHEN u.employee_id = 'EMP003' THEN 'Madhyapur Thimi'
        WHEN u.employee_id = 'EMP004' THEN 'Balaju Industrial Area'
        WHEN u.employee_id = 'EMP005' THEN 'Near TU Campus'
        WHEN u.employee_id = 'EMP006' THEN 'Mangal Bazaar'
        WHEN u.employee_id = 'EMP007' THEN 'Lakeside Area'
        WHEN u.employee_id = 'EMP008' THEN 'Near Baneshwor Chowk'
    END,
    CASE 
        WHEN u.employee_id = 'EMP001' THEN 'Kathmandu'
        WHEN u.employee_id = 'EMP002' THEN 'Lalitpur'
        WHEN u.employee_id = 'EMP003' THEN 'Bhaktapur'
        WHEN u.employee_id = 'EMP004' THEN 'Kathmandu'
        WHEN u.employee_id = 'EMP005' THEN 'Kathmandu'
        WHEN u.employee_id = 'EMP006' THEN 'Lalitpur'
        WHEN u.employee_id = 'EMP007' THEN 'Pokhara'
        WHEN u.employee_id = 'EMP008' THEN 'Kathmandu'
    END,
    CASE 
        WHEN u.employee_id IN ('EMP001', 'EMP004', 'EMP005', 'EMP008') THEN 'Kathmandu'
        WHEN u.employee_id IN ('EMP002', 'EMP006') THEN 'Lalitpur'
        WHEN u.employee_id = 'EMP003' THEN 'Bhaktapur'
        WHEN u.employee_id = 'EMP007' THEN 'Kaski'
    END,
    CASE 
        WHEN u.employee_id IN ('EMP001', 'EMP002', 'EMP003', 'EMP004', 'EMP005', 'EMP006', 'EMP008') THEN 'Bagmati'
        WHEN u.employee_id = 'EMP007' THEN 'Gandaki'
    END,
    CASE 
        WHEN u.employee_id IN ('EMP001', 'EMP004', 'EMP005', 'EMP008') THEN '44600'
        WHEN u.employee_id IN ('EMP002', 'EMP006') THEN '44700'
        WHEN u.employee_id = 'EMP003' THEN '44800'
        WHEN u.employee_id = 'EMP007' THEN '33700'
    END,
    true
FROM users u
WHERE u.employee_id IS NOT NULL;

-- Update department managers
UPDATE departments SET manager_id = (SELECT id FROM users WHERE employee_id = 'EMP001') WHERE name = 'Information Technology';
UPDATE departments SET manager_id = (SELECT id FROM users WHERE employee_id = 'EMP002') WHERE name = 'Human Resources';
UPDATE departments SET manager_id = (SELECT id FROM users WHERE employee_id = 'EMP003') WHERE name = 'Operations';
UPDATE departments SET manager_id = (SELECT id FROM users WHERE employee_id = 'EMP004') WHERE name = 'Finance & Accounting';
UPDATE departments SET manager_id = (SELECT id FROM users WHERE employee_id = 'EMP006') WHERE name = 'Marketing';
UPDATE departments SET manager_id = (SELECT id FROM users WHERE employee_id = 'EMP007') WHERE name = 'Customer Service';
UPDATE departments SET manager_id = (SELECT id FROM users WHERE employee_id = 'EMP008') WHERE name = 'Sales';
