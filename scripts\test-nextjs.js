#!/usr/bin/env node

// Test Next.js setup and identify issues
const { spawn } = require('child_process');
const fs = require('fs');
const path = require('path');

async function testNextJS() {
  console.log('🔍 Testing Next.js setup...\n');
  
  // Check if package.json exists
  if (!fs.existsSync('package.json')) {
    console.log('❌ package.json not found');
    return;
  }
  
  // Check if node_modules exists
  if (!fs.existsSync('node_modules')) {
    console.log('❌ node_modules not found - run npm install');
    return;
  }
  
  // Check if Next.js is installed
  if (!fs.existsSync('node_modules/next')) {
    console.log('❌ Next.js not installed');
    return;
  }
  
  console.log('✅ Basic setup checks passed');
  
  // Try to start Next.js dev server
  console.log('🔄 Starting Next.js dev server...');
  
  const nextProcess = spawn('npx', ['next', 'dev', '--port', '3001'], {
    stdio: ['pipe', 'pipe', 'pipe'],
    shell: true
  });
  
  let hasStarted = false;
  let hasError = false;
  
  // Monitor stdout
  nextProcess.stdout.on('data', (data) => {
    const output = data.toString();
    console.log('📤 Next.js:', output.trim());
    
    if (output.includes('Ready') || output.includes('started server') || output.includes('Local:')) {
      hasStarted = true;
      console.log('\n✅ Next.js server started successfully!');
      console.log('🌐 Try accessing: http://localhost:3001');
      
      // Stop the server after successful start
      setTimeout(() => {
        console.log('\n🛑 Stopping test server...');
        nextProcess.kill('SIGTERM');
      }, 3000);
    }
  });
  
  // Monitor stderr for errors
  nextProcess.stderr.on('data', (data) => {
    const error = data.toString();
    console.error('❌ Next.js Error:', error.trim());
    hasError = true;
    
    if (error.includes('EADDRINUSE')) {
      console.log('\n💡 Port 3001 is in use, trying port 3002...');
      nextProcess.kill('SIGTERM');
      // Could restart on different port here
    }
    
    if (error.includes('Module not found')) {
      console.log('\n💡 Module resolution error detected');
    }
    
    if (error.includes('Cannot find module')) {
      console.log('\n💡 Missing dependency detected');
    }
  });
  
  // Handle process exit
  nextProcess.on('close', (code) => {
    if (code !== 0 && !hasStarted) {
      console.log(`\n❌ Next.js process exited with code ${code}`);
      
      if (!hasError) {
        console.log('\n💡 Possible issues:');
        console.log('- Configuration errors in next.config.mjs');
        console.log('- Missing dependencies');
        console.log('- TypeScript compilation errors');
        console.log('- Import/export issues');
      }
    } else if (hasStarted) {
      console.log('\n🎉 Next.js test completed successfully!');
    }
    
    process.exit(code);
  });
  
  // Timeout after 30 seconds
  setTimeout(() => {
    if (!hasStarted) {
      console.log('\n⏰ Timeout: Server did not start within 30 seconds');
      nextProcess.kill('SIGTERM');
      process.exit(1);
    }
  }, 30000);
}

// Handle Ctrl+C gracefully
process.on('SIGINT', () => {
  console.log('\n\n🛑 Test interrupted by user');
  process.exit(0);
});

testNextJS();
