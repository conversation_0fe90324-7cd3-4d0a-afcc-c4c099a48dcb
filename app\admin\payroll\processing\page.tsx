// Payroll Processing Workflow Interface
// Phase 4: User Interface Development - Processing Workflow

"use client"

import React, { useState, useEffect } from 'react'
import { 
  Calculator, 
  Play, 
  Pause, 
  CheckCircle, 
  AlertTriangle, 
  Clock, 
  Users, 
  FileText, 
  Download,
  RefreshCw,
  Settings,
  Eye,
  Edit,
  Trash2,
  Filter,
  Search
} from 'lucide-react'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Progress } from '@/components/ui/progress'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table'
import { Input } from '@/components/ui/input'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert'
import { Dialog, DialogContent, DialogHeader, DialogT<PERSON>le, DialogTrigger } from '@/components/ui/dialog'
import { CurrencyDisplay } from '@/components/ui/currency-display'
import { PayrollPeriodSelector } from '@/components/ui/fiscal-year-selector'
import { fiscalYearManager } from '@/lib/fiscal-year-manager'

interface ProcessingJob {
  id: string
  employeeId: string
  employeeName: string
  department: string
  status: 'pending' | 'processing' | 'completed' | 'error' | 'review'
  progress: number
  startTime?: string
  endTime?: string
  grossPay?: number
  netPay?: number
  errors?: string[]
  warnings?: string[]
}

interface ProcessingBatch {
  id: string
  name: string
  period: string
  totalEmployees: number
  processedEmployees: number
  status: 'draft' | 'running' | 'paused' | 'completed' | 'error'
  startTime?: string
  endTime?: string
  estimatedCompletion?: string
  jobs: ProcessingJob[]
}

export default function PayrollProcessingPage() {
  const [currentBatch, setCurrentBatch] = useState<ProcessingBatch | null>(null)
  const [processingHistory, setProcessingHistory] = useState<ProcessingBatch[]>([])
  const [selectedPeriod, setSelectedPeriod] = useState<any>(null)
  const [selectedFiscalYear, setSelectedFiscalYear] = useState(fiscalYearManager.getCurrentFiscalYear())
  const [searchTerm, setSearchTerm] = useState('')
  const [statusFilter, setStatusFilter] = useState('all')
  const [loading, setLoading] = useState(true)
  const [activeTab, setActiveTab] = useState('current')

  useEffect(() => {
    loadProcessingData()
  }, [selectedPeriod])

  const loadProcessingData = async () => {
    setLoading(true)
    try {
      // Mock data - in production, this would come from API
      const mockBatch: ProcessingBatch = {
        id: 'batch-001',
        name: 'Kartik 2081 Payroll',
        period: 'Kartik 2081',
        totalEmployees: 150,
        processedEmployees: 138,
        status: 'running',
        startTime: new Date(Date.now() - 3600000).toISOString(),
        estimatedCompletion: new Date(Date.now() + 1800000).toISOString(),
        jobs: [
          {
            id: 'job-001',
            employeeId: 'EMP001',
            employeeName: 'Ram Sharma',
            department: 'Engineering',
            status: 'completed',
            progress: 100,
            startTime: new Date(Date.now() - 3600000).toISOString(),
            endTime: new Date(Date.now() - 3500000).toISOString(),
            grossPay: 81000,
            netPay: 66000
          },
          {
            id: 'job-002',
            employeeId: 'EMP002',
            employeeName: 'Sita Poudel',
            department: 'Marketing',
            status: 'processing',
            progress: 75,
            startTime: new Date(Date.now() - 300000).toISOString(),
            grossPay: 78000
          },
          {
            id: 'job-003',
            employeeId: 'EMP003',
            employeeName: 'Hari Thapa',
            department: 'Design',
            status: 'error',
            progress: 0,
            errors: ['Missing attendance data', 'Invalid overtime hours']
          },
          {
            id: 'job-004',
            employeeId: 'EMP004',
            employeeName: 'Gita Rai',
            department: 'HR',
            status: 'review',
            progress: 100,
            grossPay: 75000,
            netPay: 62000,
            warnings: ['Overtime exceeds daily limit']
          }
        ]
      }

      const mockHistory: ProcessingBatch[] = [
        {
          id: 'batch-002',
          name: 'Ashwin 2081 Payroll',
          period: 'Ashwin 2081',
          totalEmployees: 149,
          processedEmployees: 149,
          status: 'completed',
          startTime: '2024-10-16T10:00:00Z',
          endTime: '2024-10-16T12:30:00Z',
          jobs: []
        },
        {
          id: 'batch-003',
          name: 'Bhadra 2081 Payroll',
          period: 'Bhadra 2081',
          totalEmployees: 148,
          processedEmployees: 148,
          status: 'completed',
          startTime: '2024-09-16T10:00:00Z',
          endTime: '2024-09-16T11:45:00Z',
          jobs: []
        }
      ]

      setCurrentBatch(mockBatch)
      setProcessingHistory(mockHistory)
    } catch (error) {
      console.error('Error loading processing data:', error)
    } finally {
      setLoading(false)
    }
  }

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'completed':
        return <CheckCircle className="h-4 w-4 text-green-600" />
      case 'processing':
      case 'running':
        return <RefreshCw className="h-4 w-4 text-blue-600 animate-spin" />
      case 'error':
        return <AlertTriangle className="h-4 w-4 text-red-600" />
      case 'review':
        return <Eye className="h-4 w-4 text-yellow-600" />
      case 'pending':
      case 'paused':
        return <Clock className="h-4 w-4 text-gray-600" />
      default:
        return <Clock className="h-4 w-4 text-gray-600" />
    }
  }

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'completed':
        return <Badge variant="default">Completed</Badge>
      case 'processing':
      case 'running':
        return <Badge variant="secondary">Processing</Badge>
      case 'error':
        return <Badge variant="destructive">Error</Badge>
      case 'review':
        return <Badge variant="outline">Review</Badge>
      case 'pending':
        return <Badge variant="outline">Pending</Badge>
      case 'paused':
        return <Badge variant="secondary">Paused</Badge>
      default:
        return <Badge variant="outline">Unknown</Badge>
    }
  }

  const handleStartProcessing = () => {
    console.log('Starting payroll processing...')
  }

  const handlePauseProcessing = () => {
    console.log('Pausing payroll processing...')
  }

  const handleRetryJob = (jobId: string) => {
    console.log('Retrying job:', jobId)
  }

  const handleViewJob = (jobId: string) => {
    console.log('Viewing job details:', jobId)
  }

  const filteredJobs = currentBatch?.jobs.filter(job => {
    const matchesSearch = job.employeeName.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         job.employeeId.toLowerCase().includes(searchTerm.toLowerCase())
    const matchesStatus = statusFilter === 'all' || job.status === statusFilter
    return matchesSearch && matchesStatus
  }) || []

  if (loading) {
    return (
      <div className="p-6">
        <div className="flex items-center justify-center h-64">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
        </div>
      </div>
    )
  }

  return (
    <div className="p-6 space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold">Payroll Processing</h1>
          <p className="text-muted-foreground">
            Monitor and manage payroll calculation workflows
          </p>
        </div>
        
        <div className="flex items-center gap-4">
          <PayrollPeriodSelector
            fiscalYear={selectedFiscalYear}
            value={selectedPeriod}
            onChange={setSelectedPeriod}
            periodType="monthly"
          />
          <Button>
            <Settings className="h-4 w-4 mr-2" />
            Settings
          </Button>
        </div>
      </div>

      {/* Current Processing Status */}
      {currentBatch && (
        <Card>
          <CardHeader>
            <div className="flex items-center justify-between">
              <CardTitle className="flex items-center gap-2">
                {getStatusIcon(currentBatch.status)}
                Current Processing: {currentBatch.name}
              </CardTitle>
              <div className="flex items-center gap-2">
                {getStatusBadge(currentBatch.status)}
                {currentBatch.status === 'running' && (
                  <Button variant="outline" size="sm" onClick={handlePauseProcessing}>
                    <Pause className="h-4 w-4 mr-1" />
                    Pause
                  </Button>
                )}
                {(currentBatch.status === 'paused' || currentBatch.status === 'draft') && (
                  <Button size="sm" onClick={handleStartProcessing}>
                    <Play className="h-4 w-4 mr-1" />
                    Start
                  </Button>
                )}
              </div>
            </div>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
              <div className="text-center">
                <div className="text-2xl font-bold">{currentBatch.totalEmployees}</div>
                <div className="text-sm text-muted-foreground">Total Employees</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-green-600">{currentBatch.processedEmployees}</div>
                <div className="text-sm text-muted-foreground">Processed</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-yellow-600">
                  {currentBatch.jobs.filter(j => j.status === 'review').length}
                </div>
                <div className="text-sm text-muted-foreground">Need Review</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-red-600">
                  {currentBatch.jobs.filter(j => j.status === 'error').length}
                </div>
                <div className="text-sm text-muted-foreground">Errors</div>
              </div>
            </div>

            <div className="space-y-2">
              <div className="flex justify-between text-sm">
                <span>Progress</span>
                <span>{Math.round((currentBatch.processedEmployees / currentBatch.totalEmployees) * 100)}%</span>
              </div>
              <Progress value={(currentBatch.processedEmployees / currentBatch.totalEmployees) * 100} />
            </div>

            {currentBatch.estimatedCompletion && (
              <div className="text-sm text-muted-foreground">
                Estimated completion: {new Date(currentBatch.estimatedCompletion).toLocaleTimeString()}
              </div>
            )}
          </CardContent>
        </Card>
      )}

      {/* Processing Alerts */}
      {currentBatch && currentBatch.jobs.some(j => j.status === 'error' || j.status === 'review') && (
        <div className="space-y-4">
          {currentBatch.jobs.filter(j => j.status === 'error').length > 0 && (
            <Alert variant="destructive">
              <AlertTriangle className="h-4 w-4" />
              <AlertTitle>Processing Errors</AlertTitle>
              <AlertDescription>
                {currentBatch.jobs.filter(j => j.status === 'error').length} employee{currentBatch.jobs.filter(j => j.status === 'error').length !== 1 ? 's' : ''} failed to process. 
                Review and fix errors to continue.
              </AlertDescription>
            </Alert>
          )}
          
          {currentBatch.jobs.filter(j => j.status === 'review').length > 0 && (
            <Alert>
              <Eye className="h-4 w-4" />
              <AlertTitle>Review Required</AlertTitle>
              <AlertDescription>
                {currentBatch.jobs.filter(j => j.status === 'review').length} employee{currentBatch.jobs.filter(j => j.status === 'review').length !== 1 ? 's' : ''} require manual review before approval.
              </AlertDescription>
            </Alert>
          )}
        </div>
      )}

      {/* Main Content Tabs */}
      <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-4">
        <TabsList className="grid w-full grid-cols-3">
          <TabsTrigger value="current">Current Processing</TabsTrigger>
          <TabsTrigger value="history">Processing History</TabsTrigger>
          <TabsTrigger value="settings">Batch Settings</TabsTrigger>
        </TabsList>

        <TabsContent value="current" className="space-y-6">
          {currentBatch && (
            <Card>
              <CardHeader>
                <div className="flex items-center justify-between">
                  <CardTitle>Processing Queue</CardTitle>
                  <div className="flex items-center gap-4">
                    <div className="flex items-center space-x-4">
                      <div className="relative flex-1">
                        <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
                        <Input
                          placeholder="Search employees..."
                          value={searchTerm}
                          onChange={(e) => setSearchTerm(e.target.value)}
                          className="pl-8 w-64"
                        />
                      </div>
                      <Select value={statusFilter} onValueChange={setStatusFilter}>
                        <SelectTrigger className="w-40">
                          <Filter className="mr-2 h-4 w-4" />
                          <SelectValue placeholder="Filter status" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="all">All Status</SelectItem>
                          <SelectItem value="pending">Pending</SelectItem>
                          <SelectItem value="processing">Processing</SelectItem>
                          <SelectItem value="completed">Completed</SelectItem>
                          <SelectItem value="error">Error</SelectItem>
                          <SelectItem value="review">Review</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                  </div>
                </div>
              </CardHeader>
              <CardContent>
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Employee</TableHead>
                      <TableHead>Department</TableHead>
                      <TableHead>Status</TableHead>
                      <TableHead>Progress</TableHead>
                      <TableHead>Gross Pay</TableHead>
                      <TableHead>Net Pay</TableHead>
                      <TableHead>Actions</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {filteredJobs.map((job) => (
                      <TableRow key={job.id}>
                        <TableCell>
                          <div>
                            <div className="font-medium">{job.employeeName}</div>
                            <div className="text-sm text-muted-foreground">{job.employeeId}</div>
                          </div>
                        </TableCell>
                        <TableCell>{job.department}</TableCell>
                        <TableCell>
                          <div className="flex items-center gap-2">
                            {getStatusIcon(job.status)}
                            {getStatusBadge(job.status)}
                          </div>
                        </TableCell>
                        <TableCell>
                          <div className="space-y-1">
                            <Progress value={job.progress} className="h-2" />
                            <div className="text-xs text-muted-foreground">{job.progress}%</div>
                          </div>
                        </TableCell>
                        <TableCell>
                          {job.grossPay ? <CurrencyDisplay amount={job.grossPay} size="sm" /> : '-'}
                        </TableCell>
                        <TableCell>
                          {job.netPay ? <CurrencyDisplay amount={job.netPay} size="sm" /> : '-'}
                        </TableCell>
                        <TableCell>
                          <div className="flex items-center gap-2">
                            <Button size="sm" variant="outline" onClick={() => handleViewJob(job.id)}>
                              <Eye className="h-4 w-4" />
                            </Button>
                            {job.status === 'error' && (
                              <Button size="sm" variant="outline" onClick={() => handleRetryJob(job.id)}>
                                <RefreshCw className="h-4 w-4" />
                              </Button>
                            )}
                          </div>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </CardContent>
            </Card>
          )}
        </TabsContent>

        <TabsContent value="history" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Processing History</CardTitle>
            </CardHeader>
            <CardContent>
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Batch Name</TableHead>
                    <TableHead>Period</TableHead>
                    <TableHead>Employees</TableHead>
                    <TableHead>Status</TableHead>
                    <TableHead>Duration</TableHead>
                    <TableHead>Actions</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {processingHistory.map((batch) => (
                    <TableRow key={batch.id}>
                      <TableCell className="font-medium">{batch.name}</TableCell>
                      <TableCell>{batch.period}</TableCell>
                      <TableCell>{batch.totalEmployees}</TableCell>
                      <TableCell>
                        <div className="flex items-center gap-2">
                          {getStatusIcon(batch.status)}
                          {getStatusBadge(batch.status)}
                        </div>
                      </TableCell>
                      <TableCell>
                        {batch.startTime && batch.endTime && (
                          <span>
                            {Math.round((new Date(batch.endTime).getTime() - new Date(batch.startTime).getTime()) / 60000)} min
                          </span>
                        )}
                      </TableCell>
                      <TableCell>
                        <div className="flex items-center gap-2">
                          <Button size="sm" variant="outline">
                            <Eye className="h-4 w-4" />
                          </Button>
                          <Button size="sm" variant="outline">
                            <Download className="h-4 w-4" />
                          </Button>
                        </div>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="settings" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Batch Processing Settings</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-center py-8 text-muted-foreground">
                Batch processing configuration interface coming soon
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  )
}
