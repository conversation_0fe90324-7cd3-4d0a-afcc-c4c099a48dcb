"use client"

import React, { useState, useEffect } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Badge } from "@/components/ui/badge"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Textarea } from "@/components/ui/textarea"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Clock, Edit, UserCheck, UserX, Search, Filter, Calendar, Plus, Loader2, Trash2, RefreshC<PERSON>, Users, CheckCircle, XCircle, AlertCircle, CalendarDays } from "lucide-react"
import { toast } from "@/hooks/use-toast"
import { useAuth } from "@/components/auth-provider"
import { formatTime, formatDate, formatTimeForInput, localTimeToUTC } from "@/lib/attendance-utils"
import { MonthlyAttendanceCalendar } from "@/components/admin/monthly-attendance-calendar"

interface AttendanceRecord {
  id: string
  user_id: string
  user_name: string
  user_email: string
  date: string
  check_in_time?: string
  check_out_time?: string
  status: "present" | "absent" | "late" | "half_day" | "on_leave"
  hours_worked?: number
  notes?: string
  created_at: string
  updated_at: string
  // New fields for multiple entries
  daily_sequence?: number
  entry_type?: string
  is_active?: boolean
}

interface AttendanceStats {
  totalPresent: number
  totalAbsent: number
  totalLate: number
  totalHalfDay: number
  totalOnLeave: number
  averageHoursWorked: number
}

interface User {
  id: string
  email: string
  full_name: string
  role: string
  department?: string
  position?: string
}

interface EmployeeAttendanceStatus {
  id: string
  employee_id: string
  email: string
  full_name: string
  role: string
  department?: string
  position?: string
  is_active: boolean
  current_status: "checked_in" | "checked_out" | "not_started"
  active_session?: {
    id: string
    check_in_time: string
    daily_sequence: number
    entry_type: string
    hours_worked_today: number
  }
  today_sessions: number
  total_hours_today: number
  remaining_check_ins: number
  remaining_check_outs: number
  last_action?: {
    type: "check_in" | "check_out"
    time: string
  }
}

interface EmployeeStatusSummary {
  total_employees: number
  checked_in: number
  checked_out: number
  not_started: number
  total_hours_today: number
}



export default function AdminAttendancePage() {
  const { user } = useAuth()

  // Check if user has admin or HR manager permissions
  const hasQuickActionPermissions = user && ["admin", "hr_manager"].includes(user.role)
  const [loading, setLoading] = useState(false)
  const [attendanceData, setAttendanceData] = useState<AttendanceRecord[]>([])
  const [attendanceStats, setAttendanceStats] = useState<AttendanceStats | null>(null)
  const [searchTerm, setSearchTerm] = useState("")
  const [statusFilter, setStatusFilter] = useState<string>("all")
  const [selectedDate, setSelectedDate] = useState(new Date().toISOString().split("T")[0])
  const [selectedRecord, setSelectedRecord] = useState<AttendanceRecord | null>(null)
  const [editDialogOpen, setEditDialogOpen] = useState(false)
  const [createDialogOpen, setCreateDialogOpen] = useState(false)
  const [checkInTime, setCheckInTime] = useState("")
  const [checkOutTime, setCheckOutTime] = useState("")
  const [selectedStatus, setSelectedStatus] = useState<string>("present")
  const [hoursWorked, setHoursWorked] = useState("")
  const [notes, setNotes] = useState("")
  const [users, setUsers] = useState<User[]>([])
  const [selectedUserId, setSelectedUserId] = useState("")
  const [autoRefresh, setAutoRefresh] = useState(false)
  const [refreshInterval, setRefreshInterval] = useState<NodeJS.Timeout | null>(null)
  const [viewMode, setViewMode] = useState<"summary" | "detailed">("summary")

  // Employee list state
  const [employeeList, setEmployeeList] = useState<EmployeeAttendanceStatus[]>([])
  const [employeeSummary, setEmployeeSummary] = useState<EmployeeStatusSummary | null>(null)
  const [employeeSearchTerm, setEmployeeSearchTerm] = useState("")
  const [employeeStatusFilter, setEmployeeStatusFilter] = useState<string>("all")
  const [loadingEmployees, setLoadingEmployees] = useState(false)
  const [activeView, setActiveView] = useState<"attendance" | "employees" | "monthly">("attendance")
  const [calendarType, setCalendarType] = useState<"english" | "nepali">("english")

  // Confirmation dialog state
  const [confirmDialogOpen, setConfirmDialogOpen] = useState(false)
  const [pendingAction, setPendingAction] = useState<{
    userId: string
    action: "clock-in" | "clock-out"
    employeeName: string
  } | null>(null)

  // Fetch data on component mount and when date changes
  useEffect(() => {
    fetchAttendanceData()
    fetchUsers()
    fetchEmployeeAttendanceStatus()
  }, [selectedDate])

  // Auto-refresh functionality
  useEffect(() => {
    if (autoRefresh) {
      const interval = setInterval(() => {
        fetchAttendanceData()
        fetchEmployeeAttendanceStatus()
      }, 30000) // Refresh every 30 seconds

      setRefreshInterval(interval)
      return () => {
        if (interval) clearInterval(interval)
      }
    } else {
      if (refreshInterval) {
        clearInterval(refreshInterval)
        setRefreshInterval(null)
      }
    }
  }, [autoRefresh, selectedDate])

  // Cleanup interval on unmount
  useEffect(() => {
    return () => {
      if (refreshInterval) {
        clearInterval(refreshInterval)
      }
    }
  }, [])

  const fetchAttendanceData = async () => {
    setLoading(true)
    try {
      const response = await fetch(`/api/admin/attendance?date=${selectedDate}`, {
        credentials: "include",
      })
      const data = await response.json()

      if (data.success) {
        setAttendanceData(data.attendance)
        setAttendanceStats(data.stats)
      } else {
        toast({
          title: "Error",
          description: data.error || "Failed to fetch attendance data",
          variant: "destructive",
        })
      }
    } catch (error) {
      console.error("Error fetching attendance data:", error)
      toast({
        title: "Error",
        description: "Failed to fetch attendance data",
        variant: "destructive",
      })
    } finally {
      setLoading(false)
    }
  }

  const fetchUsers = async () => {
    try {
      const response = await fetch("/api/admin/users", {
        credentials: "include",
      })
      const data = await response.json()

      if (data.success) {
        setUsers(data.users || [])
      }
    } catch (error) {
      console.error("Error fetching users:", error)
    }
  }

  const fetchEmployeeAttendanceStatus = async () => {
    setLoadingEmployees(true)
    try {
      const response = await fetch("/api/admin/employees/attendance-status", {
        credentials: "include",
      })
      const data = await response.json()

      if (data.success) {
        setEmployeeList(data.employees || [])
        setEmployeeSummary(data.summary || null)
      } else {
        toast({
          title: "Error",
          description: data.error || "Failed to fetch employee attendance status",
          variant: "destructive",
        })
      }
    } catch (error) {
      console.error("Error fetching employee attendance status:", error)
      toast({
        title: "Error",
        description: "Failed to fetch employee attendance status",
        variant: "destructive",
      })
    } finally {
      setLoadingEmployees(false)
    }
  }

  // Process data based on view mode
  const processedData = viewMode === "summary"
    ? // Summary view: Group by user and show aggregated data
      attendanceData.reduce((acc, record) => {
        const existingUser = acc.find(item => item.user_id === record.user_id);
        if (existingUser) {
          // Update existing user summary
          existingUser.hours_worked = (Number(existingUser.hours_worked) || 0) + (Number(record.hours_worked) || 0);
          if (record.check_in_time && !existingUser.check_in_time) {
            existingUser.check_in_time = record.check_in_time;
          }
          if (record.check_out_time) {
            existingUser.check_out_time = record.check_out_time;
          }
          // Show most recent status
          if (new Date(record.created_at) > new Date(existingUser.created_at)) {
            existingUser.status = record.status;
            existingUser.created_at = record.created_at;
          }
        } else {
          // Add new user summary
          acc.push({
            ...record,
            notes: `${attendanceData.filter(r => r.user_id === record.user_id).length} sessions today`
          });
        }
        return acc;
      }, [] as AttendanceRecord[])
    : // Detailed view: Show all entries
      attendanceData.sort((a, b) => {
        // Sort by user name, then by sequence
        if (a.user_name !== b.user_name) {
          return a.user_name.localeCompare(b.user_name);
        }
        return (a.daily_sequence || 0) - (b.daily_sequence || 0);
      });

  const filteredData = processedData.filter((record) => {
    const matchesSearch =
      record.user_name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      record.user_email.toLowerCase().includes(searchTerm.toLowerCase())
    const matchesStatus = statusFilter === "all" || record.status === statusFilter
    return matchesSearch && matchesStatus
  })

  // Filter employee list
  const filteredEmployees = employeeList.filter((employee) => {
    const matchesSearch =
      employee.full_name.toLowerCase().includes(employeeSearchTerm.toLowerCase()) ||
      employee.email.toLowerCase().includes(employeeSearchTerm.toLowerCase()) ||
      employee.employee_id.toLowerCase().includes(employeeSearchTerm.toLowerCase()) ||
      (employee.department && employee.department.toLowerCase().includes(employeeSearchTerm.toLowerCase()))
    const matchesStatus = employeeStatusFilter === "all" || employee.current_status === employeeStatusFilter
    return matchesSearch && matchesStatus
  })

  const handleManualClockRequest = (userId: string, action: "clock-in" | "clock-out") => {
    const employee = employeeList.find(emp => emp.id === userId)
    const employeeName = employee?.full_name || "Employee"

    setPendingAction({ userId, action, employeeName })
    setConfirmDialogOpen(true)
  }

  const handleConfirmManualClock = async () => {
    if (!pendingAction) return

    setConfirmDialogOpen(false)
    await handleManualClock(pendingAction.userId, pendingAction.action)
    setPendingAction(null)
  }

  const handleManualClock = async (userId: string, action: "clock-in" | "clock-out") => {
    setLoading(true)
    setLoadingEmployees(true)

    try {
      const response = await fetch("/api/admin/attendance/manual-clock", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        credentials: "include",
        body: JSON.stringify({ userId, action }),
      })

      const data = await response.json()

      if (data.success) {
        // Find employee name for better toast message
        const employee = employeeList.find(emp => emp.id === userId)
        const employeeName = employee?.full_name || "Employee"

        toast({
          title: "Success",
          description: `${employeeName} successfully ${action.replace("-", "ed ")}`,
        })

        // Refresh both views immediately for real-time updates
        await Promise.all([
          fetchAttendanceData(),
          fetchEmployeeAttendanceStatus()
        ])
      } else {
        toast({
          title: "Error",
          description: data.error,
          variant: "destructive",
        })
      }
    } catch (error) {
      console.error("Error with manual clock:", error)
      toast({
        title: "Error",
        description: `Failed to ${action} employee`,
        variant: "destructive",
      })
    } finally {
      setLoading(false)
      setLoadingEmployees(false)
    }
  }

  const handleEditAttendance = (record: AttendanceRecord) => {
    setSelectedRecord(record)
    setCheckInTime(record.check_in_time ? formatTimeForInput(record.check_in_time) : "")
    setCheckOutTime(record.check_out_time ? formatTimeForInput(record.check_out_time) : "")
    setSelectedStatus(record.status)
    setHoursWorked(record.hours_worked?.toString() || "")
    setNotes(record.notes || "")
    setEditDialogOpen(true)
  }

  const handleSaveEdit = async () => {
    if (!selectedRecord) return

    setLoading(true)
    try {
      const response = await fetch(`/api/admin/attendance/${selectedRecord.id}`, {
        method: "PUT",
        headers: {
          "Content-Type": "application/json",
        },
        credentials: "include",
        body: JSON.stringify({
          checkInTime: checkInTime ? localTimeToUTC(checkInTime, selectedDate) : undefined,
          checkOutTime: checkOutTime ? localTimeToUTC(checkOutTime, selectedDate) : undefined,
          status: selectedStatus,
          hoursWorked: hoursWorked ? Number.parseFloat(hoursWorked) : undefined,
          notes: notes.trim() || undefined,
        }),
      })

      const data = await response.json()

      if (data.success) {
        toast({
          title: "Success",
          description: data.message,
        })
        setEditDialogOpen(false)
        fetchAttendanceData() // Refresh data
        resetForm()
      } else {
        toast({
          title: "Error",
          description: data.error,
          variant: "destructive",
        })
      }
    } catch (error) {
      console.error("Error updating attendance:", error)
      toast({
        title: "Error",
        description: "Failed to update attendance record",
        variant: "destructive",
      })
    } finally {
      setLoading(false)
    }
  }

  const handleCreateAttendance = async () => {
    if (!selectedUserId) {
      toast({
        title: "Error",
        description: "Please select a user",
        variant: "destructive",
      })
      return
    }

    setLoading(true)
    try {
      const response = await fetch("/api/admin/attendance", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        credentials: "include",
        body: JSON.stringify({
          userId: selectedUserId,
          date: selectedDate,
          checkInTime: checkInTime ? localTimeToUTC(checkInTime, selectedDate) : undefined,
          checkOutTime: checkOutTime ? localTimeToUTC(checkOutTime, selectedDate) : undefined,
          status: selectedStatus,
          hoursWorked: hoursWorked ? Number.parseFloat(hoursWorked) : undefined,
          notes: notes.trim() || undefined,
        }),
      })

      const data = await response.json()

      if (data.success) {
        toast({
          title: "Success",
          description: data.message,
        })
        setCreateDialogOpen(false)
        fetchAttendanceData() // Refresh data
        resetForm()
      } else {
        toast({
          title: "Error",
          description: data.error,
          variant: "destructive",
        })
      }
    } catch (error) {
      console.error("Error creating attendance:", error)
      toast({
        title: "Error",
        description: "Failed to create attendance record",
        variant: "destructive",
      })
    } finally {
      setLoading(false)
    }
  }

  const handleDeleteAttendance = async (attendanceId: string) => {
    if (!confirm("Are you sure you want to delete this attendance record?")) {
      return
    }

    setLoading(true)
    try {
      const response = await fetch(`/api/admin/attendance/${attendanceId}`, {
        method: "DELETE",
        credentials: "include",
      })

      const data = await response.json()

      if (data.success) {
        toast({
          title: "Success",
          description: data.message,
        })
        fetchAttendanceData() // Refresh data
      } else {
        toast({
          title: "Error",
          description: data.error,
          variant: "destructive",
        })
      }
    } catch (error) {
      console.error("Error deleting attendance:", error)
      toast({
        title: "Error",
        description: "Failed to delete attendance record",
        variant: "destructive",
      })
    } finally {
      setLoading(false)
    }
  }

  const resetForm = () => {
    setCheckInTime("")
    setCheckOutTime("")
    setSelectedStatus("present")
    setHoursWorked("")
    setNotes("")
    setSelectedUserId("")
  }

  // Helper functions for employee status
  const getEmployeeStatusBadge = (status: string) => {
    switch (status) {
      case "checked_in":
        return "bg-green-100 text-green-800 border-green-200"
      case "checked_out":
        return "bg-blue-100 text-blue-800 border-blue-200"
      case "not_started":
        return "bg-gray-100 text-gray-800 border-gray-200"
      default:
        return "bg-gray-100 text-gray-800 border-gray-200"
    }
  }

  const getEmployeeStatusIcon = (status: string) => {
    switch (status) {
      case "checked_in":
        return <CheckCircle className="h-4 w-4 text-green-600" />
      case "checked_out":
        return <XCircle className="h-4 w-4 text-blue-600" />
      case "not_started":
        return <AlertCircle className="h-4 w-4 text-gray-600" />
      default:
        return <AlertCircle className="h-4 w-4 text-gray-600" />
    }
  }

  const formatEmployeeStatus = (status: string) => {
    switch (status) {
      case "checked_in":
        return "Checked In"
      case "checked_out":
        return "Checked Out"
      case "not_started":
        return "Not Started"
      default:
        return "Unknown"
    }
  }

  // Using utility functions for consistent formatting

  const getStatusBadge = (status: string) => {
    const variants = {
      present: "bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300",
      absent: "bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300",
      late: "bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-300",
      half_day: "bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300",
      on_leave: "bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-300",
    }
    return variants[status as keyof typeof variants] || variants.present
  }

  const stats = {
    present: attendanceData.filter((r) => r.status === "present").length,
    absent: attendanceData.filter((r) => r.status === "absent").length,
    late: attendanceData.filter((r) => r.status === "late").length,
    total: attendanceData.length,
  }

  return (
    <div className="space-y-6">
      {/* Header Section */}
      <div className="flex flex-col space-y-4 lg:flex-row lg:items-center lg:justify-between lg:space-y-0">
        <div>
          <h1 className="text-2xl lg:text-3xl font-bold tracking-tight">Attendance Management</h1>
          <p className="text-muted-foreground">Manage employee attendance, check-ins, and working hours</p>
        </div>

        {/* Primary Actions - Always visible */}
        <div className="flex flex-col space-y-3 sm:flex-row sm:items-center sm:space-y-0 sm:space-x-3">
          {/* View Toggle */}
          <div className="flex items-center space-x-1 border rounded-lg p-1 bg-muted/50">
            <Button
              onClick={() => setActiveView("attendance")}
              variant={activeView === "attendance" ? "default" : "ghost"}
              size="sm"
              className="text-xs sm:text-sm"
            >
              <Calendar className="mr-1 sm:mr-2 h-3 w-3 sm:h-4 sm:w-4" />
              <span className="hidden sm:inline">Daily Records</span>
              <span className="sm:hidden">Daily</span>
            </Button>
            {hasQuickActionPermissions && (
              <>
                <Button
                  onClick={() => setActiveView("employees")}
                  variant={activeView === "employees" ? "default" : "ghost"}
                  size="sm"
                  className="text-xs sm:text-sm"
                >
                  <Users className="mr-1 sm:mr-2 h-3 w-3 sm:h-4 sm:w-4" />
                  <span className="hidden sm:inline">Employee List</span>
                  <span className="sm:hidden">Employees</span>
                </Button>
                <Button
                  onClick={() => setActiveView("monthly")}
                  variant={activeView === "monthly" ? "default" : "ghost"}
                  size="sm"
                  className="text-xs sm:text-sm"
                >
                  <CalendarDays className="mr-1 sm:mr-2 h-3 w-3 sm:h-4 sm:w-4" />
                  <span className="hidden sm:inline">Monthly View</span>
                  <span className="sm:hidden">Monthly</span>
                </Button>
              </>
            )}
          </div>

          {/* Date Input - Only show for daily views */}
          {activeView !== "monthly" && (
            <Input
              type="date"
              value={selectedDate}
              onChange={(e) => setSelectedDate(e.target.value)}
              className="w-full sm:w-auto"
            />
          )}
        </div>
      </div>

      {/* Secondary Actions - Below main content on mobile */}
      {activeView === "monthly" ? (
        <div className="flex flex-wrap items-center gap-2 justify-between border-b pb-4">
          <div className="flex flex-wrap items-center gap-2">
            <Button
              onClick={() => setAutoRefresh(!autoRefresh)}
              variant={autoRefresh ? "default" : "outline"}
              size="sm"
              className="text-xs sm:text-sm"
            >
              <Clock className="mr-1 sm:mr-2 h-3 w-3 sm:h-4 sm:w-4" />
              <span className="hidden sm:inline">{autoRefresh ? "Auto-refresh ON" : "Auto-refresh OFF"}</span>
              <span className="sm:hidden">{autoRefresh ? "Auto ON" : "Auto OFF"}</span>
            </Button>
          </div>
          <div className="text-sm text-muted-foreground">
            Monthly view • {autoRefresh ? "Updates every 30 seconds" : "Manual refresh only"}
          </div>
        </div>
      ) : (
        <div className="flex flex-wrap items-center gap-2 justify-between border-b pb-4">
          <div className="flex flex-wrap items-center gap-2">
            <Button
              onClick={() => {
                fetchAttendanceData()
                fetchEmployeeAttendanceStatus()
              }}
              variant="outline"
              size="sm"
              disabled={loading || loadingEmployees}
              className="text-xs sm:text-sm"
            >
              <RefreshCw className={`mr-1 sm:mr-2 h-3 w-3 sm:h-4 sm:w-4 ${(loading || loadingEmployees) ? 'animate-spin' : ''}`} />
              <span className="hidden sm:inline">Refresh</span>
              <span className="sm:hidden">↻</span>
            </Button>

            <Button
              onClick={() => setAutoRefresh(!autoRefresh)}
              variant={autoRefresh ? "default" : "outline"}
              size="sm"
              className="text-xs sm:text-sm"
            >
              <Clock className="mr-1 sm:mr-2 h-3 w-3 sm:h-4 sm:w-4" />
              <span className="hidden sm:inline">{autoRefresh ? "Auto-refresh ON" : "Auto-refresh OFF"}</span>
              <span className="sm:hidden">{autoRefresh ? "Auto ON" : "Auto OFF"}</span>
            </Button>
          </div>

          <Button
            onClick={() => setCreateDialogOpen(true)}
            className="bg-teal-600 hover:bg-teal-700 text-xs sm:text-sm"
            size="sm"
          >
            <Plus className="mr-1 sm:mr-2 h-3 w-3 sm:h-4 sm:w-4" />
            <span className="hidden sm:inline">Add Record</span>
            <span className="sm:hidden">Add</span>
          </Button>
        </div>
      )}

      {/* Stats Cards - Conditional based on active view */}
      {activeView === "attendance" && (
        <div className="grid gap-4 md:grid-cols-4">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Total Records</CardTitle>
              <Clock className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{stats.total}</div>
            </CardContent>
          </Card>
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Present</CardTitle>
              <UserCheck className="h-4 w-4 text-green-600" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-green-600">{stats.present}</div>
            </CardContent>
          </Card>
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Absent</CardTitle>
              <UserX className="h-4 w-4 text-red-600" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-red-600">{stats.absent}</div>
            </CardContent>
          </Card>
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Late</CardTitle>
              <Clock className="h-4 w-4 text-warning" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-warning">{stats.late}</div>
            </CardContent>
          </Card>
        </div>
      )}

      {activeView === "employees" && employeeSummary && (
        <div className="grid gap-4 md:grid-cols-5">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Total Employees</CardTitle>
              <Users className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{employeeSummary.total_employees}</div>
            </CardContent>
          </Card>
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Checked In</CardTitle>
              <CheckCircle className="h-4 w-4 text-green-600" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-green-600">{employeeSummary.checked_in}</div>
            </CardContent>
          </Card>
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Checked Out</CardTitle>
              <XCircle className="h-4 w-4 text-blue-600" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-blue-600">{employeeSummary.checked_out}</div>
            </CardContent>
          </Card>
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Not Started</CardTitle>
              <AlertCircle className="h-4 w-4 text-gray-600" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-gray-600">{employeeSummary.not_started}</div>
            </CardContent>
          </Card>
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Total Hours Today</CardTitle>
              <Clock className="h-4 w-4 text-purple-600" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-purple-600">{employeeSummary.total_hours_today.toFixed(1)}h</div>
            </CardContent>
          </Card>
        </div>
      )}

      {/* Attendance Records View */}
      {activeView === "attendance" && (
        <Card>
          <CardHeader>
            <CardTitle>Employee Attendance Records</CardTitle>
            <CardDescription>Track and manage employee attendance for {selectedDate}</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="flex items-center space-x-4 mb-6">
              <div className="relative flex-1">
                <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
                <Input
                  placeholder="Search employees..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-8"
                />
              </div>
              <Select value={statusFilter} onValueChange={setStatusFilter}>
                <SelectTrigger className="w-[180px]">
                  <Filter className="mr-2 h-4 w-4" />
                  <SelectValue placeholder="Filter by status" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Status</SelectItem>
                  <SelectItem value="present">Present</SelectItem>
                  <SelectItem value="absent">Absent</SelectItem>
                  <SelectItem value="late">Late</SelectItem>
                  <SelectItem value="half_day">Half Day</SelectItem>
                  <SelectItem value="on_leave">On Leave</SelectItem>
                </SelectContent>
              </Select>
              <Select value={viewMode} onValueChange={(value: "summary" | "detailed") => setViewMode(value)}>
                <SelectTrigger className="w-[150px]">
                  <SelectValue placeholder="View mode" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="summary">Summary View</SelectItem>
                  <SelectItem value="detailed">Detailed View</SelectItem>
                </SelectContent>
              </Select>
            </div>

          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Employee</TableHead>
                <TableHead>Email</TableHead>
                {viewMode === "detailed" && <TableHead>Session #</TableHead>}
                <TableHead>Status</TableHead>
                <TableHead>Check In</TableHead>
                <TableHead>Check Out</TableHead>
                <TableHead>Hours Worked</TableHead>
                {viewMode === "detailed" && <TableHead>Type</TableHead>}
                <TableHead>Actions</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {loading ? (
                <TableRow>
                  <TableCell colSpan={viewMode === "detailed" ? 9 : 7} className="text-center py-8">
                    <Loader2 className="h-6 w-6 animate-spin mx-auto" />
                  </TableCell>
                </TableRow>
              ) : filteredData.length > 0 ? (
                filteredData.map((record) => (
                  <TableRow key={record.id}>
                    <TableCell className="flex items-center space-x-3">
                      <Avatar className="h-8 w-8">
                        <AvatarImage src="/placeholder.svg" />
                        <AvatarFallback>{record.user_name.charAt(0)}</AvatarFallback>
                      </Avatar>
                      <span className="font-medium">{record.user_name}</span>
                    </TableCell>
                    <TableCell>{record.user_email}</TableCell>
                    {viewMode === "detailed" && (
                      <TableCell>
                        <Badge variant="outline">#{record.daily_sequence || 1}</Badge>
                        {record.is_active && <span className="ml-1 text-green-600 text-xs">●</span>}
                      </TableCell>
                    )}
                    <TableCell>
                      <Badge className={getStatusBadge(record.status)}>
                        {record.status.replace("_", " ").toUpperCase()}
                      </Badge>
                    </TableCell>
                    <TableCell>{formatTime(record.check_in_time)}</TableCell>
                    <TableCell>{formatTime(record.check_out_time)}</TableCell>
                    <TableCell>{record.hours_worked ? `${Number(record.hours_worked).toFixed(1)}h` : "-"}</TableCell>
                    {viewMode === "detailed" && (
                      <TableCell>
                        <span className="text-sm text-muted-foreground capitalize">
                          {record.entry_type || 'regular'}
                        </span>
                      </TableCell>
                    )}
                    <TableCell>
                      <div className="flex items-center space-x-2">
                        {hasQuickActionPermissions && !record.check_in_time && (
                          <Button
                            size="sm"
                            onClick={() => handleManualClockRequest(record.user_id, "clock-in")}
                            className="bg-green-600 hover:bg-green-700"
                            disabled={loading}
                          >
                            {loading ? (
                              <Loader2 className="h-4 w-4 mr-1 animate-spin" />
                            ) : null}
                            Check In
                          </Button>
                        )}
                        {hasQuickActionPermissions && record.check_in_time && !record.check_out_time && (
                          <Button
                            size="sm"
                            variant="outline"
                            onClick={() => handleManualClockRequest(record.user_id, "clock-out")}
                            disabled={loading}
                          >
                            {loading ? (
                              <Loader2 className="h-4 w-4 mr-1 animate-spin" />
                            ) : null}
                            Check Out
                          </Button>
                        )}
                        <Button size="sm" variant="outline" onClick={() => handleEditAttendance(record)}>
                          <Edit className="h-4 w-4" />
                        </Button>
                        <Button
                          size="sm"
                          variant="outline"
                          onClick={() => handleDeleteAttendance(record.id)}
                          className="text-red-600 hover:text-red-700"
                        >
                          <Trash2 className="h-4 w-4" />
                        </Button>
                      </div>
                    </TableCell>
                  </TableRow>
                ))
              ) : (
                <TableRow>
                  <TableCell colSpan={viewMode === "detailed" ? 9 : 7} className="text-center py-8 text-muted-foreground">
                    No attendance records found for {selectedDate}
                  </TableCell>
                </TableRow>
              )}
            </TableBody>
          </Table>
          </CardContent>
        </Card>
      )}

      {/* Employee List View */}
      {activeView === "employees" && (
        <Card>
          <CardHeader>
            <CardTitle>Employee Quick Actions</CardTitle>
            <CardDescription>Manage employee check-ins and check-outs with quick action buttons</CardDescription>
          </CardHeader>
          <CardContent>
            {/* Search and Filter Controls */}
            <div className="flex flex-col space-y-4 sm:flex-row sm:items-center sm:space-y-0 sm:space-x-4 mb-6">
              <div className="relative flex-1">
                <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
                <Input
                  placeholder="Search employees by name, email, or ID..."
                  value={employeeSearchTerm}
                  onChange={(e) => setEmployeeSearchTerm(e.target.value)}
                  className="pl-8"
                />
              </div>
              <Select value={employeeStatusFilter} onValueChange={setEmployeeStatusFilter}>
                <SelectTrigger className="w-full sm:w-[180px]">
                  <Filter className="mr-2 h-4 w-4" />
                  <SelectValue placeholder="Filter by status" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Status</SelectItem>
                  <SelectItem value="checked_in">Checked In</SelectItem>
                  <SelectItem value="checked_out">Checked Out</SelectItem>
                  <SelectItem value="not_started">Not Started</SelectItem>
                </SelectContent>
              </Select>
            </div>

            {/* Responsive Table Container */}
            <div className="overflow-x-auto border rounded-lg">

              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead className="min-w-[200px]">Employee</TableHead>
                    <TableHead className="hidden sm:table-cell">Employee ID</TableHead>
                    <TableHead className="hidden lg:table-cell">Department</TableHead>
                    <TableHead className="min-w-[120px]">Quick Actions</TableHead>
                    <TableHead className="min-w-[100px]">Current Status</TableHead>
                    <TableHead className="hidden md:table-cell text-center">Sessions</TableHead>
                    <TableHead className="hidden md:table-cell text-center">Hours</TableHead>
                    <TableHead className="hidden xl:table-cell">Last Action</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {loadingEmployees ? (
                    <TableRow>
                      <TableCell colSpan={8} className="text-center py-8">
                        <Loader2 className="h-6 w-6 animate-spin mx-auto" />
                      </TableCell>
                    </TableRow>
                  ) : filteredEmployees.length > 0 ? (
                    filteredEmployees.map((employee) => (
                      <TableRow key={employee.id} className="hover:bg-muted/50">
                        {/* Employee Info - Always visible */}
                        <TableCell className="min-w-[200px]">
                          <div className="flex items-center space-x-3">
                            <Avatar className="h-8 w-8 flex-shrink-0">
                              <AvatarImage src="/placeholder.svg" />
                              <AvatarFallback>{employee.full_name.charAt(0)}</AvatarFallback>
                            </Avatar>
                            <div className="min-w-0 flex-1">
                              <div className="font-medium truncate">{employee.full_name}</div>
                              <div className="text-sm text-muted-foreground truncate">{employee.email}</div>
                              {/* Show additional info on mobile */}
                              <div className="sm:hidden text-xs text-muted-foreground mt-1 space-y-1">
                                <div>ID: {employee.employee_id}</div>
                                <div className="lg:hidden">{employee.department || "N/A"}</div>
                                <div className="md:hidden flex items-center space-x-2">
                                  <span>{employee.today_sessions} sessions</span>
                                  <span>•</span>
                                  <span>{employee.total_hours_today.toFixed(1)}h</span>
                                </div>
                              </div>
                            </div>
                          </div>
                        </TableCell>

                        {/* Employee ID - Hidden on mobile */}
                        <TableCell className="hidden sm:table-cell">
                          <Badge variant="outline" className="text-xs">{employee.employee_id}</Badge>
                        </TableCell>

                        {/* Department - Hidden on mobile and tablet */}
                        <TableCell className="hidden lg:table-cell">
                          <span className="text-sm">{employee.department || "N/A"}</span>
                        </TableCell>

                        {/* Quick Actions - Moved before status, always visible */}
                        <TableCell className="min-w-[120px]">
                          <div className="flex flex-col space-y-1">
                            {hasQuickActionPermissions ? (
                              <>
                                {employee.current_status !== "checked_in" && employee.remaining_check_ins > 0 && (
                                  <Button
                                    size="sm"
                                    onClick={() => handleManualClockRequest(employee.id, "clock-in")}
                                    className="bg-green-600 hover:bg-green-700 text-white text-xs h-8 min-h-[44px] sm:min-h-[32px]"
                                    disabled={loading || loadingEmployees}
                                  >
                                    {loading || loadingEmployees ? (
                                      <Loader2 className="h-3 w-3 sm:h-4 sm:w-4 mr-1 animate-spin" />
                                    ) : (
                                      <UserCheck className="h-3 w-3 sm:h-4 sm:w-4 mr-1" />
                                    )}
                                    <span className="hidden sm:inline">Check In</span>
                                    <span className="sm:hidden">In</span>
                                  </Button>
                                )}
                                {employee.current_status === "checked_in" && (
                                  <Button
                                    size="sm"
                                    variant="outline"
                                    onClick={() => handleManualClockRequest(employee.id, "clock-out")}
                                    disabled={loading || loadingEmployees}
                                    className="border-blue-200 text-blue-700 hover:bg-blue-50 text-xs h-8 min-h-[44px] sm:min-h-[32px]"
                                  >
                                    {loading || loadingEmployees ? (
                                      <Loader2 className="h-3 w-3 sm:h-4 sm:w-4 mr-1 animate-spin" />
                                    ) : (
                                      <UserX className="h-3 w-3 sm:h-4 sm:w-4 mr-1" />
                                    )}
                                    <span className="hidden sm:inline">Check Out</span>
                                    <span className="sm:hidden">Out</span>
                                  </Button>
                                )}
                                {employee.remaining_check_ins === 0 && employee.current_status !== "checked_in" && (
                                  <Badge variant="secondary" className="text-xs text-center">
                                    <span className="hidden sm:inline">Daily limit reached</span>
                                    <span className="sm:hidden">Limit reached</span>
                                  </Badge>
                                )}
                              </>
                            ) : (
                              <Badge variant="outline" className="text-xs text-muted-foreground text-center">
                                <span className="hidden sm:inline">Insufficient permissions</span>
                                <span className="sm:hidden">No access</span>
                              </Badge>
                            )}
                          </div>
                        </TableCell>

                        {/* Current Status - Always visible */}
                        <TableCell className="min-w-[100px]">
                          <div className="flex items-center space-x-2">
                            {getEmployeeStatusIcon(employee.current_status)}
                            <Badge className={`${getEmployeeStatusBadge(employee.current_status)} text-xs`}>
                              <span className="hidden sm:inline">{formatEmployeeStatus(employee.current_status)}</span>
                              <span className="sm:hidden">
                                {employee.current_status === "checked_in" ? "In" :
                                 employee.current_status === "checked_out" ? "Out" : "—"}
                              </span>
                            </Badge>
                          </div>
                        </TableCell>

                        {/* Sessions - Hidden on mobile */}
                        <TableCell className="hidden md:table-cell text-center">
                          <div className="text-center">
                            <span className="font-medium">{employee.today_sessions}</span>
                            <div className="text-xs text-muted-foreground">
                              {employee.remaining_check_ins} left
                            </div>
                          </div>
                        </TableCell>

                        {/* Hours - Hidden on mobile */}
                        <TableCell className="hidden md:table-cell text-center">
                          <span className="font-medium">{employee.total_hours_today.toFixed(1)}h</span>
                        </TableCell>

                        {/* Last Action - Hidden on mobile and tablet */}
                        <TableCell className="hidden xl:table-cell">
                          {employee.last_action ? (
                            <div className="text-sm">
                              <span className="capitalize">{employee.last_action.type.replace("_", " ")}</span>
                              <div className="text-xs text-muted-foreground">
                                {formatTime(employee.last_action.time)}
                              </div>
                            </div>
                          ) : (
                            <span className="text-muted-foreground">No activity</span>
                          )}
                        </TableCell>
                      </TableRow>
                    ))
                  ) : (
                    <TableRow>
                      <TableCell colSpan={8} className="text-center py-8 text-muted-foreground">
                        No employees found matching your search criteria
                      </TableCell>
                    </TableRow>
                  )}
                </TableBody>
              </Table>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Edit Attendance Dialog */}
      <Dialog open={editDialogOpen} onOpenChange={setEditDialogOpen}>
        <DialogContent className="max-w-md">
          <DialogHeader>
            <DialogTitle>Edit Attendance</DialogTitle>
            <DialogDescription>Update attendance record for {selectedRecord?.user_name}</DialogDescription>
          </DialogHeader>
          <div className="grid gap-4 py-4">
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="edit-status" className="text-right">
                Status
              </Label>
              <Select value={selectedStatus} onValueChange={setSelectedStatus}>
                <SelectTrigger className="col-span-3">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="present">Present</SelectItem>
                  <SelectItem value="absent">Absent</SelectItem>
                  <SelectItem value="late">Late</SelectItem>
                  <SelectItem value="half_day">Half Day</SelectItem>
                  <SelectItem value="on_leave">On Leave</SelectItem>
                </SelectContent>
              </Select>
            </div>
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="edit-checkin" className="text-right">
                Check In
              </Label>
              <Input
                id="edit-checkin"
                type="time"
                value={checkInTime}
                onChange={(e) => setCheckInTime(e.target.value)}
                className="col-span-3"
              />
            </div>
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="edit-checkout" className="text-right">
                Check Out
              </Label>
              <Input
                id="edit-checkout"
                type="time"
                value={checkOutTime}
                onChange={(e) => setCheckOutTime(e.target.value)}
                className="col-span-3"
              />
            </div>
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="edit-hours" className="text-right">
                Hours
              </Label>
              <Input
                id="edit-hours"
                type="number"
                step="0.1"
                placeholder="Auto-calculated"
                value={hoursWorked}
                onChange={(e) => setHoursWorked(e.target.value)}
                className="col-span-3"
              />
            </div>
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="edit-notes" className="text-right">
                Notes
              </Label>
              <Textarea
                id="edit-notes"
                placeholder="Optional notes..."
                value={notes}
                onChange={(e) => setNotes(e.target.value)}
                className="col-span-3"
                rows={3}
              />
            </div>
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setEditDialogOpen(false)}>
              Cancel
            </Button>
            <Button onClick={handleSaveEdit} disabled={loading}>
              {loading ? <Loader2 className="mr-2 h-4 w-4 animate-spin" /> : null}
              Save Changes
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Monthly Attendance View */}
      {activeView === "monthly" && hasQuickActionPermissions && (
        <MonthlyAttendanceCalendar
          calendarType={calendarType}
          onCalendarTypeChange={setCalendarType}
          autoRefresh={autoRefresh}
          refreshInterval={30000}
        />
      )}

      {/* Create Attendance Dialog */}
      <Dialog open={createDialogOpen} onOpenChange={setCreateDialogOpen}>
        <DialogContent className="max-w-md">
          <DialogHeader>
            <DialogTitle>Create Attendance Record</DialogTitle>
            <DialogDescription>Add a new attendance record for {selectedDate}</DialogDescription>
          </DialogHeader>
          <div className="grid gap-4 py-4">
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="create-user" className="text-right">
                Employee
              </Label>
              <Select value={selectedUserId} onValueChange={setSelectedUserId}>
                <SelectTrigger className="col-span-3">
                  <SelectValue placeholder="Select employee" />
                </SelectTrigger>
                <SelectContent>
                  {users.map((user) => (
                    <SelectItem key={user.id} value={user.id}>
                      {user.full_name} ({user.email})
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="create-status" className="text-right">
                Status
              </Label>
              <Select value={selectedStatus} onValueChange={setSelectedStatus}>
                <SelectTrigger className="col-span-3">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="present">Present</SelectItem>
                  <SelectItem value="absent">Absent</SelectItem>
                  <SelectItem value="late">Late</SelectItem>
                  <SelectItem value="half_day">Half Day</SelectItem>
                  <SelectItem value="on_leave">On Leave</SelectItem>
                </SelectContent>
              </Select>
            </div>
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="create-checkin" className="text-right">
                Check In
              </Label>
              <Input
                id="create-checkin"
                type="time"
                value={checkInTime}
                onChange={(e) => setCheckInTime(e.target.value)}
                className="col-span-3"
              />
            </div>
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="create-checkout" className="text-right">
                Check Out
              </Label>
              <Input
                id="create-checkout"
                type="time"
                value={checkOutTime}
                onChange={(e) => setCheckOutTime(e.target.value)}
                className="col-span-3"
              />
            </div>
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="create-hours" className="text-right">
                Hours
              </Label>
              <Input
                id="create-hours"
                type="number"
                step="0.1"
                placeholder="Auto-calculated"
                value={hoursWorked}
                onChange={(e) => setHoursWorked(e.target.value)}
                className="col-span-3"
              />
            </div>
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="create-notes" className="text-right">
                Notes
              </Label>
              <Textarea
                id="create-notes"
                placeholder="Optional notes..."
                value={notes}
                onChange={(e) => setNotes(e.target.value)}
                className="col-span-3"
                rows={3}
              />
            </div>
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => { setCreateDialogOpen(false); resetForm(); }}>
              Cancel
            </Button>
            <Button onClick={handleCreateAttendance} disabled={loading}>
              {loading ? <Loader2 className="mr-2 h-4 w-4 animate-spin" /> : null}
              Create Record
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Confirmation Dialog for Manual Clock Actions */}
      <Dialog open={confirmDialogOpen} onOpenChange={setConfirmDialogOpen}>
        <DialogContent className="max-w-md">
          <DialogHeader>
            <DialogTitle>Confirm Action</DialogTitle>
            <DialogDescription>
              Are you sure you want to {pendingAction?.action.replace("-", " ")} {pendingAction?.employeeName}?
            </DialogDescription>
          </DialogHeader>
          <div className="py-4">
            <div className="flex items-center space-x-2 text-sm text-muted-foreground">
              <AlertCircle className="h-4 w-4" />
              <span>This action will be recorded with the current timestamp.</span>
            </div>
          </div>
          <DialogFooter>
            <Button
              variant="outline"
              onClick={() => {
                setConfirmDialogOpen(false)
                setPendingAction(null)
              }}
            >
              Cancel
            </Button>
            <Button
              onClick={handleConfirmManualClock}
              className={pendingAction?.action === "clock-in" ? "bg-green-600 hover:bg-green-700" : ""}
            >
              Confirm {pendingAction?.action.replace("-", " ")}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  )
}
