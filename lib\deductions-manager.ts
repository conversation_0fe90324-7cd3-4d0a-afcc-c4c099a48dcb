// Enhanced Deductions Management System
// Complete deductions management with mandatory notes, audit trail, and approval workflow

import { db } from './neon';

export interface DeductionType {
  id: string;
  name: string;
  code: string;
  type: 'deduction';
  category: 'statutory' | 'voluntary' | 'company_policy' | 'custom';
  calculation_type: 'fixed' | 'percentage' | 'formula';
  fixed_amount?: number;
  percentage?: number;
  percentage_base?: 'base_salary' | 'gross_pay' | 'net_pay';
  is_taxable: boolean;
  is_statutory: boolean;
  description?: string;
  is_active: boolean;
  effective_from: string;
  effective_to?: string;
}

export interface DeductionApproval {
  id: string;
  user_id: string;
  component_id: string;
  component_name?: string;
  deduction_amount: number;
  deduction_reason: string; // Mandatory field
  requested_by: string;
  requested_by_name?: string;
  approved_by?: string;
  approved_by_name?: string;
  status: 'pending' | 'approved' | 'rejected' | 'cancelled';
  request_date: string;
  approval_date?: string;
  effective_from: string;
  effective_to?: string;
  notes?: string;
  rejection_reason?: string;
  created_at: string;
  updated_at: string;
}

export interface DeductionRequest {
  user_id: string;
  component_id: string;
  deduction_amount: number;
  deduction_reason: string; // Mandatory - minimum 10 characters
  effective_from: string;
  effective_to?: string;
  notes?: string;
}

export interface EmployeeDeductionSummary {
  user_id: string;
  full_name: string;
  email: string;
  department?: string;
  position?: string;
  base_salary?: number;
  total_deductions: number;
  pending_deductions: number;
  approved_deductions: number;
  deductions: DeductionApproval[];
}

export interface DeductionAuditTrail {
  id: string;
  table_name: string;
  record_id: string;
  action: 'INSERT' | 'UPDATE' | 'DELETE';
  field_name?: string;
  old_value?: string;
  new_value?: string;
  changed_by?: string;
  changed_by_name?: string;
  change_reason?: string;
  ip_address?: string;
  created_at: string;
}

export interface DeductionStatistics {
  totalDeductionTypes: number;
  totalPendingApprovals: number;
  totalApprovedDeductions: number;
  totalDeductionAmount: number;
  mostUsedDeductionType: string;
  averageDeductionPerEmployee: number;
  approvalRate: number;
}

export class DeductionsManager {
  
  // Get all deduction types from master table
  async getDeductionTypes(): Promise<DeductionType[]> {
    try {
      const deductionTypes = await db.sql`
        SELECT * FROM payroll_components_master 
        WHERE type = 'deduction' AND is_active = TRUE
        ORDER BY category, name
      `;
      
      return deductionTypes;
      
    } catch (error) {
      console.error('Error getting deduction types:', error);
      throw new Error('Failed to get deduction types');
    }
  }
  
  // Create new deduction type
  async createDeductionType(deductionType: Omit<DeductionType, 'id' | 'created_at' | 'updated_at'>): Promise<string> {
    try {
      const result = await db.sql`
        INSERT INTO payroll_components_master (
          name, code, type, category, calculation_type, fixed_amount, percentage,
          percentage_base, is_taxable, is_statutory, description, is_active, effective_from, effective_to
        ) VALUES (
          ${deductionType.name}, ${deductionType.code}, 'deduction', ${deductionType.category},
          ${deductionType.calculation_type}, ${deductionType.fixed_amount || null}, ${deductionType.percentage || null},
          ${deductionType.percentage_base || null}, ${deductionType.is_taxable}, ${deductionType.is_statutory},
          ${deductionType.description || null}, ${deductionType.is_active}, ${deductionType.effective_from},
          ${deductionType.effective_to || null}
        ) RETURNING id
      `;
      
      return result[0].id;
      
    } catch (error) {
      console.error('Error creating deduction type:', error);
      if (error.message?.includes('duplicate key')) {
        throw new Error('Deduction code already exists');
      }
      throw new Error('Failed to create deduction type');
    }
  }
  
  // Request deduction for employee (with mandatory reason)
  async requestDeduction(
    deductionRequest: DeductionRequest,
    requestedBy: string
  ): Promise<string> {
    try {
      // Validate mandatory reason field (minimum 10 characters)
      if (!deductionRequest.deduction_reason || deductionRequest.deduction_reason.trim().length < 10) {
        throw new Error('Deduction reason is mandatory and must be at least 10 characters long');
      }
      
      // Validate deduction amount
      if (deductionRequest.deduction_amount <= 0) {
        throw new Error('Deduction amount must be greater than 0');
      }
      
      const result = await db.sql`
        INSERT INTO deduction_approvals (
          user_id, component_id, deduction_amount, deduction_reason, requested_by,
          status, request_date, effective_from, effective_to, notes
        ) VALUES (
          ${deductionRequest.user_id}, ${deductionRequest.component_id}, ${deductionRequest.deduction_amount},
          ${deductionRequest.deduction_reason.trim()}, ${requestedBy}, 'pending', CURRENT_DATE,
          ${deductionRequest.effective_from}, ${deductionRequest.effective_to || null}, ${deductionRequest.notes || null}
        ) RETURNING id
      `;
      
      return result[0].id;
      
    } catch (error) {
      console.error('Error requesting deduction:', error);
      throw new Error(error.message || 'Failed to request deduction');
    }
  }
  
  // Approve deduction request
  async approveDeduction(
    approvalId: string,
    approvedBy: string,
    notes?: string
  ): Promise<boolean> {
    try {
      await db.sql`
        UPDATE deduction_approvals 
        SET status = 'approved', 
            approved_by = ${approvedBy}, 
            approval_date = CURRENT_DATE,
            notes = CASE 
              WHEN ${notes} IS NOT NULL THEN 
                CASE WHEN notes IS NULL THEN ${notes} ELSE notes || '; ' || ${notes} END
              ELSE notes 
            END,
            updated_at = NOW()
        WHERE id = ${approvalId} AND status = 'pending'
      `;
      
      return true;
      
    } catch (error) {
      console.error('Error approving deduction:', error);
      throw new Error('Failed to approve deduction');
    }
  }
  
  // Reject deduction request
  async rejectDeduction(
    approvalId: string,
    approvedBy: string,
    rejectionReason: string
  ): Promise<boolean> {
    try {
      if (!rejectionReason || rejectionReason.trim().length < 10) {
        throw new Error('Rejection reason is mandatory and must be at least 10 characters long');
      }
      
      await db.sql`
        UPDATE deduction_approvals 
        SET status = 'rejected', 
            approved_by = ${approvedBy}, 
            approval_date = CURRENT_DATE,
            rejection_reason = ${rejectionReason.trim()},
            updated_at = NOW()
        WHERE id = ${approvalId} AND status = 'pending'
      `;
      
      return true;
      
    } catch (error) {
      console.error('Error rejecting deduction:', error);
      throw new Error('Failed to reject deduction');
    }
  }
  
  // Cancel deduction request
  async cancelDeduction(approvalId: string): Promise<boolean> {
    try {
      await db.sql`
        UPDATE deduction_approvals 
        SET status = 'cancelled', updated_at = NOW()
        WHERE id = ${approvalId} AND status = 'pending'
      `;
      
      return true;
      
    } catch (error) {
      console.error('Error cancelling deduction:', error);
      throw new Error('Failed to cancel deduction');
    }
  }
  
  // Get pending deduction approvals
  async getPendingApprovals(): Promise<DeductionApproval[]> {
    try {
      const approvals = await db.sql`
        SELECT 
          da.*,
          pcm.name as component_name,
          u1.full_name as requested_by_name,
          u2.full_name as approved_by_name
        FROM deduction_approvals da
        JOIN payroll_components_master pcm ON da.component_id = pcm.id
        JOIN users u1 ON da.requested_by = u1.id
        LEFT JOIN users u2 ON da.approved_by = u2.id
        WHERE da.status = 'pending'
        ORDER BY da.request_date ASC
      `;
      
      return approvals;
      
    } catch (error) {
      console.error('Error getting pending approvals:', error);
      throw new Error('Failed to get pending approvals');
    }
  }
  
  // Get employee deductions
  async getEmployeeDeductions(userId: string): Promise<DeductionApproval[]> {
    try {
      const deductions = await db.sql`
        SELECT 
          da.*,
          pcm.name as component_name,
          u1.full_name as requested_by_name,
          u2.full_name as approved_by_name
        FROM deduction_approvals da
        JOIN payroll_components_master pcm ON da.component_id = pcm.id
        JOIN users u1 ON da.requested_by = u1.id
        LEFT JOIN users u2 ON da.approved_by = u2.id
        WHERE da.user_id = ${userId}
        ORDER BY da.created_at DESC
      `;
      
      return deductions;
      
    } catch (error) {
      console.error('Error getting employee deductions:', error);
      throw new Error('Failed to get employee deductions');
    }
  }
  
  // Get all employees with deduction summary
  async getAllEmployeesDeductionSummary(): Promise<EmployeeDeductionSummary[]> {
    try {
      const employees = await db.sql`
        SELECT 
          u.id as user_id,
          u.full_name,
          u.email,
          u.department,
          u.position,
          u.salary as base_salary,
          COALESCE(SUM(CASE WHEN da.status = 'approved' THEN da.deduction_amount ELSE 0 END), 0) as total_deductions,
          COALESCE(SUM(CASE WHEN da.status = 'pending' THEN da.deduction_amount ELSE 0 END), 0) as pending_deductions,
          COALESCE(SUM(CASE WHEN da.status = 'approved' THEN da.deduction_amount ELSE 0 END), 0) as approved_deductions
        FROM users u
        LEFT JOIN deduction_approvals da ON u.id = da.user_id
        WHERE u.role != 'admin'
        GROUP BY u.id, u.full_name, u.email, u.department, u.position, u.salary
        ORDER BY u.full_name
      `;
      
      // Get detailed deductions for each employee
      const result = [];
      for (const emp of employees) {
        const deductions = await this.getEmployeeDeductions(emp.user_id);
        result.push({
          ...emp,
          deductions
        });
      }
      
      return result;
      
    } catch (error) {
      console.error('Error getting employees deduction summary:', error);
      throw new Error('Failed to get employees deduction summary');
    }
  }
  
  // Get deduction audit trail
  async getDeductionAuditTrail(recordId?: string, limit: number = 50): Promise<DeductionAuditTrail[]> {
    try {
      let query;
      if (recordId) {
        query = db.sql`
          SELECT 
            pat.*,
            u.full_name as changed_by_name
          FROM payroll_audit_trail pat
          LEFT JOIN users u ON pat.changed_by = u.id
          WHERE pat.table_name = 'deduction_approvals' AND pat.record_id = ${recordId}
          ORDER BY pat.created_at DESC
          LIMIT ${limit}
        `;
      } else {
        query = db.sql`
          SELECT 
            pat.*,
            u.full_name as changed_by_name
          FROM payroll_audit_trail pat
          LEFT JOIN users u ON pat.changed_by = u.id
          WHERE pat.table_name = 'deduction_approvals'
          ORDER BY pat.created_at DESC
          LIMIT ${limit}
        `;
      }
      
      const auditTrail = await query;
      return auditTrail;
      
    } catch (error) {
      console.error('Error getting deduction audit trail:', error);
      throw new Error('Failed to get deduction audit trail');
    }
  }
  
  // Get deduction statistics
  async getDeductionStatistics(): Promise<DeductionStatistics> {
    try {
      // Get total deduction types
      const deductionTypes = await db.sql`
        SELECT COUNT(*) as count FROM payroll_components_master 
        WHERE type = 'deduction' AND is_active = TRUE
      `;
      
      // Get pending approvals
      const pendingApprovals = await db.sql`
        SELECT COUNT(*) as count FROM deduction_approvals 
        WHERE status = 'pending'
      `;
      
      // Get approved deductions
      const approvedDeductions = await db.sql`
        SELECT COUNT(*) as count FROM deduction_approvals 
        WHERE status = 'approved'
      `;
      
      // Get total deduction amount (approved only)
      const totalAmount = await db.sql`
        SELECT COALESCE(SUM(deduction_amount), 0) as total
        FROM deduction_approvals 
        WHERE status = 'approved'
      `;
      
      // Get most used deduction type
      const mostUsed = await db.sql`
        SELECT pcm.name, COUNT(*) as count
        FROM deduction_approvals da
        JOIN payroll_components_master pcm ON da.component_id = pcm.id
        WHERE da.status = 'approved'
        GROUP BY pcm.name
        ORDER BY count DESC
        LIMIT 1
      `;
      
      // Get average deduction per employee
      const employeeCount = await db.sql`
        SELECT COUNT(DISTINCT user_id) as count 
        FROM deduction_approvals 
        WHERE status = 'approved'
      `;
      
      // Calculate approval rate
      const totalRequests = await db.sql`
        SELECT COUNT(*) as count FROM deduction_approvals 
        WHERE status IN ('approved', 'rejected')
      `;
      
      const totalEmployees = employeeCount[0]?.count || 1;
      const averageDeduction = totalAmount[0]?.total / totalEmployees || 0;
      const approvalRate = totalRequests[0]?.count > 0 
        ? (approvedDeductions[0]?.count / totalRequests[0]?.count) * 100 
        : 0;
      
      return {
        totalDeductionTypes: deductionTypes[0]?.count || 0,
        totalPendingApprovals: pendingApprovals[0]?.count || 0,
        totalApprovedDeductions: approvedDeductions[0]?.count || 0,
        totalDeductionAmount: totalAmount[0]?.total || 0,
        mostUsedDeductionType: mostUsed[0]?.name || 'None',
        averageDeductionPerEmployee: averageDeduction,
        approvalRate: approvalRate
      };
      
    } catch (error) {
      console.error('Error getting deduction statistics:', error);
      throw new Error('Failed to get deduction statistics');
    }
  }
  
  // Update deduction type
  async updateDeductionType(id: string, updates: Partial<DeductionType>): Promise<boolean> {
    try {
      const updateFields = [];
      const values = [];
      let paramIndex = 1;
      
      const allowedFields = [
        'name', 'category', 'calculation_type', 'fixed_amount', 'percentage',
        'percentage_base', 'is_taxable', 'is_statutory', 'description', 'is_active', 'effective_to'
      ];
      
      for (const field of allowedFields) {
        if (updates[field] !== undefined) {
          updateFields.push(`${field} = $${paramIndex}`);
          values.push(updates[field]);
          paramIndex++;
        }
      }
      
      if (updateFields.length === 0) {
        return true;
      }
      
      updateFields.push(`updated_at = NOW()`);
      values.push(id);
      
      const query = `
        UPDATE payroll_components_master 
        SET ${updateFields.join(', ')}
        WHERE id = $${paramIndex}
      `;
      
      await db.sql([query, ...values]);
      return true;
      
    } catch (error) {
      console.error('Error updating deduction type:', error);
      throw new Error('Failed to update deduction type');
    }
  }
}

export const deductionsManager = new DeductionsManager();
