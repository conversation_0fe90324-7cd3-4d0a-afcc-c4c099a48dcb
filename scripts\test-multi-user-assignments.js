const { neon } = require('@neondatabase/serverless');

// Load environment variables
require('dotenv').config({ path: '.env.local' });

const sql = neon(process.env.DATABASE_URL);

async function testMultiUserAssignments() {
  try {
    console.log('🧪 Testing Multi-User Assignment System...\n');

    // 1. Get some test users
    console.log('1. Getting test users...');
    const users = await sql`
      SELECT id, full_name, email, role 
      FROM users 
      WHERE is_active = true 
      ORDER BY created_at 
      LIMIT 5
    `;
    
    if (users.length < 2) {
      console.log('❌ Need at least 2 users to test multi-user assignments');
      return;
    }
    
    console.log(`✅ Found ${users.length} test users:`);
    users.forEach(user => {
      console.log(`   - ${user.full_name} (${user.email}) - ${user.role}`);
    });

    // 2. Get an existing task or create one
    console.log('\n2. Getting existing tasks...');
    const existingTasks = await sql`
      SELECT id, title, assigned_to, assigned_by 
      FROM tasks 
      ORDER BY created_at DESC 
      LIMIT 3
    `;
    
    if (existingTasks.length === 0) {
      console.log('❌ No existing tasks found. Please create some tasks first.');
      return;
    }
    
    console.log(`✅ Found ${existingTasks.length} existing tasks:`);
    existingTasks.forEach(task => {
      console.log(`   - ${task.title} (ID: ${task.id})`);
    });

    const testTask = existingTasks[0];
    console.log(`\n📝 Using task: "${testTask.title}" for testing`);

    // 3. Test creating task assignments
    console.log('\n3. Testing task assignment creation...');
    
    // Clear existing assignments for this task
    await sql`DELETE FROM task_assignments WHERE task_id = ${testTask.id}`;
    console.log('   Cleared existing assignments');

    // Create assignments for multiple users
    const assignedUsers = users.slice(0, 3); // Take first 3 users
    const primaryUser = assignedUsers[0];
    
    for (let i = 0; i < assignedUsers.length; i++) {
      const user = assignedUsers[i];
      const isPrimary = i === 0; // First user is primary
      
      await sql`
        INSERT INTO task_assignments (task_id, user_id, assigned_by, is_primary)
        VALUES (${testTask.id}, ${user.id}, ${testTask.assigned_by}, ${isPrimary})
      `;
      
      console.log(`   ✅ Assigned ${user.full_name} ${isPrimary ? '(PRIMARY)' : ''}`);
    }

    // Update the main task's assigned_to field
    await sql`
      UPDATE tasks 
      SET assigned_to = ${primaryUser.id}, updated_at = NOW()
      WHERE id = ${testTask.id}
    `;
    console.log(`   ✅ Updated main task assignment to primary user`);

    // 4. Test retrieving task assignments
    console.log('\n4. Testing task assignment retrieval...');
    
    const assignments = await sql`
      SELECT 
        ta.*,
        u.full_name,
        u.email,
        u.employee_id,
        u.department,
        u.position,
        assigned_by_user.full_name as assigned_by_name
      FROM task_assignments ta
      JOIN users u ON ta.user_id = u.id
      LEFT JOIN users assigned_by_user ON ta.assigned_by = assigned_by_user.id
      WHERE ta.task_id = ${testTask.id}
      ORDER BY ta.is_primary DESC, ta.assigned_at ASC
    `;

    console.log(`✅ Retrieved ${assignments.length} assignments:`);
    assignments.forEach(assignment => {
      console.log(`   - ${assignment.full_name} (${assignment.email}) ${assignment.is_primary ? '- PRIMARY' : ''}`);
      console.log(`     Assigned by: ${assignment.assigned_by_name || 'Unknown'}`);
      console.log(`     Department: ${assignment.department || 'N/A'}, Position: ${assignment.position || 'N/A'}`);
    });

    // 5. Test task retrieval with assignment information
    console.log('\n5. Testing enhanced task retrieval...');
    
    const taskWithAssignments = await sql`
      SELECT 
        t.*,
        assigned_user.full_name as assigned_to_name,
        assigned_user.email as assigned_to_email,
        created_user.full_name as created_by_name,
        created_user.email as created_by_email,
        (
          SELECT COUNT(*)
          FROM task_assignments ta
          WHERE ta.task_id = t.id
        ) as assignment_count,
        (
          SELECT COUNT(*)
          FROM sub_tasks st
          WHERE st.parent_task_id = t.id
        ) as subtask_count
      FROM tasks t
      LEFT JOIN users assigned_user ON t.assigned_to = assigned_user.id
      LEFT JOIN users created_user ON t.assigned_by = created_user.id
      WHERE t.id = ${testTask.id}
    `;

    const task = taskWithAssignments[0];
    console.log(`✅ Enhanced task data:`);
    console.log(`   Title: ${task.title}`);
    console.log(`   Primary Assignee: ${task.assigned_to_name} (${task.assigned_to_email})`);
    console.log(`   Created by: ${task.created_by_name}`);
    console.log(`   Total Assignments: ${task.assignment_count}`);
    console.log(`   Sub-tasks: ${task.subtask_count}`);
    console.log(`   Status: ${task.status}, Priority: ${task.priority}`);
    console.log(`   Completion: ${task.completion_percentage || 0}%`);

    // 6. Test removing an assignment
    console.log('\n6. Testing assignment removal...');
    
    if (assignedUsers.length > 1) {
      const userToRemove = assignedUsers[1]; // Remove second user
      
      await sql`
        DELETE FROM task_assignments 
        WHERE task_id = ${testTask.id} AND user_id = ${userToRemove.id}
      `;
      
      console.log(`✅ Removed ${userToRemove.full_name} from task`);
      
      // Verify removal
      const remainingAssignments = await sql`
        SELECT COUNT(*) as count
        FROM task_assignments 
        WHERE task_id = ${testTask.id}
      `;
      
      console.log(`   Remaining assignments: ${remainingAssignments[0].count}`);
    }

    // 7. Test access control simulation
    console.log('\n7. Testing access control logic...');
    
    const testUserId = users[2].id; // Third user
    
    // Check if user has access to the task
    const hasDirectAccess = task.assigned_to === testUserId || task.assigned_by === testUserId;
    
    const isAssigned = await sql`
      SELECT EXISTS (
        SELECT 1 FROM task_assignments 
        WHERE task_id = ${testTask.id} AND user_id = ${testUserId}
      )
    `;
    
    const hasAccess = hasDirectAccess || isAssigned[0].exists;
    
    console.log(`✅ Access control for ${users[2].full_name}:`);
    console.log(`   Direct access (assigned_to/assigned_by): ${hasDirectAccess}`);
    console.log(`   Assignment access: ${isAssigned[0].exists}`);
    console.log(`   Total access: ${hasAccess}`);

    console.log('\n🎉 Multi-User Assignment System test completed successfully!');
    console.log('\n📋 Summary:');
    console.log(`   - Task assignments table: ✅ Working`);
    console.log(`   - Multi-user assignment creation: ✅ Working`);
    console.log(`   - Assignment retrieval with user details: ✅ Working`);
    console.log(`   - Enhanced task data with assignment counts: ✅ Working`);
    console.log(`   - Assignment removal: ✅ Working`);
    console.log(`   - Access control logic: ✅ Working`);

  } catch (error) {
    console.error('❌ Test failed:', error.message);
    console.error('Full error:', error);
  }
}

testMultiUserAssignments();
