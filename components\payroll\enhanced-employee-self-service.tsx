// Enhanced Employee Self-Service Payroll Component
// Addresses Phase 2 Task 2.2 requirements from payroll.md

"use client"

import React, { useState, useEffect } from 'react'
import {
  Download,
  Eye,
  Bell,
  Calendar,
  DollarSign,
  FileText,
  TrendingUp,
  Clock,
  CheckCircle,
  AlertCircle,
  Mail,
  Printer,
  Search,
  Filter
} from 'lucide-react'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  Dialog<PERSON>eader,
  DialogTitle,
} from '@/components/ui/dialog'
import { CurrencyDisplay } from '@/components/ui/currency-display'
import { Progress } from '@/components/ui/progress'
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert'
import { toast } from '@/hooks/use-toast'

interface PayrollRecord {
  id: string
  payPeriod: string
  bsPayPeriod: string
  periodStart: string
  periodEnd: string
  baseSalary: number
  allowances: number
  deductions: number
  grossPay: number
  taxes: number
  providentFund: number
  netPay: number
  status: string
  processedAt: string
  paidAt?: string
}

interface PayrollNotification {
  id: string
  type: 'payroll_processed' | 'salary_credited' | 'tax_document' | 'payslip_ready'
  title: string
  message: string
  timestamp: string
  isRead: boolean
  actionUrl?: string
}

interface YearToDateSummary {
  totalGrossPay: number
  totalNetPay: number
  totalTaxes: number
  totalProvidentFund: number
  totalAllowances: number
  totalDeductions: number
  payrollPeriods: number
  averageMonthlyPay: number
}

export function EnhancedEmployeeSelfService() {
  const [payrollHistory, setPayrollHistory] = useState<PayrollRecord[]>([])
  const [notifications, setNotifications] = useState<PayrollNotification[]>([])
  const [ytdSummary, setYtdSummary] = useState<YearToDateSummary | null>(null)
  const [loading, setLoading] = useState(true)
  const [activeTab, setActiveTab] = useState('dashboard')
  const [selectedYear, setSelectedYear] = useState(new Date().getFullYear())
  const [searchTerm, setSearchTerm] = useState('')
  
  // Dialog states
  const [showPayslipDialog, setShowPayslipDialog] = useState(false)
  const [selectedPayslip, setSelectedPayslip] = useState<PayrollRecord | null>(null)
  const [showNotificationDialog, setShowNotificationDialog] = useState(false)

  useEffect(() => {
    loadEmployeePayrollData()
  }, [selectedYear])

  const loadEmployeePayrollData = async () => {
    try {
      setLoading(true)
      
      // Load payroll history
      const historyResponse = await fetch(`/api/employee/payroll/history?year=${selectedYear}`)
      const historyData = await historyResponse.json()
      
      if (historyData.success) {
        setPayrollHistory(historyData.data.records || [])
      }

      // Load YTD summary
      const summaryResponse = await fetch(`/api/employee/payroll/summary?year=${selectedYear}`)
      const summaryData = await summaryResponse.json()
      
      if (summaryData.success) {
        setYtdSummary(summaryData.data.summary)
      }

      // Load notifications
      const notificationsResponse = await fetch('/api/employee/payroll/notifications?limit=20')
      const notificationsData = await notificationsResponse.json()
      
      if (notificationsData.success) {
        setNotifications(notificationsData.data.notifications || [])
      }

    } catch (error) {
      console.error('Error loading employee payroll data:', error)
      toast({
        title: "Error",
        description: "Failed to load payroll data",
        variant: "destructive"
      })
    } finally {
      setLoading(false)
    }
  }

  const handleDownloadPayslip = async (payrollId: string, format: string = 'pdf') => {
    try {
      const response = await fetch(`/api/employee/payroll/download?payrollId=${payrollId}&format=${format}`)
      
      if (response.ok) {
        const blob = await response.blob()
        const url = window.URL.createObjectURL(blob)
        const a = document.createElement('a')
        a.href = url
        a.download = `payslip_${payrollId}.${format}`
        document.body.appendChild(a)
        a.click()
        window.URL.revokeObjectURL(url)
        document.body.removeChild(a)
        
        toast({
          title: "Success",
          description: `Payslip downloaded successfully`
        })
      } else {
        throw new Error('Download failed')
      }
    } catch (error) {
      console.error('Error downloading payslip:', error)
      toast({
        title: "Error",
        description: "Failed to download payslip",
        variant: "destructive"
      })
    }
  }

  const handleMarkNotificationRead = async (notificationId: string) => {
    try {
      const response = await fetch('/api/employee/payroll/notifications', {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          notificationId,
          action: 'mark_read'
        })
      })

      if (response.ok) {
        setNotifications(prev => 
          prev.map(n => n.id === notificationId ? { ...n, isRead: true } : n)
        )
      }
    } catch (error) {
      console.error('Error marking notification as read:', error)
    }
  }

  const getStatusBadge = (status: string) => {
    const variants: Record<string, any> = {
      'draft': 'secondary',
      'calculated': 'outline',
      'approved': 'default',
      'processed': 'default',
      'paid': 'default'
    }
    return <Badge variant={variants[status] || 'secondary'}>{status}</Badge>
  }

  const getNotificationIcon = (type: string) => {
    const icons: Record<string, any> = {
      'payroll_processed': <CheckCircle className="h-4 w-4 text-green-500" />,
      'salary_credited': <DollarSign className="h-4 w-4 text-blue-500" />,
      'tax_document': <FileText className="h-4 w-4 text-orange-500" />,
      'payslip_ready': <Download className="h-4 w-4 text-purple-500" />
    }
    return icons[type] || <Bell className="h-4 w-4" />
  }

  const filteredHistory = payrollHistory.filter(record =>
    record.payPeriod.toLowerCase().includes(searchTerm.toLowerCase()) ||
    record.bsPayPeriod.toLowerCase().includes(searchTerm.toLowerCase())
  )

  const unreadNotifications = notifications.filter(n => !n.isRead).length

  if (loading) {
    return <div className="flex items-center justify-center h-64">Loading payroll data...</div>
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold">My Payroll</h1>
          <p className="text-muted-foreground">View your payroll history, download payslips, and manage notifications</p>
        </div>
        <div className="flex items-center space-x-2">
          <Select value={selectedYear.toString()} onValueChange={(value) => setSelectedYear(parseInt(value))}>
            <SelectTrigger className="w-32">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="2024">2024</SelectItem>
              <SelectItem value="2023">2023</SelectItem>
              <SelectItem value="2022">2022</SelectItem>
            </SelectContent>
          </Select>
          <Button variant="outline" onClick={() => setShowNotificationDialog(true)}>
            <Bell className="h-4 w-4 mr-2" />
            Notifications
            {unreadNotifications > 0 && (
              <Badge variant="destructive" className="ml-2 h-5 w-5 rounded-full p-0 text-xs">
                {unreadNotifications}
              </Badge>
            )}
          </Button>
        </div>
      </div>

      {/* Year-to-Date Summary */}
      {ytdSummary && (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Total Gross Pay</CardTitle>
              <DollarSign className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">
                <CurrencyDisplay amount={ytdSummary.totalGrossPay} />
              </div>
              <p className="text-xs text-muted-foreground">
                {ytdSummary.payrollPeriods} pay periods in {selectedYear}
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Total Net Pay</CardTitle>
              <TrendingUp className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">
                <CurrencyDisplay amount={ytdSummary.totalNetPay} />
              </div>
              <p className="text-xs text-muted-foreground">
                Average: <CurrencyDisplay amount={ytdSummary.averageMonthlyPay} />
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Total Taxes</CardTitle>
              <FileText className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">
                <CurrencyDisplay amount={ytdSummary.totalTaxes} />
              </div>
              <p className="text-xs text-muted-foreground">
                {((ytdSummary.totalTaxes / ytdSummary.totalGrossPay) * 100).toFixed(1)}% of gross pay
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Provident Fund</CardTitle>
              <CheckCircle className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">
                <CurrencyDisplay amount={ytdSummary.totalProvidentFund} />
              </div>
              <p className="text-xs text-muted-foreground">
                10% contribution rate
              </p>
            </CardContent>
          </Card>
        </div>
      )}

      {/* Main Content */}
      <Tabs value={activeTab} onValueChange={setActiveTab}>
        <TabsList>
          <TabsTrigger value="dashboard">Dashboard</TabsTrigger>
          <TabsTrigger value="history">Payroll History</TabsTrigger>
          <TabsTrigger value="documents">Documents</TabsTrigger>
          <TabsTrigger value="notifications">Notifications</TabsTrigger>
        </TabsList>

        <TabsContent value="dashboard" className="space-y-6">
          {/* Recent Payroll */}
          <Card>
            <CardHeader>
              <CardTitle>Recent Payroll</CardTitle>
              <CardDescription>Your latest payroll records</CardDescription>
            </CardHeader>
            <CardContent>
              {payrollHistory.slice(0, 3).map((record) => (
                <div key={record.id} className="flex items-center justify-between py-3 border-b last:border-b-0">
                  <div>
                    <div className="font-medium">{record.payPeriod}</div>
                    <div className="text-sm text-muted-foreground">{record.bsPayPeriod}</div>
                  </div>
                  <div className="text-right">
                    <div className="font-medium">
                      <CurrencyDisplay amount={record.netPay} />
                    </div>
                    <div className="flex items-center space-x-2">
                      {getStatusBadge(record.status)}
                      <Button
                        size="sm"
                        variant="outline"
                        onClick={() => handleDownloadPayslip(record.id)}
                      >
                        <Download className="h-4 w-4" />
                      </Button>
                    </div>
                  </div>
                </div>
              ))}
            </CardContent>
          </Card>

          {/* Quick Actions */}
          <Card>
            <CardHeader>
              <CardTitle>Quick Actions</CardTitle>
            </CardHeader>
            <CardContent className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <Button className="h-20 flex-col">
                <Download className="h-6 w-6 mb-2" />
                Download Latest Payslip
              </Button>
              <Button variant="outline" className="h-20 flex-col">
                <FileText className="h-6 w-6 mb-2" />
                Tax Documents
              </Button>
              <Button variant="outline" className="h-20 flex-col">
                <Calendar className="h-6 w-6 mb-2" />
                View Pay Calendar
              </Button>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="history" className="space-y-6">
          {/* Search and Filter */}
          <div className="flex items-center space-x-4">
            <div className="relative flex-1">
              <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
              <Input
                placeholder="Search payroll periods..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-8"
              />
            </div>
          </div>

          {/* Payroll History Table */}
          <Card>
            <CardContent className="p-0">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Pay Period</TableHead>
                    <TableHead>Gross Pay</TableHead>
                    <TableHead>Deductions</TableHead>
                    <TableHead>Net Pay</TableHead>
                    <TableHead>Status</TableHead>
                    <TableHead>Actions</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {filteredHistory.map((record) => (
                    <TableRow key={record.id}>
                      <TableCell>
                        <div>
                          <div className="font-medium">{record.payPeriod}</div>
                          <div className="text-sm text-muted-foreground">{record.bsPayPeriod}</div>
                        </div>
                      </TableCell>
                      <TableCell>
                        <CurrencyDisplay amount={record.grossPay} />
                      </TableCell>
                      <TableCell>
                        <CurrencyDisplay amount={record.deductions + record.taxes} />
                      </TableCell>
                      <TableCell className="font-semibold">
                        <CurrencyDisplay amount={record.netPay} />
                      </TableCell>
                      <TableCell>{getStatusBadge(record.status)}</TableCell>
                      <TableCell>
                        <div className="flex items-center space-x-2">
                          <Button
                            size="sm"
                            variant="outline"
                            onClick={() => {
                              setSelectedPayslip(record)
                              setShowPayslipDialog(true)
                            }}
                          >
                            <Eye className="h-4 w-4" />
                          </Button>
                          <Button
                            size="sm"
                            variant="outline"
                            onClick={() => handleDownloadPayslip(record.id)}
                          >
                            <Download className="h-4 w-4" />
                          </Button>
                        </div>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="documents" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Payroll Documents</CardTitle>
              <CardDescription>Download your payroll-related documents</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="border rounded-lg p-4">
                  <div className="flex items-center space-x-3">
                    <FileText className="h-8 w-8 text-blue-500" />
                    <div>
                      <h3 className="font-medium">Annual Tax Statement</h3>
                      <p className="text-sm text-muted-foreground">Tax year {selectedYear}</p>
                    </div>
                  </div>
                  <Button className="w-full mt-3" variant="outline">
                    <Download className="h-4 w-4 mr-2" />
                    Download PDF
                  </Button>
                </div>

                <div className="border rounded-lg p-4">
                  <div className="flex items-center space-x-3">
                    <FileText className="h-8 w-8 text-green-500" />
                    <div>
                      <h3 className="font-medium">Provident Fund Statement</h3>
                      <p className="text-sm text-muted-foreground">Year {selectedYear}</p>
                    </div>
                  </div>
                  <Button className="w-full mt-3" variant="outline">
                    <Download className="h-4 w-4 mr-2" />
                    Download PDF
                  </Button>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="notifications" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Payroll Notifications</CardTitle>
              <CardDescription>Stay updated with your payroll activities</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {notifications.map((notification) => (
                  <div
                    key={notification.id}
                    className={`flex items-start space-x-3 p-3 rounded-lg border ${
                      notification.isRead ? 'bg-muted/50' : 'bg-background'
                    }`}
                  >
                    {getNotificationIcon(notification.type)}
                    <div className="flex-1">
                      <div className="flex items-center justify-between">
                        <h4 className="font-medium">{notification.title}</h4>
                        <span className="text-xs text-muted-foreground">
                          {new Date(notification.timestamp).toLocaleDateString()}
                        </span>
                      </div>
                      <p className="text-sm text-muted-foreground mt-1">{notification.message}</p>
                      {!notification.isRead && (
                        <Button
                          size="sm"
                          variant="ghost"
                          className="mt-2 h-6 px-2 text-xs"
                          onClick={() => handleMarkNotificationRead(notification.id)}
                        >
                          Mark as read
                        </Button>
                      )}
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>

      {/* Payslip Detail Dialog */}
      <Dialog open={showPayslipDialog} onOpenChange={setShowPayslipDialog}>
        <DialogContent className="max-w-2xl">
          <DialogHeader>
            <DialogTitle>Payslip Details</DialogTitle>
            <DialogDescription>
              {selectedPayslip?.payPeriod} ({selectedPayslip?.bsPayPeriod})
            </DialogDescription>
          </DialogHeader>
          {selectedPayslip && (
            <div className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Label>Base Salary</Label>
                  <div className="font-medium">
                    <CurrencyDisplay amount={selectedPayslip.baseSalary} />
                  </div>
                </div>
                <div>
                  <Label>Allowances</Label>
                  <div className="font-medium">
                    <CurrencyDisplay amount={selectedPayslip.allowances} />
                  </div>
                </div>
                <div>
                  <Label>Gross Pay</Label>
                  <div className="font-medium">
                    <CurrencyDisplay amount={selectedPayslip.grossPay} />
                  </div>
                </div>
                <div>
                  <Label>Tax Deductions</Label>
                  <div className="font-medium">
                    <CurrencyDisplay amount={selectedPayslip.taxes} />
                  </div>
                </div>
                <div>
                  <Label>Provident Fund</Label>
                  <div className="font-medium">
                    <CurrencyDisplay amount={selectedPayslip.providentFund} />
                  </div>
                </div>
                <div>
                  <Label>Net Pay</Label>
                  <div className="font-bold text-lg">
                    <CurrencyDisplay amount={selectedPayslip.netPay} />
                  </div>
                </div>
              </div>
            </div>
          )}
          <DialogFooter>
            <Button variant="outline" onClick={() => setShowPayslipDialog(false)}>
              Close
            </Button>
            <Button onClick={() => selectedPayslip && handleDownloadPayslip(selectedPayslip.id)}>
              <Download className="h-4 w-4 mr-2" />
              Download PDF
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  )
}
