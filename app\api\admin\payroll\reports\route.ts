// Payroll Reports API Endpoint
// Handles payroll report generation and export functionality

import { NextRequest, NextResponse } from 'next/server'
import { AuthService } from '@/lib/auth-utils'
import { db } from '@/lib/neon'

// GET - Generate various payroll reports
export async function GET(request: NextRequest) {
  try {
    const sessionToken = request.cookies.get("session-token")?.value;

    if (!sessionToken) {
      return NextResponse.json({
        success: false,
        error: 'Unauthorized'
      }, { status: 401 });
    }

    const user = await AuthService.verifySession(sessionToken);

    if (!user || !["admin", "hr_manager"].includes(user.role)) {
      return NextResponse.json({
        success: false,
        error: 'Insufficient permissions'
      }, { status: 403 });
    }

    const { searchParams } = new URL(request.url);
    const reportType = searchParams.get('type') || 'summary';
    const startDate = searchParams.get('startDate');
    const endDate = searchParams.get('endDate');
    const department = searchParams.get('department');
    const userId = searchParams.get('userId');
    const fiscalYear = searchParams.get('fiscalYear');
    const format = searchParams.get('format') || 'json';

    if (reportType === 'summary') {
      // Payroll summary report
      const summary = await db.sql`
        SELECT 
          COUNT(DISTINCT p.user_id) as total_employees,
          COUNT(p.id) as total_payroll_records,
          SUM(p.gross_pay) as total_gross_pay,
          SUM(p.net_pay) as total_net_pay,
          SUM(p.tax_deductions) as total_tax_deductions,
          SUM(p.provident_fund) as total_provident_fund,
          AVG(p.net_pay) as average_net_pay,
          p.pay_period_start,
          p.pay_period_end
        FROM payroll p
        JOIN users u ON p.user_id = u.id
        WHERE 1=1
        ${startDate ? db.sql`AND p.pay_period_start >= ${startDate}` : db.sql``}
        ${endDate ? db.sql`AND p.pay_period_end <= ${endDate}` : db.sql``}
        ${department ? db.sql`AND u.department = ${department}` : db.sql``}
        ${userId ? db.sql`AND p.user_id = ${userId}` : db.sql``}
        GROUP BY p.pay_period_start, p.pay_period_end
        ORDER BY p.pay_period_start DESC
      `;

      return NextResponse.json({
        success: true,
        data: {
          summary: summary,
          reportType: 'summary',
          filters: { startDate, endDate, department, userId },
          generatedAt: new Date().toISOString()
        },
        message: 'Payroll summary report generated successfully'
      });

    } else if (reportType === 'detailed') {
      // Detailed payroll report
      const detailed = await db.sql`
        SELECT 
          p.id,
          p.user_id,
          u.full_name,
          u.employee_id,
          u.department,
          u.position,
          p.pay_period_start,
          p.pay_period_end,
          p.base_salary,
          p.overtime_hours,
          p.overtime_rate,
          p.bonuses,
          p.allowances,
          p.deductions,
          p.gross_pay,
          p.tax_deductions,
          p.provident_fund,
          p.net_pay,
          p.status,
          p.processed_at
        FROM payroll p
        JOIN users u ON p.user_id = u.id
        WHERE 1=1
        ${startDate ? db.sql`AND p.pay_period_start >= ${startDate}` : db.sql``}
        ${endDate ? db.sql`AND p.pay_period_end <= ${endDate}` : db.sql``}
        ${department ? db.sql`AND u.department = ${department}` : db.sql``}
        ${userId ? db.sql`AND p.user_id = ${userId}` : db.sql``}
        ORDER BY u.department, u.full_name, p.pay_period_start DESC
      `;

      return NextResponse.json({
        success: true,
        data: {
          records: detailed,
          total: detailed.length,
          reportType: 'detailed',
          filters: { startDate, endDate, department, userId },
          generatedAt: new Date().toISOString()
        },
        message: `Detailed payroll report generated with ${detailed.length} records`
      });

    } else if (reportType === 'tax_compliance') {
      // Tax compliance report
      const taxReport = await db.sql`
        SELECT 
          u.full_name,
          u.employee_id,
          u.pan_number,
          u.citizenship_number,
          SUM(p.gross_pay) as total_gross_pay,
          SUM(p.tax_deductions) as total_tax_deducted,
          SUM(p.provident_fund) as total_pf_contribution,
          COUNT(p.id) as payroll_periods,
          AVG(p.tax_deductions / NULLIF(p.gross_pay, 0) * 100) as avg_tax_rate
        FROM payroll p
        JOIN users u ON p.user_id = u.id
        WHERE 1=1
        ${startDate ? db.sql`AND p.pay_period_start >= ${startDate}` : db.sql``}
        ${endDate ? db.sql`AND p.pay_period_end <= ${endDate}` : db.sql``}
        ${fiscalYear ? db.sql`AND p.nepali_fiscal_year = ${fiscalYear}` : db.sql``}
        GROUP BY u.id, u.full_name, u.employee_id, u.pan_number, u.citizenship_number
        ORDER BY total_tax_deducted DESC
      `;

      return NextResponse.json({
        success: true,
        data: {
          taxReport: taxReport,
          total: taxReport.length,
          reportType: 'tax_compliance',
          fiscalYear: fiscalYear,
          totalTaxDeducted: taxReport.reduce((sum, record) => sum + (record.total_tax_deducted || 0), 0),
          generatedAt: new Date().toISOString()
        },
        message: `Tax compliance report generated for ${taxReport.length} employees`
      });

    } else if (reportType === 'department_analysis') {
      // Department-wise analysis
      const departmentAnalysis = await db.sql`
        SELECT 
          u.department,
          COUNT(DISTINCT p.user_id) as employee_count,
          SUM(p.gross_pay) as total_gross_pay,
          SUM(p.net_pay) as total_net_pay,
          AVG(p.net_pay) as average_net_pay,
          SUM(p.tax_deductions) as total_tax_deductions,
          SUM(p.provident_fund) as total_provident_fund,
          SUM(p.overtime_hours) as total_overtime_hours
        FROM payroll p
        JOIN users u ON p.user_id = u.id
        WHERE 1=1
        ${startDate ? db.sql`AND p.pay_period_start >= ${startDate}` : db.sql``}
        ${endDate ? db.sql`AND p.pay_period_end <= ${endDate}` : db.sql``}
        GROUP BY u.department
        ORDER BY total_net_pay DESC
      `;

      return NextResponse.json({
        success: true,
        data: {
          departmentAnalysis: departmentAnalysis,
          total: departmentAnalysis.length,
          reportType: 'department_analysis',
          filters: { startDate, endDate },
          generatedAt: new Date().toISOString()
        },
        message: `Department analysis report generated for ${departmentAnalysis.length} departments`
      });

    } else if (reportType === 'allowances_deductions') {
      // Allowances and deductions report
      const componentsReport = await db.sql`
        SELECT 
          u.full_name,
          u.employee_id,
          u.department,
          pcm.name as component_name,
          pcm.code as component_code,
          pcm.type as component_type,
          pcm.category,
          eca.effective_from,
          eca.effective_to,
          eca.override_amount,
          eca.override_percentage,
          eca.is_active,
          eca.approved_by,
          eca.approval_date
        FROM employee_component_assignments eca
        JOIN users u ON eca.user_id = u.id
        JOIN payroll_components_master pcm ON eca.component_id = pcm.id
        WHERE 1=1
        ${department ? db.sql`AND u.department = ${department}` : db.sql``}
        ${userId ? db.sql`AND eca.user_id = ${userId}` : db.sql``}
        ORDER BY u.department, u.full_name, pcm.type, pcm.name
      `;

      return NextResponse.json({
        success: true,
        data: {
          componentsReport: componentsReport,
          total: componentsReport.length,
          reportType: 'allowances_deductions',
          filters: { department, userId },
          generatedAt: new Date().toISOString()
        },
        message: `Components report generated with ${componentsReport.length} assignments`
      });

    } else {
      return NextResponse.json({
        success: false,
        error: 'Invalid report type. Supported types: summary, detailed, tax_compliance, department_analysis, allowances_deductions'
      }, { status: 400 });
    }

  } catch (error) {
    console.error('Error in payroll reports GET:', error);
    return NextResponse.json({
      success: false,
      error: error.message || 'Internal server error'
    }, { status: 500 });
  }
}

// POST - Export reports in different formats
export async function POST(request: NextRequest) {
  try {
    const sessionToken = request.cookies.get("session-token")?.value;

    if (!sessionToken) {
      return NextResponse.json({
        success: false,
        error: 'Unauthorized'
      }, { status: 401 });
    }

    const user = await AuthService.verifySession(sessionToken);

    if (!user || !["admin", "hr_manager"].includes(user.role)) {
      return NextResponse.json({
        success: false,
        error: 'Insufficient permissions'
      }, { status: 403 });
    }

    const body = await request.json();
    const {
      reportType = 'summary',
      format = 'csv', // csv, pdf, excel
      filters = {},
      includeCharts = false,
      emailRecipients = []
    } = body;

    // Generate export job (in a real implementation, this would queue a background job)
    const exportJob = {
      id: `export_${Date.now()}`,
      reportType: reportType,
      format: format,
      filters: filters,
      includeCharts: includeCharts,
      requestedBy: user.id,
      requestedAt: new Date().toISOString(),
      status: 'processing',
      estimatedCompletion: new Date(Date.now() + 60000).toISOString() // 1 minute from now
    };

    // In a real implementation, you would:
    // 1. Queue the export job in a background processing system
    // 2. Generate the actual file (CSV, PDF, Excel)
    // 3. Store the file in cloud storage
    // 4. Send email notifications if recipients are provided
    // 5. Update the job status

    // For now, we'll simulate the process
    setTimeout(async () => {
      try {
        // Simulate export completion
        console.log(`Export job ${exportJob.id} completed`);
        
        // If email recipients are provided, simulate sending emails
        if (emailRecipients.length > 0) {
          console.log(`Sending report to ${emailRecipients.join(', ')}`);
        }
      } catch (error) {
        console.error('Export job failed:', error);
      }
    }, 5000);

    return NextResponse.json({
      success: true,
      data: {
        exportJob: exportJob,
        downloadUrl: `/api/admin/payroll/reports/download/${exportJob.id}`, // Would be implemented separately
        estimatedCompletion: exportJob.estimatedCompletion
      },
      message: `Export job ${exportJob.id} started. ${format.toUpperCase()} file will be ready shortly.`
    });

  } catch (error) {
    console.error('Error in payroll reports POST:', error);
    return NextResponse.json({
      success: false,
      error: error.message || 'Internal server error'
    }, { status: 500 });
  }
}
