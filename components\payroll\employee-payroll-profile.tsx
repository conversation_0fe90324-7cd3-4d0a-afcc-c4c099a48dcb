"use client"

import React, { useState, useEffect } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Textarea } from '@/components/ui/textarea'
import { Badge } from '@/components/ui/badge'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog'
import { toast } from 'sonner'
import { User, CreditCard, Plus, Edit, Trash2, Save, X } from 'lucide-react'

interface EmployeePayrollProfile {
  id: string
  email: string
  full_name: string
  role: string
  department?: string
  position?: string
  phone?: string
  hire_date?: string
  salary?: number
  employee_type: string
  employee_category: string
  tax_identification_number?: string
  citizenship_number?: string
  pan_number?: string
  employment_status: string
  pay_grade?: string
  joining_bonus?: number
  bank_accounts: BankAccount[]
  allowances: any[]
  deductions: any[]
  is_active: boolean
}

interface BankAccount {
  id: string
  bank_name: string
  bank_branch?: string
  account_number: string
  account_holder_name: string
  account_type: string
  is_primary: boolean
  is_active: boolean
}

interface EmployeePayrollProfileProps {
  employeeId: string
  onClose?: () => void
}

export function EmployeePayrollProfile({ employeeId, onClose }: EmployeePayrollProfileProps) {
  const [profile, setProfile] = useState<EmployeePayrollProfile | null>(null)
  const [loading, setLoading] = useState(true)
  const [saving, setSaving] = useState(false)
  const [editMode, setEditMode] = useState(false)
  const [showBankAccountDialog, setShowBankAccountDialog] = useState(false)
  const [editingBankAccount, setEditingBankAccount] = useState<BankAccount | null>(null)

  const [formData, setFormData] = useState({
    employee_type: '',
    employee_category: '',
    tax_identification_number: '',
    citizenship_number: '',
    pan_number: '',
    employment_status: '',
    pay_grade: '',
    joining_bonus: 0,
    salary: 0,
    department: '',
    position: '',
    phone: ''
  })

  const [bankAccountForm, setBankAccountForm] = useState({
    bank_name: '',
    bank_branch: '',
    account_number: '',
    account_holder_name: '',
    account_type: 'savings',
    is_primary: false
  })

  useEffect(() => {
    fetchEmployeeProfile()
  }, [employeeId])

  const fetchEmployeeProfile = async () => {
    try {
      setLoading(true)
      const response = await fetch(`/api/admin/employees/payroll-profile?employeeId=${employeeId}`)
      const data = await response.json()

      if (data.success) {
        setProfile(data.data)
        setFormData({
          employee_type: data.data.employee_type || '',
          employee_category: data.data.employee_category || '',
          tax_identification_number: data.data.tax_identification_number || '',
          citizenship_number: data.data.citizenship_number || '',
          pan_number: data.data.pan_number || '',
          employment_status: data.data.employment_status || '',
          pay_grade: data.data.pay_grade || '',
          joining_bonus: data.data.joining_bonus || 0,
          salary: data.data.salary || 0,
          department: data.data.department || '',
          position: data.data.position || '',
          phone: data.data.phone || ''
        })
      } else {
        toast.error(data.error || 'Failed to fetch employee profile')
      }
    } catch (error) {
      console.error('Error fetching employee profile:', error)
      toast.error('Failed to fetch employee profile')
    } finally {
      setLoading(false)
    }
  }

  const handleSaveProfile = async () => {
    try {
      setSaving(true)
      const response = await fetch('/api/admin/employees/payroll-profile', {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          employeeId,
          updates: formData
        })
      })

      const data = await response.json()

      if (data.success) {
        toast.success('Employee profile updated successfully')
        setEditMode(false)
        fetchEmployeeProfile()
      } else {
        toast.error(data.error || 'Failed to update employee profile')
      }
    } catch (error) {
      console.error('Error updating employee profile:', error)
      toast.error('Failed to update employee profile')
    } finally {
      setSaving(false)
    }
  }

  const handleAddBankAccount = async () => {
    try {
      const response = await fetch('/api/admin/employees/payroll-profile', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          employeeId,
          bankAccount: bankAccountForm
        })
      })

      const data = await response.json()

      if (data.success) {
        toast.success('Bank account added successfully')
        setShowBankAccountDialog(false)
        setBankAccountForm({
          bank_name: '',
          bank_branch: '',
          account_number: '',
          account_holder_name: '',
          account_type: 'savings',
          is_primary: false
        })
        fetchEmployeeProfile()
      } else {
        toast.error(data.error || 'Failed to add bank account')
      }
    } catch (error) {
      console.error('Error adding bank account:', error)
      toast.error('Failed to add bank account')
    }
  }

  const handleUpdateBankAccount = async (accountId: string, updates: Partial<BankAccount>) => {
    try {
      const response = await fetch('/api/admin/employees/bank-accounts', {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          accountId,
          updates
        })
      })

      const data = await response.json()

      if (data.success) {
        toast.success('Bank account updated successfully')
        fetchEmployeeProfile()
      } else {
        toast.error(data.error || 'Failed to update bank account')
      }
    } catch (error) {
      console.error('Error updating bank account:', error)
      toast.error('Failed to update bank account')
    }
  }

  const handleDeactivateBankAccount = async (accountId: string) => {
    try {
      const response = await fetch(`/api/admin/employees/bank-accounts?accountId=${accountId}`, {
        method: 'DELETE'
      })

      const data = await response.json()

      if (data.success) {
        toast.success('Bank account deactivated successfully')
        fetchEmployeeProfile()
      } else {
        toast.error(data.error || 'Failed to deactivate bank account')
      }
    } catch (error) {
      console.error('Error deactivating bank account:', error)
      toast.error('Failed to deactivate bank account')
    }
  }

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
      </div>
    )
  }

  if (!profile) {
    return (
      <div className="text-center py-8">
        <p className="text-muted-foreground">Employee profile not found</p>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <div className="h-12 w-12 rounded-full bg-primary/10 flex items-center justify-center">
            <User className="h-6 w-6 text-primary" />
          </div>
          <div>
            <h2 className="text-2xl font-bold">{profile.full_name}</h2>
            <p className="text-muted-foreground">{profile.email}</p>
          </div>
        </div>
        <div className="flex items-center space-x-2">
          {editMode ? (
            <>
              <Button onClick={handleSaveProfile} disabled={saving}>
                <Save className="h-4 w-4 mr-2" />
                {saving ? 'Saving...' : 'Save'}
              </Button>
              <Button variant="outline" onClick={() => setEditMode(false)}>
                <X className="h-4 w-4 mr-2" />
                Cancel
              </Button>
            </>
          ) : (
            <Button onClick={() => setEditMode(true)}>
              <Edit className="h-4 w-4 mr-2" />
              Edit Profile
            </Button>
          )}
          {onClose && (
            <Button variant="outline" onClick={onClose}>
              Close
            </Button>
          )}
        </div>
      </div>

      <Tabs defaultValue="basic" className="space-y-4">
        <TabsList>
          <TabsTrigger value="basic">Basic Information</TabsTrigger>
          <TabsTrigger value="payroll">Payroll Details</TabsTrigger>
          <TabsTrigger value="banking">Banking Information</TabsTrigger>
          <TabsTrigger value="allowances">Allowances & Deductions</TabsTrigger>
        </TabsList>

        <TabsContent value="basic" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Employee Information</CardTitle>
              <CardDescription>Basic employee details and employment information</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="role">Role</Label>
                  <Input id="role" value={profile.role} disabled />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="department">Department</Label>
                  <Input 
                    id="department" 
                    value={editMode ? formData.department : profile.department || ''} 
                    onChange={(e) => editMode && setFormData({...formData, department: e.target.value})}
                    disabled={!editMode}
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="position">Position</Label>
                  <Input 
                    id="position" 
                    value={editMode ? formData.position : profile.position || ''} 
                    onChange={(e) => editMode && setFormData({...formData, position: e.target.value})}
                    disabled={!editMode}
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="phone">Phone</Label>
                  <Input 
                    id="phone" 
                    value={editMode ? formData.phone : profile.phone || ''} 
                    onChange={(e) => editMode && setFormData({...formData, phone: e.target.value})}
                    disabled={!editMode}
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="hire_date">Hire Date</Label>
                  <Input id="hire_date" value={profile.hire_date || ''} disabled />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="employment_status">Employment Status</Label>
                  {editMode ? (
                    <Select value={formData.employment_status} onValueChange={(value) => setFormData({...formData, employment_status: value})}>
                      <SelectTrigger>
                        <SelectValue placeholder="Select status" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="active">Active</SelectItem>
                        <SelectItem value="inactive">Inactive</SelectItem>
                        <SelectItem value="terminated">Terminated</SelectItem>
                        <SelectItem value="resigned">Resigned</SelectItem>
                        <SelectItem value="retired">Retired</SelectItem>
                      </SelectContent>
                    </Select>
                  ) : (
                    <Badge variant={profile.employment_status === 'active' ? 'default' : 'secondary'}>
                      {profile.employment_status}
                    </Badge>
                  )}
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="payroll" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Payroll Configuration</CardTitle>
              <CardDescription>Employee type, salary, and payroll-specific information</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="employee_type">Employee Type</Label>
                  {editMode ? (
                    <Select value={formData.employee_type} onValueChange={(value) => setFormData({...formData, employee_type: value})}>
                      <SelectTrigger>
                        <SelectValue placeholder="Select type" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="full_time">Full Time</SelectItem>
                        <SelectItem value="part_time">Part Time</SelectItem>
                        <SelectItem value="contract">Contract</SelectItem>
                        <SelectItem value="intern">Intern</SelectItem>
                        <SelectItem value="consultant">Consultant</SelectItem>
                      </SelectContent>
                    </Select>
                  ) : (
                    <Badge>{profile.employee_type.replace('_', ' ')}</Badge>
                  )}
                </div>
                <div className="space-y-2">
                  <Label htmlFor="employee_category">Employee Category</Label>
                  {editMode ? (
                    <Select value={formData.employee_category} onValueChange={(value) => setFormData({...formData, employee_category: value})}>
                      <SelectTrigger>
                        <SelectValue placeholder="Select category" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="regular">Regular</SelectItem>
                        <SelectItem value="probation">Probation</SelectItem>
                        <SelectItem value="temporary">Temporary</SelectItem>
                        <SelectItem value="seasonal">Seasonal</SelectItem>
                        <SelectItem value="project_based">Project Based</SelectItem>
                      </SelectContent>
                    </Select>
                  ) : (
                    <Badge variant="outline">{profile.employee_category.replace('_', ' ')}</Badge>
                  )}
                </div>
                <div className="space-y-2">
                  <Label htmlFor="salary">Base Salary (NPR)</Label>
                  <Input
                    id="salary"
                    type="number"
                    value={editMode ? formData.salary : profile.salary || 0}
                    onChange={(e) => editMode && setFormData({...formData, salary: parseFloat(e.target.value) || 0})}
                    disabled={!editMode}
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="pay_grade">Pay Grade</Label>
                  <Input
                    id="pay_grade"
                    value={editMode ? formData.pay_grade : profile.pay_grade || ''}
                    onChange={(e) => editMode && setFormData({...formData, pay_grade: e.target.value})}
                    disabled={!editMode}
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="joining_bonus">Joining Bonus (NPR)</Label>
                  <Input
                    id="joining_bonus"
                    type="number"
                    value={editMode ? formData.joining_bonus : profile.joining_bonus || 0}
                    onChange={(e) => editMode && setFormData({...formData, joining_bonus: parseFloat(e.target.value) || 0})}
                    disabled={!editMode}
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="tax_id">Tax ID Number</Label>
                  <Input
                    id="tax_id"
                    value={editMode ? formData.tax_identification_number : profile.tax_identification_number || ''}
                    onChange={(e) => editMode && setFormData({...formData, tax_identification_number: e.target.value})}
                    disabled={!editMode}
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="citizenship">Citizenship Number</Label>
                  <Input
                    id="citizenship"
                    value={editMode ? formData.citizenship_number : profile.citizenship_number || ''}
                    onChange={(e) => editMode && setFormData({...formData, citizenship_number: e.target.value})}
                    disabled={!editMode}
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="pan">PAN Number</Label>
                  <Input
                    id="pan"
                    value={editMode ? formData.pan_number : profile.pan_number || ''}
                    onChange={(e) => editMode && setFormData({...formData, pan_number: e.target.value})}
                    disabled={!editMode}
                  />
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="banking" className="space-y-4">
          <Card>
            <CardHeader>
              <div className="flex items-center justify-between">
                <div>
                  <CardTitle>Bank Accounts</CardTitle>
                  <CardDescription>Employee bank account information for salary transfers</CardDescription>
                </div>
                <Dialog open={showBankAccountDialog} onOpenChange={setShowBankAccountDialog}>
                  <DialogTrigger asChild>
                    <Button>
                      <Plus className="h-4 w-4 mr-2" />
                      Add Bank Account
                    </Button>
                  </DialogTrigger>
                  <DialogContent>
                    <DialogHeader>
                      <DialogTitle>Add Bank Account</DialogTitle>
                      <DialogDescription>Add a new bank account for this employee</DialogDescription>
                    </DialogHeader>
                    <div className="space-y-4">
                      <div className="space-y-2">
                        <Label htmlFor="bank_name">Bank Name</Label>
                        <Input
                          id="bank_name"
                          value={bankAccountForm.bank_name}
                          onChange={(e) => setBankAccountForm({...bankAccountForm, bank_name: e.target.value})}
                          placeholder="e.g., Nepal Investment Bank"
                        />
                      </div>
                      <div className="space-y-2">
                        <Label htmlFor="bank_branch">Branch</Label>
                        <Input
                          id="bank_branch"
                          value={bankAccountForm.bank_branch}
                          onChange={(e) => setBankAccountForm({...bankAccountForm, bank_branch: e.target.value})}
                          placeholder="e.g., Kathmandu Branch"
                        />
                      </div>
                      <div className="space-y-2">
                        <Label htmlFor="account_number">Account Number</Label>
                        <Input
                          id="account_number"
                          value={bankAccountForm.account_number}
                          onChange={(e) => setBankAccountForm({...bankAccountForm, account_number: e.target.value})}
                          placeholder="Account number"
                        />
                      </div>
                      <div className="space-y-2">
                        <Label htmlFor="account_holder">Account Holder Name</Label>
                        <Input
                          id="account_holder"
                          value={bankAccountForm.account_holder_name}
                          onChange={(e) => setBankAccountForm({...bankAccountForm, account_holder_name: e.target.value})}
                          placeholder="Full name as per bank records"
                        />
                      </div>
                      <div className="space-y-2">
                        <Label htmlFor="account_type">Account Type</Label>
                        <Select value={bankAccountForm.account_type} onValueChange={(value) => setBankAccountForm({...bankAccountForm, account_type: value})}>
                          <SelectTrigger>
                            <SelectValue />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="savings">Savings</SelectItem>
                            <SelectItem value="current">Current</SelectItem>
                            <SelectItem value="salary">Salary</SelectItem>
                          </SelectContent>
                        </Select>
                      </div>
                      <div className="flex items-center space-x-2">
                        <input
                          type="checkbox"
                          id="is_primary"
                          checked={bankAccountForm.is_primary}
                          onChange={(e) => setBankAccountForm({...bankAccountForm, is_primary: e.target.checked})}
                        />
                        <Label htmlFor="is_primary">Set as primary account</Label>
                      </div>
                    </div>
                    <DialogFooter>
                      <Button variant="outline" onClick={() => setShowBankAccountDialog(false)}>
                        Cancel
                      </Button>
                      <Button onClick={handleAddBankAccount}>
                        Add Account
                      </Button>
                    </DialogFooter>
                  </DialogContent>
                </Dialog>
              </div>
            </CardHeader>
            <CardContent>
              {profile.bank_accounts.length === 0 ? (
                <div className="text-center py-8 text-muted-foreground">
                  <CreditCard className="h-12 w-12 mx-auto mb-4 opacity-50" />
                  <p>No bank accounts added yet</p>
                </div>
              ) : (
                <div className="space-y-4">
                  {profile.bank_accounts.map((account) => (
                    <div key={account.id} className="border rounded-lg p-4">
                      <div className="flex items-center justify-between">
                        <div className="space-y-1">
                          <div className="flex items-center space-x-2">
                            <h4 className="font-medium">{account.bank_name}</h4>
                            {account.is_primary && <Badge variant="default">Primary</Badge>}
                            {!account.is_active && <Badge variant="destructive">Inactive</Badge>}
                          </div>
                          <p className="text-sm text-muted-foreground">
                            {account.account_holder_name} • {account.account_number}
                          </p>
                          <p className="text-sm text-muted-foreground">
                            {account.account_type} • {account.bank_branch}
                          </p>
                        </div>
                        <div className="flex items-center space-x-2">
                          {!account.is_primary && account.is_active && (
                            <Button
                              size="sm"
                              variant="outline"
                              onClick={() => handleUpdateBankAccount(account.id, { is_primary: true })}
                            >
                              Set Primary
                            </Button>
                          )}
                          {account.is_active && (
                            <Button
                              size="sm"
                              variant="destructive"
                              onClick={() => handleDeactivateBankAccount(account.id)}
                            >
                              <Trash2 className="h-4 w-4" />
                            </Button>
                          )}
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="allowances" className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <Card>
              <CardHeader>
                <CardTitle>Allowances</CardTitle>
                <CardDescription>Active allowances for this employee</CardDescription>
              </CardHeader>
              <CardContent>
                {profile.allowances.length === 0 ? (
                  <p className="text-muted-foreground text-center py-4">No allowances assigned</p>
                ) : (
                  <div className="space-y-3">
                    {profile.allowances.map((allowance) => (
                      <div key={allowance.id} className="border rounded-lg p-3">
                        <div className="flex items-center justify-between">
                          <div>
                            <h4 className="font-medium">{allowance.allowance_name}</h4>
                            <p className="text-sm text-muted-foreground">
                              {allowance.calculation_type === 'fixed'
                                ? `NPR ${allowance.amount}`
                                : `${allowance.percentage}% of ${allowance.percentage_base}`
                              }
                            </p>
                          </div>
                          <Badge variant={allowance.is_taxable ? 'default' : 'secondary'}>
                            {allowance.is_taxable ? 'Taxable' : 'Non-taxable'}
                          </Badge>
                        </div>
                      </div>
                    ))}
                  </div>
                )}
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Deductions</CardTitle>
                <CardDescription>Active deductions for this employee</CardDescription>
              </CardHeader>
              <CardContent>
                {profile.deductions.length === 0 ? (
                  <p className="text-muted-foreground text-center py-4">No deductions applied</p>
                ) : (
                  <div className="space-y-3">
                    {profile.deductions.map((deduction) => (
                      <div key={deduction.id} className="border rounded-lg p-3">
                        <div className="flex items-center justify-between">
                          <div>
                            <h4 className="font-medium">{deduction.component_name}</h4>
                            <p className="text-sm text-muted-foreground">
                              NPR {deduction.deduction_amount}
                            </p>
                            <p className="text-xs text-muted-foreground">
                              Reason: {deduction.deduction_reason}
                            </p>
                          </div>
                          <Badge variant={
                            deduction.status === 'approved' ? 'default' :
                            deduction.status === 'pending' ? 'secondary' :
                            deduction.status === 'rejected' ? 'destructive' : 'outline'
                          }>
                            {deduction.status}
                          </Badge>
                        </div>
                      </div>
                    ))}
                  </div>
                )}
              </CardContent>
            </Card>
          </div>
        </TabsContent>
      </Tabs>
    </div>
  )
}
