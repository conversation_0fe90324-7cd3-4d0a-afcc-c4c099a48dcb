"use client"

import { useState } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { <PERSON><PERSON>, Ta<PERSON><PERSON>ontent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON>hart, <PERSON><PERSON>hart } from "@/components/ui/chart"
import { Download, Calendar, TrendingUp, TrendingDown, DollarSign, Users, CreditCard, Filter } from "lucide-react"

export default function RevenueReportsPage() {
  const [timeRange, setTimeRange] = useState("month")
  const [showFilters, setShowFilters] = useState(false)

  return (
    <div className="space-y-6">
      {/* Header Section */}
      <div className="flex flex-col space-y-4 lg:flex-row lg:items-center lg:justify-between lg:space-y-0 mb-6">
        <div>
          <h1 className="text-xl lg:text-2xl font-semibold text-teal-800 dark:text-teal-300">Revenue Reports</h1>
          <p className="text-sm text-gray-500 dark:text-gray-400">Financial performance and revenue analytics</p>
        </div>
      </div>

      {/* Secondary Actions - Below main content on mobile */}
      <div className="flex flex-wrap items-center gap-2 justify-between border-b pb-4 mb-6">
        <div className="flex flex-wrap items-center gap-2">
          <Select defaultValue="month" onValueChange={setTimeRange}>
            <SelectTrigger className="w-full sm:w-[130px]">
              <SelectValue placeholder="Select period" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="week">This Week</SelectItem>
              <SelectItem value="month">This Month</SelectItem>
              <SelectItem value="quarter">This Quarter</SelectItem>
              <SelectItem value="year">This Year</SelectItem>
              <SelectItem value="custom">Custom Range</SelectItem>
            </SelectContent>
          </Select>

          <Button variant="outline" size="sm" onClick={() => setShowFilters(!showFilters)} className="text-xs sm:text-sm">
            <Filter className="mr-1 sm:mr-2 h-3 w-3 sm:h-4 sm:w-4" />
            <span className="hidden sm:inline">Filters</span>
            <span className="sm:hidden">Filter</span>
          </Button>
        </div>

        <div className="flex flex-wrap items-center gap-2">
          <Button variant="outline" size="sm" className="text-xs sm:text-sm">
            <Calendar className="mr-1 sm:mr-2 h-3 w-3 sm:h-4 sm:w-4" />
            <span className="hidden sm:inline">Date Range</span>
            <span className="sm:hidden">Date</span>
          </Button>

          <Button variant="outline" size="sm" className="text-xs sm:text-sm">
            <Download className="mr-1 sm:mr-2 h-3 w-3 sm:h-4 sm:w-4" />
            <span className="hidden sm:inline">Export</span>
            <span className="sm:hidden">Export</span>
          </Button>
        </div>
      </div>

      {showFilters && (
        <Card className="mb-6 dark:bg-gray-800 border-gray-200 dark:border-gray-700">
          <CardContent className="p-4">
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div>
                <label className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-1 block">
                  Department
                </label>
                <select className="w-full rounded-md border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 px-3 py-2 text-sm">
                  <option value="">All Departments</option>
                  <option value="marketing">Marketing</option>
                  <option value="sales">Sales</option>
                  <option value="finance">Finance</option>
                </select>
              </div>

              <div>
                <label className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-1 block">
                  Revenue Type
                </label>
                <select className="w-full rounded-md border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 px-3 py-2 text-sm">
                  <option value="">All Types</option>
                  <option value="product">Product Sales</option>
                  <option value="service">Services</option>
                  <option value="subscription">Subscriptions</option>
                </select>
              </div>

              <div className="flex items-end">
                <Button className="w-full bg-exobank-green hover:bg-exobank-green/90">Apply Filters</Button>
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
        <Card className="dark:bg-gray-800 border-gray-200 dark:border-gray-700">
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600 dark:text-gray-400">Total Revenue</p>
                <p className="text-2xl font-bold text-gray-900 dark:text-gray-100">₹4,235,890</p>
                <p className="text-xs text-green-600 dark:text-green-400 flex items-center mt-1">
                  <TrendingUp className="h-3 w-3 mr-1" />
                  15.3% from last period
                </p>
              </div>
              <div className="p-2 bg-green-100 dark:bg-green-900 rounded-lg">
                <DollarSign className="h-6 w-6 text-green-600 dark:text-green-400" />
              </div>
            </div>
          </CardContent>
        </Card>

        <Card className="dark:bg-gray-800 border-gray-200 dark:border-gray-700">
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600 dark:text-gray-400">Average Transaction</p>
                <p className="text-2xl font-bold text-gray-900 dark:text-gray-100">₹12,450</p>
                <p className="text-xs text-green-600 dark:text-green-400 flex items-center mt-1">
                  <TrendingUp className="h-3 w-3 mr-1" />
                  8.2% from last period
                </p>
              </div>
              <div className="p-2 bg-blue-100 dark:bg-blue-900 rounded-lg">
                <CreditCard className="h-6 w-6 text-blue-600 dark:text-blue-400" />
              </div>
            </div>
          </CardContent>
        </Card>

        <Card className="dark:bg-gray-800 border-gray-200 dark:border-gray-700">
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600 dark:text-gray-400">New Customers</p>
                <p className="text-2xl font-bold text-gray-900 dark:text-gray-100">342</p>
                <p className="text-xs text-red-600 dark:text-red-400 flex items-center mt-1">
                  <TrendingDown className="h-3 w-3 mr-1" />
                  2.5% from last period
                </p>
              </div>
              <div className="p-2 bg-purple-100 dark:bg-purple-900 rounded-lg">
                <Users className="h-6 w-6 text-purple-600 dark:text-purple-400" />
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      <Tabs defaultValue="overview" className="space-y-4">
        <TabsList>
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="products">Products</TabsTrigger>
          <TabsTrigger value="channels">Channels</TabsTrigger>
          <TabsTrigger value="geography">Geography</TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="space-y-4">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <Card className="dark:bg-gray-800 border-gray-200 dark:border-gray-700">
              <CardHeader>
                <CardTitle className="text-lg font-semibold text-gray-900 dark:text-gray-100">Revenue Trend</CardTitle>
                <CardDescription className="text-gray-600 dark:text-gray-400">Monthly revenue over time</CardDescription>
              </CardHeader>
              <CardContent>
                <LineChart
                  data={[
                    { month: "Jan", revenue: 3200000 },
                    { month: "Feb", revenue: 3800000 },
                    { month: "Mar", revenue: 4100000 },
                    { month: "Apr", revenue: 3900000 },
                    { month: "May", revenue: 4235890 },
                  ]}
                  index="month"
                  categories={["revenue"]}
                  colors={["emerald"]}
                  valueFormatter={(value) => (value ? `₹${(value / 1000000).toFixed(1)}M` : "")}
                  yAxisWidth={60}
                  className="h-80"
                />
              </CardContent>
            </Card>

            <Card className="dark:bg-gray-800 border-gray-200 dark:border-gray-700">
              <CardHeader>
                <CardTitle className="text-lg font-semibold text-gray-900 dark:text-gray-100">Revenue by Category</CardTitle>
                <CardDescription className="text-gray-600 dark:text-gray-400">Distribution across business segments</CardDescription>
              </CardHeader>
              <CardContent>
                <PieChart
                  data={[
                    { name: "Banking Services", value: 45 },
                    { name: "Investment", value: 30 },
                    { name: "Insurance", value: 15 },
                    { name: "Other", value: 10 },
                  ]}
                  index="name"
                  category="value"
                  colors={["emerald", "blue", "amber", "rose"]}
                  className="h-80"
                />
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="products" className="space-y-4">
          <Card className="dark:bg-gray-800 border-gray-200 dark:border-gray-700">
            <CardHeader>
              <CardTitle className="text-lg font-semibold text-gray-900 dark:text-gray-100">Product Performance</CardTitle>
              <CardDescription className="text-gray-600 dark:text-gray-400">Revenue by product category</CardDescription>
            </CardHeader>
            <CardContent>
              <BarChart
                data={[
                  { product: "Savings Account", revenue: 1500000 },
                  { product: "Fixed Deposit", revenue: 1200000 },
                  { product: "Personal Loan", revenue: 800000 },
                  { product: "Credit Card", revenue: 735890 },
                ]}
                index="product"
                categories={["revenue"]}
                colors={["emerald"]}
                valueFormatter={(value) => (value ? `₹${(value / 1000000).toFixed(1)}M` : "")}
                yAxisWidth={60}
                className="h-80"
              />
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="channels" className="space-y-4">
          <Card className="dark:bg-gray-800 border-gray-200 dark:border-gray-700">
            <CardHeader>
              <CardTitle className="text-lg font-semibold text-gray-900 dark:text-gray-100">Channel Performance</CardTitle>
              <CardDescription className="text-gray-600 dark:text-gray-400">Revenue by sales channel</CardDescription>
            </CardHeader>
            <CardContent>
              <BarChart
                data={[
                  { channel: "Online Banking", revenue: 1800000 },
                  { channel: "Branch", revenue: 1400000 },
                  { channel: "Mobile App", revenue: 1035890 },
                ]}
                index="channel"
                categories={["revenue"]}
                colors={["blue"]}
                valueFormatter={(value) => (value ? `₹${(value / 1000000).toFixed(1)}M` : "")}
                yAxisWidth={60}
                className="h-80"
              />
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="geography" className="space-y-4">
          <Card className="dark:bg-gray-800 border-gray-200 dark:border-gray-700">
            <CardHeader>
              <CardTitle className="text-lg font-semibold text-gray-900 dark:text-gray-100">Geographic Performance</CardTitle>
              <CardDescription className="text-gray-600 dark:text-gray-400">Revenue by region</CardDescription>
            </CardHeader>
            <CardContent>
              <BarChart
                data={[
                  { region: "Kathmandu", revenue: 2000000 },
                  { region: "Pokhara", revenue: 1200000 },
                  { region: "Chitwan", revenue: 800000 },
                  { region: "Butwal", revenue: 235890 },
                ]}
                index="region"
                categories={["revenue"]}
                colors={["amber"]}
                valueFormatter={(value) => (value ? `₹${(value / 1000000).toFixed(1)}M` : "")}
                yAxisWidth={60}
                className="h-80"
              />
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  )
}
