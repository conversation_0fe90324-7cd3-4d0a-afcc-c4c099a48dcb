-- Working Days Configuration Schema for Attendance-Based Payroll
-- This script creates tables and functions for managing working days configuration

-- ============================================================================
-- Step 1: Create working_days_configuration table
-- ============================================================================

CREATE TABLE IF NOT EXISTS working_days_configuration (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    fiscal_year VARCHAR(10) NOT NULL, -- e.g., "2081-82"
    bs_month INTEGER NOT NULL CHECK (bs_month >= 1 AND bs_month <= 12), -- 1=Baisakh, 2=Jestha, etc.
    bs_month_name VARCHAR(20) NOT NULL, -- e.g., "Baisakh", "Jestha", etc.
    total_days_in_month INTEGER NOT NULL CHECK (total_days_in_month >= 28 AND total_days_in_month <= 32),
    working_days INTEGER NOT NULL CHECK (working_days >= 0 AND working_days <= total_days_in_month),
    public_holidays INTEGER DEFAULT 0 CHECK (public_holidays >= 0),
    weekend_days INTEGER DEFAULT 0 CHECK (weekend_days >= 0),
    
    -- Additional configuration
    late_penalty_type VARCHAR(20) DEFAULT 'half_day' CHECK (late_penalty_type IN ('none', 'half_day', 'custom')),
    late_penalty_amount DECIMAL(8,2) DEFAULT 0, -- Custom penalty amount if type is 'custom'
    half_day_calculation_method VARCHAR(20) DEFAULT 'fifty_percent' CHECK (half_day_calculation_method IN ('fifty_percent', 'custom')),
    
    -- Metadata
    created_by UUID REFERENCES users(id),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_by UUID REFERENCES users(id),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    -- Ensure unique configuration per fiscal year and month
    UNIQUE(fiscal_year, bs_month)
);

-- ============================================================================
-- Step 2: Create attendance_calculation_settings table
-- ============================================================================

CREATE TABLE IF NOT EXISTS attendance_calculation_settings (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    setting_name VARCHAR(100) NOT NULL UNIQUE,
    setting_value TEXT NOT NULL,
    setting_type VARCHAR(20) NOT NULL CHECK (setting_type IN ('boolean', 'number', 'string', 'json')),
    description TEXT,
    category VARCHAR(50) DEFAULT 'general',
    is_system_setting BOOLEAN DEFAULT FALSE,
    
    created_by UUID REFERENCES users(id),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_by UUID REFERENCES users(id),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- ============================================================================
-- Step 3: Insert default working days configuration for current fiscal year
-- ============================================================================

-- Get current fiscal year (assuming 2081-82)
DO $$
DECLARE
    current_fy VARCHAR(10) := '2081-82';
    month_data RECORD;
BEGIN
    -- Default working days for each Nepali month (typical values)
    FOR month_data IN 
        SELECT * FROM (VALUES
            (1, 'Baisakh', 31, 22),
            (2, 'Jestha', 32, 23),
            (3, 'Ashadh', 32, 23),
            (4, 'Shrawan', 32, 23),
            (5, 'Bhadra', 32, 23),
            (6, 'Ashwin', 30, 22),
            (7, 'Kartik', 30, 22),
            (8, 'Mangsir', 30, 22),
            (9, 'Poush', 30, 22),
            (10, 'Magh', 30, 22),
            (11, 'Falgun', 30, 22),
            (12, 'Chaitra', 30, 22)
        ) AS t(month_num, month_name, total_days, working_days)
    LOOP
        INSERT INTO working_days_configuration (
            fiscal_year, bs_month, bs_month_name, total_days_in_month, 
            working_days, public_holidays, weekend_days
        ) VALUES (
            current_fy, 
            month_data.month_num, 
            month_data.month_name, 
            month_data.total_days,
            month_data.working_days,
            2, -- Typical public holidays per month
            month_data.total_days - month_data.working_days - 2 -- Weekend days
        )
        ON CONFLICT (fiscal_year, bs_month) DO NOTHING;
    END LOOP;
END $$;

-- ============================================================================
-- Step 4: Insert default attendance calculation settings
-- ============================================================================

INSERT INTO attendance_calculation_settings (setting_name, setting_value, setting_type, description, category, is_system_setting) VALUES
('enable_attendance_based_calculation', 'true', 'boolean', 'Enable automatic salary calculation based on attendance', 'calculation', TRUE),
('default_working_hours_per_day', '8', 'number', 'Standard working hours per day', 'calculation', TRUE),
('late_penalty_enabled', 'true', 'boolean', 'Enable late penalty deductions', 'penalties', TRUE),
('late_penalty_threshold_minutes', '15', 'number', 'Minutes after which late penalty applies', 'penalties', TRUE),
('half_day_threshold_hours', '4', 'number', 'Minimum hours for half day consideration', 'calculation', TRUE),
('overtime_calculation_enabled', 'true', 'boolean', 'Enable overtime calculation in attendance-based payroll', 'calculation', TRUE),
('leave_salary_calculation', 'full', 'string', 'How to calculate salary for leave days (full, half, none)', 'calculation', TRUE),
('weekend_work_multiplier', '1.5', 'number', 'Multiplier for weekend work', 'calculation', TRUE),
('holiday_work_multiplier', '2.0', 'number', 'Multiplier for holiday work', 'calculation', TRUE),
('attendance_bonus_threshold', '95', 'number', 'Attendance percentage threshold for bonus', 'bonus', TRUE),
('attendance_bonus_amount', '2000', 'number', 'Bonus amount for good attendance (NPR)', 'bonus', TRUE)
ON CONFLICT (setting_name) DO NOTHING;

-- ============================================================================
-- Step 5: Create helper functions
-- ============================================================================

-- Function to get working days for a specific month and fiscal year
CREATE OR REPLACE FUNCTION get_working_days_for_period(
    p_fiscal_year VARCHAR(10),
    p_bs_month INTEGER
) RETURNS INTEGER AS $$
DECLARE
    working_days INTEGER;
BEGIN
    SELECT wdc.working_days INTO working_days
    FROM working_days_configuration wdc
    WHERE wdc.fiscal_year = p_fiscal_year 
    AND wdc.bs_month = p_bs_month;
    
    -- Return default if not found
    RETURN COALESCE(working_days, 22);
END;
$$ LANGUAGE plpgsql;

-- Function to get attendance calculation setting
CREATE OR REPLACE FUNCTION get_attendance_setting(
    p_setting_name VARCHAR(100)
) RETURNS TEXT AS $$
DECLARE
    setting_value TEXT;
BEGIN
    SELECT acs.setting_value INTO setting_value
    FROM attendance_calculation_settings acs
    WHERE acs.setting_name = p_setting_name;
    
    RETURN setting_value;
END;
$$ LANGUAGE plpgsql;

-- Function to calculate attendance-based salary
CREATE OR REPLACE FUNCTION calculate_attendance_based_salary(
    p_base_salary DECIMAL(10,2),
    p_working_days INTEGER,
    p_days_present INTEGER,
    p_days_late INTEGER DEFAULT 0,
    p_days_half_day INTEGER DEFAULT 0,
    p_days_on_leave INTEGER DEFAULT 0
) RETURNS DECIMAL(10,2) AS $$
DECLARE
    daily_rate DECIMAL(10,2);
    payable_salary DECIMAL(10,2);
    late_penalty_type VARCHAR(20);
    late_deduction DECIMAL(10,2) := 0;
BEGIN
    -- Calculate daily rate
    daily_rate := p_base_salary / p_working_days;
    
    -- Get late penalty configuration
    SELECT late_penalty_type INTO late_penalty_type 
    FROM working_days_configuration 
    WHERE fiscal_year = (SELECT setting_value FROM attendance_calculation_settings WHERE setting_name = 'current_fiscal_year')
    LIMIT 1;
    
    -- Calculate base payable salary
    payable_salary := daily_rate * p_days_present;
    
    -- Add leave days (assuming paid leave)
    payable_salary := payable_salary + (daily_rate * p_days_on_leave);
    
    -- Add half day calculation
    payable_salary := payable_salary + (daily_rate * 0.5 * p_days_half_day);
    
    -- Apply late penalty if configured
    IF late_penalty_type = 'half_day' THEN
        late_deduction := daily_rate * 0.5 * p_days_late;
        payable_salary := payable_salary - late_deduction;
    END IF;
    
    -- Ensure non-negative result
    RETURN GREATEST(payable_salary, 0);
END;
$$ LANGUAGE plpgsql;

-- ============================================================================
-- Step 6: Create indexes for performance
-- ============================================================================

CREATE INDEX IF NOT EXISTS idx_working_days_config_fiscal_year ON working_days_configuration(fiscal_year);
CREATE INDEX IF NOT EXISTS idx_working_days_config_month ON working_days_configuration(bs_month);
CREATE INDEX IF NOT EXISTS idx_attendance_calc_settings_name ON attendance_calculation_settings(setting_name);
CREATE INDEX IF NOT EXISTS idx_attendance_calc_settings_category ON attendance_calculation_settings(category);

-- ============================================================================
-- Step 7: Add RLS policies
-- ============================================================================

-- Enable RLS on working_days_configuration
ALTER TABLE working_days_configuration ENABLE ROW LEVEL SECURITY;

-- Policy for admin and HR managers to manage working days configuration
CREATE POLICY "Admin and HR can manage working days config" ON working_days_configuration
    FOR ALL USING (
        EXISTS (
            SELECT 1 FROM users 
            WHERE users.id = auth.uid() 
            AND users.role IN ('admin', 'hr_manager')
        )
    );

-- Enable RLS on attendance_calculation_settings
ALTER TABLE attendance_calculation_settings ENABLE ROW LEVEL SECURITY;

-- Policy for admin and HR managers to manage attendance calculation settings
CREATE POLICY "Admin and HR can manage attendance calc settings" ON attendance_calculation_settings
    FOR ALL USING (
        EXISTS (
            SELECT 1 FROM users 
            WHERE users.id = auth.uid() 
            AND users.role IN ('admin', 'hr_manager')
        )
    );

-- Grant necessary permissions
GRANT SELECT, INSERT, UPDATE, DELETE ON working_days_configuration TO authenticated;
GRANT SELECT, INSERT, UPDATE, DELETE ON attendance_calculation_settings TO authenticated;
GRANT USAGE ON SCHEMA public TO authenticated;
