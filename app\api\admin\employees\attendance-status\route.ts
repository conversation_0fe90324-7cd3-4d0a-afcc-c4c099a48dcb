import { type NextRequest, NextResponse } from "next/server"
import { AuthService } from "@/lib/auth-utils"
import { serverDb } from "@/lib/server-db"

interface EmployeeAttendanceStatus {
  id: string
  employee_id: string
  email: string
  full_name: string
  role: string
  department?: string
  position?: string
  is_active: boolean
  current_status: "checked_in" | "checked_out" | "not_started"
  active_session?: {
    id: string
    check_in_time: string
    daily_sequence: number
    entry_type: string
    hours_worked_today: number
  }
  today_sessions: number
  total_hours_today: number
  remaining_check_ins: number
  remaining_check_outs: number
  last_action?: {
    type: "check_in" | "check_out"
    time: string
  }
}

export async function GET(request: NextRequest) {
  try {
    const sessionToken = request.cookies.get("session-token")?.value

    if (!sessionToken) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 })
    }

    const user = await AuthService.verifySession(sessionToken)

    if (!user || !["admin", "hr_manager"].includes(user.role)) {
      return NextResponse.json({ error: "Forbidden" }, { status: 403 })
    }

    const today = new Date().toISOString().split("T")[0]

    // Get all employees (excluding admins for cleaner list)
    const employees = await serverDb.sql`
      SELECT
        id, email, full_name, role, department, position, is_active,
        employee_id
      FROM users
      WHERE role NOT IN ('admin')
      AND is_active = true
      ORDER BY full_name
    `

    // Get today's attendance data for all employees
    const attendanceData = await serverDb.sql`
      SELECT
        user_id,
        id,
        check_in_time,
        check_out_time,
        status,
        created_at
      FROM attendance
      WHERE date = ${today}
      ORDER BY user_id, created_at
    `

    // Process each employee's attendance status
    const employeesWithStatus: EmployeeAttendanceStatus[] = employees.map(employee => {
      const userAttendance = attendanceData.filter(att => att.user_id === employee.id)

      // Find active session (checked in but not checked out)
      const activeSession = userAttendance.find(att =>
        att.check_in_time && !att.check_out_time
      )

      // Calculate total hours today (simplified for basic schema)
      const completedSessions = userAttendance.filter(att =>
        att.check_in_time && att.check_out_time
      )

      // Simple hour calculation - in a real system this would be more sophisticated
      const totalHoursToday = completedSessions.length * 8 // Assume 8 hours per completed session

      // For basic schema, we'll assume 1 session per day max
      const totalSessions = userAttendance.length
      const remainingCheckIns = Math.max(0, 1 - totalSessions)
      const remainingCheckOuts = activeSession ? 1 : 0

      // Determine current status
      let currentStatus: "checked_in" | "checked_out" | "not_started" = "not_started"
      if (activeSession) {
        currentStatus = "checked_in"
      } else if (userAttendance.length > 0) {
        currentStatus = "checked_out"
      }

      // Get last action
      let lastAction: { type: "check_in" | "check_out"; time: string } | undefined
      if (userAttendance.length > 0) {
        const lastEntry = userAttendance[userAttendance.length - 1]
        if (lastEntry.check_out_time) {
          lastAction = {
            type: "check_out",
            time: lastEntry.check_out_time
          }
        } else if (lastEntry.check_in_time) {
          lastAction = {
            type: "check_in",
            time: lastEntry.check_in_time
          }
        }
      }

      return {
        id: employee.id,
        employee_id: employee.employee_id || employee.id.slice(0, 8).toUpperCase(),
        email: employee.email,
        full_name: employee.full_name,
        role: employee.role,
        department: employee.department,
        position: employee.position,
        is_active: employee.is_active,
        current_status: currentStatus,
        active_session: activeSession ? {
          id: activeSession.id,
          check_in_time: activeSession.check_in_time,
          daily_sequence: 1,
          entry_type: 'regular',
          hours_worked_today: totalHoursToday
        } : undefined,
        today_sessions: totalSessions,
        total_hours_today: totalHoursToday,
        remaining_check_ins: remainingCheckIns,
        remaining_check_outs: remainingCheckOuts,
        last_action: lastAction
      }
    })

    // Get summary statistics
    const summary = {
      total_employees: employeesWithStatus.length,
      checked_in: employeesWithStatus.filter(emp => emp.current_status === "checked_in").length,
      checked_out: employeesWithStatus.filter(emp => emp.current_status === "checked_out").length,
      not_started: employeesWithStatus.filter(emp => emp.current_status === "not_started").length,
      total_hours_today: employeesWithStatus.reduce((total, emp) => total + emp.total_hours_today, 0)
    }

    return NextResponse.json({
      success: true,
      employees: employeesWithStatus,
      summary,
      date: today,
    })
  } catch (error) {
    console.error("Employee attendance status API error:", error)
    return NextResponse.json({ 
      error: "Internal server error" 
    }, { status: 500 })
  }
}
