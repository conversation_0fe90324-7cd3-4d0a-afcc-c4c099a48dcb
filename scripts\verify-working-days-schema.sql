-- ============================================================================
-- VERIFICATION SCRIPT FOR WORKING DAYS CONFIGURATION SCHEMA
-- ============================================================================
-- Run this script after executing the main schema to verify everything is working correctly

-- ============================================================================
-- 1. Verify Tables Exist
-- ============================================================================

SELECT 
    'Tables Check' as test_category,
    table_name,
    CASE 
        WHEN table_name IS NOT NULL THEN '✅ EXISTS'
        ELSE '❌ MISSING'
    END as status
FROM information_schema.tables 
WHERE table_schema = 'public' 
AND table_name IN ('working_days_configuration', 'attendance_calculation_settings')
ORDER BY table_name;

-- ============================================================================
-- 2. Verify Table Structures
-- ============================================================================

-- Check working_days_configuration columns
SELECT 
    'working_days_configuration Columns' as test_category,
    column_name,
    data_type,
    is_nullable,
    column_default
FROM information_schema.columns 
WHERE table_schema = 'public' 
AND table_name = 'working_days_configuration'
ORDER BY ordinal_position;

-- Check attendance_calculation_settings columns
SELECT 
    'attendance_calculation_settings Columns' as test_category,
    column_name,
    data_type,
    is_nullable,
    column_default
FROM information_schema.columns 
WHERE table_schema = 'public' 
AND table_name = 'attendance_calculation_settings'
ORDER BY ordinal_position;

-- ============================================================================
-- 3. Verify Constraints and Indexes
-- ============================================================================

-- Check constraints
SELECT 
    'Constraints Check' as test_category,
    constraint_name,
    constraint_type,
    table_name
FROM information_schema.table_constraints 
WHERE table_schema = 'public' 
AND table_name IN ('working_days_configuration', 'attendance_calculation_settings')
ORDER BY table_name, constraint_type;

-- Check indexes
SELECT 
    'Indexes Check' as test_category,
    indexname,
    tablename,
    indexdef
FROM pg_indexes 
WHERE schemaname = 'public' 
AND tablename IN ('working_days_configuration', 'attendance_calculation_settings')
ORDER BY tablename, indexname;

-- ============================================================================
-- 4. Verify Functions Exist
-- ============================================================================

SELECT 
    'Functions Check' as test_category,
    routine_name,
    routine_type,
    CASE 
        WHEN routine_name IS NOT NULL THEN '✅ EXISTS'
        ELSE '❌ MISSING'
    END as status
FROM information_schema.routines 
WHERE routine_schema = 'public' 
AND routine_name IN (
    'get_working_days_for_period',
    'get_attendance_setting',
    'calculate_attendance_based_salary'
)
ORDER BY routine_name;

-- ============================================================================
-- 5. Verify Default Data
-- ============================================================================

-- Check working days configuration data
SELECT 
    'Default Data - Working Days' as test_category,
    COUNT(*) as total_records,
    COUNT(DISTINCT fiscal_year) as fiscal_years,
    COUNT(DISTINCT bs_month) as months_configured,
    CASE 
        WHEN COUNT(*) >= 12 THEN '✅ SUFFICIENT DATA'
        ELSE '⚠️ INSUFFICIENT DATA'
    END as status
FROM working_days_configuration;

-- Show sample working days data
SELECT 
    'Sample Working Days Data' as test_category,
    fiscal_year,
    bs_month,
    bs_month_name,
    working_days,
    total_days_in_month
FROM working_days_configuration 
WHERE fiscal_year = '2081-82'
ORDER BY bs_month
LIMIT 5;

-- Check attendance calculation settings data
SELECT 
    'Default Data - Attendance Settings' as test_category,
    COUNT(*) as total_settings,
    COUNT(DISTINCT category) as categories,
    CASE 
        WHEN COUNT(*) >= 10 THEN '✅ SUFFICIENT SETTINGS'
        ELSE '⚠️ INSUFFICIENT SETTINGS'
    END as status
FROM attendance_calculation_settings;

-- Show sample settings data
SELECT 
    'Sample Settings Data' as test_category,
    setting_name,
    setting_value,
    setting_type,
    category
FROM attendance_calculation_settings 
ORDER BY category, setting_name
LIMIT 8;

-- ============================================================================
-- 6. Test Functions
-- ============================================================================

-- Test get_working_days_for_period function
SELECT 
    'Function Test - get_working_days_for_period' as test_category,
    get_working_days_for_period('2081-82', 1) as baisakh_working_days,
    get_working_days_for_period('2081-82', 6) as ashwin_working_days,
    get_working_days_for_period('2081-82', 12) as chaitra_working_days,
    CASE 
        WHEN get_working_days_for_period('2081-82', 1) > 0 THEN '✅ WORKING'
        ELSE '❌ NOT WORKING'
    END as status;

-- Test get_attendance_setting function
SELECT 
    'Function Test - get_attendance_setting' as test_category,
    get_attendance_setting('enable_attendance_based_calculation') as attendance_calc_enabled,
    get_attendance_setting('default_working_hours_per_day') as working_hours_per_day,
    get_attendance_setting('late_penalty_enabled') as late_penalty_enabled,
    CASE 
        WHEN get_attendance_setting('enable_attendance_based_calculation') IS NOT NULL THEN '✅ WORKING'
        ELSE '❌ NOT WORKING'
    END as status;

-- Test calculate_attendance_based_salary function
SELECT 
    'Function Test - calculate_attendance_based_salary' as test_category,
    calculate_attendance_based_salary(50000, 22, 22, 0, 0, 0) as full_attendance_salary,
    calculate_attendance_based_salary(50000, 22, 20, 2, 0, 0) as with_late_days_salary,
    calculate_attendance_based_salary(50000, 22, 18, 0, 2, 2) as with_half_and_leave_salary,
    CASE 
        WHEN calculate_attendance_based_salary(50000, 22, 22, 0, 0, 0) = 50000 THEN '✅ WORKING'
        ELSE '❌ NOT WORKING'
    END as status;

-- ============================================================================
-- 7. Verify RLS Policies
-- ============================================================================

-- Check RLS is enabled
SELECT 
    'RLS Check' as test_category,
    schemaname,
    tablename,
    rowsecurity,
    CASE 
        WHEN rowsecurity THEN '✅ RLS ENABLED'
        ELSE '❌ RLS DISABLED'
    END as status
FROM pg_tables 
WHERE schemaname = 'public' 
AND tablename IN ('working_days_configuration', 'attendance_calculation_settings');

-- Check policies exist
SELECT 
    'Policies Check' as test_category,
    schemaname,
    tablename,
    policyname,
    permissive,
    roles
FROM pg_policies 
WHERE schemaname = 'public' 
AND tablename IN ('working_days_configuration', 'attendance_calculation_settings');

-- ============================================================================
-- 8. Overall System Health Check
-- ============================================================================

-- Summary report
WITH health_check AS (
    SELECT 
        (SELECT COUNT(*) FROM information_schema.tables 
         WHERE table_schema = 'public' 
         AND table_name IN ('working_days_configuration', 'attendance_calculation_settings')) as tables_count,
        
        (SELECT COUNT(*) FROM information_schema.routines 
         WHERE routine_schema = 'public' 
         AND routine_name IN ('get_working_days_for_period', 'get_attendance_setting', 'calculate_attendance_based_salary')) as functions_count,
        
        (SELECT COUNT(*) FROM working_days_configuration) as working_days_records,
        
        (SELECT COUNT(*) FROM attendance_calculation_settings) as settings_records,
        
        (SELECT COUNT(*) FROM pg_indexes 
         WHERE schemaname = 'public' 
         AND tablename IN ('working_days_configuration', 'attendance_calculation_settings')) as indexes_count
)
SELECT 
    'SYSTEM HEALTH SUMMARY' as test_category,
    CASE 
        WHEN tables_count = 2 AND functions_count = 3 AND working_days_records >= 12 AND settings_records >= 10 AND indexes_count >= 4
        THEN '🎉 ALL SYSTEMS OPERATIONAL'
        ELSE '⚠️ ISSUES DETECTED - CHECK INDIVIDUAL TESTS ABOVE'
    END as overall_status,
    tables_count || '/2 tables' as tables_status,
    functions_count || '/3 functions' as functions_status,
    working_days_records || ' working days configs' as data_status,
    settings_records || ' attendance settings' as settings_status,
    indexes_count || ' indexes' as indexes_status
FROM health_check;

-- ============================================================================
-- VERIFICATION COMPLETE
-- ============================================================================
-- If all tests show ✅ status, your attendance-based payroll system is ready!
-- If any tests show ❌ or ⚠️, review the specific issues and re-run the main schema script.
