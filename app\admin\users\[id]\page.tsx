"use client"

import React, { useState, useEffect } from "react"
import { use<PERSON><PERSON><PERSON>, useRout<PERSON> } from "next/navigation"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Separator } from "@/components/ui/separator"
import { UserProfileForm } from "@/components/admin/user-profile-form"
import { RoleGuard } from "@/components/role-guard"
import { FileUpload } from "@/components/ui/file-upload"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { Dialog, DialogContent, DialogHeader, DialogTitle } from "@/components/ui/dialog"
import { ArrowLeft, Edit, Save, X, Files, Download, Trash2, User as UserIcon } from "lucide-react"
import type { User } from "@/components/auth-provider"

export default function UserDetailPage() {
  const params = useParams()
  const router = useRouter()
  const [user, setUser] = useState<User | null>(null)
  const [loading, setLoading] = useState(true)
  const [isEditing, setIsEditing] = useState(false)
  const [saving, setSaving] = useState(false)
  const [userFiles, setUserFiles] = useState<any[]>([])
  const [loadingFiles, setLoadingFiles] = useState(false)
  const [profileImage, setProfileImage] = useState<string | null>(null)
  const [showImagePreview, setShowImagePreview] = useState(false)

  useEffect(() => {
    if (params.id) {
      fetchUser(params.id as string)
      fetchUserFiles(params.id as string)
    }
  }, [params.id])

  const fetchUser = async (userId: string) => {
    try {
      setLoading(true)
      const response = await fetch(`/api/admin/users/${userId}`)
      
      if (response.ok) {
        const userData = await response.json()
        setUser(userData)
      } else {
        console.error("Failed to fetch user")
        router.push("/admin/users")
      }
    } catch (error) {
      console.error("Error fetching user:", error)
      router.push("/admin/users")
    } finally {
      setLoading(false)
    }
  }

  const handleSave = async (userData: Partial<User>) => {
    if (!user) return

    try {
      setSaving(true)
      const response = await fetch(`/api/admin/users/${user.id}`, {
        method: "PUT",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(userData),
      })

      if (response.ok) {
        const updatedUser = await response.json()
        setUser(updatedUser)
        setIsEditing(false)
        // Show success message
        alert("User updated successfully!")
      } else {
        const error = await response.json()
        alert(error.error || "Failed to update user")
      }
    } catch (error) {
      console.error("Error updating user:", error)
      alert("Failed to update user")
    } finally {
      setSaving(false)
    }
  }

  const handleCancel = () => {
    setIsEditing(false)
  }

  const handleBack = () => {
    router.push("/admin/users")
  }

  const fetchUserFiles = async (userId: string) => {
    try {
      setLoadingFiles(true)
      const response = await fetch(`/api/admin/users/${userId}/files`)
      if (response.ok) {
        const data = await response.json()
        setUserFiles(data.files)

        // Find and set profile image
        const profilePicture = data.files.find((file: any) => file.file_type === 'profile_picture')
        if (profilePicture) {
          setProfileImage(`/api/admin/files/${profilePicture.id}`)
        } else {
          setProfileImage(null)
        }
      }
    } catch (error) {
      console.error("Error fetching user files:", error)
    } finally {
      setLoadingFiles(false)
    }
  }

  const getInitials = (name: string) => {
    return name
      .split(' ')
      .map(word => word.charAt(0))
      .join('')
      .toUpperCase()
      .slice(0, 2)
  }

  const handleAvatarClick = () => {
    if (profileImage) {
      setShowImagePreview(true)
    }
  }

  const handleFileUpload = async (fileType: string, file: File) => {
    if (!user) return

    try {
      const formData = new FormData()
      formData.append('file', file)
      formData.append('fileType', fileType)

      const response = await fetch(`/api/admin/users/${user.id}/files`, {
        method: 'POST',
        body: formData
      })

      if (response.ok) {
        const data = await response.json()
        alert(data.message || "File uploaded successfully")
        fetchUserFiles(user.id) // Refresh files list
      } else {
        const data = await response.json()
        alert(data.error || "Failed to upload file")
      }
    } catch (error) {
      console.error("Error uploading file:", error)
      alert("Failed to upload file")
    }
  }

  const handleFileDelete = async (fileId: string) => {
    if (!user || !confirm("Are you sure you want to delete this file?")) return

    try {
      const response = await fetch(`/api/admin/files/${fileId}`, {
        method: 'DELETE'
      })

      if (response.ok) {
        const data = await response.json()
        alert(data.message || "File deleted successfully")
        fetchUserFiles(user.id) // Refresh files list
      } else {
        const data = await response.json()
        alert(data.error || "Failed to delete file")
      }
    } catch (error) {
      console.error("Error deleting file:", error)
      alert("Failed to delete file")
    }
  }

  if (loading) {
    return (
      <div className="container mx-auto py-6">
        <div className="text-center">Loading user details...</div>
      </div>
    )
  }

  if (!user) {
    return (
      <div className="container mx-auto py-6">
        <div className="text-center">User not found</div>
      </div>
    )
  }

  return (
    <RoleGuard allowedRoles={["admin", "hr_manager"]}>
      <div className="container mx-auto py-6 space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-4">
            <Button variant="outline" onClick={handleBack}>
              <ArrowLeft className="w-4 h-4 mr-2" />
              Back to Users
            </Button>
            <div className="flex items-center gap-4">
              <Avatar
                className="w-20 h-20 cursor-pointer hover:opacity-80 transition-opacity"
                onClick={handleAvatarClick}
              >
                <AvatarImage src={profileImage || undefined} alt={user.full_name} />
                <AvatarFallback className="text-lg font-semibold bg-gradient-to-br from-blue-500 to-purple-600 text-white">
                  {getInitials(user.full_name)}
                </AvatarFallback>
              </Avatar>
              <div>
                <h1 className="text-3xl font-bold">{user.full_name}</h1>
                <p className="text-gray-600">{user.email}</p>
              </div>
            </div>
          </div>
          <div className="flex items-center gap-2">
            {!isEditing ? (
              <Button onClick={() => setIsEditing(true)}>
                <Edit className="w-4 h-4 mr-2" />
                Edit Profile
              </Button>
            ) : (
              <div className="flex gap-2">
                <Button variant="outline" onClick={handleCancel}>
                  <X className="w-4 h-4 mr-2" />
                  Cancel
                </Button>
              </div>
            )}
          </div>
        </div>

        {/* Quick Info Cards */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          <Card>
            <CardHeader className="pb-2">
              <CardTitle className="text-sm font-medium">Employee ID</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{user.employee_id || "N/A"}</div>
            </CardContent>
          </Card>
          <Card>
            <CardHeader className="pb-2">
              <CardTitle className="text-sm font-medium">Role</CardTitle>
            </CardHeader>
            <CardContent>
              <Badge className="text-sm">
                {user.role.replace("_", " ").toUpperCase()}
              </Badge>
            </CardContent>
          </Card>
          <Card>
            <CardHeader className="pb-2">
              <CardTitle className="text-sm font-medium">Department</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-lg font-semibold">{user.department || "N/A"}</div>
            </CardContent>
          </Card>
          <Card>
            <CardHeader className="pb-2">
              <CardTitle className="text-sm font-medium">Status</CardTitle>
            </CardHeader>
            <CardContent>
              <Badge className={user.is_active ? "bg-green-100 text-green-800" : "bg-red-100 text-red-800"}>
                {user.is_active ? "Active" : "Inactive"}
              </Badge>
            </CardContent>
          </Card>
        </div>

        {/* Employment Summary */}
        <Card>
          <CardHeader>
            <CardTitle>Employment Summary</CardTitle>
            <CardDescription>Key employment information at a glance</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
              <div>
                <div className="text-sm text-gray-500">Position</div>
                <div className="font-medium">{user.position || "N/A"}</div>
              </div>
              <div>
                <div className="text-sm text-gray-500">Employment Type</div>
                <div className="font-medium">{user.employment_type?.replace("_", " ") || "N/A"}</div>
              </div>
              <div>
                <div className="text-sm text-gray-500">Employment Status</div>
                <div className="font-medium">
                  <span className={`px-2 py-1 rounded-full text-xs ${
                    user.employment_status === 'active' ? 'bg-green-100 text-green-800' :
                    user.employment_status === 'probation' ? 'bg-yellow-100 text-yellow-800' :
                    user.employment_status === 'terminated' ? 'bg-red-100 text-red-800' :
                    user.employment_status === 'resigned' ? 'bg-gray-100 text-gray-800' :
                    'bg-gray-100 text-gray-800'
                  }`}>
                    {user.employment_status?.replace("_", " ").toUpperCase() || "N/A"}
                  </span>
                </div>
              </div>
              <div>
                <div className="text-sm text-gray-500">Hire Date</div>
                <div className="font-medium">
                  {user.hire_date ? new Date(user.hire_date).toLocaleDateString() : "N/A"}
                </div>
              </div>
              <div>
                <div className="text-sm text-gray-500">Salary</div>
                <div className="font-medium">
                  {user.salary ? `${user.salary_currency || "NPR"} ${user.salary.toLocaleString()}` : "N/A"}
                </div>
              </div>
              {user.termination_date && (
                <div>
                  <div className="text-sm text-gray-500">Termination Date</div>
                  <div className="font-medium">
                    {new Date(user.termination_date).toLocaleDateString()}
                  </div>
                </div>
              )}
            </div>
          </CardContent>
        </Card>

        {/* Contact Information */}
        <Card>
          <CardHeader>
            <CardTitle>Contact Information</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-2 md:grid-cols-3 gap-4">
              <div>
                <div className="text-sm text-gray-500">Phone</div>
                <div className="font-medium">{user.phone || "N/A"}</div>
              </div>
              <div>
                <div className="text-sm text-gray-500">Emergency Contact</div>
                <div className="font-medium">{user.emergency_contact_name || "N/A"}</div>
                {user.emergency_contact_phone && (
                  <div className="text-sm text-gray-500">{user.emergency_contact_phone}</div>
                )}
              </div>
              <div>
                <div className="text-sm text-gray-500">Bank Details</div>
                <div className="font-medium">{user.bank_name || "N/A"}</div>
                {user.bank_branch && (
                  <div className="text-sm text-gray-500">Branch: {user.bank_branch}</div>
                )}
                {user.bank_account_number && (
                  <div className="text-sm text-gray-500">Account: {user.bank_account_number}</div>
                )}
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Personal Information */}
        <Card>
          <CardHeader>
            <CardTitle>Personal Information</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
              <div>
                <div className="text-sm text-gray-500">Date of Birth</div>
                <div className="font-medium">
                  {user.date_of_birth ? new Date(user.date_of_birth).toLocaleDateString() : "N/A"}
                </div>
              </div>
              <div>
                <div className="text-sm text-gray-500">Gender</div>
                <div className="font-medium">{user.gender || "N/A"}</div>
              </div>
              <div>
                <div className="text-sm text-gray-500">Marital Status</div>
                <div className="font-medium">{user.marital_status || "N/A"}</div>
              </div>
              <div>
                <div className="text-sm text-gray-500">Nationality</div>
                <div className="font-medium">{user.nationality || "N/A"}</div>
              </div>
              <div>
                <div className="text-sm text-gray-500">Citizenship Number</div>
                <div className="font-medium">{user.citizenship_number || "N/A"}</div>
              </div>
              <div>
                <div className="text-sm text-gray-500">PAN Number</div>
                <div className="font-medium">{user.pan_number || "N/A"}</div>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Edit Form */}
        {isEditing && (
          <Card>
            <CardHeader>
              <CardTitle>Edit User Profile</CardTitle>
              <CardDescription>Update user information and employment details</CardDescription>
            </CardHeader>
            <CardContent>
              <UserProfileForm
                user={user}
                onSave={handleSave}
                onCancel={handleCancel}
                isEditing={true}
              />
            </CardContent>
          </Card>
        )}

        {/* File Management */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Files className="h-5 w-5" />
              File Management
            </CardTitle>
            <CardDescription>
              Manage user documents, contracts, and signatures. Profile picture is displayed in the header above.
            </CardDescription>
          </CardHeader>
          <CardContent>
            {loadingFiles ? (
              <div className="text-center py-4">Loading files...</div>
            ) : (
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <FileUpload
                  fileType="profile_picture"
                  label="Profile Picture"
                  description="Upload user's profile photo (displayed in header)"
                  accept="image/*"
                  maxSizeMB={5}
                  existingFiles={userFiles.filter(f => f.file_type === 'profile_picture')}
                  onFileSelect={(file) => {
                    if (file) {
                      handleFileUpload('profile_picture', file)
                    }
                  }}
                  onFileDelete={handleFileDelete}
                  hideExistingFiles={true}
                />
                <FileUpload
                  fileType="signature"
                  label="Digital Signature"
                  description="Upload user's signature"
                  accept="image/*"
                  maxSizeMB={2}
                  existingFiles={userFiles.filter(f => f.file_type === 'signature')}
                  onFileSelect={(file) => {
                    if (file) {
                      handleFileUpload('signature', file)
                    }
                  }}
                  onFileDelete={handleFileDelete}
                />
                <FileUpload
                  fileType="document"
                  label="Documents"
                  description="Upload ID, certificates, etc."
                  accept=".pdf,.doc,.docx,.jpg,.jpeg,.png"
                  maxSizeMB={10}
                  existingFiles={userFiles.filter(f => f.file_type === 'document')}
                  onFileSelect={(file) => {
                    if (file) {
                      handleFileUpload('document', file)
                    }
                  }}
                  onFileDelete={handleFileDelete}
                />
                <FileUpload
                  fileType="contract"
                  label="Employment Contract"
                  description="Upload employment agreement"
                  accept=".pdf,.doc,.docx,.jpg,.jpeg,.png"
                  maxSizeMB={10}
                  existingFiles={userFiles.filter(f => f.file_type === 'contract')}
                  onFileSelect={(file) => {
                    if (file) {
                      handleFileUpload('contract', file)
                    }
                  }}
                  onFileDelete={handleFileDelete}
                />
              </div>
            )}
          </CardContent>
        </Card>

        {/* Account Information */}
        <Card>
          <CardHeader>
            <CardTitle>Account Information</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-2 md:grid-cols-3 gap-4">
              <div>
                <div className="text-sm text-gray-500">Created</div>
                <div className="font-medium">
                  {new Date(user.created_at).toLocaleDateString()}
                </div>
              </div>
              <div>
                <div className="text-sm text-gray-500">Last Updated</div>
                <div className="font-medium">
                  {new Date(user.updated_at).toLocaleDateString()}
                </div>
              </div>
              <div>
                <div className="text-sm text-gray-500">Last Login</div>
                <div className="font-medium">
                  {user.last_login ? new Date(user.last_login).toLocaleDateString() : "Never"}
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Profile Image Preview Modal */}
        <Dialog open={showImagePreview} onOpenChange={setShowImagePreview}>
          <DialogContent className="max-w-4xl max-h-[90vh] overflow-hidden">
            <DialogHeader>
              <DialogTitle>{user.full_name} - Profile Picture</DialogTitle>
            </DialogHeader>
            <div className="flex items-center justify-center p-4">
              {profileImage && (
                <img
                  src={profileImage}
                  alt={`${user.full_name} profile picture`}
                  className="max-w-full max-h-[70vh] object-contain rounded-lg"
                />
              )}
            </div>
          </DialogContent>
        </Dialog>
      </div>
    </RoleGuard>
  )
}
