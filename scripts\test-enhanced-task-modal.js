const { neon } = require('@neondatabase/serverless');

// Load environment variables
require('dotenv').config({ path: '.env.local' });

const sql = neon(process.env.DATABASE_URL);

async function testEnhancedTaskModal() {
  try {
    console.log('🧪 Testing Enhanced Task Modal Data...\n');

    // 1. Get a test task with all related data
    console.log('1. Getting test task with enhanced data...');
    
    const tasks = await sql`
      SELECT
        t.*,
        assigned_user.full_name as assigned_to_name,
        assigned_user.email as assigned_to_email,
        created_user.full_name as created_by_name,
        created_user.email as created_by_email,
        (
          SELECT COUNT(*)
          FROM task_attachments ta
          WHERE ta.task_id = t.id
        ) as attachment_count,
        (
          SELECT COUNT(*)
          FROM sub_tasks st
          WHERE st.parent_task_id = t.id
        ) as subtask_count,
        (
          SELECT COUNT(*)
          FROM sub_tasks st
          WHERE st.parent_task_id = t.id AND st.status = 'completed'
        ) as completed_subtasks
      FROM tasks t
      LEFT JOIN users assigned_user ON t.assigned_to = assigned_user.id
      LEFT JOIN users created_user ON t.assigned_by = created_user.id
      ORDER BY t.created_at DESC
      LIMIT 3
    `;

    if (tasks.length === 0) {
      console.log('❌ No tasks found for testing');
      return;
    }

    const testTask = tasks[0];
    console.log(`✅ Found ${tasks.length} tasks for testing`);
    console.log(`📝 Using task: "${testTask.title}" (ID: ${testTask.id})`);
    console.log(`   Status: ${testTask.status}`);
    console.log(`   Priority: ${testTask.priority}`);
    console.log(`   Assigned to: ${testTask.assigned_to_name || 'Unassigned'}`);
    console.log(`   Created by: ${testTask.created_by_name || 'Unknown'}`);
    console.log(`   Attachments: ${testTask.attachment_count}`);
    console.log(`   Sub-tasks: ${testTask.subtask_count} (${testTask.completed_subtasks} completed)`);

    // 2. Test task assignments
    console.log('\n2. Testing task assignments...');
    
    const assignments = await sql`
      SELECT 
        ta.id,
        ta.is_primary,
        ta.assigned_at,
        u.id as user_id,
        u.full_name,
        u.email,
        u.employee_id,
        u.department,
        u.position,
        assigned_by_user.full_name as assigned_by_name
      FROM task_assignments ta
      LEFT JOIN users u ON ta.user_id = u.id
      LEFT JOIN users assigned_by_user ON ta.assigned_by = assigned_by_user.id
      WHERE ta.task_id = ${testTask.id}
      ORDER BY ta.is_primary DESC, ta.assigned_at ASC
    `;

    console.log(`✅ Found ${assignments.length} task assignments:`);
    assignments.forEach((assignment, index) => {
      console.log(`   ${index + 1}. ${assignment.full_name} (${assignment.email})`);
      console.log(`      Primary: ${assignment.is_primary}`);
      console.log(`      Department: ${assignment.department || 'N/A'}`);
      console.log(`      Position: ${assignment.position || 'N/A'}`);
      console.log(`      Assigned by: ${assignment.assigned_by_name || 'N/A'}`);
      console.log(`      Assigned at: ${new Date(assignment.assigned_at).toLocaleString()}`);
    });

    // 3. Test sub-tasks
    console.log('\n3. Testing sub-tasks...');
    
    const subTasks = await sql`
      SELECT 
        st.*,
        assigned_user.full_name as assigned_to_name,
        assigned_user.email as assigned_to_email
      FROM sub_tasks st
      LEFT JOIN users assigned_user ON st.assigned_to = assigned_user.id
      WHERE st.parent_task_id = ${testTask.id}
      ORDER BY st.position ASC, st.created_at ASC
    `;

    console.log(`✅ Found ${subTasks.length} sub-tasks:`);
    subTasks.forEach((subtask, index) => {
      console.log(`   ${index + 1}. ${subtask.title}`);
      console.log(`      Status: ${subtask.status}`);
      console.log(`      Description: ${subtask.description || 'No description'}`);
      console.log(`      Assigned to: ${subtask.assigned_to_name || 'Unassigned'}`);
      console.log(`      Position: ${subtask.position}`);
      console.log(`      Created: ${new Date(subtask.created_at).toLocaleString()}`);
      if (subtask.completed_at) {
        console.log(`      Completed: ${new Date(subtask.completed_at).toLocaleString()}`);
      }
    });

    // 4. Test attachments
    console.log('\n4. Testing attachments...');
    
    const attachments = await sql`
      SELECT 
        a.*,
        u.full_name as uploaded_by_name,
        u.email as uploaded_by_email
      FROM task_attachments a
      LEFT JOIN users u ON a.user_id = u.id
      WHERE a.task_id = ${testTask.id}
      ORDER BY a.created_at DESC
    `;

    console.log(`✅ Found ${attachments.length} attachments:`);
    attachments.forEach((attachment, index) => {
      console.log(`   ${index + 1}. ${attachment.file_name}`);
      console.log(`      Size: ${(attachment.file_size / 1024).toFixed(1)}KB`);
      console.log(`      Type: ${attachment.file_type}`);
      console.log(`      Uploaded by: ${attachment.uploaded_by_name} (${attachment.uploaded_by_email})`);
      console.log(`      Path: ${attachment.file_path}`);
      console.log(`      Created: ${new Date(attachment.created_at).toLocaleString()}`);
    });

    // 5. Test modal data structure
    console.log('\n5. Testing complete modal data structure...');
    
    const modalData = {
      task: {
        ...testTask,
        assignments: assignments.map(a => ({
          id: a.user_id,
          full_name: a.full_name,
          email: a.email,
          employee_id: a.employee_id,
          department: a.department,
          position: a.position,
          is_primary: a.is_primary,
          assigned_at: a.assigned_at,
          assigned_by_name: a.assigned_by_name
        })),
        subtasks: subTasks.map(st => ({
          id: st.id,
          title: st.title,
          description: st.description,
          status: st.status,
          assigned_to: st.assigned_to,
          assigned_to_name: st.assigned_to_name,
          due_date: st.due_date,
          position: st.position,
          created_at: st.created_at,
          updated_at: st.updated_at,
          completed_at: st.completed_at
        })),
        attachments: attachments.map(a => ({
          id: a.id,
          file_name: a.file_name,
          file_size: a.file_size,
          file_type: a.file_type,
          file_path: a.file_path,
          uploaded_by_name: a.uploaded_by_name,
          uploaded_by_email: a.uploaded_by_email,
          created_at: a.created_at
        }))
      }
    };

    console.log('✅ Complete modal data structure:');
    console.log(`   Task: "${modalData.task.title}"`);
    console.log(`   Assignments: ${modalData.task.assignments.length} users`);
    console.log(`   Sub-tasks: ${modalData.task.subtasks.length} items`);
    console.log(`   Attachments: ${modalData.task.attachments.length} files`);
    console.log(`   Total attachment size: ${(modalData.task.attachments.reduce((sum, a) => sum + a.file_size, 0) / 1024).toFixed(1)}KB`);

    // 6. Test access control scenarios
    console.log('\n6. Testing access control scenarios...');
    
    const users = await sql`
      SELECT id, full_name, email, role 
      FROM users 
      WHERE is_active = true 
      ORDER BY created_at 
      LIMIT 3
    `;

    for (const user of users) {
      const isAssigned = assignments.some(a => a.user_id === user.id);
      const isCreator = testTask.assigned_by === user.id;
      const isAssignee = testTask.assigned_to === user.id;
      const canEdit = ['admin', 'hr_manager'].includes(user.role) || isCreator || isAssignee || isAssigned;
      
      console.log(`   ${user.full_name} (${user.role}):`);
      console.log(`     Can edit: ${canEdit}`);
      console.log(`     Is assigned: ${isAssigned}`);
      console.log(`     Is creator: ${isCreator}`);
      console.log(`     Is assignee: ${isAssignee}`);
    }

    // 7. Test task statistics
    console.log('\n7. Testing task statistics...');
    
    const stats = await sql`
      SELECT 
        COUNT(*) as total_tasks,
        COUNT(CASE WHEN status = 'completed' THEN 1 END) as completed_tasks,
        COUNT(CASE WHEN status = 'in_progress' THEN 1 END) as in_progress_tasks,
        COUNT(CASE WHEN status = 'todo' THEN 1 END) as todo_tasks,
        COUNT(CASE WHEN due_date < NOW() AND status != 'completed' THEN 1 END) as overdue_tasks,
        AVG(
          CASE WHEN status = 'completed' 
          THEN EXTRACT(EPOCH FROM (updated_at - created_at)) / 86400 
          END
        ) as avg_completion_days
      FROM tasks
    `;

    const taskStats = stats[0];
    console.log('✅ Task statistics:');
    console.log(`   Total tasks: ${taskStats.total_tasks}`);
    console.log(`   Completed: ${taskStats.completed_tasks}`);
    console.log(`   In progress: ${taskStats.in_progress_tasks}`);
    console.log(`   To do: ${taskStats.todo_tasks}`);
    console.log(`   Overdue: ${taskStats.overdue_tasks}`);
    console.log(`   Average completion time: ${taskStats.avg_completion_days ? parseFloat(taskStats.avg_completion_days).toFixed(1) : 'N/A'} days`);

    console.log('\n🎉 Enhanced Task Modal test completed successfully!');
    console.log('\n📋 Summary:');
    console.log(`   - Task data retrieval: ✅ Working`);
    console.log(`   - Multi-user assignments: ✅ Working (${assignments.length} assignments)`);
    console.log(`   - Sub-task management: ✅ Working (${subTasks.length} sub-tasks)`);
    console.log(`   - File attachments: ✅ Working (${attachments.length} files)`);
    console.log(`   - Access control logic: ✅ Working`);
    console.log(`   - Task statistics: ✅ Working`);
    console.log(`   - Complete modal data structure: ✅ Ready for UI`);

  } catch (error) {
    console.error('❌ Test failed:', error.message);
    console.error('Full error:', error);
  }
}

testEnhancedTaskModal();
