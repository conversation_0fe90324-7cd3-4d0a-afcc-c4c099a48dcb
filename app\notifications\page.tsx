"use client"

import { useState } from "react"
import { <PERSON><PERSON><PERSON><PERSON><PERSON> } from "@/components/app-header"
import { Card, CardContent } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Ta<PERSON>, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Badge } from "@/components/ui/badge"
import { ChatbotButton } from "@/components/chatbot-button"
import { Bell, Clock, CheckCircle, Info, MessageSquare, MoreHorizontal } from "lucide-react"

const notifications = [
  {
    id: "1",
    title: "New Lead Assigned",
    description: "A new high-value lead has been assigned to you: <PERSON>",
    time: "10 minutes ago",
    type: "task",
    read: false,
  },
  {
    id: "2",
    title: "Meeting Reminder",
    description: "You have a client meeting with <PERSON> at 2:00 PM today",
    time: "1 hour ago",
    type: "reminder",
    read: false,
  },
  {
    id: "3",
    title: "Task Completed",
    description: "<PERSON> completed the task 'Update client profiles'",
    time: "3 hours ago",
    type: "update",
    read: true,
  },
  {
    id: "4",
    title: "New Training Available",
    description: "New training course 'Advanced Sales Techniques' is now available",
    time: "Yesterday",
    type: "system",
    read: true,
  },
  {
    id: "5",
    title: "Performance Update",
    description: "Your monthly performance report is ready to view",
    time: "2 days ago",
    type: "update",
    read: true,
  },
  {
    id: "6",
    title: "Campaign Results",
    description: "Summer Savings Promotion campaign has ended with 85% success rate",
    time: "3 days ago",
    type: "system",
    read: true,
  },
  {
    id: "7",
    title: "Task Deadline Approaching",
    description: "The task 'Research competitors' is due tomorrow",
    time: "4 hours ago",
    type: "reminder",
    read: false,
  },
]

export default function NotificationsPage() {
  const [activeTab, setActiveTab] = useState("all")
  const [notificationList, setNotificationList] = useState(notifications)

  const filteredNotifications =
    activeTab === "all"
      ? notificationList
      : activeTab === "unread"
        ? notificationList.filter((n) => !n.read)
        : notificationList.filter((n) => n.type === activeTab)

  const markAsRead = (id: string) => {
    setNotificationList(
      notificationList.map((notification) => (notification.id === id ? { ...notification, read: true } : notification)),
    )
  }

  const markAllAsRead = () => {
    setNotificationList(notificationList.map((notification) => ({ ...notification, read: true })))
  }

  const getNotificationIcon = (type: string, read: boolean) => {
    const baseClass = `h-10 w-10 p-2 rounded-full ${read ? "opacity-60" : ""}`
    switch (type) {
      case "task":
        return (
          <div className={`${baseClass} bg-blue-100 text-blue-600 dark:bg-blue-900 dark:text-blue-300`}>
            <CheckCircle className="h-6 w-6" />
          </div>
        )
      case "reminder":
        return (
          <div className={`${baseClass} bg-orange-100 text-orange-600 dark:bg-orange-900 dark:text-orange-300`}>
            <Clock className="h-6 w-6" />
          </div>
        )
      case "update":
        return (
          <div className={`${baseClass} bg-green-100 text-green-600 dark:bg-green-900 dark:text-green-300`}>
            <Info className="h-6 w-6" />
          </div>
        )
      case "system":
        return (
          <div className={`${baseClass} bg-purple-100 text-purple-600 dark:bg-purple-900 dark:text-purple-300`}>
            <Bell className="h-6 w-6" />
          </div>
        )
      default:
        return (
          <div className={`${baseClass} bg-gray-100 text-gray-600 dark:bg-gray-700 dark:text-gray-300`}>
            <MessageSquare className="h-6 w-6" />
          </div>
        )
    }
  }

  const unreadCount = notificationList.filter((n) => !n.read).length

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900 flex flex-col">
      <AppHeader />

      <div className="p-4">
        <div className="flex items-center justify-between mb-4">
          <div className="flex items-center gap-2">
            <h2 className="text-xl font-semibold text-teal-800 dark:text-teal-300">Notifications</h2>
            {unreadCount > 0 && <Badge className="bg-exobank-red text-white">{unreadCount} new</Badge>}
          </div>
          <Button variant="outline" size="sm" onClick={markAllAsRead}>
            Mark all as read
          </Button>
        </div>

        <Tabs defaultValue="all" value={activeTab} onValueChange={setActiveTab} className="mb-6">
          <TabsList className="grid w-full grid-cols-5">
            <TabsTrigger value="all">All</TabsTrigger>
            <TabsTrigger value="unread">Unread</TabsTrigger>
            <TabsTrigger value="task">Tasks</TabsTrigger>
            <TabsTrigger value="reminder">Reminders</TabsTrigger>
            <TabsTrigger value="system">System</TabsTrigger>
          </TabsList>
        </Tabs>

        <div className="space-y-3">
          {filteredNotifications.length === 0 ? (
            <Card className="dark:bg-gray-800">
              <CardContent className="p-8 flex flex-col items-center justify-center">
                <Bell className="h-12 w-12 text-gray-300 dark:text-gray-600 mb-3" />
                <p className="text-gray-500 dark:text-gray-400 text-center">No notifications to display</p>
              </CardContent>
            </Card>
          ) : (
            filteredNotifications.map((notification) => (
              <Card
                key={notification.id}
                className={`overflow-hidden dark:bg-gray-800 ${
                  !notification.read ? "border-l-4 border-l-exobank-green" : ""
                }`}
                onClick={() => markAsRead(notification.id)}
              >
                <CardContent className="p-4 flex items-start gap-4">
                  {getNotificationIcon(notification.type, notification.read)}
                  <div className="flex-1">
                    <div className="flex items-start justify-between">
                      <div>
                        <h3
                          className={`text-base ${
                            notification.read
                              ? "font-normal text-gray-700 dark:text-gray-300"
                              : "font-medium text-teal-800 dark:text-teal-300"
                          }`}
                        >
                          {notification.title}
                        </h3>
                        <p
                          className={`text-sm ${
                            notification.read ? "text-gray-500 dark:text-gray-400" : "text-teal-600 dark:text-teal-400"
                          }`}
                        >
                          {notification.description}
                        </p>
                      </div>
                      <Button variant="ghost" size="icon" className="h-8 w-8">
                        <MoreHorizontal className="h-4 w-4" />
                      </Button>
                    </div>
                    <div className="flex items-center gap-2 mt-2">
                      <Clock className="h-3 w-3 text-gray-400" />
                      <span className="text-xs text-gray-500 dark:text-gray-400">{notification.time}</span>
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))
          )}
        </div>
      </div>

      <ChatbotButton />
    </div>
  )
}
