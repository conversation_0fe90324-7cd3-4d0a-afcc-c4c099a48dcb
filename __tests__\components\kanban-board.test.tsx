import React from 'react'
import { render, screen, fireEvent, waitFor } from '@testing-library/react'
import { QueryClient, QueryClientProvider } from '@tanstack/react-query'
import { KanbanBoard } from '@/components/kanban-board'
import { useTasks, useUpdateTaskStatus } from '@/hooks/use-tasks'

// Mock the hooks
jest.mock('@/hooks/use-tasks')

const mockUseTasks = useTasks as jest.MockedFunction<typeof useTasks>
const mockUseUpdateTaskStatus = useUpdateTaskStatus as jest.MockedFunction<typeof useUpdateTaskStatus>

// Mock data
const mockTasks = [
  {
    id: 'task-1',
    title: 'Todo Task',
    description: 'This is a todo task',
    status: 'todo',
    priority: 'medium',
    assigned_to: 'user-1',
    assigned_to_name: '<PERSON>',
    created_at: '2024-01-01T00:00:00Z'
  },
  {
    id: 'task-2',
    title: 'In Progress Task',
    description: 'This is an in progress task',
    status: 'in_progress',
    priority: 'high',
    assigned_to: 'user-2',
    assigned_to_name: '<PERSON>',
    created_at: '2024-01-02T00:00:00Z'
  },
  {
    id: 'task-3',
    title: 'Completed Task',
    description: 'This is a completed task',
    status: 'completed',
    priority: 'low',
    assigned_to: 'user-1',
    assigned_to_name: 'John Doe',
    created_at: '2024-01-03T00:00:00Z'
  }
]

const mockUpdateTaskStatus = jest.fn()

// Test wrapper with React Query
const TestWrapper: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const queryClient = new QueryClient({
    defaultOptions: {
      queries: { retry: false },
      mutations: { retry: false },
    },
  })

  return (
    <QueryClientProvider client={queryClient}>
      {children}
    </QueryClientProvider>
  )
}

describe('KanbanBoard', () => {
  beforeEach(() => {
    jest.clearAllMocks()
    
    mockUseTasks.mockReturnValue({
      data: { data: { tasks: mockTasks } },
      isLoading: false,
      error: null,
    } as any)

    mockUseUpdateTaskStatus.mockReturnValue({
      mutate: mockUpdateTaskStatus,
      isPending: false,
    } as any)
  })

  it('renders all three kanban columns', () => {
    render(
      <TestWrapper>
        <KanbanBoard />
      </TestWrapper>
    )

    expect(screen.getByText('To Do')).toBeInTheDocument()
    expect(screen.getByText('In Progress')).toBeInTheDocument()
    expect(screen.getByText('Done')).toBeInTheDocument()
  })

  it('displays tasks in correct columns', () => {
    render(
      <TestWrapper>
        <KanbanBoard />
      </TestWrapper>
    )

    // Check that tasks appear in their respective columns
    expect(screen.getByText('Todo Task')).toBeInTheDocument()
    expect(screen.getByText('In Progress Task')).toBeInTheDocument()
    expect(screen.getByText('Completed Task')).toBeInTheDocument()
  })

  it('shows task count in column headers', () => {
    render(
      <TestWrapper>
        <KanbanBoard />
      </TestWrapper>
    )

    // Each column should show the count of tasks
    const todoColumn = screen.getByText('To Do').closest('[data-testid*="kanban-column"]')
    const inProgressColumn = screen.getByText('In Progress').closest('[data-testid*="kanban-column"]')
    const doneColumn = screen.getByText('Done').closest('[data-testid*="kanban-column"]')

    expect(todoColumn).toHaveTextContent('1') // 1 todo task
    expect(inProgressColumn).toHaveTextContent('1') // 1 in progress task
    expect(doneColumn).toHaveTextContent('1') // 1 completed task
  })

  it('shows loading state when tasks are loading', () => {
    mockUseTasks.mockReturnValue({
      data: null,
      isLoading: true,
      error: null,
    } as any)

    render(
      <TestWrapper>
        <KanbanBoard />
      </TestWrapper>
    )

    expect(screen.getByText('Loading tasks...')).toBeInTheDocument()
  })

  it('shows error state when tasks fail to load', () => {
    mockUseTasks.mockReturnValue({
      data: null,
      isLoading: false,
      error: new Error('Failed to load'),
    } as any)

    render(
      <TestWrapper>
        <KanbanBoard />
      </TestWrapper>
    )

    expect(screen.getByText('Failed to load tasks')).toBeInTheDocument()
    expect(screen.getByText('Please try refreshing the page')).toBeInTheDocument()
  })

  it('calls onEditTask when task is clicked', () => {
    const mockOnEditTask = jest.fn()

    render(
      <TestWrapper>
        <KanbanBoard onEditTask={mockOnEditTask} />
      </TestWrapper>
    )

    const taskCard = screen.getByText('Todo Task').closest('[data-testid*="task-card"]')
    fireEvent.click(taskCard!)

    expect(mockOnEditTask).toHaveBeenCalledWith(
      expect.objectContaining({
        id: 'task-1',
        title: 'Todo Task'
      })
    )
  })

  it('calls onDeleteTask when delete button is clicked', () => {
    const mockOnDeleteTask = jest.fn()

    render(
      <TestWrapper>
        <KanbanBoard 
          onDeleteTask={mockOnDeleteTask}
          isAdmin={true}
          currentUserId="user-1"
        />
      </TestWrapper>
    )

    const deleteButton = screen.getAllByTestId('delete-task-button')[0]
    fireEvent.click(deleteButton)

    expect(mockOnDeleteTask).toHaveBeenCalledWith('task-1')
  })

  it('handles drag and drop between columns', async () => {
    render(
      <TestWrapper>
        <KanbanBoard />
      </TestWrapper>
    )

    const taskCard = screen.getByText('Todo Task').closest('[data-testid*="task-card"]')
    const inProgressColumn = screen.getByText('In Progress').closest('[data-testid*="kanban-column"]')

    // Simulate drag and drop
    fireEvent.dragStart(taskCard!)
    fireEvent.dragOver(inProgressColumn!)
    fireEvent.drop(inProgressColumn!)

    await waitFor(() => {
      expect(mockUpdateTaskStatus).toHaveBeenCalledWith({
        id: 'task-1',
        status: 'in_progress'
      })
    })
  })

  it('filters tasks based on provided filters', () => {
    const filters = { status: 'todo' }

    render(
      <TestWrapper>
        <KanbanBoard filters={filters} />
      </TestWrapper>
    )

    expect(mockUseTasks).toHaveBeenCalledWith(
      filters,
      { realTime: true }
    )
  })

  it('disables real-time updates when specified', () => {
    render(
      <TestWrapper>
        <KanbanBoard realTimeUpdates={false} />
      </TestWrapper>
    )

    expect(mockUseTasks).toHaveBeenCalledWith(
      {},
      { realTime: false }
    )
  })

  it('shows updating state during status changes', () => {
    mockUseUpdateTaskStatus.mockReturnValue({
      mutate: mockUpdateTaskStatus,
      isPending: true,
    } as any)

    render(
      <TestWrapper>
        <KanbanBoard />
      </TestWrapper>
    )

    // Columns should be disabled during updates
    const columns = screen.getAllByTestId(/kanban-column/)
    columns.forEach(column => {
      expect(column).toHaveClass('opacity-50', 'pointer-events-none')
    })
  })

  it('displays priority indicators on task cards', () => {
    render(
      <TestWrapper>
        <KanbanBoard />
      </TestWrapper>
    )

    // High priority task should have priority badge
    const highPriorityTask = screen.getByText('In Progress Task').closest('[data-testid*="task-card"]')
    expect(highPriorityTask).toHaveTextContent('high')

    // Medium priority task should have priority badge
    const mediumPriorityTask = screen.getByText('Todo Task').closest('[data-testid*="task-card"]')
    expect(mediumPriorityTask).toHaveTextContent('medium')
  })

  it('shows assignee information on task cards', () => {
    render(
      <TestWrapper>
        <KanbanBoard />
      </TestWrapper>
    )

    expect(screen.getByText('John Doe')).toBeInTheDocument()
    expect(screen.getByText('Jane Smith')).toBeInTheDocument()
  })

  it('handles empty task list gracefully', () => {
    mockUseTasks.mockReturnValue({
      data: { data: { tasks: [] } },
      isLoading: false,
      error: null,
    } as any)

    render(
      <TestWrapper>
        <KanbanBoard />
      </TestWrapper>
    )

    // All columns should show 0 tasks
    const columns = screen.getAllByTestId(/kanban-column/)
    columns.forEach(column => {
      expect(column).toHaveTextContent('0')
    })
  })
})
