"use client"

import Link from "next/link"
import { usePathname } from "next/navigation"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { ModeToggle } from "@/components/mode-toggle"
import { Bell, Menu, User, LogOut, Settings, UserPlus, Users, FileText, Clock, DollarSign } from "lucide-react"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuGroup,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import { Sheet, SheetContent, SheetTrigger } from "@/components/ui/sheet"
import { Avatar, AvatarFallback } from "@/components/ui/avatar"
import { useAuth } from "@/components/auth-provider"
import Image from "next/image"

export function AppHeader() {
  const pathname = usePathname()
  const { user, logout } = useAuth()

  // Don't show header on auth pages
  if (pathname?.startsWith("/auth")) {
    return null
  }

  const isAdmin = user?.role === "admin"

  return (
    <header className="fixed top-0 left-0 right-0 z-50 w-full border-b bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60">
      <div className="container flex h-16 items-center px-4">
        <Sheet>
          <SheetTrigger asChild>
            <Button variant="outline" size="icon" className="mr-2 md:hidden bg-transparent">
              <Menu className="h-5 w-5" />
              <span className="sr-only">Toggle Menu</span>
            </Button>
          </SheetTrigger>
          <SheetContent side="left" className="w-[240px] sm:w-[300px]">
            <nav className="flex flex-col gap-4 mt-8">
              <Link
                href="/dashboard"
                className={`px-2 py-1 rounded-md ${
                  pathname === "/dashboard"
                    ? "bg-exobank-green/10 text-exobank-green font-medium"
                    : "text-gray-600 hover:text-exobank-green dark:text-gray-300"
                }`}
              >
                Dashboard
              </Link>
              <Link
                href="/leads"
                className={`px-2 py-1 rounded-md ${
                  pathname === "/leads"
                    ? "bg-exobank-green/10 text-exobank-green font-medium"
                    : "text-gray-600 hover:text-exobank-green dark:text-gray-300"
                }`}
              >
                Leads
              </Link>
              <Link
                href="/training"
                className={`px-2 py-1 rounded-md ${
                  pathname === "/training"
                    ? "bg-exobank-green/10 text-exobank-green font-medium"
                    : "text-gray-600 hover:text-exobank-green dark:text-gray-300"
                }`}
              >
                Training
              </Link>
              <Link
                href="/calculator"
                className={`px-2 py-1 rounded-md ${
                  pathname === "/calculator"
                    ? "bg-exobank-green/10 text-exobank-green font-medium"
                    : "text-gray-600 hover:text-exobank-green dark:text-gray-300"
                }`}
              >
                Calculator
              </Link>
              <Link
                href="/analytics"
                className={`px-2 py-1 rounded-md ${
                  pathname === "/analytics"
                    ? "bg-exobank-green/10 text-exobank-green font-medium"
                    : "text-gray-600 hover:text-exobank-green dark:text-gray-300"
                }`}
              >
                Analytics
              </Link>
              {user && ["admin", "hr_manager"].includes(user.role) && (
                <Link
                  href="/recovery-flow"
                  className={`px-2 py-1 rounded-md ${
                    pathname === "/recovery-flow"
                      ? "bg-exobank-green/10 text-exobank-green font-medium"
                      : "text-gray-600 hover:text-exobank-green dark:text-gray-300"
                  }`}
                >
                  Recovery Flow
                </Link>
              )}
              {isAdmin && (
                <Link
                  href="/admin"
                  className={`px-2 py-1 rounded-md ${
                    pathname?.startsWith("/admin")
                      ? "bg-exobank-green/10 text-exobank-green font-medium"
                      : "text-gray-600 hover:text-exobank-green dark:text-gray-300"
                  }`}
                >
                  Admin
                </Link>
              )}
              <Link
                href="/employee/attendance"
                className={`px-2 py-1 rounded-md ${
                  pathname === "/employee/attendance"
                    ? "bg-exobank-green/10 text-exobank-green font-medium"
                    : "text-gray-600 hover:text-exobank-green dark:text-gray-300"
                }`}
              >
                Attendance
              </Link>
              <Link
                href="/employee/payroll"
                className={`px-2 py-1 rounded-md ${
                  pathname === "/employee/payroll"
                    ? "bg-exobank-green/10 text-exobank-green font-medium"
                    : "text-gray-600 hover:text-exobank-green dark:text-gray-300"
                }`}
              >
                Payroll
              </Link>
            </nav>
          </SheetContent>
        </Sheet>
        <Link href="/dashboard" className="flex items-center">
          <Image
            src="/images/exobank_logo.png"
            alt="ExoBank Logo"
            width={140}
            height={40}
            className="mr-4 object-contain"
            priority
          />
        </Link>
        <nav className="hidden md:flex items-center space-x-4 lg:space-x-6 mx-6">
          <Link
            href="/dashboard"
            className={`text-sm ${
              pathname === "/dashboard"
                ? "text-exobank-green font-medium"
                : "text-gray-600 hover:text-exobank-green dark:text-gray-300"
            }`}
          >
            Dashboard
          </Link>
          <Link
            href="/leads"
            className={`text-sm ${
              pathname === "/leads"
                ? "text-exobank-green font-medium"
                : "text-gray-600 hover:text-exobank-green dark:text-gray-300"
            }`}
          >
            Leads
          </Link>
          <Link
            href="/training"
            className={`text-sm ${
              pathname === "/training"
                ? "text-exobank-green font-medium"
                : "text-gray-600 hover:text-exobank-green dark:text-gray-300"
            }`}
          >
            Training
          </Link>
          <Link
            href="/calculator"
            className={`text-sm ${
              pathname === "/calculator"
                ? "text-exobank-green font-medium"
                : "text-gray-600 hover:text-exobank-green dark:text-gray-300"
            }`}
          >
            Calculator
          </Link>
          <Link
            href="/analytics"
            className={`text-sm ${
              pathname === "/analytics"
                ? "text-exobank-green font-medium"
                : "text-gray-600 hover:text-exobank-green dark:text-gray-300"
            }`}
          >
            Analytics
          </Link>
          {user && ["admin", "hr_manager"].includes(user.role) && (
            <Link
              href="/recovery-flow"
              className={`text-sm ${
                pathname === "/recovery-flow"
                  ? "text-exobank-green font-medium"
                  : "text-gray-600 hover:text-exobank-green dark:text-gray-300"
              }`}
            >
              Recovery Flow
            </Link>
          )}
        </nav>
        <div className="ml-auto flex items-center space-x-2">
          <Link href="/notifications">
            <Button variant="ghost" size="icon" className="relative">
              <Bell className="h-5 w-5" />
              <span className="absolute top-0 right-0 h-2 w-2 rounded-full bg-red-600" />
            </Button>
          </Link>
          <ModeToggle />
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="ghost" size="icon" className="rounded-full">
                <Avatar className="h-8 w-8">
                  <AvatarFallback className="bg-exobank-green text-white">
                    {user?.full_name?.charAt(0) || user?.email?.charAt(0)?.toUpperCase() || "U"}
                  </AvatarFallback>
                </Avatar>
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end" className="w-56">
              <DropdownMenuLabel>My Account</DropdownMenuLabel>
              <DropdownMenuSeparator />
              <DropdownMenuGroup>
                <DropdownMenuItem asChild>
                  <Link href="/profile" className="cursor-pointer w-full flex items-center">
                    <User className="mr-2 h-4 w-4" />
                    <span>Profile</span>
                  </Link>
                </DropdownMenuItem>
                <DropdownMenuItem asChild>
                  <Link href="/employee/attendance" className="cursor-pointer w-full flex items-center">
                    <Clock className="mr-2 h-4 w-4" />
                    <span>Attendance</span>
                  </Link>
                </DropdownMenuItem>
                <DropdownMenuItem asChild>
                  <Link href="/employee/payroll" className="cursor-pointer w-full flex items-center">
                    <DollarSign className="mr-2 h-4 w-4" />
                    <span>Payroll</span>
                  </Link>
                </DropdownMenuItem>
                <DropdownMenuItem asChild>
                  <Link href="/settings" className="cursor-pointer w-full flex items-center">
                    <Settings className="mr-2 h-4 w-4" />
                    <span>Settings</span>
                  </Link>
                </DropdownMenuItem>
              </DropdownMenuGroup>

              {isAdmin && (
                <>
                  <DropdownMenuSeparator />
                  <DropdownMenuLabel>Admin</DropdownMenuLabel>
                  <DropdownMenuGroup>
                    <DropdownMenuItem asChild>
                      <Link href="/admin" className="cursor-pointer w-full flex items-center">
                        <Settings className="mr-2 h-4 w-4" />
                        <span>Dashboard</span>
                      </Link>
                    </DropdownMenuItem>
                    <DropdownMenuItem asChild>
                      <Link href="/admin/users" className="cursor-pointer w-full flex items-center">
                        <Users className="mr-2 h-4 w-4" />
                        <span>Manage Users</span>
                      </Link>
                    </DropdownMenuItem>
                    <DropdownMenuItem asChild>
                      <Link href="/admin/users/add" className="cursor-pointer w-full flex items-center">
                        <UserPlus className="mr-2 h-4 w-4" />
                        <span>Add User</span>
                      </Link>
                    </DropdownMenuItem>
                    <DropdownMenuItem asChild>
                      <Link href="/admin/reports/revenue" className="cursor-pointer w-full flex items-center">
                        <FileText className="mr-2 h-4 w-4" />
                        <span>Reports</span>
                      </Link>
                    </DropdownMenuItem>
                    <DropdownMenuItem asChild>
                      <Link href="/admin/attendance" className="cursor-pointer w-full flex items-center">
                        <Clock className="mr-2 h-4 w-4" />
                        <span>Attendance Management</span>
                      </Link>
                    </DropdownMenuItem>
                    <DropdownMenuItem asChild>
                      <Link href="/admin/payroll" className="cursor-pointer w-full flex items-center">
                        <DollarSign className="mr-2 h-4 w-4" />
                        <span>Payroll Management</span>
                      </Link>
                    </DropdownMenuItem>
                  </DropdownMenuGroup>
                </>
              )}

              <DropdownMenuSeparator />
              <DropdownMenuItem onClick={logout} className="cursor-pointer text-red-600 dark:text-red-400">
                <LogOut className="mr-2 h-4 w-4" />
                <span>Log out</span>
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
      </div>
    </header>
  )
}
