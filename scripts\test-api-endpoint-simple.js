// Simple test of the API endpoint
const { neon } = require('@neondatabase/serverless');
require('dotenv').config({ path: '.env.local' });

async function testAPIEndpoint() {
  try {
    console.log('Testing API endpoint...\n');

    const sql = neon(process.env.DATABASE_URL);

    // Get a user with salary data
    const users = await sql`
      SELECT id, full_name, salary, department, position
      FROM users 
      WHERE salary IS NOT NULL 
      LIMIT 1
    `;

    if (users.length === 0) {
      console.log('❌ No users with salary data found');
      return;
    }

    const user = users[0];
    console.log(`Testing with user: ${user.full_name} (ID: ${user.id})`);

    // Test the raw database queries that the API uses
    console.log('\n1. Testing raw database queries...');
    
    try {
      console.log('Getting employee basic info...');
      const employee = await sql`SELECT * FROM users WHERE id = ${user.id}`;
      console.log(`✅ Employee found: ${employee[0].full_name}`);
      console.log(`   Salary: ${employee[0].salary}`);
      console.log(`   Position: ${employee[0].position}`);
      console.log(`   Department: ${employee[0].department}`);
      console.log(`   Employee Type: ${employee[0].employee_type}`);
      console.log(`   Employee Category: ${employee[0].employee_category}`);
      
      console.log('\nGetting bank accounts...');
      const bankAccounts = await sql`
        SELECT * FROM employee_bank_accounts 
        WHERE user_id = ${user.id} AND is_active = TRUE
        ORDER BY is_primary DESC, created_at DESC
      `;
      console.log(`✅ Found ${bankAccounts.length} bank accounts`);
      
      console.log('Getting allowances...');
      const allowances = await sql`
        SELECT * FROM allowance_assignments 
        WHERE user_id = ${user.id} AND is_active = TRUE
        ORDER BY created_at DESC
      `;
      console.log(`✅ Found ${allowances.length} allowances`);
      if (allowances.length > 0) {
        allowances.forEach(a => {
          console.log(`   - ${a.allowance_name}: ${a.allowance_amount} (${a.is_percentage ? 'percentage' : 'fixed'})`);
        });
      }
      
      console.log('Getting deductions...');
      const deductions = await sql`
        SELECT 
          da.*,
          pcm.name as component_name
        FROM deduction_approvals da
        LEFT JOIN payroll_components_master pcm ON da.component_id = pcm.id
        WHERE da.user_id = ${user.id}
        ORDER BY da.created_at DESC
      `;
      console.log(`✅ Found ${deductions.length} deductions`);
      if (deductions.length > 0) {
        deductions.forEach(d => {
          console.log(`   - ${d.deduction_name}: ${d.deduction_amount} (${d.is_percentage ? 'percentage' : 'fixed'})`);
        });
      }

      // Simulate what the API should return
      console.log('\n2. Simulating API response structure...');
      const apiResponse = {
        success: true,
        data: {
          id: employee[0].id,
          email: employee[0].email,
          full_name: employee[0].full_name,
          role: employee[0].role,
          department: employee[0].department,
          position: employee[0].position,
          phone: employee[0].phone,
          hire_date: employee[0].hire_date,
          salary: employee[0].salary,
          employee_type: employee[0].employee_type || 'full_time',
          employee_category: employee[0].employee_category || 'regular',
          tax_identification_number: employee[0].tax_identification_number,
          citizenship_number: employee[0].citizenship_number,
          pan_number: employee[0].pan_number,
          employment_status: employee[0].employment_status || 'active',
          pay_grade: employee[0].pay_grade,
          joining_bonus: employee[0].joining_bonus || 0,
          bank_accounts: bankAccounts,
          allowances: allowances,
          deductions: deductions,
          is_active: employee[0].is_active,
          created_at: employee[0].created_at,
          updated_at: employee[0].updated_at
        }
      };

      console.log('✅ API response structure created');
      console.log('Response data keys:', Object.keys(apiResponse.data));
      
      // Test calculation functions
      console.log('\n3. Testing calculations...');
      
      // Calculate total allowances
      let totalAllowances = 0;
      allowances.forEach(allowance => {
        if (allowance.is_percentage) {
          const base = allowance.percentage_base === 'base_salary' ? employee[0].salary : 0;
          totalAllowances += (base * allowance.allowance_amount / 100);
        } else {
          totalAllowances += parseFloat(allowance.allowance_amount);
        }
      });
      
      // Calculate total deductions
      let totalDeductions = 0;
      const grossPay = employee[0].salary + totalAllowances;
      deductions.forEach(deduction => {
        if (deduction.is_percentage) {
          let base = 0;
          switch (deduction.percentage_base) {
            case 'base_salary':
              base = employee[0].salary;
              break;
            case 'gross_pay':
              base = grossPay;
              break;
            default:
              base = employee[0].salary;
          }
          totalDeductions += (base * deduction.deduction_amount / 100);
        } else {
          totalDeductions += parseFloat(deduction.deduction_amount);
        }
      });

      console.log(`Base Salary: ${employee[0].salary}`);
      console.log(`Total Allowances: ${totalAllowances}`);
      console.log(`Gross Pay: ${grossPay}`);
      console.log(`Total Deductions: ${totalDeductions}`);
      console.log(`Net Pay: ${grossPay - totalDeductions}`);

    } catch (error) {
      console.log('❌ Error with database queries:', error.message);
      console.log('Stack trace:', error.stack);
    }

  } catch (error) {
    console.error('❌ Error:', error.message);
  }
}

testAPIEndpoint();
