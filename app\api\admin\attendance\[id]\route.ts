import { type NextRequest, NextResponse } from "next/server"
import { AuthService } from "@/lib/auth-utils"
import { db } from "@/lib/neon"

export async function PUT(request: NextRequest, { params }: { params: { id: string } }) {
  try {
    const sessionToken = request.cookies.get("session-token")?.value

    if (!sessionToken) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 })
    }

    const user = await AuthService.verifySession(sessionToken)

    if (!user || !["admin", "hr_manager"].includes(user.role)) {
      return NextResponse.json({ error: "Forbidden" }, { status: 403 })
    }

    const { id } = params
    const data = await request.json()
    const { checkInTime, checkOutTime, status, hoursWorked, notes } = data

    const attendance = await db.updateAttendanceRecord(id, {
      checkInTime,
      checkOutTime,
      status,
      hoursWorked,
      notes,
      updatedBy: user.id,
    })

    return NextResponse.json({
      success: true,
      attendance,
      message: "Attendance record updated successfully",
    })
  } catch (error) {
    console.error("Update attendance API error:", error)
    const message = error instanceof Error ? error.message : "Failed to update attendance record"
    return NextResponse.json({ error: message }, { status: 400 })
  }
}

export async function DELETE(request: NextRequest, { params }: { params: { id: string } }) {
  try {
    const sessionToken = request.cookies.get("session-token")?.value

    if (!sessionToken) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 })
    }

    const user = await AuthService.verifySession(sessionToken)

    if (!user || !["admin", "hr_manager"].includes(user.role)) {
      return NextResponse.json({ error: "Forbidden" }, { status: 403 })
    }

    const { id } = params

    await db.deleteAttendanceRecord(id)

    return NextResponse.json({
      success: true,
      message: "Attendance record deleted successfully",
    })
  } catch (error) {
    console.error("Delete attendance API error:", error)
    const message = error instanceof Error ? error.message : "Failed to delete attendance record"
    return NextResponse.json({ error: message }, { status: 400 })
  }
}
