#!/usr/bin/env node

/**
 * Test script for user management system
 * This script validates the database setup and basic functionality
 */

const { neon } = require('@neondatabase/serverless');
require('dotenv').config({ path: '.env.local' });

async function testUserManagement() {
  console.log('🧪 Testing User Management System...\n');
  
  if (!process.env.DATABASE_URL) {
    console.error('❌ ERROR: DATABASE_URL environment variable is not set');
    process.exit(1);
  }
  
  try {
    const sql = neon(process.env.DATABASE_URL);
    
    // Test 1: Database Connection
    console.log('🔄 Test 1: Database Connection...');
    await sql`SELECT 1`;
    console.log('✅ Database connection successful\n');
    
    // Test 2: Table Existence
    console.log('🔄 Test 2: Checking table existence...');
    const tables = await sql`
      SELECT table_name 
      FROM information_schema.tables 
      WHERE table_schema = 'public' 
      ORDER BY table_name
    `;
    
    const expectedTables = [
      'audit_logs',
      'attendance', 
      'departments',
      'employee_addresses',
      'employee_documents',
      'payroll',
      'permissions',
      'role_permissions',
      'user_sessions',
      'users'
    ];
    
    const existingTables = tables.map(t => t.table_name);
    const missingTables = expectedTables.filter(t => !existingTables.includes(t));
    
    if (missingTables.length === 0) {
      console.log('✅ All required tables exist');
      console.log(`📊 Found ${tables.length} tables: ${existingTables.join(', ')}\n`);
    } else {
      console.log('❌ Missing tables:', missingTables.join(', '));
      return;
    }
    
    // Test 3: Sample Users
    console.log('🔄 Test 3: Checking sample users...');
    const users = await sql`
      SELECT email, role, full_name, is_active, employee_id 
      FROM users 
      ORDER BY role, email
    `;
    
    if (users.length > 0) {
      console.log(`✅ Found ${users.length} users:`);
      users.forEach(user => {
        console.log(`   - ${user.email} (${user.role}) - ${user.full_name} ${user.is_active ? '✅' : '❌'}`);
      });
      console.log();
    } else {
      console.log('❌ No users found in database\n');
    }
    
    // Test 4: Departments
    console.log('🔄 Test 4: Checking departments...');
    const departments = await sql`
      SELECT d.name, d.location, d.is_active, u.full_name as manager_name
      FROM departments d
      LEFT JOIN users u ON d.manager_id = u.id
      ORDER BY d.name
    `;
    
    if (departments.length > 0) {
      console.log(`✅ Found ${departments.length} departments:`);
      departments.forEach(dept => {
        console.log(`   - ${dept.name} (${dept.location || 'No Location'}) - Manager: ${dept.manager_name || 'None'} ${dept.is_active ? '✅' : '❌'}`);
      });
      console.log();
    } else {
      console.log('❌ No departments found in database\n');
    }
    
    // Test 5: Permissions
    console.log('🔄 Test 5: Checking permissions...');
    const permissions = await sql`
      SELECT COUNT(*) as count FROM permissions
    `;
    
    const rolePermissions = await sql`
      SELECT role, COUNT(*) as permission_count
      FROM role_permissions
      GROUP BY role
      ORDER BY role
    `;
    
    console.log(`✅ Found ${permissions[0].count} permissions`);
    console.log('📊 Role permission counts:');
    rolePermissions.forEach(rp => {
      console.log(`   - ${rp.role}: ${rp.permission_count} permissions`);
    });
    console.log();
    
    // Test 6: User Authentication Data
    console.log('🔄 Test 6: Checking user authentication data...');
    const authUsers = await sql`
      SELECT email, password_hash IS NOT NULL as has_password, email_verified
      FROM users
      WHERE is_active = true
      ORDER BY email
    `;
    
    const usersWithPasswords = authUsers.filter(u => u.has_password).length;
    const verifiedUsers = authUsers.filter(u => u.email_verified).length;
    
    console.log(`✅ Authentication data check:`);
    console.log(`   - Users with passwords: ${usersWithPasswords}/${authUsers.length}`);
    console.log(`   - Verified users: ${verifiedUsers}/${authUsers.length}`);
    console.log();
    
    // Test 7: Database Indexes
    console.log('🔄 Test 7: Checking database indexes...');
    const indexes = await sql`
      SELECT indexname, tablename
      FROM pg_indexes
      WHERE schemaname = 'public'
      AND indexname LIKE 'idx_%'
      ORDER BY tablename, indexname
    `;
    
    console.log(`✅ Found ${indexes.length} custom indexes:`);
    indexes.forEach(idx => {
      console.log(`   - ${idx.tablename}.${idx.indexname}`);
    });
    console.log();
    
    // Test 8: Sample Data Integrity
    console.log('🔄 Test 8: Checking data integrity...');
    
    // Check for users with addresses
    const usersWithAddresses = await sql`
      SELECT COUNT(DISTINCT u.id) as count
      FROM users u
      JOIN employee_addresses ea ON u.id = ea.user_id
    `;
    
    // Check for role-permission mappings
    const rolePermissionMappings = await sql`
      SELECT COUNT(*) as count
      FROM role_permissions rp
      JOIN permissions p ON rp.permission_id = p.id
    `;
    
    console.log(`✅ Data integrity check:`);
    console.log(`   - Users with addresses: ${usersWithAddresses[0].count}`);
    console.log(`   - Role-permission mappings: ${rolePermissionMappings[0].count}`);
    console.log();
    
    // Test 9: Sample Query Performance
    console.log('🔄 Test 9: Testing query performance...');
    const startTime = Date.now();
    
    await sql`
      SELECT u.*, d.name as department_name, ea.city
      FROM users u
      LEFT JOIN departments d ON u.department = d.name
      LEFT JOIN employee_addresses ea ON u.id = ea.user_id AND ea.is_primary = true
      WHERE u.is_active = true
      ORDER BY u.full_name
    `;
    
    const queryTime = Date.now() - startTime;
    console.log(`✅ Complex query executed in ${queryTime}ms\n`);
    
    // Summary
    console.log('🎉 User Management System Test Summary:');
    console.log('=====================================');
    console.log('✅ Database connection: Working');
    console.log('✅ Table structure: Complete');
    console.log(`✅ Sample users: ${users.length} created`);
    console.log(`✅ Departments: ${departments.length} created`);
    console.log(`✅ Permissions: ${permissions[0].count} configured`);
    console.log(`✅ Indexes: ${indexes.length} optimized`);
    console.log('✅ Data integrity: Verified');
    console.log('✅ Query performance: Acceptable');
    console.log();
    
    console.log('📋 Ready to use! Login credentials:');
    const activeUsers = users.filter(u => u.is_active);
    activeUsers.forEach(user => {
      console.log(`   ${user.email} / admin123 (${user.role})`);
    });
    
    console.log('\n🚀 Next steps:');
    console.log('1. Start your development server: npm run dev');
    console.log('2. Navigate to /admin to access the admin dashboard');
    console.log('3. Navigate to /admin/users for user management');
    console.log('4. Login with any of the credentials above');
    
  } catch (error) {
    console.error('❌ Test failed:', error.message);
    console.error('Full error:', error);
    process.exit(1);
  }
}

// Run the test
if (require.main === module) {
  testUserManagement();
}

module.exports = { testUserManagement };
