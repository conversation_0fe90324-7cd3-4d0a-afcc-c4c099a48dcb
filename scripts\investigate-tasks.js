#!/usr/bin/env node

// Phase 1 - Database Investigation Script
const { neon } = require('@neondatabase/serverless');
require('dotenv').config({ path: '.env.local' });

async function investigateTasks() {
  console.log('🔍 PHASE 1 - DATABASE INVESTIGATION');
  console.log('===================================\n');

  if (!process.env.DATABASE_URL) {
    console.error('❌ ERROR: DATABASE_URL environment variable is not set');
    process.exit(1);
  }

  try {
    const sql = neon(process.env.DATABASE_URL);

    // Test connection
    console.log('🔄 Testing database connection...');
    await sql`SELECT 1`;
    console.log('✅ Database connection successful!\n');

    // 1. Check tasks table structure
    console.log('📋 TASKS TABLE STRUCTURE:');
    console.log('========================');
    const columns = await sql`
      SELECT column_name, data_type, is_nullable, column_default, character_maximum_length
      FROM information_schema.columns 
      WHERE table_name = 'tasks' AND table_schema = 'public'
      ORDER BY ordinal_position
    `;
    
    columns.forEach(col => {
      console.log(`  - ${col.column_name}: ${col.data_type}${col.character_maximum_length ? '(' + col.character_maximum_length + ')' : ''} ${col.is_nullable === 'NO' ? 'NOT NULL' : 'NULL'} ${col.column_default ? 'DEFAULT ' + col.column_default : ''}`);
    });

    // 2. Check if any tasks exist
    console.log('\n📊 TASKS COUNT:');
    console.log('===============');
    const taskCount = await sql`SELECT COUNT(*) as total FROM tasks`;
    console.log(`Total tasks in database: ${taskCount[0].total}`);

    // 3. If tasks exist, show recent ones
    if (taskCount[0].total > 0) {
      console.log('\n📝 RECENT TASKS (Last 10):');
      console.log('==========================');
      const recentTasks = await sql`
        SELECT 
          id, title, status, priority, assigned_to, assigned_by, 
          created_at, updated_at
        FROM tasks 
        ORDER BY created_at DESC 
        LIMIT 10
      `;
      
      recentTasks.forEach((task, index) => {
        console.log(`${index + 1}. ${task.title}`);
        console.log(`   ID: ${task.id}`);
        console.log(`   Status: ${task.status}`);
        console.log(`   Priority: ${task.priority}`);
        console.log(`   Assigned to: ${task.assigned_to || 'Unassigned'}`);
        console.log(`   Created: ${task.created_at}`);
        console.log(`   Updated: ${task.updated_at}`);
        console.log('');
      });

      // 4. Check status distribution
      console.log('📈 STATUS DISTRIBUTION:');
      console.log('======================');
      const statusDist = await sql`
        SELECT status, COUNT(*) as count 
        FROM tasks 
        GROUP BY status 
        ORDER BY count DESC
      `;
      
      statusDist.forEach(stat => {
        console.log(`  ${stat.status}: ${stat.count} tasks`);
      });

      // 5. Check priority distribution
      console.log('\n🎯 PRIORITY DISTRIBUTION:');
      console.log('=========================');
      const priorityDist = await sql`
        SELECT priority, COUNT(*) as count 
        FROM tasks 
        GROUP BY priority 
        ORDER BY count DESC
      `;
      
      priorityDist.forEach(stat => {
        console.log(`  ${stat.priority}: ${stat.count} tasks`);
      });
    } else {
      console.log('\n⚠️  NO TASKS FOUND IN DATABASE');
      console.log('This explains why the kanban board is empty.');
    }

    // 6. Check users table for task assignment
    console.log('\n👥 AVAILABLE USERS FOR TASK ASSIGNMENT:');
    console.log('======================================');
    const users = await sql`
      SELECT id, full_name, email, role, is_active 
      FROM users 
      WHERE is_active = true 
      ORDER BY full_name
      LIMIT 10
    `;
    
    if (users.length > 0) {
      users.forEach((user, index) => {
        console.log(`${index + 1}. ${user.full_name} (${user.email})`);
        console.log(`   ID: ${user.id}`);
        console.log(`   Role: ${user.role}`);
        console.log('');
      });
    } else {
      console.log('⚠️  No active users found for task assignment');
    }

    // 7. Check task constraints
    console.log('🔒 TASK TABLE CONSTRAINTS:');
    console.log('==========================');
    const constraints = await sql`
      SELECT constraint_name, constraint_type, check_clause
      FROM information_schema.table_constraints tc
      LEFT JOIN information_schema.check_constraints cc ON tc.constraint_name = cc.constraint_name
      WHERE tc.table_name = 'tasks' AND tc.table_schema = 'public'
    `;
    
    constraints.forEach(constraint => {
      console.log(`  - ${constraint.constraint_name}: ${constraint.constraint_type} ${constraint.check_clause || ''}`);
    });

    console.log('\n✅ PHASE 1 INVESTIGATION COMPLETE');
    console.log('==================================');
    
    if (taskCount[0].total === 0) {
      console.log('\n🔍 FINDINGS:');
      console.log('- Database connection is working');
      console.log('- Tasks table exists with proper structure');
      console.log('- No tasks currently exist in the database');
      console.log('- This explains why the kanban board appears empty');
      console.log('\n📋 NEXT STEPS:');
      console.log('- Proceed to Phase 2: Test task creation flow');
      console.log('- If task creation fails, proceed to Phase 3: Create sample data');
    } else {
      console.log('\n🔍 FINDINGS:');
      console.log(`- Database contains ${taskCount[0].total} tasks`);
      console.log('- Tasks exist but may not be displaying in the UI');
      console.log('- This suggests a frontend display issue rather than database issue');
      console.log('\n📋 NEXT STEPS:');
      console.log('- Proceed to Phase 4: Investigate kanban board display');
      console.log('- Check data flow from API to frontend components');
    }

  } catch (error) {
    console.error('❌ Database investigation failed:', error);
    console.error('Stack trace:', error.stack);
    process.exit(1);
  }
}

investigateTasks().catch(console.error);
