const { neon } = require('@neondatabase/serverless');

// Load environment variables
require('dotenv').config({ path: '.env.local' });

const sql = neon(process.env.DATABASE_URL);

async function applySchemaStepByStep() {
  try {
    console.log('🚀 Starting enhanced task management schema application...\n');
    
    // Step 1: Create task_assignments table
    console.log('1. Creating task_assignments table...');
    await sql`
      CREATE TABLE IF NOT EXISTS task_assignments (
          id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
          task_id UUID NOT NULL REFERENCES tasks(id) ON DELETE CASCADE,
          user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
          assigned_by UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
          assigned_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
          is_primary BOOLEAN DEFAULT false,
          created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
          
          CONSTRAINT task_assignments_unique UNIQUE(task_id, user_id)
      )
    `;
    console.log('✅ task_assignments table created');

    // Step 2: Create sub_tasks table
    console.log('\n2. Creating sub_tasks table...');
    await sql`
      CREATE TABLE IF NOT EXISTS sub_tasks (
          id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
          parent_task_id UUID NOT NULL REFERENCES tasks(id) ON DELETE CASCADE,
          title VARCHAR(255) NOT NULL,
          description TEXT,
          status VARCHAR(20) NOT NULL DEFAULT 'todo' CHECK (status IN ('todo', 'in_progress', 'completed', 'cancelled')),
          assigned_to UUID REFERENCES users(id) ON DELETE SET NULL,
          assigned_by UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
          due_date TIMESTAMP WITH TIME ZONE,
          position INTEGER DEFAULT 0,
          completed_at TIMESTAMP WITH TIME ZONE,
          created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
          updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
      )
    `;
    console.log('✅ sub_tasks table created');

    // Step 3: Update task_attachments table to use user_files
    console.log('\n3. Updating task_attachments table...');
    
    // First check if the current task_attachments table exists and its structure
    const attachmentsExists = await sql`
      SELECT EXISTS (
        SELECT 1 FROM information_schema.tables 
        WHERE table_schema = 'public' AND table_name = 'task_attachments'
      )
    `;
    
    if (attachmentsExists[0].exists) {
      console.log('   Existing task_attachments table found, checking structure...');
      
      const hasFileId = await sql`
        SELECT EXISTS (
          SELECT 1 FROM information_schema.columns 
          WHERE table_schema = 'public' AND table_name = 'task_attachments' AND column_name = 'file_id'
        )
      `;
      
      if (!hasFileId[0].exists) {
        console.log('   Adding file_id column to existing task_attachments...');
        await sql`ALTER TABLE task_attachments ADD COLUMN file_id UUID REFERENCES user_files(id) ON DELETE CASCADE`;
        await sql`ALTER TABLE task_attachments ADD COLUMN attachment_type VARCHAR(50) DEFAULT 'document'`;
        await sql`ALTER TABLE task_attachments ADD COLUMN description TEXT`;
      }
    } else {
      console.log('   Creating new task_attachments table...');
      await sql`
        CREATE TABLE task_attachments (
            id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
            task_id UUID NOT NULL REFERENCES tasks(id) ON DELETE CASCADE,
            file_id UUID NOT NULL REFERENCES user_files(id) ON DELETE CASCADE,
            uploaded_by UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
            attachment_type VARCHAR(50) DEFAULT 'document' CHECK (attachment_type IN ('document', 'image', 'video', 'other')),
            description TEXT,
            created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
            
            CONSTRAINT task_attachments_unique UNIQUE(task_id, file_id)
        )
      `;
    }
    console.log('✅ task_attachments table updated');

    // Step 4: Add new columns to tasks table
    console.log('\n4. Adding new columns to tasks table...');
    
    const tasksColumns = await sql`
      SELECT column_name 
      FROM information_schema.columns 
      WHERE table_schema = 'public' AND table_name = 'tasks'
      AND column_name IN ('actual_hours', 'tags', 'completion_percentage')
    `;
    
    const existingColumns = tasksColumns.map(c => c.column_name);
    
    if (!existingColumns.includes('actual_hours')) {
      await sql`ALTER TABLE tasks ADD COLUMN actual_hours DECIMAL(5,2) DEFAULT 0`;
      console.log('   ✅ Added actual_hours column');
    }
    
    if (!existingColumns.includes('tags')) {
      await sql`ALTER TABLE tasks ADD COLUMN tags TEXT[]`;
      console.log('   ✅ Added tags column');
    }
    
    if (!existingColumns.includes('completion_percentage')) {
      await sql`ALTER TABLE tasks ADD COLUMN completion_percentage INTEGER DEFAULT 0 CHECK (completion_percentage >= 0 AND completion_percentage <= 100)`;
      console.log('   ✅ Added completion_percentage column');
    }

    // Step 5: Create indexes
    console.log('\n5. Creating performance indexes...');
    
    await sql`CREATE INDEX IF NOT EXISTS idx_task_assignments_task_id ON task_assignments(task_id)`;
    await sql`CREATE INDEX IF NOT EXISTS idx_task_assignments_user_id ON task_assignments(user_id)`;
    await sql`CREATE INDEX IF NOT EXISTS idx_task_assignments_primary ON task_assignments(task_id, is_primary) WHERE is_primary = true`;
    
    await sql`CREATE INDEX IF NOT EXISTS idx_sub_tasks_parent_task_id ON sub_tasks(parent_task_id)`;
    await sql`CREATE INDEX IF NOT EXISTS idx_sub_tasks_assigned_to ON sub_tasks(assigned_to)`;
    await sql`CREATE INDEX IF NOT EXISTS idx_sub_tasks_status ON sub_tasks(status)`;
    await sql`CREATE INDEX IF NOT EXISTS idx_sub_tasks_position ON sub_tasks(parent_task_id, position)`;
    
    await sql`CREATE INDEX IF NOT EXISTS idx_task_attachments_task_id ON task_attachments(task_id)`;
    await sql`CREATE INDEX IF NOT EXISTS idx_task_attachments_file_id ON task_attachments(file_id)`;
    
    console.log('✅ Indexes created');

    // Step 6: Create triggers and functions
    console.log('\n6. Creating triggers and functions...');
    
    await sql`
      CREATE OR REPLACE FUNCTION update_updated_at_column()
      RETURNS TRIGGER AS $$
      BEGIN
          NEW.updated_at = NOW();
          RETURN NEW;
      END;
      $$ language 'plpgsql'
    `;
    
    await sql`DROP TRIGGER IF EXISTS update_sub_tasks_updated_at ON sub_tasks`;
    await sql`
      CREATE TRIGGER update_sub_tasks_updated_at 
          BEFORE UPDATE ON sub_tasks 
          FOR EACH ROW EXECUTE FUNCTION update_updated_at_column()
    `;
    
    console.log('✅ Triggers and functions created');

    // Step 7: Verify everything was created
    console.log('\n7. Verifying schema application...');
    
    const newTables = await sql`
      SELECT table_name 
      FROM information_schema.tables 
      WHERE table_schema = 'public' 
      AND table_name IN ('task_assignments', 'sub_tasks', 'task_attachments')
      ORDER BY table_name
    `;
    
    console.log('✅ New tables verified:', newTables.map(t => t.table_name));
    
    const newTaskColumns = await sql`
      SELECT column_name, data_type
      FROM information_schema.columns 
      WHERE table_schema = 'public' AND table_name = 'tasks'
      AND column_name IN ('actual_hours', 'tags', 'completion_percentage')
      ORDER BY column_name
    `;
    
    console.log('✅ New task columns verified:', newTaskColumns.map(c => `${c.column_name} (${c.data_type})`));
    
    console.log('\n🎉 Enhanced task management schema applied successfully!');
    
  } catch (error) {
    console.error('❌ Error applying schema:', error.message);
    console.error('Full error:', error);
    process.exit(1);
  }
}

applySchemaStepByStep();
