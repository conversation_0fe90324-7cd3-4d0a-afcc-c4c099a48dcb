#!/usr/bin/env node

// Test database connection script
let neon, dotenv;

try {
  ({ neon } = require('@neondatabase/serverless'));
  dotenv = require('dotenv');
  dotenv.config({ path: '.env.local' });
} catch (error) {
  console.log('⚠️  Required packages not installed yet. Please run: npm install @neondatabase/serverless dotenv');
  console.log('Error:', error.message);
  process.exit(1);
}

async function testConnection() {
  console.log('🔍 Testing Neon database connection...\n');
  
  // Check if DATABASE_URL is set
  if (!process.env.DATABASE_URL) {
    console.error('❌ ERROR: DATABASE_URL environment variable is not set');
    console.log('📝 Please update your .env.local file with your Neon connection string');
    process.exit(1);
  }
  
  console.log('✅ DATABASE_URL found in environment');
  console.log('🔗 Connection string:', process.env.DATABASE_URL.replace(/:[^:@]*@/, ':****@'));
  
  try {
    const sql = neon(process.env.DATABASE_URL);
    
    // Test basic connection
    console.log('\n🔄 Testing basic connection...');
    const result = await sql`SELECT NOW() as current_time, version() as postgres_version`;
    console.log('✅ Connection successful!');
    console.log('⏰ Server time:', result[0].current_time);
    console.log('🐘 PostgreSQL version:', result[0].postgres_version.split(' ')[0]);
    
    // Check if tables exist
    console.log('\n🔄 Checking database tables...');
    const tables = await sql`
      SELECT table_name 
      FROM information_schema.tables 
      WHERE table_schema = 'public' 
      ORDER BY table_name
    `;
    
    if (tables.length === 0) {
      console.log('⚠️  No tables found - database needs to be initialized');
    } else {
      console.log('✅ Found tables:', tables.map(t => t.table_name).join(', '));
    }
    
    // Check for users table specifically
    const userTable = await sql`
      SELECT column_name, data_type 
      FROM information_schema.columns 
      WHERE table_name = 'users' AND table_schema = 'public'
      ORDER BY ordinal_position
    `;
    
    if (userTable.length === 0) {
      console.log('⚠️  Users table not found - needs to be created');
    } else {
      console.log('✅ Users table exists with columns:', userTable.map(c => c.column_name).join(', '));
    }
    
    console.log('\n🎉 Database connection test completed successfully!');
    
  } catch (error) {
    console.error('\n❌ Database connection failed:');
    console.error('Error:', error.message);
    
    if (error.message.includes('password authentication failed')) {
      console.log('\n💡 Troubleshooting tips:');
      console.log('1. Check your username and password in the connection string');
      console.log('2. Ensure your Neon database is active (not suspended)');
      console.log('3. Verify the connection string is copied correctly');
    } else if (error.message.includes('does not exist')) {
      console.log('\n💡 Troubleshooting tips:');
      console.log('1. Check the database name in your connection string');
      console.log('2. Ensure the database exists in your Neon project');
    } else if (error.message.includes('timeout')) {
      console.log('\n💡 Troubleshooting tips:');
      console.log('1. Check your internet connection');
      console.log('2. Verify the endpoint URL is correct');
      console.log('3. Try again in a few moments');
    }
    
    process.exit(1);
  }
}

testConnection();
