#!/usr/bin/env node

// Database setup script for Neon PostgreSQL
const fs = require('fs');
const path = require('path');

let neon, dotenv;

try {
  ({ neon } = require('@neondatabase/serverless'));
  dotenv = require('dotenv');
  dotenv.config({ path: '.env.local' });
} catch (error) {
  console.log('⚠️  Required packages not installed yet. Please run: npm install @neondatabase/serverless dotenv');
  console.log('Error:', error.message);
  process.exit(1);
}

async function setupDatabase() {
  console.log('🚀 Setting up Neon database for Kanban Board application...\n');
  
  // Check if DATABASE_URL is set
  if (!process.env.DATABASE_URL) {
    console.error('❌ ERROR: DATABASE_URL environment variable is not set');
    console.log('📝 Please update your .env.local file with your Neon connection string');
    process.exit(1);
  }
  
  console.log('✅ DATABASE_URL found in environment');
  
  try {
    const sql = neon(process.env.DATABASE_URL);
    
    // Test connection first
    console.log('🔄 Testing database connection...');
    await sql`SELECT 1`;
    console.log('✅ Database connection successful!\n');
    
    // SQL files to execute in order
    const sqlFiles = [
      '01-create-neon-tables.sql',
      '02-insert-neon-permissions.sql', 
      '03-create-default-admin.sql'
    ];
    
    for (const fileName of sqlFiles) {
      const filePath = path.join(__dirname, fileName);
      
      if (!fs.existsSync(filePath)) {
        console.log(`⚠️  Skipping ${fileName} - file not found`);
        continue;
      }
      
      console.log(`🔄 Executing ${fileName}...`);
      
      try {
        const sqlContent = fs.readFileSync(filePath, 'utf8');
        
        // Split by semicolon and execute each statement
        const statements = sqlContent
          .split(';')
          .map(stmt => stmt.trim())
          .filter(stmt => stmt.length > 0 && !stmt.startsWith('--'));
        
        for (const statement of statements) {
          if (statement.trim()) {
            await sql.unsafe(statement);
          }
        }
        
        console.log(`✅ ${fileName} executed successfully`);
        
      } catch (error) {
        if (error.message.includes('already exists')) {
          console.log(`ℹ️  ${fileName} - Some objects already exist (this is normal)`);
        } else {
          console.error(`❌ Error executing ${fileName}:`, error.message);
          throw error;
        }
      }
    }
    
    // Verify setup
    console.log('\n🔍 Verifying database setup...');
    
    // Check tables
    const tables = await sql`
      SELECT table_name 
      FROM information_schema.tables 
      WHERE table_schema = 'public' 
      ORDER BY table_name
    `;
    console.log('✅ Tables created:', tables.map(t => t.table_name).join(', '));
    
    // Check demo users
    const users = await sql`SELECT email, role FROM users ORDER BY role`;
    console.log('✅ Demo users created:');
    users.forEach(user => {
      console.log(`   - ${user.email} (${user.role})`);
    });
    
    // Check permissions
    const permissions = await sql`SELECT COUNT(*) as count FROM permissions`;
    console.log(`✅ Permissions created: ${permissions[0].count} permissions`);
    
    console.log('\n🎉 Database setup completed successfully!');
    console.log('\n📋 Demo Login Credentials:');
    console.log('   Admin:      <EMAIL> / admin123');
    console.log('   HR Manager: <EMAIL> / admin123');
    console.log('   Manager:    <EMAIL> / admin123');
    console.log('   Staff:      <EMAIL> / admin123');
    
  } catch (error) {
    console.error('\n❌ Database setup failed:');
    console.error('Error:', error.message);
    
    if (error.message.includes('password authentication failed')) {
      console.log('\n💡 Troubleshooting tips:');
      console.log('1. Check your username and password in the DATABASE_URL');
      console.log('2. Ensure your Neon database is active (not suspended)');
      console.log('3. Verify the connection string is copied correctly from Neon console');
    }
    
    process.exit(1);
  }
}

setupDatabase();
