import { NextRequest, NextResponse } from "next/server"
import { serverDb } from "@/lib/server-db"
import { AuthService } from "@/lib/auth-utils"

// DELETE /api/admin/users/[id]/hard-delete - Permanently delete user and all associated data
export async function DELETE(request: NextRequest, { params }: { params: { id: string } }) {
  try {
    const sessionToken = request.cookies.get("session-token")?.value

    if (!sessionToken) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 })
    }

    const currentUser = await AuthService.verifySession(sessionToken)

    // Only admin can perform hard delete
    if (!currentUser || currentUser.role !== "admin") {
      return NextResponse.json({ error: "Forbidden - Admin access required" }, { status: 403 })
    }

    // Prevent admin from deleting themselves
    if (currentUser.id === params.id) {
      return NextResponse.json({ error: "Cannot delete your own account" }, { status: 400 })
    }

    // Get user info before deletion for logging
    const userToDelete = await serverDb.getUserById(params.id)
    if (!userToDelete) {
      return NextResponse.json({ error: "User not found" }, { status: 404 })
    }

    // Perform hard delete
    const success = await serverDb.hardDeleteUser(params.id)

    if (!success) {
      return NextResponse.json({ error: "Failed to delete user" }, { status: 500 })
    }

    // Log the hard delete action
    console.log(`HARD DELETE: Admin ${currentUser.email} permanently deleted user ${userToDelete.email} (${userToDelete.full_name})`)

    return NextResponse.json({ 
      message: `User ${userToDelete.full_name} has been permanently deleted`,
      deletedUser: {
        id: userToDelete.id,
        email: userToDelete.email,
        full_name: userToDelete.full_name
      }
    })
  } catch (error) {
    console.error("Hard delete user API error:", error)
    return NextResponse.json({ 
      error: error instanceof Error ? error.message : "Internal server error" 
    }, { status: 500 })
  }
}
