import { NextRequest, NextResponse } from "next/server"
import { AuthService } from "@/lib/auth-utils"
import { serverDb } from "@/lib/server-db"

// GET /api/users - Get users for dropdowns and selectors
export async function GET(request: NextRequest) {
  try {
    const sessionToken = request.cookies.get("session-token")?.value

    if (!sessionToken) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 })
    }

    const user = await AuthService.verifySession(sessionToken)

    if (!user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 })
    }

    // Parse query parameters
    const { searchParams } = new URL(request.url)
    const search = searchParams.get("search")
    const role = searchParams.get("role")
    const department = searchParams.get("department")
    const is_active = searchParams.get("is_active")

    // Build query conditions
    let whereConditions = ["1=1"]
    let queryParams: any[] = []
    let paramIndex = 1

    // Always filter for active users by default
    const activeFilter = is_active !== null ? is_active === "true" : true
    whereConditions.push(`is_active = $${paramIndex}`)
    queryParams.push(activeFilter)
    paramIndex++

    // Add search filter
    if (search) {
      whereConditions.push(`(full_name ILIKE $${paramIndex} OR email ILIKE $${paramIndex} OR employee_id ILIKE $${paramIndex})`)
      queryParams.push(`%${search}%`)
      paramIndex++
    }

    // Add role filter
    if (role && role !== "all") {
      whereConditions.push(`role = $${paramIndex}`)
      queryParams.push(role)
      paramIndex++
    }

    // Add department filter
    if (department && department !== "all") {
      whereConditions.push(`department = $${paramIndex}`)
      queryParams.push(department)
      paramIndex++
    }

    // Build and execute query using serverDb methods
    let users;

    if (search) {
      // Use search query
      users = await serverDb.sql`
        SELECT
          id,
          email,
          full_name,
          role,
          department,
          position,
          employee_id,
          is_active,
          created_at
        FROM users
        WHERE is_active = ${activeFilter}
        AND (full_name ILIKE ${`%${search}%`} OR email ILIKE ${`%${search}%`} OR employee_id ILIKE ${`%${search}%`})
        ${role && role !== "all" ? serverDb.sql`AND role = ${role}` : serverDb.sql``}
        ${department && department !== "all" ? serverDb.sql`AND department = ${department}` : serverDb.sql``}
        ORDER BY full_name
        LIMIT 100
      `;
    } else {
      // Use simple query
      users = await serverDb.sql`
        SELECT
          id,
          email,
          full_name,
          role,
          department,
          position,
          employee_id,
          is_active,
          created_at
        FROM users
        WHERE is_active = ${activeFilter}
        ${role && role !== "all" ? serverDb.sql`AND role = ${role}` : serverDb.sql``}
        ${department && department !== "all" ? serverDb.sql`AND department = ${department}` : serverDb.sql``}
        ORDER BY full_name
        LIMIT 100
      `;
    }

    return NextResponse.json({
      success: true,
      data: users,
      count: users.length
    })

  } catch (error) {
    console.error("Users API error:", error)
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    )
  }
}
