require('dotenv').config({ path: '.env.local' });
const { neon } = require('@neondatabase/serverless');
const fs = require('fs');
const path = require('path');

async function runPayrollSchemaEnhancement() {
  try {
    const sql = neon(process.env.DATABASE_URL);
    
    console.log('🚀 Starting Payroll Schema Enhancement - Phase 1...\n');
    
    // Step 1: Run schema enhancement
    console.log('📋 Step 1: Creating payroll workflow tables...');
    const schemaScript = fs.readFileSync(
      path.join(__dirname, '01-payroll-schema-enhancement.sql'), 
      'utf8'
    );
    
    // Split by semicolon and execute each statement
    const schemaStatements = schemaScript
      .split(';')
      .map(stmt => stmt.trim())
      .filter(stmt => stmt.length > 0 && !stmt.startsWith('--'));
    
    for (const statement of schemaStatements) {
      if (statement.includes('SELECT') && statement.includes('information_schema')) {
        // This is a verification query, execute and show results
        try {
          const result = await sql([statement]);
          if (result.length > 0) {
            console.log('✅ Tables created:', result.map(r => r.table_name || r.column_name).join(', '));
          }
        } catch (err) {
          // Ignore verification query errors
        }
      } else {
        try {
          await sql([statement]);
        } catch (err) {
          if (!err.message.includes('already exists')) {
            console.warn(`⚠️  Warning in schema statement: ${err.message}`);
          }
        }
      }
    }
    
    console.log('✅ Schema enhancement completed\n');
    
    // Step 2: Apply RLS policies
    console.log('🔒 Step 2: Applying Row Level Security policies...');
    const rlsScript = fs.readFileSync(
      path.join(__dirname, '02-payroll-rls-policies.sql'), 
      'utf8'
    );
    
    const rlsStatements = rlsScript
      .split(';')
      .map(stmt => stmt.trim())
      .filter(stmt => stmt.length > 0 && !stmt.startsWith('--'));
    
    for (const statement of rlsStatements) {
      if (statement.includes('SELECT') && statement.includes('pg_tables')) {
        // This is a verification query
        try {
          const result = await sql([statement]);
          console.log('✅ RLS enabled on tables:', result.map(r => r.tablename).join(', '));
        } catch (err) {
          // Ignore verification query errors
        }
      } else {
        try {
          await sql([statement]);
        } catch (err) {
          if (!err.message.includes('already exists') && !err.message.includes('does not exist')) {
            console.warn(`⚠️  Warning in RLS statement: ${err.message}`);
          }
        }
      }
    }
    
    console.log('✅ RLS policies applied\n');
    
    // Step 3: Insert default data
    console.log('📊 Step 3: Inserting default payroll settings and components...');
    const dataScript = fs.readFileSync(
      path.join(__dirname, '03-payroll-default-data.sql'), 
      'utf8'
    );
    
    const dataStatements = dataScript
      .split(';')
      .map(stmt => stmt.trim())
      .filter(stmt => stmt.length > 0 && !stmt.startsWith('--'));
    
    for (const statement of dataStatements) {
      if (statement.includes('SELECT COUNT') || statement.includes('SELECT name') || statement.includes('SELECT period_name')) {
        // This is a verification query
        try {
          const result = await sql([statement]);
          if (statement.includes('COUNT')) {
            console.log(`✅ Settings inserted: ${result[0]?.settings_count || 0} records`);
          } else if (statement.includes('name, code')) {
            console.log(`✅ Components inserted: ${result.length} components`);
          } else if (statement.includes('period_name')) {
            console.log(`✅ Periods inserted: ${result.length} periods`);
          }
        } catch (err) {
          // Ignore verification query errors
        }
      } else {
        try {
          await sql([statement]);
        } catch (err) {
          if (!err.message.includes('duplicate key') && !err.message.includes('already exists')) {
            console.warn(`⚠️  Warning in data statement: ${err.message}`);
          }
        }
      }
    }
    
    console.log('✅ Default data inserted\n');
    
    // Final verification
    console.log('🔍 Final Verification...');
    
    // Check tables
    const tables = await sql`
      SELECT table_name 
      FROM information_schema.tables 
      WHERE table_name IN (
        'payroll_approvals', 
        'payroll_disbursements', 
        'payroll_audit_log',
        'payroll_components_master',
        'employee_component_assignments',
        'payroll_periods',
        'payroll_settings',
        'monthly_payroll_summary'
      )
      ORDER BY table_name
    `;
    
    console.log('✅ Created tables:', tables.map(t => t.table_name));
    
    // Check RLS status
    const rlsStatus = await sql`
      SELECT tablename, rowsecurity 
      FROM pg_tables 
      WHERE tablename IN ('payroll', 'users', 'attendance', 'payroll_approvals')
      ORDER BY tablename
    `;
    
    console.log('✅ RLS enabled:', rlsStatus.filter(t => t.rowsecurity).map(t => t.tablename));
    
    // Check settings count
    const settingsCount = await sql`SELECT COUNT(*) as count FROM payroll_settings`;
    console.log(`✅ Payroll settings: ${settingsCount[0].count} records`);
    
    // Check components count
    const componentsCount = await sql`SELECT COUNT(*) as count FROM payroll_components_master`;
    console.log(`✅ Payroll components: ${componentsCount[0].count} records`);
    
    console.log('\n🎉 Payroll Schema Enhancement Phase 1 completed successfully!');
    console.log('\n📋 Summary:');
    console.log('   ✅ Database schema enhanced with workflow tables');
    console.log('   ✅ Row Level Security policies implemented');
    console.log('   ✅ Audit logging system created');
    console.log('   ✅ Approval workflow tables created');
    console.log('   ✅ Default settings and components inserted');
    console.log('\n🚀 Ready for Phase 1.2: Core API Enhancement');
    
    return true;
    
  } catch (error) {
    console.error('❌ Payroll schema enhancement failed:', error);
    console.error('Error details:', error.message);
    return false;
  }
}

// Run the enhancement
runPayrollSchemaEnhancement()
  .then(success => {
    if (success) {
      console.log('\n✅ All operations completed successfully');
      process.exit(0);
    } else {
      console.log('\n❌ Some operations failed');
      process.exit(1);
    }
  })
  .catch(error => {
    console.error('❌ Unexpected error:', error);
    process.exit(1);
  });
