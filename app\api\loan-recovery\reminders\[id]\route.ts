import { NextRequest, NextResponse } from "next/server"
import { AuthService } from "@/lib/auth-utils"
import { serverDb } from "@/lib/server-db"
import { z } from "zod"

// Validation schema for reminder updates
const updateReminderSchema = z.object({
  title: z.string().min(1, "Title is required").max(255, "Title too long").optional(),
  reminder_date: z.string().regex(/^\d{4}-\d{2}-\d{2}$/, "Invalid date format").optional(),
  reminder_date_bs: z.string().optional(),
  status: z.enum(["pending", "completed", "cancelled"]).optional(),
})

export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const sessionToken = request.cookies.get("session-token")?.value

    if (!sessionToken) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 })
    }

    const user = await AuthService.verifySession(sessionToken)

    if (!user || !["admin", "hr_manager"].includes(user.role)) {
      return NextResponse.json({ error: "Forbidden" }, { status: 403 })
    }

    const reminderId = params.id
    const body = await request.json()
    const validatedData = updateReminderSchema.parse(body)

    // Check if reminder exists
    const existingReminder = await serverDb.sql`
      SELECT * FROM loan_reminders WHERE id = ${reminderId}
    `

    if (existingReminder.length === 0) {
      return NextResponse.json({ error: "Reminder not found" }, { status: 404 })
    }

    const currentReminder = existingReminder[0]

    // Build update query dynamically
    const updateFields = []
    const updateValues = []

    if (validatedData.title !== undefined) {
      updateFields.push("title = $" + (updateValues.length + 1))
      updateValues.push(validatedData.title)
    }
    if (validatedData.reminder_date !== undefined) {
      updateFields.push("reminder_date = $" + (updateValues.length + 1))
      updateValues.push(validatedData.reminder_date)
    }
    if (validatedData.reminder_date_bs !== undefined) {
      updateFields.push("reminder_date_bs = $" + (updateValues.length + 1))
      updateValues.push(validatedData.reminder_date_bs)
    }
    if (validatedData.status !== undefined) {
      updateFields.push("status = $" + (updateValues.length + 1))
      updateValues.push(validatedData.status)
      
      // If marking as completed, set completed_by and completed_at
      if (validatedData.status === "completed") {
        updateFields.push("completed_by = $" + (updateValues.length + 1))
        updateValues.push(user.id)
        updateFields.push("completed_at = NOW()")
      } else if (validatedData.status === "pending" && currentReminder.status === "completed") {
        // If changing from completed back to pending, clear completion fields
        updateFields.push("completed_by = NULL")
        updateFields.push("completed_at = NULL")
      }
    }

    updateFields.push("updated_at = NOW()")

    if (updateFields.length === 1) { // Only updated_at
      return NextResponse.json({ error: "No fields to update" }, { status: 400 })
    }

    // Construct the update query
    const setClause = updateFields.join(", ")
    const reminder = await serverDb.sql`
      UPDATE loan_reminders 
      SET ${serverDb.sql([setClause], ...updateValues)}
      WHERE id = ${reminderId}
      RETURNING *
    `

    // Get reminder with user details
    const reminderWithDetails = await serverDb.sql`
      SELECT 
        lr.*,
        u.full_name as created_by_name,
        uc.full_name as completed_by_name
      FROM loan_reminders lr
      LEFT JOIN users u ON lr.created_by = u.id
      LEFT JOIN users uc ON lr.completed_by = uc.id
      WHERE lr.id = ${reminderId}
    `

    return NextResponse.json({
      success: true,
      reminder: reminderWithDetails[0],
    })
  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: "Validation error", details: error.errors },
        { status: 400 }
      )
    }

    console.error("Update reminder API error:", error)
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    )
  }
}

export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const sessionToken = request.cookies.get("session-token")?.value

    if (!sessionToken) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 })
    }

    const user = await AuthService.verifySession(sessionToken)

    if (!user || !["admin", "hr_manager"].includes(user.role)) {
      return NextResponse.json({ error: "Forbidden" }, { status: 403 })
    }

    const reminderId = params.id

    // Check if reminder exists
    const existingReminder = await serverDb.sql`
      SELECT id FROM loan_reminders WHERE id = ${reminderId}
    `

    if (existingReminder.length === 0) {
      return NextResponse.json({ error: "Reminder not found" }, { status: 404 })
    }

    // Delete reminder
    await serverDb.sql`
      DELETE FROM loan_reminders WHERE id = ${reminderId}
    `

    return NextResponse.json({
      success: true,
      message: "Reminder deleted successfully",
    })
  } catch (error) {
    console.error("Delete reminder API error:", error)
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    )
  }
}
