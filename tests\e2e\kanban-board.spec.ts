import { test, expect } from '@playwright/test'

// Test configuration
const TEST_USER = {
  email: '<EMAIL>',
  password: 'testpassword123',
  name: 'Test User'
}

const ADMIN_USER = {
  email: '<EMAIL>',
  password: 'admin123',
  name: 'Admin User'
}

test.describe('Kanban Board Task Management', () => {
  test.beforeEach(async ({ page }) => {
    // Navigate to login page
    await page.goto('/auth/login')
    
    // Login as test user
    await page.fill('input[type="email"]', TEST_USER.email)
    await page.fill('input[type="password"]', TEST_USER.password)
    await page.click('button[type="submit"]')
    
    // Wait for redirect to dashboard
    await page.waitForURL('/dashboard')
    
    // Navigate to tasks tab
    await page.click('[data-testid="tasks-tab"]')
    await page.waitForSelector('[data-testid="kanban-board"]')
  })

  test('should display kanban board with three columns', async ({ page }) => {
    // Check that all three kanban columns are present
    await expect(page.locator('[data-testid="kanban-column-todo"]')).toBeVisible()
    await expect(page.locator('[data-testid="kanban-column-inprogress"]')).toBeVisible()
    await expect(page.locator('[data-testid="kanban-column-done"]')).toBeVisible()
    
    // Check column headers
    await expect(page.locator('[data-testid="kanban-column-todo"] h3')).toContainText('To Do')
    await expect(page.locator('[data-testid="kanban-column-inprogress"] h3')).toContainText('In Progress')
    await expect(page.locator('[data-testid="kanban-column-done"] h3')).toContainText('Done')
  })

  test('should create a new task', async ({ page }) => {
    // Click add task button
    await page.click('[data-testid="add-task-button"]')
    
    // Wait for modal to open
    await page.waitForSelector('[data-testid="task-modal"]')
    
    // Fill in task details
    const taskTitle = 'Test Task ' + Date.now()
    const taskDescription = 'This is a test task created by Playwright'
    
    await page.fill('[data-testid="task-title-input"]', taskTitle)
    await page.fill('[data-testid="task-description-input"]', taskDescription)
    
    // Select priority
    await page.click('[data-testid="task-priority-select"]')
    await page.click('[data-testid="priority-option-high"]')
    
    // Set due date
    const futureDate = new Date()
    futureDate.setDate(futureDate.getDate() + 7)
    const dueDateString = futureDate.toISOString().split('T')[0]
    await page.fill('[data-testid="task-due-date-input"]', dueDateString)
    
    // Save task
    await page.click('[data-testid="save-task-button"]')
    
    // Wait for modal to close
    await page.waitForSelector('[data-testid="task-modal"]', { state: 'hidden' })
    
    // Verify task appears in To Do column
    const todoColumn = page.locator('[data-testid="kanban-column-todo"]')
    await expect(todoColumn.locator(`text=${taskTitle}`)).toBeVisible()
    
    // Verify task has high priority indicator
    const taskCard = todoColumn.locator(`[data-testid*="task-card"]`).filter({ hasText: taskTitle })
    await expect(taskCard.locator('[data-testid="priority-badge"]')).toContainText('high')
  })

  test('should drag and drop task between columns', async ({ page }) => {
    // First create a task to drag
    await page.click('[data-testid="add-task-button"]')
    await page.waitForSelector('[data-testid="task-modal"]')
    
    const taskTitle = 'Drag Test Task ' + Date.now()
    await page.fill('[data-testid="task-title-input"]', taskTitle)
    await page.click('[data-testid="save-task-button"]')
    await page.waitForSelector('[data-testid="task-modal"]', { state: 'hidden' })
    
    // Find the task card in To Do column
    const todoColumn = page.locator('[data-testid="kanban-column-todo"]')
    const taskCard = todoColumn.locator(`[data-testid*="task-card"]`).filter({ hasText: taskTitle })
    await expect(taskCard).toBeVisible()
    
    // Get the In Progress column as drop target
    const inProgressColumn = page.locator('[data-testid="kanban-column-inprogress"]')
    
    // Perform drag and drop
    await taskCard.dragTo(inProgressColumn)
    
    // Wait for the task to appear in In Progress column
    await expect(inProgressColumn.locator(`text=${taskTitle}`)).toBeVisible()
    
    // Verify task is no longer in To Do column
    await expect(todoColumn.locator(`text=${taskTitle}`)).not.toBeVisible()
    
    // Verify the task status was updated by checking if it persists after page reload
    await page.reload()
    await page.waitForSelector('[data-testid="kanban-board"]')
    await expect(inProgressColumn.locator(`text=${taskTitle}`)).toBeVisible()
  })

  test('should edit an existing task', async ({ page }) => {
    // Create a task first
    await page.click('[data-testid="add-task-button"]')
    await page.waitForSelector('[data-testid="task-modal"]')
    
    const originalTitle = 'Original Task ' + Date.now()
    await page.fill('[data-testid="task-title-input"]', originalTitle)
    await page.click('[data-testid="save-task-button"]')
    await page.waitForSelector('[data-testid="task-modal"]', { state: 'hidden' })
    
    // Find and click the task to edit it
    const todoColumn = page.locator('[data-testid="kanban-column-todo"]')
    const taskCard = todoColumn.locator(`[data-testid*="task-card"]`).filter({ hasText: originalTitle })
    await taskCard.click()
    
    // Wait for edit modal to open
    await page.waitForSelector('[data-testid="task-modal"]')
    
    // Verify the form is pre-filled with existing data
    await expect(page.locator('[data-testid="task-title-input"]')).toHaveValue(originalTitle)
    
    // Update the task
    const updatedTitle = 'Updated Task ' + Date.now()
    await page.fill('[data-testid="task-title-input"]', updatedTitle)
    await page.fill('[data-testid="task-description-input"]', 'Updated description')
    
    // Change priority
    await page.click('[data-testid="task-priority-select"]')
    await page.click('[data-testid="priority-option-low"]')
    
    // Save changes
    await page.click('[data-testid="save-task-button"]')
    await page.waitForSelector('[data-testid="task-modal"]', { state: 'hidden' })
    
    // Verify the task was updated
    await expect(todoColumn.locator(`text=${updatedTitle}`)).toBeVisible()
    await expect(todoColumn.locator(`text=${originalTitle}`)).not.toBeVisible()
    
    // Verify priority was updated
    const updatedTaskCard = todoColumn.locator(`[data-testid*="task-card"]`).filter({ hasText: updatedTitle })
    await expect(updatedTaskCard.locator('[data-testid="priority-badge"]')).toContainText('low')
  })

  test('should delete a task', async ({ page }) => {
    // Create a task first
    await page.click('[data-testid="add-task-button"]')
    await page.waitForSelector('[data-testid="task-modal"]')
    
    const taskTitle = 'Task to Delete ' + Date.now()
    await page.fill('[data-testid="task-title-input"]', taskTitle)
    await page.click('[data-testid="save-task-button"]')
    await page.waitForSelector('[data-testid="task-modal"]', { state: 'hidden' })
    
    // Find the task card
    const todoColumn = page.locator('[data-testid="kanban-column-todo"]')
    const taskCard = todoColumn.locator(`[data-testid*="task-card"]`).filter({ hasText: taskTitle })
    
    // Click delete button on the task card
    await taskCard.hover()
    await taskCard.locator('[data-testid="delete-task-button"]').click()
    
    // Confirm deletion in the confirmation dialog
    await page.click('[data-testid="confirm-delete-button"]')
    
    // Verify the task is no longer visible
    await expect(todoColumn.locator(`text=${taskTitle}`)).not.toBeVisible()
  })

  test('should filter tasks by status', async ({ page }) => {
    // Create tasks in different statuses
    const tasks = [
      { title: 'Todo Task', status: 'todo' },
      { title: 'Progress Task', status: 'inprogress' },
      { title: 'Done Task', status: 'done' }
    ]
    
    // Create tasks and move them to appropriate columns
    for (const task of tasks) {
      await page.click('[data-testid="add-task-button"]')
      await page.waitForSelector('[data-testid="task-modal"]')
      await page.fill('[data-testid="task-title-input"]', task.title)
      await page.click('[data-testid="save-task-button"]')
      await page.waitForSelector('[data-testid="task-modal"]', { state: 'hidden' })
      
      if (task.status !== 'todo') {
        const taskCard = page.locator(`[data-testid*="task-card"]`).filter({ hasText: task.title })
        const targetColumn = page.locator(`[data-testid="kanban-column-${task.status}"]`)
        await taskCard.dragTo(targetColumn)
      }
    }
    
    // Test filtering by status
    await page.click('[data-testid="filter-button"]')
    await page.click('[data-testid="filter-status-inprogress"]')
    
    // Verify only in-progress tasks are visible
    await expect(page.locator('text=Progress Task')).toBeVisible()
    await expect(page.locator('text=Todo Task')).not.toBeVisible()
    await expect(page.locator('text=Done Task')).not.toBeVisible()
    
    // Clear filter
    await page.click('[data-testid="clear-filters-button"]')
    
    // Verify all tasks are visible again
    await expect(page.locator('text=Progress Task')).toBeVisible()
    await expect(page.locator('text=Todo Task')).toBeVisible()
    await expect(page.locator('text=Done Task')).toBeVisible()
  })

  test('should search for tasks', async ({ page }) => {
    // Create a task with a unique title
    const uniqueTitle = 'Unique Search Task ' + Date.now()
    await page.click('[data-testid="add-task-button"]')
    await page.waitForSelector('[data-testid="task-modal"]')
    await page.fill('[data-testid="task-title-input"]', uniqueTitle)
    await page.click('[data-testid="save-task-button"]')
    await page.waitForSelector('[data-testid="task-modal"]', { state: 'hidden' })
    
    // Use search functionality
    await page.fill('[data-testid="search-input"]', 'Unique Search')
    await page.press('[data-testid="search-input"]', 'Enter')
    
    // Verify only the searched task is visible
    await expect(page.locator(`text=${uniqueTitle}`)).toBeVisible()
    
    // Clear search
    await page.fill('[data-testid="search-input"]', '')
    await page.press('[data-testid="search-input"]', 'Enter')
    
    // Verify all tasks are visible again
    await expect(page.locator(`text=${uniqueTitle}`)).toBeVisible()
  })

  test('should handle task assignment (admin only)', async ({ page }) => {
    // Logout and login as admin
    await page.click('[data-testid="user-menu"]')
    await page.click('[data-testid="logout-button"]')
    
    await page.goto('/auth/login')
    await page.fill('input[type="email"]', ADMIN_USER.email)
    await page.fill('input[type="password"]', ADMIN_USER.password)
    await page.click('button[type="submit"]')
    await page.waitForURL('/dashboard')
    await page.click('[data-testid="tasks-tab"]')
    
    // Create a task and assign it to another user
    await page.click('[data-testid="add-task-button"]')
    await page.waitForSelector('[data-testid="task-modal"]')
    
    const taskTitle = 'Assigned Task ' + Date.now()
    await page.fill('[data-testid="task-title-input"]', taskTitle)
    
    // Select assignee
    await page.click('[data-testid="task-assignee-select"]')
    await page.click('[data-testid="assignee-option"]', { hasText: TEST_USER.name })
    
    await page.click('[data-testid="save-task-button"]')
    await page.waitForSelector('[data-testid="task-modal"]', { state: 'hidden' })
    
    // Verify task shows assigned user
    const taskCard = page.locator(`[data-testid*="task-card"]`).filter({ hasText: taskTitle })
    await expect(taskCard.locator('[data-testid="assignee-avatar"]')).toBeVisible()
  })

  test('should be responsive on mobile devices', async ({ page }) => {
    // Set mobile viewport
    await page.setViewportSize({ width: 375, height: 667 })
    
    // Verify kanban board adapts to mobile layout
    await expect(page.locator('[data-testid="kanban-board"]')).toBeVisible()
    
    // On mobile, columns should stack vertically or be scrollable horizontally
    const columns = page.locator('[data-testid*="kanban-column"]')
    await expect(columns).toHaveCount(3)
    
    // Test creating a task on mobile
    await page.click('[data-testid="add-task-button"]')
    await page.waitForSelector('[data-testid="task-modal"]')
    
    // Modal should be full-screen on mobile
    const modal = page.locator('[data-testid="task-modal"]')
    const modalBox = await modal.boundingBox()
    const viewport = page.viewportSize()
    
    // Modal should take up most of the screen width on mobile
    expect(modalBox?.width).toBeGreaterThan(viewport!.width * 0.8)
  })
})

test.describe('Kanban Board Accessibility', () => {
  test('should be keyboard navigable', async ({ page }) => {
    await page.goto('/auth/login')
    await page.fill('input[type="email"]', TEST_USER.email)
    await page.fill('input[type="password"]', TEST_USER.password)
    await page.click('button[type="submit"]')
    await page.waitForURL('/dashboard')
    await page.click('[data-testid="tasks-tab"]')
    
    // Test keyboard navigation
    await page.keyboard.press('Tab') // Should focus on first interactive element
    await page.keyboard.press('Enter') // Should activate the focused element
    
    // Verify screen reader accessibility
    await expect(page.locator('[data-testid="kanban-board"]')).toHaveAttribute('role', 'main')
    await expect(page.locator('[data-testid="kanban-column-todo"]')).toHaveAttribute('role', 'region')
    await expect(page.locator('[data-testid="kanban-column-todo"]')).toHaveAttribute('aria-label', 'To Do tasks')
  })

  test('should have proper ARIA labels and roles', async ({ page }) => {
    await page.goto('/auth/login')
    await page.fill('input[type="email"]', TEST_USER.email)
    await page.fill('input[type="password"]', TEST_USER.password)
    await page.click('button[type="submit"]')
    await page.waitForURL('/dashboard')
    await page.click('[data-testid="tasks-tab"]')
    
    // Check ARIA attributes
    await expect(page.locator('[data-testid="add-task-button"]')).toHaveAttribute('aria-label', 'Add new task')
    await expect(page.locator('[data-testid="search-input"]')).toHaveAttribute('aria-label', 'Search tasks')
    
    // Check that task cards have proper accessibility attributes
    const taskCards = page.locator('[data-testid*="task-card"]')
    if (await taskCards.count() > 0) {
      await expect(taskCards.first()).toHaveAttribute('role', 'button')
      await expect(taskCards.first()).toHaveAttribute('tabindex', '0')
    }
  })
})
