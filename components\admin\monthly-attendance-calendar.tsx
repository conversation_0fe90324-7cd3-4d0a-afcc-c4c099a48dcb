"use client"

import React, { useState, useEffect } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Input } from "@/components/ui/input"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { 
  ChevronLeft, 
  ChevronRight, 
  Search, 
  Filter, 
  Calendar,
  CalendarDays,
  Users,
  Clock,
  TrendingUp
} from "lucide-react"
import { toast } from "@/hooks/use-toast"
import { cn } from "@/lib/utils"
import {
  getCurrentNepaliDate,
  navigateNepaliMonth,
  getNepaliMonth,
  getNepaliMonthCalendarDays,
  formatNepaliDate,
  gregor<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  type NepaliDate
} from "@/lib/nepali-calendar"

// Helper functions for calendar grid
const getStatusColor = (status: string) => {
  switch (status) {
    case "present":
      return "bg-green-500 text-white border-green-600 shadow-sm"
    case "late":
      return "bg-orange-500 text-white border-orange-600 shadow-sm"
    case "half_day":
      return "bg-blue-500 text-white border-blue-600 shadow-sm"
    case "on_leave":
      return "bg-purple-500 text-white border-purple-600 shadow-sm"
    case "absent":
    default:
      return "bg-red-500 text-white border-red-600 shadow-sm"
  }
}

const getStatusIcon = (status: string) => {
  switch (status) {
    case "present":
      return "✓"
    case "late":
      return "⏰"
    case "half_day":
      return "½"
    case "on_leave":
      return "🏖️"
    case "absent":
    default:
      return "✗"
  }
}

const getDaysInMonth = (date: Date) => {
  const year = date.getFullYear()
  const month = date.getMonth()
  const daysInMonth = new Date(year, month + 1, 0).getDate()
  const firstDayOfWeek = new Date(year, month, 1).getDay()

  const days = []

  // Add empty cells for days before the first day of the month
  for (let i = 0; i < firstDayOfWeek; i++) {
    days.push(null)
  }

  // Add all days of the month
  for (let day = 1; day <= daysInMonth; day++) {
    days.push(day)
  }

  return days
}

const formatDateKey = (date: Date, day: number) => {
  const year = date.getFullYear()
  const month = date.getMonth()
  return new Date(year, month, day).toISOString().split('T')[0]
}

// Calendar Grid Component
interface CalendarGridProps {
  employees: any[]
  currentDate: Date | NepaliDate
  calendarType: "english" | "nepali"
}

function CalendarGrid({ employees, currentDate, calendarType }: CalendarGridProps) {
  const weekDays = ['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat']

  // Get days for the current month based on calendar type
  const days = calendarType === "nepali"
    ? getNepaliMonthCalendarDays((currentDate as NepaliDate).year, (currentDate as NepaliDate).month)
    : getDaysInMonth(currentDate as Date)

  // Organize days into weeks for proper calendar display
  const weeks: (number | null)[][] = []
  let currentWeek: (number | null)[] = []

  days.forEach((day, index) => {
    currentWeek.push(day)

    // If we have 7 days or it's the last day, complete the week
    if (currentWeek.length === 7 || index === days.length - 1) {
      // Pad the last week if necessary
      while (currentWeek.length < 7) {
        currentWeek.push(null)
      }
      weeks.push([...currentWeek])
      currentWeek = []
    }
  })

  return (
    <div className="space-y-6">{/* Employee Rows */}
      {employees.map((employee) => (
        <div key={employee.employee_id} className="border-2 border-border rounded-xl overflow-hidden shadow-lg hover:shadow-xl transition-shadow">
          {/* Two-Column Layout */}
          <div className="grid grid-cols-1 xl:grid-cols-[420px_1fr] min-h-[400px]">

            {/* Left Column - Employee Details & Summary */}
            <div className="bg-gradient-to-br from-muted/20 to-muted/5 p-6 xl:border-r border-border">
              {/* Employee Profile */}
              <div className="flex items-center space-x-4 mb-6">
                <Avatar className="h-16 w-16 ring-2 ring-primary/20">
                  <AvatarImage
                    src={employee.profile_picture_url || undefined}
                    alt={employee.employee_name}
                    onError={(e) => {
                      // Hide broken image and show fallback
                      e.currentTarget.style.display = 'none'
                    }}
                  />
                  <AvatarFallback className="text-lg font-bold bg-primary/10 text-primary">
                    {employee.employee_name.split(' ').map((n: string) => n[0]).join('').toUpperCase()}
                  </AvatarFallback>
                </Avatar>
                <div className="min-w-0 flex-1">
                  <h3 className="text-xl font-bold truncate text-foreground">
                    {employee.employee_name}
                  </h3>
                  <p className="text-sm text-muted-foreground truncate font-medium">
                    {employee.department || 'No Department'}
                  </p>
                  <p className="text-xs text-muted-foreground mt-1">
                    Employee ID: {employee.employee_id}
                  </p>
                </div>
              </div>

              {/* Monthly Summary Statistics */}
              <div className="space-y-3">
                <h4 className="text-sm font-bold text-foreground mb-3">Monthly Summary</h4>
                <div className="grid grid-cols-2 gap-3 text-xs">
                  <div className="bg-gradient-to-br from-green-50 to-green-100 border-2 border-green-200 rounded-lg p-3 shadow-sm">
                    <div className="font-bold text-green-800 mb-1">Present</div>
                    <div className="text-green-700 font-semibold text-sm">{employee.monthly_stats?.total_present_days || 0} days</div>
                  </div>
                  <div className="bg-gradient-to-br from-red-50 to-red-100 border-2 border-red-200 rounded-lg p-3 shadow-sm">
                    <div className="font-bold text-red-800 mb-1">Absent</div>
                    <div className="text-red-700 font-semibold text-sm">{employee.monthly_stats?.total_absent_days || 0} days</div>
                  </div>
                  <div className="bg-gradient-to-br from-blue-50 to-blue-100 border-2 border-blue-200 rounded-lg p-3 shadow-sm">
                    <div className="font-bold text-blue-800 mb-1">Attendance</div>
                    <div className="text-blue-700 font-semibold text-sm">{employee.monthly_stats?.attendance_percentage?.toFixed(1) || 0}%</div>
                  </div>
                  <div className="bg-gradient-to-br from-purple-50 to-purple-100 border-2 border-purple-200 rounded-lg p-3 shadow-sm">
                    <div className="font-bold text-purple-800 mb-1">Total Hours</div>
                    <div className="text-purple-700 font-semibold text-sm">{employee.monthly_stats?.total_hours_worked?.toFixed(1) || 0}h</div>
                  </div>
                  <div className="bg-gradient-to-br from-orange-50 to-orange-100 border-2 border-orange-200 rounded-lg p-3 shadow-sm col-span-2">
                    <div className="font-bold text-orange-800 mb-1">Avg Daily Hours</div>
                    <div className="text-orange-700 font-semibold text-sm">{employee.monthly_stats?.average_daily_hours?.toFixed(1) || 0}h</div>
                  </div>
                </div>
              </div>
            </div>

            {/* Right Column - Calendar Grid */}
            <div className="p-6 bg-background overflow-x-auto">
              {/* Calendar Header */}
              <div className="grid grid-cols-7 gap-2 mb-4 min-w-[500px]">
                {weekDays.map(day => (
                  <div key={day} className="p-2 md:p-3 text-center font-bold text-foreground bg-muted/30 rounded-lg border text-xs md:text-sm">
                    {day}
                  </div>
                ))}
              </div>

              {/* Calendar Weeks */}
              <div className="space-y-2 min-w-[500px]">
                {weeks.map((week, weekIndex) => (
                  <div key={weekIndex} className="grid grid-cols-7 gap-2">
                    {week.map((day, dayIndex) => {
                      if (day === null) {
                        return <div key={dayIndex} className="h-10 md:h-12"></div>
                      }

                      // Generate date key for attendance lookup
                      let dateKey: string
                      if (calendarType === "nepali") {
                        const nepaliDate = currentDate as NepaliDate
                        // For Nepali calendar, we need to convert to Gregorian date for lookup
                        // This is a simplified approach - in production, you'd use proper conversion
                        const gregorianDate = new Date()
                        gregorianDate.setFullYear(nepaliDate.year - 57) // Approximate conversion
                        gregorianDate.setMonth(nepaliDate.month - 1)
                        gregorianDate.setDate(day)
                        dateKey = gregorianDate.toISOString().split('T')[0]
                      } else {
                        dateKey = formatDateKey(currentDate as Date, day)
                      }

                      const dayData = employee.daily_attendance?.[dateKey]
                      const status = dayData?.status || "absent"

                      return (
                        <div key={day} className="h-10 md:h-12">
                          <div
                            className={cn(
                              "w-full h-full rounded-lg flex flex-col items-center justify-center text-xs font-bold border-2 cursor-pointer transition-all hover:scale-105 hover:shadow-md",
                              getStatusColor(status)
                            )}
                            title={`${employee.employee_name} - ${day}: ${status.replace('_', ' ').toUpperCase()}${dayData?.total_hours ? ` (${dayData.total_hours}h)` : ''}`}
                          >
                            <span className="text-xs md:text-sm leading-none font-bold">
                              {getStatusIcon(status)}
                            </span>
                            <span className="text-xs leading-none mt-0.5 font-semibold">
                              {day}
                            </span>
                          </div>
                        </div>
                      )
                    })}
                  </div>
                ))}
              </div>
            </div>
          </div>
        </div>
      ))}
    </div>
  )
}

interface MonthlyAttendanceCalendarProps {
  calendarType?: "english" | "nepali"
  onCalendarTypeChange?: (type: "english" | "nepali") => void
  autoRefresh?: boolean
  refreshInterval?: number
}

interface MonthlyAttendanceData {
  employee_id: string
  employee_name: string
  employee_email: string
  department?: string
  position?: string
  profile_picture_url?: string
  daily_attendance: {
    [date: string]: {
      status: "present" | "absent" | "late" | "half_day" | "on_leave"
      check_in_time?: string
      check_out_time?: string
      total_hours?: number
      sessions_count: number
    }
  }
  monthly_stats: {
    total_present_days: number
    total_absent_days: number
    total_late_days: number
    total_half_days: number
    total_leave_days: number
    attendance_percentage: number
    total_hours_worked: number
    average_daily_hours: number
  }
}

interface MonthlyAttendanceResponse {
  success: boolean
  month: string
  year: number
  calendar_type: string
  total_working_days: number
  employees: MonthlyAttendanceData[]
  overall_stats: {
    total_employees: number
    average_attendance_percentage: number
    total_hours_all_employees: number
    most_present_employee: string
    least_present_employee: string
  }
}

export function MonthlyAttendanceCalendar({
  calendarType = "english",
  onCalendarTypeChange,
  autoRefresh = false,
  refreshInterval = 30000
}: MonthlyAttendanceCalendarProps) {
  const [currentDate, setCurrentDate] = useState(new Date())
  const [currentNepaliDate, setCurrentNepaliDate] = useState<NepaliDate>(getCurrentNepaliDate())
  const [attendanceData, setAttendanceData] = useState<MonthlyAttendanceResponse | null>(null)
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [searchTerm, setSearchTerm] = useState("")
  const [departmentFilter, setDepartmentFilter] = useState("all")
  const [statusFilter, setStatusFilter] = useState("all")

  const departments = attendanceData?.employees 
    ? Array.from(new Set(attendanceData.employees.map(emp => emp.department).filter(Boolean)))
    : []

  const filteredEmployees = attendanceData?.employees?.filter(employee => {
    const searchLower = searchTerm.toLowerCase()
    const matchesSearch = searchTerm === "" ||
      employee.employee_name.toLowerCase().includes(searchLower) ||
      employee.employee_email.toLowerCase().includes(searchLower) ||
      employee.employee_id.toLowerCase().includes(searchLower) ||
      (employee.department && employee.department.toLowerCase().includes(searchLower)) ||
      (employee.position && employee.position.toLowerCase().includes(searchLower))

    const matchesDepartment = departmentFilter === "all" || employee.department === departmentFilter
    const matchesStatus = statusFilter === "all" ||
      (statusFilter === "high" && employee.monthly_stats?.attendance_percentage >= 90) ||
      (statusFilter === "medium" && employee.monthly_stats?.attendance_percentage >= 70 && employee.monthly_stats?.attendance_percentage < 90) ||
      (statusFilter === "low" && employee.monthly_stats?.attendance_percentage < 70)

    return matchesSearch && matchesDepartment && matchesStatus
  }) || []

  useEffect(() => {
    fetchMonthlyAttendance()
  }, [currentDate, currentNepaliDate, calendarType])

  useEffect(() => {
    if (autoRefresh) {
      const interval = setInterval(() => {
        fetchMonthlyAttendance()
      }, refreshInterval)
      return () => clearInterval(interval)
    }
  }, [autoRefresh, refreshInterval, currentDate, currentNepaliDate, calendarType])

  // Sync calendar dates when calendar type changes
  useEffect(() => {
    if (calendarType === "nepali") {
      setCurrentNepaliDate(gregorianToNepali(currentDate))
    } else {
      // When switching to English, keep the current date
      setCurrentDate(new Date())
    }
  }, [calendarType])

  const fetchMonthlyAttendance = async () => {
    setLoading(true)
    setError(null)
    try {
      let month: number
      let year: number

      if (calendarType === "nepali") {
        month = currentNepaliDate.month
        year = currentNepaliDate.year
      } else {
        month = currentDate.getMonth() + 1
        year = currentDate.getFullYear()
      }

      const response = await fetch(`/api/admin/attendance/monthly?month=${month}&year=${year}&calendarType=${calendarType}`, {
        credentials: "include",
      })

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`)
      }

      const data = await response.json()

      if (data.success) {
        setAttendanceData(data)
        setError(null)
      } else {
        const errorMessage = data.error || "Failed to fetch monthly attendance data"
        setError(errorMessage)
        toast({
          title: "Error",
          description: errorMessage,
          variant: "destructive",
        })
      }
    } catch (error) {
      console.error("Error fetching monthly attendance:", error)
      const errorMessage = error instanceof Error ? error.message : "Failed to fetch monthly attendance data"
      setError(errorMessage)
      toast({
        title: "Error",
        description: "Failed to fetch monthly attendance data. Please try again.",
        variant: "destructive",
      })
    } finally {
      setLoading(false)
    }
  }

  const navigateMonth = (direction: "prev" | "next") => {
    if (calendarType === "nepali") {
      const monthsToAdd = direction === "prev" ? -1 : 1
      const newNepaliDate = navigateNepaliMonth(currentNepaliDate, monthsToAdd)
      setCurrentNepaliDate(newNepaliDate)
    } else {
      const newDate = new Date(currentDate)
      if (direction === "prev") {
        newDate.setMonth(newDate.getMonth() - 1)
      } else {
        newDate.setMonth(newDate.getMonth() + 1)
      }
      setCurrentDate(newDate)
    }
  }

  if (loading) {
    return (
      <div className="space-y-6">
        <Card>
          <CardContent className="flex flex-col items-center justify-center h-64 space-y-4">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-teal-600"></div>
            <div className="text-center">
              <p className="text-lg font-medium text-foreground">Loading Monthly Attendance</p>
              <p className="text-sm text-muted-foreground">
                Fetching attendance data for {calendarType === "nepali"
                  ? formatNepaliDate(currentNepaliDate)
                  : `${new Date(currentDate).toLocaleString('default', { month: 'long' })} ${currentDate.getFullYear()}`
                }
              </p>
            </div>
          </CardContent>
        </Card>
      </div>
    )
  }

  if (error) {
    return (
      <div className="space-y-6">
        <Card>
          <CardContent className="flex flex-col items-center justify-center h-64 space-y-4">
            <div className="text-red-500">
              <CalendarDays className="h-12 w-12" />
            </div>
            <div className="text-center">
              <p className="text-lg font-medium text-foreground">Failed to Load Attendance Data</p>
              <p className="text-sm text-muted-foreground mb-4">{error}</p>
              <Button onClick={fetchMonthlyAttendance} variant="outline">
                Try Again
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      <div className="flex flex-col space-y-4 lg:flex-row lg:items-center lg:justify-between lg:space-y-0">
        <div className="flex items-center space-x-4">
          <Button
            onClick={() => navigateMonth("prev")}
            variant="outline"
            size="icon"
          >
            <ChevronLeft className="h-4 w-4" />
          </Button>
          
          <div className="text-center">
            <h2 className="text-xl font-semibold">
              {calendarType === "nepali"
                ? formatNepaliDate(currentNepaliDate)
                : `${attendanceData?.month || new Date(currentDate).toLocaleString('default', { month: 'long' })} ${attendanceData?.year || currentDate.getFullYear()}`
              }
            </h2>
            <p className="text-sm text-muted-foreground">
              {calendarType === "english" ? "Gregorian Calendar" : "Bikram Sambat Calendar"}
            </p>
            {calendarType === "nepali" && (
              <p className="text-xs text-muted-foreground">
                {new Date(currentDate).toLocaleString('default', { month: 'long', year: 'numeric' })} AD
              </p>
            )}
          </div>
          
          <Button
            onClick={() => navigateMonth("next")}
            variant="outline"
            size="icon"
          >
            <ChevronRight className="h-4 w-4" />
          </Button>
        </div>

        <div className="flex items-center space-x-2">
          <Button
            onClick={() => onCalendarTypeChange?.("english")}
            variant={calendarType === "english" ? "default" : "outline"}
            size="sm"
          >
            <Calendar className="mr-2 h-4 w-4" />
            English
          </Button>
          <Button
            onClick={() => onCalendarTypeChange?.("nepali")}
            variant={calendarType === "nepali" ? "default" : "outline"}
            size="sm"
          >
            <CalendarDays className="mr-2 h-4 w-4" />
            Nepali
          </Button>
        </div>
      </div>

      {attendanceData && (
        <div className="grid gap-4 md:grid-cols-4">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Total Employees</CardTitle>
              <Users className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{attendanceData.overall_stats?.total_employees || 0}</div>
            </CardContent>
          </Card>
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Avg Attendance</CardTitle>
              <TrendingUp className="h-4 w-4 text-green-600" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-green-600">
                {attendanceData.overall_stats?.average_attendance_percentage?.toFixed(1) || 0}%
              </div>
            </CardContent>
          </Card>
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Total Hours</CardTitle>
              <Clock className="h-4 w-4 text-blue-600" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-blue-600">
                {attendanceData.overall_stats?.total_hours_all_employees?.toFixed(1) || 0}h
              </div>
            </CardContent>
          </Card>
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Working Days</CardTitle>
              <Calendar className="h-4 w-4 text-purple-600" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-purple-600">
                {attendanceData.total_working_days || 0}
              </div>
            </CardContent>
          </Card>
        </div>
      )}

      {/* Status Legend */}
      <Card className="mb-6">
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Calendar className="h-5 w-5" />
            Attendance Status Legend
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-2 md:grid-cols-5 gap-3">
            <div className="flex items-center gap-2">
              <div className="w-6 h-6 rounded-md bg-green-500 text-white flex items-center justify-center text-xs font-bold">✓</div>
              <span className="text-sm font-medium">Present</span>
            </div>
            <div className="flex items-center gap-2">
              <div className="w-6 h-6 rounded-md bg-red-500 text-white flex items-center justify-center text-xs font-bold">✗</div>
              <span className="text-sm font-medium">Absent</span>
            </div>
            <div className="flex items-center gap-2">
              <div className="w-6 h-6 rounded-md bg-orange-500 text-white flex items-center justify-center text-xs font-bold">⏰</div>
              <span className="text-sm font-medium">Late</span>
            </div>
            <div className="flex items-center gap-2">
              <div className="w-6 h-6 rounded-md bg-blue-500 text-white flex items-center justify-center text-xs font-bold">½</div>
              <span className="text-sm font-medium">Half Day</span>
            </div>
            <div className="flex items-center gap-2">
              <div className="w-6 h-6 rounded-md bg-purple-500 text-white flex items-center justify-center text-xs font-bold">🏖️</div>
              <span className="text-sm font-medium">On Leave</span>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Search and Filter Controls */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Search className="h-5 w-5" />
            Search & Filter Employees
          </CardTitle>
          <CardDescription>
            Search by name, employee ID, email, department, or position
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="flex flex-col lg:flex-row gap-4">
            {/* Search Input */}
            <div className="relative flex-1">
              <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
              <Input
                placeholder="Search employees by name, ID, email, department, or position..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-8"
              />
              {searchTerm && (
                <Button
                  variant="ghost"
                  size="sm"
                  className="absolute right-1 top-1 h-6 w-6 p-0"
                  onClick={() => setSearchTerm("")}
                >
                  ✕
                </Button>
              )}
            </div>

            {/* Department Filter */}
            <Select value={departmentFilter} onValueChange={setDepartmentFilter}>
              <SelectTrigger className="w-full lg:w-48">
                <SelectValue placeholder="Department" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Departments</SelectItem>
                {departments.map(dept => (
                  <SelectItem key={dept} value={dept}>{dept}</SelectItem>
                ))}
              </SelectContent>
            </Select>

            {/* Status Filter */}
            <Select value={statusFilter} onValueChange={setStatusFilter}>
              <SelectTrigger className="w-full lg:w-48">
                <SelectValue placeholder="Attendance" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Attendance</SelectItem>
                <SelectItem value="high">High (&ge;90%)</SelectItem>
                <SelectItem value="medium">Medium (70-89%)</SelectItem>
                <SelectItem value="low">Low (&lt;70%)</SelectItem>
              </SelectContent>
            </Select>
          </div>

          {/* Search Results Count */}
          <div className="mt-4 flex items-center justify-between text-sm text-muted-foreground">
            <span>
              Showing {filteredEmployees.length} of {attendanceData?.employees?.length || 0} employees
              {searchTerm && ` matching "${searchTerm}"`}
            </span>
            {(searchTerm || departmentFilter !== "all" || statusFilter !== "all") && (
              <Button
                variant="ghost"
                size="sm"
                onClick={() => {
                  setSearchTerm("")
                  setDepartmentFilter("all")
                  setStatusFilter("all")
                }}
                className="text-xs"
              >
                Clear all filters
              </Button>
            )}
          </div>
        </CardContent>
      </Card>

      {/* Monthly Calendar Grid */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <CalendarDays className="h-5 w-5" />
            Monthly Attendance Overview
          </CardTitle>
          <CardDescription>
            Employee attendance status for each day of {attendanceData?.month} {attendanceData?.year}
            <br />
            <span className="text-xs text-muted-foreground mt-1 block">
              Scroll horizontally to view all days • Hover over cells for detailed information
            </span>
          </CardDescription>
        </CardHeader>
        <CardContent className="p-6">
          {filteredEmployees.length === 0 ? (
            <div className="text-center py-12">
              <Search className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
              <p className="text-muted-foreground text-lg font-medium">
                {attendanceData?.employees?.length === 0
                  ? "No employees found for this month"
                  : "No employees match your search criteria"
                }
              </p>
              <p className="text-muted-foreground text-sm mt-2 mb-4">
                {attendanceData?.employees?.length === 0
                  ? "There are no active employees with attendance data for this period."
                  : `Try adjusting your search term "${searchTerm}" or filter settings.`
                }
              </p>
              {(searchTerm || departmentFilter !== "all" || statusFilter !== "all") && (
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => {
                    setSearchTerm("")
                    setDepartmentFilter("all")
                    setStatusFilter("all")
                  }}
                >
                  Clear all filters
                </Button>
              )}
            </div>
          ) : (
            <div className="space-y-8">
              <div className="text-sm text-muted-foreground bg-muted/30 p-4 rounded-lg border">
                <p className="font-medium mb-2">📅 Calendar Layout Guide:</p>
                <ul className="space-y-1 text-xs">
                  <li>• <strong>Left Panel:</strong> Employee details and monthly statistics</li>
                  <li>• <strong>Right Panel:</strong> Daily attendance calendar with proper weekly structure</li>
                  <li>• <strong>Calendar Grid:</strong> Each row represents one week (Sun-Sat)</li>
                  <li>• <strong>Date Numbers:</strong> Actual calendar dates (1-31) aligned under correct weekdays</li>
                </ul>
              </div>

              <CalendarGrid
                employees={filteredEmployees}
                currentDate={calendarType === "nepali" ? currentNepaliDate : currentDate}
                calendarType={calendarType}
              />
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  )
}
