// Employee Payroll Self-Service Interface
// Phase 4: User Interface Development - Employee Self-Service

"use client"

import React, { useState, useEffect } from 'react'
import {
  DollarSign,
  Calendar,
  Download,
  Eye,
  TrendingUp,
  TrendingDown,
  FileText,
  Clock,
  CheckCircle,
  AlertCircle,
  User,
  CreditCard,
  <PERSON><PERSON>hart
} from 'lucide-react'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { CurrencyDisplay, CurrencyCard } from '@/components/ui/currency-display'
import { NepaliDateRangePicker } from '@/components/ui/nepali-date-range-picker'
import { fiscalYearManager } from '@/lib/fiscal-year-manager'
import { NepaliCalendar } from '@/lib/nepali-calendar'
import { useAuth } from "@/components/auth-provider"
import { AppHeader } from "@/components/app-header"

interface PayrollRecord {
  id: string
  payPeriod: string
  bsPayPeriod: string
  startDate: string
  endDate: string
  baseSalary: number
  overtime: number
  allowances: number
  bonuses: number
  grossPay: number
  deductions: number
  taxes: number
  netPay: number
  status: 'processed' | 'pending' | 'paid'
  payDate: string
  workingDays: number
  attendedDays: number
  overtimeHours: number
}

interface PayrollSummary {
  currentMonth: {
    grossPay: number
    netPay: number
    deductions: number
    status: string
  }
  yearToDate: {
    totalGross: number
    totalNet: number
    totalDeductions: number
    totalTax: number
    averageMonthly: number
  }
  trends: {
    monthlyChange: number
    yearlyGrowth: number
  }
}

export default function PayrollPage() {
  const { user } = useAuth()
  const [payrollHistory, setPayrollHistory] = useState<PayrollRecord[]>([])
  const [payrollSummary, setPayrollSummary] = useState<PayrollSummary | null>(null)
  const [selectedPeriod, setSelectedPeriod] = useState({
    from: new Date(new Date().getFullYear(), 0, 1),
    to: new Date()
  })
  const [selectedYear, setSelectedYear] = useState(new Date().getFullYear())
  const [loading, setLoading] = useState(true)
  const [activeTab, setActiveTab] = useState('overview')

  useEffect(() => {
    loadPayrollData()
  }, [selectedPeriod, selectedYear])

  const loadPayrollData = async () => {
    setLoading(true)
    try {
      // Mock data - in production, this would come from API
      const mockHistory: PayrollRecord[] = [
        {
          id: '1',
          payPeriod: 'November 2024',
          bsPayPeriod: 'Kartik 2081',
          startDate: '2024-10-17',
          endDate: '2024-11-15',
          baseSalary: 65000,
          overtime: 8000,
          allowances: 5000,
          bonuses: 3000,
          grossPay: 81000,
          deductions: 12000,
          taxes: 3000,
          netPay: 66000,
          status: 'paid',
          payDate: '2024-11-16',
          workingDays: 26,
          attendedDays: 25,
          overtimeHours: 12
        },
        {
          id: '2',
          payPeriod: 'October 2024',
          bsPayPeriod: 'Ashwin 2081',
          startDate: '2024-09-17',
          endDate: '2024-10-16',
          baseSalary: 65000,
          overtime: 6000,
          allowances: 5000,
          bonuses: 2000,
          grossPay: 78000,
          deductions: 12000,
          taxes: 2800,
          netPay: 63200,
          status: 'paid',
          payDate: '2024-10-17',
          workingDays: 26,
          attendedDays: 26,
          overtimeHours: 9
        },
        {
          id: '3',
          payPeriod: 'September 2024',
          bsPayPeriod: 'Bhadra 2081',
          startDate: '2024-08-17',
          endDate: '2024-09-16',
          baseSalary: 65000,
          overtime: 4000,
          allowances: 5000,
          bonuses: 1000,
          grossPay: 75000,
          deductions: 12000,
          taxes: 2500,
          netPay: 60500,
          status: 'paid',
          payDate: '2024-09-17',
          workingDays: 26,
          attendedDays: 24,
          overtimeHours: 6
        }
      ]

      const mockSummary: PayrollSummary = {
        currentMonth: {
          grossPay: 81000,
          netPay: 66000,
          deductions: 15000,
          status: 'paid'
        },
        yearToDate: {
          totalGross: 780000,
          totalNet: 630000,
          totalDeductions: 120000,
          totalTax: 30000,
          averageMonthly: 65000
        },
        trends: {
          monthlyChange: 4.4,
          yearlyGrowth: 8.2
        }
      }

      setPayrollHistory(mockHistory)
      setPayrollSummary(mockSummary)
    } catch (error) {
      console.error('Error loading payroll data:', error)
    } finally {
      setLoading(false)
    }
  }

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'paid':
        return <CheckCircle className="h-4 w-4 text-green-600" />
      case 'processed':
        return <Clock className="h-4 w-4 text-blue-600" />
      case 'pending':
        return <AlertCircle className="h-4 w-4 text-yellow-600" />
      default:
        return <Clock className="h-4 w-4 text-gray-600" />
    }
  }

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'paid':
        return <Badge variant="default">Paid</Badge>
      case 'processed':
        return <Badge variant="secondary">Processed</Badge>
      case 'pending':
        return <Badge variant="outline">Pending</Badge>
      default:
        return <Badge variant="outline">Unknown</Badge>
    }
  }

  const handleDownloadPayslip = (recordId: string) => {
    // In production, this would download the actual payslip
    console.log('Downloading payslip for record:', recordId)
  }

  const handleViewPayslip = (recordId: string) => {
    // In production, this would open the payslip viewer
    console.log('Viewing payslip for record:', recordId)
  }

  if (loading) {
    return (
      <div className="min-h-screen bg-background">
        <AppHeader />
        <div className="container mx-auto px-4 py-6 pb-24 md:pb-6">
          <div className="flex items-center justify-center h-64">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-background">
      <AppHeader />
      <div className="container mx-auto px-4 py-6 pb-24 md:pb-6 space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold">My Payroll</h1>
            <p className="text-muted-foreground">
              View your payroll history, payslips, and earnings summary
            </p>
          </div>

          <div className="flex items-center gap-4">
            <Select value={selectedYear.toString()} onValueChange={(value) => setSelectedYear(parseInt(value))}>
              <SelectTrigger className="w-32">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                {Array.from({ length: 5 }, (_, i) => new Date().getFullYear() - i).map(year => (
                  <SelectItem key={year} value={year.toString()}>
                    {year}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
        </div>

        {/* Summary Cards */}
        {payrollSummary && (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            <CurrencyCard
              title="Current Month"
              amount={payrollSummary.currentMonth.netPay}
              subtitle="Net pay"
              trend={{ value: payrollSummary.trends.monthlyChange, period: "last month" }}
              icon={<DollarSign className="h-4 w-4" />}
              variant="success"
            />

            <CurrencyCard
              title="Year to Date"
              amount={payrollSummary.yearToDate.totalNet}
              subtitle="Total net earnings"
              trend={{ value: payrollSummary.trends.yearlyGrowth, period: "this year" }}
              icon={<TrendingUp className="h-4 w-4" />}
              variant="default"
            />

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Average Monthly</CardTitle>
                <PieChart className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">
                  <CurrencyDisplay amount={payrollSummary.yearToDate.averageMonthly} />
                </div>
                <p className="text-xs text-muted-foreground">
                  Based on YTD earnings
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Total Deductions</CardTitle>
                <CreditCard className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">
                  <CurrencyDisplay amount={payrollSummary.yearToDate.totalDeductions} />
                </div>
                <p className="text-xs text-muted-foreground">
                  Including taxes and contributions
                </p>
              </CardContent>
            </Card>
          </div>
        )}

        {/* Main Content Tabs */}
        <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-4">
          <TabsList className="grid w-full grid-cols-3">
            <TabsTrigger value="overview">Overview</TabsTrigger>
            <TabsTrigger value="history">Payroll History</TabsTrigger>
            <TabsTrigger value="summary">Annual Summary</TabsTrigger>
          </TabsList>

          <TabsContent value="overview" className="space-y-6">
            {/* Current Month Details */}
            {payrollHistory.length > 0 && (
              <Card>
                <CardHeader>
                  <CardTitle>Current Month Details - {payrollHistory[0].bsPayPeriod}</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                    <div className="space-y-2">
                      <div className="text-sm font-medium text-muted-foreground">Gross Pay</div>
                      <div className="text-2xl font-bold">
                        <CurrencyDisplay amount={payrollHistory[0].grossPay} />
                      </div>
                    </div>
                    <div className="space-y-2">
                      <div className="text-sm font-medium text-muted-foreground">Deductions</div>
                      <div className="text-2xl font-bold text-red-600">
                        <CurrencyDisplay amount={payrollHistory[0].deductions + payrollHistory[0].taxes} />
                      </div>
                    </div>
                    <div className="space-y-2">
                      <div className="text-sm font-medium text-muted-foreground">Net Pay</div>
                      <div className="text-2xl font-bold text-green-600">
                        <CurrencyDisplay amount={payrollHistory[0].netPay} />
                      </div>
                    </div>
                    <div className="space-y-2">
                      <div className="text-sm font-medium text-muted-foreground">Status</div>
                      <div className="flex items-center gap-2">
                        {getStatusIcon(payrollHistory[0].status)}
                        {getStatusBadge(payrollHistory[0].status)}
                      </div>
                    </div>
                  </div>

                  <div className="mt-6 pt-6 border-t">
                    <div className="grid grid-cols-1 md:grid-cols-3 gap-6 text-sm">
                      <div>
                        <div className="font-medium text-muted-foreground">Attendance</div>
                        <div>{payrollHistory[0].attendedDays} / {payrollHistory[0].workingDays} days</div>
                      </div>
                      <div>
                        <div className="font-medium text-muted-foreground">Overtime</div>
                        <div>{payrollHistory[0].overtimeHours} hours</div>
                      </div>
                      <div>
                        <div className="font-medium text-muted-foreground">Pay Date</div>
                        <div>{new Date(payrollHistory[0].payDate).toLocaleDateString()}</div>
                      </div>
                    </div>
                  </div>

                  <div className="mt-6 flex gap-4">
                    <Button onClick={() => handleViewPayslip(payrollHistory[0].id)}>
                      <Eye className="h-4 w-4 mr-2" />
                      View Payslip
                    </Button>
                    <Button variant="outline" onClick={() => handleDownloadPayslip(payrollHistory[0].id)}>
                      <Download className="h-4 w-4 mr-2" />
                      Download
                    </Button>
                  </div>
                </CardContent>
              </Card>
            )}
          </TabsContent>

          <TabsContent value="history" className="space-y-6">
            <Card>
              <CardHeader>
                <div className="flex items-center justify-between">
                  <CardTitle>Payroll History</CardTitle>
                  <NepaliDateRangePicker
                    value={selectedPeriod}
                    onChange={(range) => range && setSelectedPeriod(range)}
                    showPresets={true}
                    showFiscalYearPresets={true}
                  />
                </div>
              </CardHeader>
              <CardContent>
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Pay Period</TableHead>
                      <TableHead>Gross Pay</TableHead>
                      <TableHead>Deductions</TableHead>
                      <TableHead>Net Pay</TableHead>
                      <TableHead>Status</TableHead>
                      <TableHead>Actions</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {payrollHistory.map((record) => (
                      <TableRow key={record.id}>
                        <TableCell>
                          <div>
                            <div className="font-medium">{record.payPeriod}</div>
                            <div className="text-sm text-muted-foreground">{record.bsPayPeriod}</div>
                          </div>
                        </TableCell>
                        <TableCell>
                          <CurrencyDisplay amount={record.grossPay} size="sm" />
                        </TableCell>
                        <TableCell>
                          <CurrencyDisplay amount={record.deductions + record.taxes} size="sm" />
                        </TableCell>
                        <TableCell className="font-semibold">
                          <CurrencyDisplay amount={record.netPay} size="sm" />
                        </TableCell>
                        <TableCell>
                          <div className="flex items-center gap-2">
                            {getStatusIcon(record.status)}
                            {getStatusBadge(record.status)}
                          </div>
                        </TableCell>
                        <TableCell>
                          <div className="flex items-center gap-2">
                            <Button size="sm" variant="outline" onClick={() => handleViewPayslip(record.id)}>
                              <Eye className="h-4 w-4" />
                            </Button>
                            <Button size="sm" variant="outline" onClick={() => handleDownloadPayslip(record.id)}>
                              <Download className="h-4 w-4" />
                            </Button>
                          </div>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="summary" className="space-y-6">
            {payrollSummary && (
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                <Card>
                  <CardHeader>
                    <CardTitle>Year-to-Date Summary</CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <div className="flex justify-between items-center">
                      <span>Total Gross Earnings:</span>
                      <span className="font-semibold">
                        <CurrencyDisplay amount={payrollSummary.yearToDate.totalGross} />
                      </span>
                    </div>
                    <div className="flex justify-between items-center">
                      <span>Total Deductions:</span>
                      <span className="font-semibold text-red-600">
                        <CurrencyDisplay amount={payrollSummary.yearToDate.totalDeductions} />
                      </span>
                    </div>
                    <div className="flex justify-between items-center">
                      <span>Total Tax:</span>
                      <span className="font-semibold text-red-600">
                        <CurrencyDisplay amount={payrollSummary.yearToDate.totalTax} />
                      </span>
                    </div>
                    <div className="flex justify-between items-center pt-2 border-t">
                      <span className="font-medium">Total Net Earnings:</span>
                      <span className="font-bold text-green-600">
                        <CurrencyDisplay amount={payrollSummary.yearToDate.totalNet} />
                      </span>
                    </div>
                  </CardContent>
                </Card>

                <Card>
                  <CardHeader>
                    <CardTitle>Performance Trends</CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <div className="flex justify-between items-center">
                      <span>Monthly Growth:</span>
                      <div className="flex items-center gap-1">
                        {payrollSummary.trends.monthlyChange >= 0 ? (
                          <TrendingUp className="h-4 w-4 text-green-600" />
                        ) : (
                          <TrendingDown className="h-4 w-4 text-red-600" />
                        )}
                        <span className={payrollSummary.trends.monthlyChange >= 0 ? "text-green-600" : "text-red-600"}>
                          {payrollSummary.trends.monthlyChange >= 0 ? '+' : ''}{payrollSummary.trends.monthlyChange}%
                        </span>
                      </div>
                    </div>
                    <div className="flex justify-between items-center">
                      <span>Yearly Growth:</span>
                      <div className="flex items-center gap-1">
                        {payrollSummary.trends.yearlyGrowth >= 0 ? (
                          <TrendingUp className="h-4 w-4 text-green-600" />
                        ) : (
                          <TrendingDown className="h-4 w-4 text-red-600" />
                        )}
                        <span className={payrollSummary.trends.yearlyGrowth >= 0 ? "text-green-600" : "text-red-600"}>
                          {payrollSummary.trends.yearlyGrowth >= 0 ? '+' : ''}{payrollSummary.trends.yearlyGrowth}%
                        </span>
                      </div>
                    </div>
                    <div className="flex justify-between items-center">
                      <span>Average Monthly:</span>
                      <span className="font-semibold">
                        <CurrencyDisplay amount={payrollSummary.yearToDate.averageMonthly} />
                      </span>
                    </div>
                  </CardContent>
                </Card>
              </div>
            )}
          </TabsContent>
        </Tabs>
      </div>
    </div>
  )
}
