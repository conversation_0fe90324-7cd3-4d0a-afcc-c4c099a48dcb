import { type NextRequest, NextResponse } from "next/server"
import { AuthService } from "@/lib/auth-utils"
import { db } from "@/lib/neon"
import { ATTENDANCE_ERRORS, logError, getUserFriendlyError } from "@/lib/error-handling"

export async function POST(request: NextRequest) {
  try {
    const sessionToken = request.cookies.get("session-token")?.value

    if (!sessionToken) {
      return NextResponse.json({
        success: false,
        error: ATTENDANCE_ERRORS.UNAUTHORIZED
      }, { status: 401 })
    }

    const user = await AuthService.verifySession(sessionToken)

    if (!user) {
      return NextResponse.json({
        success: false,
        error: ATTENDANCE_ERRORS.SESSION_EXPIRED
      }, { status: 401 })
    }

    const body = await request.json().catch(() => ({}))
    const { notes } = body

    // Validate notes length if provided
    if (notes && typeof notes === 'string' && notes.length > 500) {
      return NextResponse.json({
        success: false,
        error: "Notes cannot exceed 500 characters"
      }, { status: 400 })
    }

    const attendance = await db.clockOut(user.id, notes)

    return NextResponse.json({
      success: true,
      data: attendance,
      message: "Successfully clocked out",
    })
  } catch (error) {
    logError(error, "Clock out API")

    const userFriendlyMessage = getUserFriendlyError(error)
    const statusCode = error?.message?.includes("Must clock in") ||
                      error?.message?.includes("Already clocked out") ? 400 : 500

    return NextResponse.json({
      success: false,
      error: userFriendlyMessage
    }, { status: statusCode })
  }
}
