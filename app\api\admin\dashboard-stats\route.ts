import { type NextRequest, NextResponse } from "next/server"
import { AuthService } from "@/lib/auth-utils"
import { db } from "@/lib/neon"

export async function GET(request: NextRequest) {
  try {
    const sessionToken = request.cookies.get("session-token")?.value

    if (!sessionToken) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 })
    }

    const user = await AuthService.verifySession(sessionToken)

    if (!user || !["admin", "hr_manager", "manager"].includes(user.role)) {
      return NextResponse.json({ error: "Forbidden" }, { status: 403 })
    }

    const [todayAttendance, taskStats, pendingPayroll, userStats] = await Promise.all([
      db.getTodayAttendance(),
      db.getTaskStats(),
      db.getPendingPayrollCount(),
      db.getUserStats(),
    ])

    return NextResponse.json({
      todayAttendance,
      activeTasks: taskStats.active,
      completedTasks: taskStats.completed,
      pendingPayroll,
      totalUsers: userStats.total,
      activeUsers: userStats.active,
    })
  } catch (error) {
    console.error("Dashboard stats API error:", error)
    return NextResponse.json({ error: "Internal server error" }, { status: 500 })
  }
}
