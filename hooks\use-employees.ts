import { useQuery } from "@tanstack/react-query"

interface Employee {
  id: string
  email: string
  full_name: string
  role: string
  department?: string
  position?: string
  employee_id?: string
  is_active: boolean
}

interface EmployeesResponse {
  success: boolean
  users: Employee[]
}

// API functions
const employeeApi = {
  async getEmployees(filters: { search?: string; department?: string; role?: string; is_active?: boolean } = {}) {
    const params = new URLSearchParams()
    
    if (filters.search) params.append("search", filters.search)
    if (filters.department && filters.department !== "all") params.append("department", filters.department)
    if (filters.role && filters.role !== "all") params.append("role", filters.role)
    if (filters.is_active !== undefined) params.append("is_active", filters.is_active.toString())

    const response = await fetch(`/api/admin/users?${params}`, {
      credentials: "include",
    })

    if (!response.ok) {
      throw new Error("Failed to fetch employees")
    }

    return response.json() as Promise<EmployeesResponse>
  },

  async getActiveEmployees() {
    return this.getEmployees({ is_active: true })
  },
}

// React Query hooks
export function useEmployees(filters: { search?: string; department?: string; role?: string; is_active?: boolean } = {}) {
  return useQuery({
    queryKey: ["employees", filters],
    queryFn: () => employeeApi.getEmployees(filters),
    staleTime: 5 * 60 * 1000, // 5 minutes
  })
}

export function useActiveEmployees() {
  return useQuery({
    queryKey: ["employees", { is_active: true }],
    queryFn: () => employeeApi.getActiveEmployees(),
    staleTime: 5 * 60 * 1000, // 5 minutes
  })
}

// Helper function to format employee display name
export function formatEmployeeDisplayName(employee: Employee): string {
  if (employee.full_name) {
    return employee.employee_id 
      ? `${employee.full_name} (${employee.employee_id})`
      : employee.full_name
  }
  return employee.email
}

// Helper function to get employee initials for avatars
export function getEmployeeInitials(employee: Employee): string {
  if (employee.full_name) {
    return employee.full_name
      .split(' ')
      .map(name => name.charAt(0))
      .join('')
      .toUpperCase()
      .slice(0, 2)
  }
  return employee.email.charAt(0).toUpperCase()
}
