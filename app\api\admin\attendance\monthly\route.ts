import { type NextRequest, NextResponse } from "next/server"
import { AuthService } from "@/lib/auth-utils"
import { serverDb } from "@/lib/server-db"
import {
  getNepaliMonthDateRange,
  getNepaliMonth,
  NEPALI_MONTHS,
  type NepaliDate
} from "@/lib/nepali-calendar"

interface MonthlyAttendanceData {
  employee_id: string
  employee_name: string
  employee_email: string
  department?: string
  position?: string
  profile_picture_url?: string | null
  daily_attendance: {
    [date: string]: {
      status: "present" | "absent" | "late" | "half_day" | "on_leave"
      check_in_time?: string
      check_out_time?: string
      total_hours?: number
      sessions_count: number
    }
  }
  monthly_stats: {
    total_present_days: number
    total_absent_days: number
    total_late_days: number
    total_half_days: number
    total_leave_days: number
    attendance_percentage: number
    total_hours_worked: number
    average_daily_hours: number
  }
}

interface MonthlyAttendanceResponse {
  success: boolean
  month: string
  year: number
  calendar_type: string
  total_working_days: number
  employees: MonthlyAttendanceData[]
  overall_stats: {
    total_employees: number
    average_attendance_percentage: number
    total_hours_all_employees: number
    most_present_employee: string
    least_present_employee: string
  }
}

export async function GET(request: NextRequest) {
  try {
    const sessionToken = request.cookies.get("session-token")?.value

    if (!sessionToken) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 })
    }

    const user = await AuthService.verifySession(sessionToken)

    if (!user || !["admin", "hr_manager"].includes(user.role)) {
      return NextResponse.json({ error: "Forbidden" }, { status: 403 })
    }

    const { searchParams } = new URL(request.url)
    const month = searchParams.get("month") || (new Date().getMonth() + 1).toString()
    const year = searchParams.get("year") || new Date().getFullYear().toString()
    const calendarType = searchParams.get("calendarType") || "english"

    // Validate month and year
    const monthNum = parseInt(month)
    const yearNum = parseInt(year)

    if (monthNum < 1 || monthNum > 12 || yearNum < 2020 || yearNum > 2030) {
      return NextResponse.json({ error: "Invalid month or year" }, { status: 400 })
    }

    // Calculate date range for the month based on calendar type
    let startDate: Date
    let endDate: Date
    let displayMonth: string
    let displayYear: number

    if (calendarType === "nepali") {
      // For Nepali calendar, convert Nepali month/year to Gregorian date range
      const nepaliDateRange = getNepaliMonthDateRange(yearNum, monthNum)
      startDate = nepaliDateRange.startDate
      endDate = nepaliDateRange.endDate

      const nepaliMonth = getNepaliMonth(monthNum)
      displayMonth = nepaliMonth.nameEn
      displayYear = yearNum
    } else {
      // For English calendar, use standard Gregorian dates
      startDate = new Date(yearNum, monthNum - 1, 1)
      endDate = new Date(yearNum, monthNum, 0) // Last day of the month

      displayMonth = new Date(yearNum, monthNum - 1, 1).toLocaleString('default', { month: 'long' })
      displayYear = yearNum
    }
    const totalDaysInMonth = endDate.getDate()

    // Get all active employees with profile pictures
    const employees = await serverDb.sql`
      SELECT
        u.id,
        u.email,
        u.full_name,
        u.department,
        u.position,
        u.is_active,
        uf.id as profile_picture_id
      FROM users u
      LEFT JOIN user_files uf ON u.id = uf.user_id
        AND uf.file_type = 'profile_picture'
        AND uf.is_active = true
      WHERE u.role IN ('employee', 'manager', 'hr_manager', 'admin')
      AND u.is_active = true
      ORDER BY u.full_name
    `

    // Get attendance data for the month
    const attendanceData = await serverDb.sql`
      SELECT
        a.user_id,
        a.date,
        a.check_in_time,
        a.check_out_time,
        a.status,
        COUNT(*) OVER (PARTITION BY a.user_id, a.date) as sessions_count
      FROM attendance a
      WHERE a.date >= ${startDate.toISOString().split('T')[0]}
      AND a.date <= ${endDate.toISOString().split('T')[0]}
      ORDER BY a.user_id, a.date, a.created_at
    `

    // Process data for each employee
    const employeesData: MonthlyAttendanceData[] = employees.map(employee => {
      const userAttendance = attendanceData.filter(att => att.user_id === employee.id)
      
      // Group by date
      const dailyAttendance: { [date: string]: any } = {}
      let totalPresentDays = 0
      let totalAbsentDays = 0
      let totalLateDays = 0
      let totalHalfDays = 0
      let totalLeaveDays = 0
      let totalHoursWorked = 0

      // Initialize all days of the month
      for (let day = 1; day <= totalDaysInMonth; day++) {
        const dateStr = new Date(yearNum, monthNum - 1, day).toISOString().split('T')[0]
        dailyAttendance[dateStr] = {
          status: "absent" as const,
          sessions_count: 0
        }
      }

      // Process actual attendance data
      userAttendance.forEach(att => {
        const dateStr = att.date
        if (!dailyAttendance[dateStr]) {
          dailyAttendance[dateStr] = {
            status: "absent" as const,
            sessions_count: 0
          }
        }

        dailyAttendance[dateStr] = {
          status: att.status,
          check_in_time: att.check_in_time,
          check_out_time: att.check_out_time,
          total_hours: 0, // Hours calculation will be added later
          sessions_count: att.sessions_count || 1
        }

        // Count statistics
        switch (att.status) {
          case "present":
            totalPresentDays++
            break
          case "late":
            totalLateDays++
            break
          case "half_day":
            totalHalfDays++
            break
          case "on_leave":
            totalLeaveDays++
            break
          default:
            totalAbsentDays++
        }

        // Hours calculation will be implemented later
        // For now, we'll use a basic estimation based on check-in/check-out times
        if (att.check_in_time && att.check_out_time) {
          const checkIn = new Date(att.check_in_time)
          const checkOut = new Date(att.check_out_time)
          const diffMs = checkOut.getTime() - checkIn.getTime()
          const hours = diffMs / (1000 * 60 * 60)
          if (hours > 0 && hours <= 24) { // Sanity check
            totalHoursWorked += hours
          }
        }
      })

      // Calculate actual working days (exclude weekends if needed)
      const workingDays = totalDaysInMonth // Simplified - could exclude weekends
      const attendedDays = totalPresentDays + totalLateDays + totalHalfDays
      const attendancePercentage = workingDays > 0 ? (attendedDays / workingDays) * 100 : 0

      return {
        employee_id: employee.id,
        employee_name: employee.full_name,
        employee_email: employee.email,
        department: employee.department,
        position: employee.position,
        profile_picture_url: employee.profile_picture_id ? `/api/admin/files/${employee.profile_picture_id}` : null,
        daily_attendance: dailyAttendance,
        monthly_stats: {
          total_present_days: totalPresentDays,
          total_absent_days: totalAbsentDays,
          total_late_days: totalLateDays,
          total_half_days: totalHalfDays,
          total_leave_days: totalLeaveDays,
          attendance_percentage: Math.round(attendancePercentage * 100) / 100,
          total_hours_worked: Math.round(totalHoursWorked * 100) / 100,
          average_daily_hours: attendedDays > 0 ? Math.round((totalHoursWorked / attendedDays) * 100) / 100 : 0
        }
      }
    })

    // Calculate overall statistics
    const totalEmployees = employeesData.length
    const averageAttendancePercentage = totalEmployees > 0 
      ? employeesData.reduce((sum, emp) => sum + emp.monthly_stats.attendance_percentage, 0) / totalEmployees 
      : 0
    const totalHoursAllEmployees = employeesData.reduce((sum, emp) => sum + emp.monthly_stats.total_hours_worked, 0)
    
    // Find most and least present employees
    const sortedByAttendance = [...employeesData].sort((a, b) => b.monthly_stats.attendance_percentage - a.monthly_stats.attendance_percentage)
    const mostPresentEmployee = sortedByAttendance[0]?.employee_name || "N/A"
    const leastPresentEmployee = sortedByAttendance[sortedByAttendance.length - 1]?.employee_name || "N/A"

    const response: MonthlyAttendanceResponse = {
      success: true,
      month: displayMonth,
      year: displayYear,
      calendar_type: calendarType,
      total_working_days: totalDaysInMonth,
      employees: employeesData,
      overall_stats: {
        total_employees: totalEmployees,
        average_attendance_percentage: Math.round(averageAttendancePercentage * 100) / 100,
        total_hours_all_employees: Math.round(totalHoursAllEmployees * 100) / 100,
        most_present_employee: mostPresentEmployee,
        least_present_employee: leastPresentEmployee
      }
    }

    return NextResponse.json(response)

  } catch (error) {
    console.error("Error fetching monthly attendance:", error)
    return NextResponse.json(
      { error: "Failed to fetch monthly attendance data" },
      { status: 500 }
    )
  }
}
