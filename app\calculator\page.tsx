"use client"

import { useState } from "react"
import { <PERSON>ppHeader } from "@/components/app-header"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { <PERSON><PERSON>, <PERSON>bsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Slider } from "@/components/ui/slider"
import { CalculatorIcon as CalcIcon, PiggyBank, Landmark, CreditCard, LineChart, Save } from "lucide-react"

export default function CalculatorPage() {
  const [amount, setAmount] = useState(100000)
  const [interestRate, setInterestRate] = useState(5)
  const [tenure, setTenure] = useState(5)
  const [calculatedAmount, setCalculatedAmount] = useState(0)
  const [monthlyInstallment, setMonthlyInstallment] = useState(0)

  const calculateFD = () => {
    // Simple FD calculation: P(1 + r/100)^t
    const principal = amount
    const rate = interestRate / 100
    const time = tenure

    const maturityAmount = principal * Math.pow(1 + rate, time)
    setCalculatedAmount(Math.round(maturityAmount))
  }

  const calculateEMI = () => {
    // EMI calculation: P * r * (1+r)^n / ((1+r)^n - 1)
    const principal = amount
    const rate = interestRate / 100 / 12 // monthly interest rate
    const time = tenure * 12 // tenure in months

    const emi = (principal * rate * Math.pow(1 + rate, time)) / (Math.pow(1 + rate, time) - 1)
    setMonthlyInstallment(Math.round(emi))
  }

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900 flex flex-col">
      <AppHeader title="Financial Calculators" />

      <div className="p-4">
        <Tabs defaultValue="fd" className="w-full">
          <TabsList className="grid w-full grid-cols-4 mb-4">
            <TabsTrigger value="fd">
              <PiggyBank className="h-4 w-4 mr-1 md:mr-2" />
              <span className="hidden md:inline">Fixed Deposit</span>
              <span className="md:hidden">FD</span>
            </TabsTrigger>
            <TabsTrigger value="loan">
              <Landmark className="h-4 w-4 mr-1 md:mr-2" />
              <span className="hidden md:inline">Loan</span>
              <span className="md:hidden">Loan</span>
            </TabsTrigger>
            <TabsTrigger value="sip">
              <LineChart className="h-4 w-4 mr-1 md:mr-2" />
              <span className="hidden md:inline">SIP</span>
              <span className="md:hidden">SIP</span>
            </TabsTrigger>
            <TabsTrigger value="rd">
              <CreditCard className="h-4 w-4 mr-1 md:mr-2" />
              <span className="hidden md:inline">Recurring Deposit</span>
              <span className="md:hidden">RD</span>
            </TabsTrigger>
          </TabsList>

          <TabsContent value="fd">
            <Card className="dark:bg-gray-800">
              <CardHeader>
                <CardTitle className="flex items-center">
                  <PiggyBank className="h-5 w-5 mr-2 text-exobank-green dark:text-green-400" />
                  Fixed Deposit Calculator
                </CardTitle>
                <CardDescription className="dark:text-gray-400">
                  Calculate the maturity amount for your fixed deposit investment.
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-2">
                  <div className="flex justify-between">
                    <Label htmlFor="amount" className="dark:text-gray-300">
                      Principal Amount (₹)
                    </Label>
                    <span className="text-sm font-medium dark:text-gray-300">₹{amount.toLocaleString()}</span>
                  </div>
                  <Slider
                    id="amount"
                    min={1000}
                    max={1000000}
                    step={1000}
                    value={[amount]}
                    onValueChange={(value) => setAmount(value[0])}
                  />
                  <Input
                    type="number"
                    value={amount}
                    onChange={(e) => setAmount(Number(e.target.value))}
                    className="mt-2 dark:bg-gray-700 dark:border-gray-600"
                  />
                </div>

                <div className="space-y-2">
                  <div className="flex justify-between">
                    <Label htmlFor="interest" className="dark:text-gray-300">
                      Interest Rate (%)
                    </Label>
                    <span className="text-sm font-medium dark:text-gray-300">{interestRate}%</span>
                  </div>
                  <Slider
                    id="interest"
                    min={1}
                    max={10}
                    step={0.1}
                    value={[interestRate]}
                    onValueChange={(value) => setInterestRate(value[0])}
                  />
                  <Input
                    type="number"
                    value={interestRate}
                    onChange={(e) => setInterestRate(Number(e.target.value))}
                    step="0.1"
                    className="mt-2 dark:bg-gray-700 dark:border-gray-600"
                  />
                </div>

                <div className="space-y-2">
                  <div className="flex justify-between">
                    <Label htmlFor="tenure" className="dark:text-gray-300">
                      Tenure (Years)
                    </Label>
                    <span className="text-sm font-medium dark:text-gray-300">{tenure} years</span>
                  </div>
                  <Slider
                    id="tenure"
                    min={1}
                    max={20}
                    step={1}
                    value={[tenure]}
                    onValueChange={(value) => setTenure(value[0])}
                  />
                  <Input
                    type="number"
                    value={tenure}
                    onChange={(e) => setTenure(Number(e.target.value))}
                    className="mt-2 dark:bg-gray-700 dark:border-gray-600"
                  />
                </div>
              </CardContent>
              <CardFooter className="flex-col space-y-4">
                <Button
                  onClick={calculateFD}
                  className="w-full bg-exobank-green hover:bg-exobank-green/90 text-white dark:bg-green-700 dark:hover:bg-green-600"
                >
                  <CalcIcon className="h-4 w-4 mr-2" />
                  Calculate
                </Button>

                {calculatedAmount > 0 && (
                  <div className="w-full p-4 bg-gray-50 dark:bg-gray-700 rounded-lg border border-gray-200 dark:border-gray-600">
                    <div className="text-center">
                      <h3 className="text-sm font-medium text-gray-500 dark:text-gray-400 mb-1">Maturity Amount</h3>
                      <p className="text-2xl font-bold text-exobank-green dark:text-green-400">
                        ₹{calculatedAmount.toLocaleString()}
                      </p>
                    </div>
                    <div className="flex justify-between mt-3 text-sm">
                      <div>
                        <p className="text-gray-500 dark:text-gray-400">Principal</p>
                        <p className="font-medium dark:text-gray-300">₹{amount.toLocaleString()}</p>
                      </div>
                      <div>
                        <p className="text-gray-500 dark:text-gray-400">Interest Earned</p>
                        <p className="font-medium dark:text-gray-300">
                          ₹{(calculatedAmount - amount).toLocaleString()}
                        </p>
                      </div>
                      <div>
                        <p className="text-gray-500 dark:text-gray-400">Interest Rate</p>
                        <p className="font-medium dark:text-gray-300">{interestRate}%</p>
                      </div>
                    </div>
                  </div>
                )}

                <Button variant="outline" size="sm" className="w-full dark:border-gray-600 dark:text-gray-300">
                  <Save className="h-4 w-4 mr-2" />
                  Save Calculation
                </Button>
              </CardFooter>
            </Card>
          </TabsContent>

          <TabsContent value="loan">
            <Card className="dark:bg-gray-800">
              <CardHeader>
                <CardTitle className="flex items-center">
                  <Landmark className="h-5 w-5 mr-2 text-exobank-green dark:text-green-400" />
                  Loan EMI Calculator
                </CardTitle>
                <CardDescription className="dark:text-gray-400">
                  Calculate your monthly EMI for different loan amounts and tenures.
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-2">
                  <div className="flex justify-between">
                    <Label htmlFor="loan-amount" className="dark:text-gray-300">
                      Loan Amount (₹)
                    </Label>
                    <span className="text-sm font-medium dark:text-gray-300">₹{amount.toLocaleString()}</span>
                  </div>
                  <Slider
                    id="loan-amount"
                    min={10000}
                    max={********}
                    step={10000}
                    value={[amount]}
                    onValueChange={(value) => setAmount(value[0])}
                  />
                  <Input
                    type="number"
                    value={amount}
                    onChange={(e) => setAmount(Number(e.target.value))}
                    className="mt-2 dark:bg-gray-700 dark:border-gray-600"
                  />
                </div>

                <div className="space-y-2">
                  <div className="flex justify-between">
                    <Label htmlFor="loan-interest" className="dark:text-gray-300">
                      Interest Rate (%)
                    </Label>
                    <span className="text-sm font-medium dark:text-gray-300">{interestRate}%</span>
                  </div>
                  <Slider
                    id="loan-interest"
                    min={5}
                    max={20}
                    step={0.1}
                    value={[interestRate]}
                    onValueChange={(value) => setInterestRate(value[0])}
                  />
                  <Input
                    type="number"
                    value={interestRate}
                    onChange={(e) => setInterestRate(Number(e.target.value))}
                    step="0.1"
                    className="mt-2 dark:bg-gray-700 dark:border-gray-600"
                  />
                </div>

                <div className="space-y-2">
                  <div className="flex justify-between">
                    <Label htmlFor="loan-tenure" className="dark:text-gray-300">
                      Tenure (Years)
                    </Label>
                    <span className="text-sm font-medium dark:text-gray-300">{tenure} years</span>
                  </div>
                  <Slider
                    id="loan-tenure"
                    min={1}
                    max={30}
                    step={1}
                    value={[tenure]}
                    onValueChange={(value) => setTenure(value[0])}
                  />
                  <Input
                    type="number"
                    value={tenure}
                    onChange={(e) => setTenure(Number(e.target.value))}
                    className="mt-2 dark:bg-gray-700 dark:border-gray-600"
                  />
                </div>
              </CardContent>
              <CardFooter className="flex-col space-y-4">
                <Button
                  onClick={calculateEMI}
                  className="w-full bg-exobank-green hover:bg-exobank-green/90 text-white dark:bg-green-700 dark:hover:bg-green-600"
                >
                  <CalcIcon className="h-4 w-4 mr-2" />
                  Calculate EMI
                </Button>

                {monthlyInstallment > 0 && (
                  <div className="w-full p-4 bg-gray-50 dark:bg-gray-700 rounded-lg border border-gray-200 dark:border-gray-600">
                    <div className="text-center">
                      <h3 className="text-sm font-medium text-gray-500 dark:text-gray-400 mb-1">Monthly EMI</h3>
                      <p className="text-2xl font-bold text-exobank-green dark:text-green-400">
                        ₹{monthlyInstallment.toLocaleString()}
                      </p>
                    </div>
                    <div className="flex justify-between mt-3 text-sm">
                      <div>
                        <p className="text-gray-500 dark:text-gray-400">Principal</p>
                        <p className="font-medium dark:text-gray-300">₹{amount.toLocaleString()}</p>
                      </div>
                      <div>
                        <p className="text-gray-500 dark:text-gray-400">Total Interest</p>
                        <p className="font-medium dark:text-gray-300">
                          ₹{(monthlyInstallment * tenure * 12 - amount).toLocaleString()}
                        </p>
                      </div>
                      <div>
                        <p className="text-gray-500 dark:text-gray-400">Total Amount</p>
                        <p className="font-medium dark:text-gray-300">
                          ₹{(monthlyInstallment * tenure * 12).toLocaleString()}
                        </p>
                      </div>
                    </div>
                  </div>
                )}

                <Button variant="outline" size="sm" className="w-full dark:border-gray-600 dark:text-gray-300">
                  <Save className="h-4 w-4 mr-2" />
                  Save Calculation
                </Button>
              </CardFooter>
            </Card>
          </TabsContent>

          <TabsContent value="sip">
            <Card className="dark:bg-gray-800">
              <CardHeader>
                <CardTitle className="flex items-center">
                  <LineChart className="h-5 w-5 mr-2 text-exobank-green dark:text-green-400" />
                  SIP Calculator
                </CardTitle>
                <CardDescription className="dark:text-gray-400">
                  Calculate returns on your Systematic Investment Plan.
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="p-8 flex items-center justify-center">
                  <p className="text-gray-500 dark:text-gray-400 text-center">SIP calculator coming soon</p>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="rd">
            <Card className="dark:bg-gray-800">
              <CardHeader>
                <CardTitle className="flex items-center">
                  <CreditCard className="h-5 w-5 mr-2 text-exobank-green dark:text-green-400" />
                  Recurring Deposit Calculator
                </CardTitle>
                <CardDescription className="dark:text-gray-400">
                  Calculate the maturity amount for your recurring deposit.
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="p-8 flex items-center justify-center">
                  <p className="text-gray-500 dark:text-gray-400 text-center">RD calculator coming soon</p>
                </div>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </div>
    </div>
  )
}
