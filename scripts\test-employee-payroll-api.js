// Test script to debug the employee payroll API endpoint
const fetch = require('node-fetch');

async function testEmployeePayrollAPI() {
  try {
    console.log('Testing Employee Payroll API...\n');

    // First, get the list of active employees
    console.log('1. Getting active employees...');
    const employeesResponse = await fetch('http://localhost:3000/api/admin/users?is_active=true', {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
        // Note: In a real test, you'd need to include session cookies
      }
    });

    if (!employeesResponse.ok) {
      console.log('❌ Failed to fetch employees:', employeesResponse.status, employeesResponse.statusText);
      return;
    }

    const employeesData = await employeesResponse.json();
    console.log('✅ Employees response:', JSON.stringify(employeesData, null, 2));

    if (employeesData.success && employeesData.users && employeesData.users.length > 0) {
      const firstEmployee = employeesData.users[0];
      console.log(`\n2. Testing payroll profile for employee: ${firstEmployee.full_name} (ID: ${firstEmployee.id})`);

      // Test the payroll profile endpoint
      const profileResponse = await fetch(`http://localhost:3000/api/admin/employees/payroll-profile?employeeId=${firstEmployee.id}`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        }
      });

      console.log('Profile response status:', profileResponse.status);
      
      if (!profileResponse.ok) {
        const errorText = await profileResponse.text();
        console.log('❌ Failed to fetch payroll profile:', errorText);
        return;
      }

      const profileData = await profileResponse.json();
      console.log('✅ Payroll profile response:', JSON.stringify(profileData, null, 2));

      // Check if the response has the expected structure
      if (profileData.success && profileData.data) {
        const profile = profileData.data;
        console.log('\n3. Profile data analysis:');
        console.log('- Employee ID:', profile.id);
        console.log('- Full Name:', profile.full_name);
        console.log('- Position:', profile.position);
        console.log('- Salary:', profile.salary);
        console.log('- Department:', profile.department);
        console.log('- Allowances count:', profile.allowances ? profile.allowances.length : 'undefined');
        console.log('- Deductions count:', profile.deductions ? profile.deductions.length : 'undefined');
        console.log('- Bank accounts count:', profile.bank_accounts ? profile.bank_accounts.length : 'undefined');
      } else {
        console.log('❌ Unexpected response structure');
      }
    } else {
      console.log('❌ No employees found or invalid response structure');
    }

  } catch (error) {
    console.error('❌ Error testing API:', error.message);
  }
}

// Run the test
testEmployeePayrollAPI();
