#!/usr/bin/env node

require('dotenv').config({ path: '.env.local' });
const { neon } = require('@neondatabase/serverless');

async function testAPIConsistency() {
  console.log('🎯 TESTING API CONSISTENCY AFTER CONSOLIDATION');
  console.log('===============================================');
  
  if (!process.env.DATABASE_URL) {
    console.error('❌ DATABASE_URL environment variable is not set');
    process.exit(1);
  }
  
  try {
    const sql = neon(process.env.DATABASE_URL);
    
    // Test connection
    console.log('🔄 Testing database connection...');
    await sql`SELECT 1`;
    console.log('✅ Database connection successful');
    
    // Test 1: User Management API queries
    console.log('\n👥 USER MANAGEMENT API TESTS:');
    
    // Query that would be used by /api/admin/users
    const allUsers = await sql`
      SELECT id, email, full_name, role, department, is_active, created_at
      FROM users 
      ORDER BY created_at DESC
    `;
    console.log(`   Total users in system: ${allUsers.length}`);
    
    // Query that would be used by user dropdown/selectors
    const activeUsers = await sql`
      SELECT id, full_name, email, role
      FROM users 
      WHERE is_active = true
      ORDER BY full_name
    `;
    console.log(`   Active users for dropdowns: ${activeUsers.length}`);
    
    // Query for admin dashboard stats
    const userStats = await sql`
      SELECT 
        COUNT(*) as total_users,
        COUNT(CASE WHEN is_active = true THEN 1 END) as active_users,
        COUNT(CASE WHEN role = 'admin' THEN 1 END) as admin_users,
        COUNT(CASE WHEN role = 'staff' THEN 1 END) as staff_users
      FROM users
    `;
    console.log(`   Dashboard stats: ${userStats[0].total_users} total, ${userStats[0].active_users} active`);
    
    // Test 2: Attendance API queries
    console.log('\n📅 ATTENDANCE API TESTS:');
    
    // Query for today's attendance
    const todayAttendance = await sql`
      SELECT 
        a.*, 
        u.full_name, 
        u.email
      FROM attendance a
      JOIN users u ON a.user_id = u.id
      WHERE DATE(a.check_in_time) = CURRENT_DATE
      ORDER BY a.check_in_time DESC
    `;
    console.log(`   Today's attendance entries: ${todayAttendance.length}`);
    
    // Query for attendance overview (last 7 days)
    const recentAttendance = await sql`
      SELECT 
        DATE(a.check_in_time) as date,
        COUNT(*) as entries,
        COUNT(DISTINCT a.user_id) as unique_users
      FROM attendance a
      WHERE a.check_in_time >= CURRENT_DATE - INTERVAL '7 days'
      GROUP BY DATE(a.check_in_time)
      ORDER BY date DESC
    `;
    console.log(`   Recent attendance days: ${recentAttendance.length}`);
    recentAttendance.forEach(day => {
      console.log(`     ${day.date}: ${day.entries} entries, ${day.unique_users} users`);
    });
    
    // Query for user attendance history
    const usersWithAttendance = await sql`
      SELECT 
        u.id,
        u.full_name,
        COUNT(a.id) as attendance_count
      FROM users u
      LEFT JOIN attendance a ON u.id = a.user_id
      WHERE u.is_active = true
      GROUP BY u.id, u.full_name
      ORDER BY attendance_count DESC
    `;
    console.log(`   Users with attendance data: ${usersWithAttendance.filter(u => u.attendance_count > 0).length}`);
    
    // Test 3: Admin Dashboard queries
    console.log('\n📊 ADMIN DASHBOARD TESTS:');
    
    // Query for dashboard stats
    const dashboardStats = await sql`
      SELECT 
        (SELECT COUNT(*) FROM users WHERE is_active = true) as active_employees,
        (SELECT COUNT(*) FROM attendance WHERE DATE(check_in_time) = CURRENT_DATE) as today_attendance,
        (SELECT COUNT(DISTINCT user_id) FROM attendance WHERE DATE(check_in_time) = CURRENT_DATE) as today_unique_users,
        (SELECT COUNT(*) FROM tasks WHERE status != 'completed') as pending_tasks
    `;
    
    const stats = dashboardStats[0];
    console.log(`   Active employees: ${stats.active_employees}`);
    console.log(`   Today's attendance: ${stats.today_attendance}`);
    console.log(`   Today's unique users: ${stats.today_unique_users}`);
    console.log(`   Pending tasks: ${stats.pending_tasks}`);
    
    // Test 4: Payroll queries
    console.log('\n💰 PAYROLL API TESTS:');
    
    // Query for payroll-eligible employees
    const payrollEmployees = await sql`
      SELECT 
        id, 
        full_name, 
        email, 
        role, 
        department,
        salary
      FROM users 
      WHERE is_active = true 
      AND salary IS NOT NULL
      ORDER BY full_name
    `;
    console.log(`   Payroll-eligible employees: ${payrollEmployees.length}`);
    
    // Test 5: Sample user data
    console.log('\n📋 SAMPLE USER DATA:');
    const sampleUsers = await sql`
      SELECT 
        full_name, 
        email, 
        role, 
        department,
        is_active,
        created_at
      FROM users 
      ORDER BY created_at
      LIMIT 5
    `;
    
    sampleUsers.forEach(user => {
      const status = user.is_active ? '✅' : '❌';
      console.log(`   ${status} ${user.full_name} (${user.email}) - ${user.role}`);
    });
    
    // Final consistency check
    console.log('\n🔍 CONSISTENCY VERIFICATION:');
    
    // Check for any orphaned attendance records
    const orphanedAttendance = await sql`
      SELECT COUNT(*) as count
      FROM attendance a
      LEFT JOIN users u ON a.user_id = u.id
      WHERE u.id IS NULL
    `;
    
    console.log(`   Orphaned attendance records: ${orphanedAttendance[0].count}`);
    
    // Check for duplicate emails
    const duplicateEmails = await sql`
      SELECT email, COUNT(*) as count
      FROM users
      GROUP BY email
      HAVING COUNT(*) > 1
    `;
    
    console.log(`   Duplicate email addresses: ${duplicateEmails.length}`);
    
    console.log('\n🎉 CONSOLIDATION VALIDATION RESULTS:');
    console.log('===================================');
    console.log(`✅ Total users: ${allUsers.length}`);
    console.log(`✅ Active users: ${activeUsers.length}`);
    console.log(`✅ Attendance records: ${todayAttendance.length} today`);
    console.log(`✅ Data integrity: ${orphanedAttendance[0].count === 0 && duplicateEmails.length === 0 ? 'Good' : 'Issues found'}`);
    
    if (activeUsers.length >= 8) {
      console.log('\n🎯 SUCCESS: Employee count is now consistent!');
      console.log('   All parts of the application should show the same number of employees.');
    } else {
      console.log('\n⚠️  WARNING: Employee count seems low. Expected at least 8 active users.');
    }
    
  } catch (error) {
    console.error('❌ Test failed:', error.message);
    process.exit(1);
  }
}

testAPIConsistency();
