// Employee Payroll Summary API
// Provides year-to-date summary for employees

import { NextRequest, NextResponse } from 'next/server'
import { AuthService } from '@/lib/auth-utils'
import { serverDb as db } from '@/lib/server-db'

export async function GET(request: NextRequest) {
  try {
    const sessionToken = request.cookies.get("session-token")?.value;

    if (!sessionToken) {
      return NextResponse.json({
        success: false,
        error: 'Unauthorized'
      }, { status: 401 });
    }

    const user = await AuthService.verifySession(sessionToken);

    if (!user) {
      return NextResponse.json({
        success: false,
        error: 'Invalid session'
      }, { status: 401 });
    }

    const { searchParams } = new URL(request.url);
    const year = parseInt(searchParams.get('year') || new Date().getFullYear().toString());

    // Get employee's year-to-date summary
    const summary = await db.sql`
      SELECT 
        COUNT(*) as payroll_periods,
        SUM(gross_pay) as total_gross_pay,
        SUM(net_pay) as total_net_pay,
        SUM(tax_deductions) as total_taxes,
        SUM(provident_fund) as total_provident_fund,
        SUM(allowances) as total_allowances,
        SUM(deductions) as total_deductions,
        AVG(net_pay) as average_monthly_pay
      FROM payroll
      WHERE user_id = ${user.id}
      AND EXTRACT(YEAR FROM pay_period_start) = ${year}
      AND status IN ('approved', 'processed', 'paid')
    `;

    const summaryData = summary[0] || {
      payroll_periods: 0,
      total_gross_pay: 0,
      total_net_pay: 0,
      total_taxes: 0,
      total_provident_fund: 0,
      total_allowances: 0,
      total_deductions: 0,
      average_monthly_pay: 0
    };

    return NextResponse.json({
      success: true,
      data: {
        summary: {
          totalGrossPay: summaryData.total_gross_pay || 0,
          totalNetPay: summaryData.total_net_pay || 0,
          totalTaxes: summaryData.total_taxes || 0,
          totalProvidentFund: summaryData.total_provident_fund || 0,
          totalAllowances: summaryData.total_allowances || 0,
          totalDeductions: summaryData.total_deductions || 0,
          payrollPeriods: summaryData.payroll_periods || 0,
          averageMonthlyPay: summaryData.average_monthly_pay || 0
        },
        year: year
      },
      message: `Year-to-date summary for ${year} retrieved successfully`
    });

  } catch (error) {
    console.error('Error in employee payroll summary:', error);
    return NextResponse.json({
      success: false,
      error: error.message || 'Internal server error'
    }, { status: 500 });
  }
}
