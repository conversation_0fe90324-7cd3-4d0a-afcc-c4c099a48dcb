#!/usr/bin/env node

/**
 * Setup script for task management tables
 * Creates all the missing tables needed for the kanban board functionality
 */

const fs = require('fs');
const path = require('path');

// Load environment variables
require('dotenv').config({ path: '.env.local' });

let neon;

try {
  ({ neon } = require('@neondatabase/serverless'));
} catch (error) {
  console.log('⚠️  Required packages not installed. Please run: npm install @neondatabase/serverless');
  console.log('Error:', error.message);
  process.exit(1);
}

async function setupTaskManagement() {
  console.log('🚀 Setting up task management tables...\n');
  
  // Check if DATABASE_URL is set
  if (!process.env.DATABASE_URL) {
    console.error('❌ ERROR: DATABASE_URL environment variable is not set');
    console.log('📝 Please update your .env.local file with your Neon connection string');
    process.exit(1);
  }
  
  console.log('✅ DATABASE_URL found in environment');
  
  try {
    const sql = neon(process.env.DATABASE_URL);
    
    // Test connection first
    console.log('🔄 Testing database connection...');
    await sql`SELECT 1`;
    console.log('✅ Database connection successful!\n');
    
    // Read the SQL file
    const sqlFilePath = path.join(__dirname, 'create-task-management-tables.sql');
    
    if (!fs.existsSync(sqlFilePath)) {
      console.error('❌ SQL file not found:', sqlFilePath);
      process.exit(1);
    }
    
    console.log('🔄 Reading SQL file...');
    const sqlContent = fs.readFileSync(sqlFilePath, 'utf8');
    
    // Split by semicolon and execute each statement
    console.log('🔄 Executing SQL statements...');
    const statements = sqlContent
      .split(';')
      .map(stmt => stmt.trim())
      .filter(stmt => stmt.length > 0 && !stmt.startsWith('--'));
    
    let successCount = 0;
    let skipCount = 0;
    
    for (const statement of statements) {
      if (statement.trim()) {
        try {
          await sql.unsafe(statement);
          successCount++;
        } catch (error) {
          if (error.message.includes('already exists')) {
            console.log(`ℹ️  Skipped: ${error.message.split(' ')[1]} already exists`);
            skipCount++;
          } else {
            console.error(`❌ Error executing statement:`, error.message);
            console.log('Statement:', statement.substring(0, 100) + '...');
            throw error;
          }
        }
      }
    }
    
    console.log(`✅ Successfully executed ${successCount} statements`);
    if (skipCount > 0) {
      console.log(`ℹ️  Skipped ${skipCount} statements (objects already exist)`);
    }
    
    // Verify setup
    console.log('\n🔍 Verifying task management setup...');
    
    // Check tables
    const tables = await sql`
      SELECT table_name 
      FROM information_schema.tables 
      WHERE table_schema = 'public' 
      AND table_name LIKE 'task%'
      ORDER BY table_name
    `;
    
    console.log('✅ Task management tables created:');
    tables.forEach(t => {
      console.log(`   - ${t.table_name}`);
    });
    
    // Check projects
    const projects = await sql`SELECT name, color FROM task_projects ORDER BY created_at`;
    console.log('\n✅ Sample projects created:');
    projects.forEach(project => {
      console.log(`   - ${project.name} (${project.color})`);
    });
    
    // Check if we have any admin users
    const adminUsers = await sql`SELECT email FROM users WHERE role = 'admin' LIMIT 3`;
    console.log('\n✅ Admin users available for project creation:');
    adminUsers.forEach(user => {
      console.log(`   - ${user.email}`);
    });
    
    console.log('\n🎉 Task management setup completed successfully!');
    console.log('\n📋 Next steps:');
    console.log('1. Fix SQL syntax in API routes');
    console.log('2. Test the task creation workflow');
    console.log('3. Verify project management functionality');
    
  } catch (error) {
    console.error('\n❌ Task management setup failed:');
    console.error('Error:', error.message);
    
    if (error.message.includes('password authentication failed')) {
      console.log('\n💡 Troubleshooting tips:');
      console.log('1. Check your username and password in the DATABASE_URL');
      console.log('2. Ensure your Neon database is active (not suspended)');
      console.log('3. Verify the connection string is copied correctly from Neon console');
    }
    
    process.exit(1);
  }
}

setupTaskManagement();
