'use client'

import React, { useState, useEffect } from 'react'
import { Check, ChevronsUpDown, Search, User, Building, Hash } from 'lucide-react'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar'
import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
  CommandList,
} from '@/components/ui/command'
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/ui/popover'
import { cn } from '@/lib/utils'
import { useActiveEmployees } from '@/hooks/use-employees'

export interface EmployeeOption {
  id: string
  full_name: string
  employee_id?: string
  department?: string
  position?: string
  email: string
  salary?: number
  phone?: string
  hire_date?: string
  profile_picture?: string
}

interface EmployeeSelectorProps {
  value?: string
  onSelect: (employee: EmployeeOption | null) => void
  placeholder?: string
  className?: string
  disabled?: boolean
  showDetails?: boolean
}

export function EmployeeSelector({
  value,
  onSelect,
  placeholder = "Select employee...",
  className,
  disabled = false,
  showDetails = true
}: EmployeeSelectorProps) {
  const [open, setOpen] = useState(false)
  const [searchQuery, setSearchQuery] = useState("")
  
  const { data: employeesResponse, isLoading, error } = useActiveEmployees()
  const employees = employeesResponse?.users || []

  const selectedEmployee = employees.find(emp => emp.id === value)

  // Filter employees based on search query
  const filteredEmployees = employees.filter(employee => {
    if (!searchQuery) return true
    
    const searchLower = searchQuery.toLowerCase()
    return (
      employee.full_name?.toLowerCase().includes(searchLower) ||
      employee.employee_id?.toLowerCase().includes(searchLower) ||
      employee.email?.toLowerCase().includes(searchLower) ||
      employee.department?.toLowerCase().includes(searchLower) ||
      employee.position?.toLowerCase().includes(searchLower)
    )
  })

  const handleSelect = (employee: EmployeeOption) => {
    onSelect(employee)
    setOpen(false)
    setSearchQuery("")
  }

  const handleClear = () => {
    onSelect(null)
    setOpen(false)
    setSearchQuery("")
  }

  return (
    <div className={cn("space-y-2", className)}>
      <Label>Employee</Label>
      <Popover open={open} onOpenChange={setOpen}>
        <PopoverTrigger asChild>
          <Button
            variant="outline"
            role="combobox"
            aria-expanded={open}
            className={cn(
              "w-full justify-between",
              !selectedEmployee && "text-muted-foreground"
            )}
            disabled={disabled}
          >
            {selectedEmployee ? (
              <div className="flex items-center gap-2 min-w-0 flex-1">
                <Avatar className="h-6 w-6 flex-shrink-0">
                  <AvatarImage src={selectedEmployee.profile_picture} />
                  <AvatarFallback className="text-xs">
                    {selectedEmployee.full_name?.charAt(0) || 'U'}
                  </AvatarFallback>
                </Avatar>
                <div className="min-w-0 flex-1 text-left">
                  <div className="font-medium truncate">{selectedEmployee.full_name}</div>
                  {showDetails && (
                    <div className="text-xs text-muted-foreground truncate">
                      {selectedEmployee.employee_id} • {selectedEmployee.department}
                    </div>
                  )}
                </div>
              </div>
            ) : (
              placeholder
            )}
            <ChevronsUpDown className="ml-2 h-4 w-4 shrink-0 opacity-50" />
          </Button>
        </PopoverTrigger>
        <PopoverContent className="w-full p-0" align="start">
          <Command>
            <div className="flex items-center border-b px-3">
              <Search className="mr-2 h-4 w-4 shrink-0 opacity-50" />
              <Input
                placeholder="Search employees..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="border-0 focus-visible:ring-0 focus-visible:ring-offset-0"
              />
            </div>
            <CommandList>
              {isLoading ? (
                <div className="p-4 text-center text-sm text-muted-foreground">
                  Loading employees...
                </div>
              ) : error ? (
                <div className="p-4 text-center text-sm text-destructive">
                  Failed to load employees
                </div>
              ) : filteredEmployees.length === 0 ? (
                <CommandEmpty>No employees found.</CommandEmpty>
              ) : (
                <CommandGroup>
                  {selectedEmployee && (
                    <CommandItem
                      value="clear"
                      onSelect={handleClear}
                      className="text-muted-foreground"
                    >
                      <div className="flex items-center gap-2">
                        <div className="h-6 w-6 rounded-full border-2 border-dashed border-muted-foreground/50 flex items-center justify-center">
                          <span className="text-xs">×</span>
                        </div>
                        Clear selection
                      </div>
                    </CommandItem>
                  )}
                  {filteredEmployees.map((employee) => (
                    <CommandItem
                      key={employee.id}
                      value={employee.id}
                      onSelect={() => handleSelect(employee)}
                    >
                      <div className="flex items-center gap-3 w-full">
                        <Avatar className="h-8 w-8 flex-shrink-0">
                          <AvatarImage src={employee.profile_picture} />
                          <AvatarFallback className="text-xs">
                            {employee.full_name?.charAt(0) || 'U'}
                          </AvatarFallback>
                        </Avatar>
                        <div className="min-w-0 flex-1">
                          <div className="font-medium truncate">{employee.full_name}</div>
                          <div className="text-sm text-muted-foreground truncate flex items-center gap-2">
                            <span className="flex items-center gap-1">
                              <Hash className="h-3 w-3" />
                              {employee.employee_id || 'No ID'}
                            </span>
                            <span>•</span>
                            <span className="flex items-center gap-1">
                              <Building className="h-3 w-3" />
                              {employee.department || 'No Dept'}
                            </span>
                          </div>
                          {showDetails && employee.position && (
                            <div className="text-xs text-muted-foreground truncate">
                              {employee.position}
                            </div>
                          )}
                        </div>
                        {value === employee.id && (
                          <Check className="h-4 w-4 text-primary" />
                        )}
                      </div>
                    </CommandItem>
                  ))}
                </CommandGroup>
              )}
            </CommandList>
          </Command>
        </PopoverContent>
      </Popover>
    </div>
  )
}
