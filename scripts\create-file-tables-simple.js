const { neon } = require('@neondatabase/serverless');
require('dotenv').config({ path: '.env.local' });

async function createFileTables() {
  try {
    const sql = neon(process.env.DATABASE_URL);
    console.log('🔄 Creating file management tables...');
    
    // Drop existing tables if they exist
    console.log('Dropping existing tables...');
    await sql`DROP TABLE IF EXISTS file_access_log CASCADE`;
    await sql`DROP TABLE IF EXISTS file_storage CASCADE`;
    await sql`DROP TABLE IF EXISTS user_files CASCADE`;
    await sql`DROP TABLE IF EXISTS file_type_config CASCADE`;
    await sql`DROP VIEW IF EXISTS user_file_summary CASCADE`;
    
    // Create file_type_config table
    console.log('Creating file_type_config table...');
    await sql`
      CREATE TABLE file_type_config (
        id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
        file_type VARCHAR(50) NOT NULL,
        allowed_extensions TEXT[] NOT NULL,
        allowed_mime_types TEXT[] NOT NULL,
        max_file_size_mb INTEGER NOT NULL,
        description TEXT,
        is_active BOOLEAN DEFAULT true,
        created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
      )
    `;
    
    // Create user_files table
    console.log('Creating user_files table...');
    await sql`
      CREATE TABLE user_files (
        id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
        user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
        file_type VARCHAR(50) NOT NULL,
        original_filename VARCHAR(255) NOT NULL,
        stored_filename VARCHAR(255) NOT NULL,
        file_path VARCHAR(500) NOT NULL,
        file_size INTEGER NOT NULL,
        mime_type VARCHAR(100) NOT NULL,
        file_extension VARCHAR(10) NOT NULL,
        is_validated BOOLEAN DEFAULT false,
        validation_status VARCHAR(20) DEFAULT 'pending',
        uploaded_by UUID NOT NULL REFERENCES users(id),
        upload_ip_address INET,
        upload_user_agent TEXT,
        is_active BOOLEAN DEFAULT true,
        created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
        updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
      )
    `;
    
    // Create file_storage table
    console.log('Creating file_storage table...');
    await sql`
      CREATE TABLE file_storage (
        id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
        file_id UUID NOT NULL REFERENCES user_files(id) ON DELETE CASCADE,
        file_content BYTEA NOT NULL,
        content_hash VARCHAR(64) NOT NULL,
        created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
        UNIQUE(file_id)
      )
    `;
    
    // Create file_access_log table
    console.log('Creating file_access_log table...');
    await sql`
      CREATE TABLE file_access_log (
        id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
        file_id UUID NOT NULL REFERENCES user_files(id) ON DELETE CASCADE,
        accessed_by UUID NOT NULL REFERENCES users(id),
        access_type VARCHAR(20) NOT NULL,
        access_ip_address INET,
        user_agent TEXT,
        success BOOLEAN DEFAULT true,
        error_message TEXT,
        created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
      )
    `;
    
    // Create indexes
    console.log('Creating indexes...');
    await sql`CREATE INDEX idx_user_files_user_id ON user_files(user_id)`;
    await sql`CREATE INDEX idx_user_files_type ON user_files(file_type)`;
    await sql`CREATE INDEX idx_user_files_active ON user_files(is_active)`;
    await sql`CREATE INDEX idx_file_storage_file_id ON file_storage(file_id)`;
    await sql`CREATE INDEX idx_file_access_log_file_id ON file_access_log(file_id)`;
    
    // Insert file type configurations
    console.log('Inserting file type configurations...');
    await sql`
      INSERT INTO file_type_config (file_type, allowed_extensions, allowed_mime_types, max_file_size_mb, description) VALUES
      ('profile_picture', ARRAY['jpg', 'jpeg', 'png', 'gif', 'webp'], ARRAY['image/jpeg', 'image/png', 'image/gif', 'image/webp'], 5, 'User profile pictures'),
      ('document', ARRAY['pdf', 'doc', 'docx', 'jpg', 'jpeg', 'png'], ARRAY['application/pdf', 'application/msword', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document', 'image/jpeg', 'image/png'], 10, 'General documents'),
      ('contract', ARRAY['pdf', 'doc', 'docx', 'jpg', 'jpeg', 'png'], ARRAY['application/pdf', 'application/msword', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document', 'image/jpeg', 'image/png'], 10, 'Employment contracts'),
      ('signature', ARRAY['jpg', 'jpeg', 'png', 'gif', 'webp'], ARRAY['image/jpeg', 'image/png', 'image/gif', 'image/webp'], 2, 'Digital signatures')
    `;
    
    // Test the setup
    console.log('Testing file management setup...');
    const tables = await sql`
      SELECT table_name 
      FROM information_schema.tables 
      WHERE table_schema = 'public' 
      AND table_name IN ('user_files', 'file_storage', 'file_access_log', 'file_type_config')
      ORDER BY table_name
    `;
    
    const configs = await sql`SELECT file_type FROM file_type_config WHERE is_active = true`;
    
    console.log('✅ File management tables created successfully!');
    console.log('📋 Tables:', tables.map(t => t.table_name));
    console.log('📋 File types:', configs.map(c => c.file_type));
    
  } catch (error) {
    console.error('❌ Error creating file tables:', error);
  }
}

createFileTables();
