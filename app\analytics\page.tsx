"use client"

import { useState } from "react"
import { AppHeader } from "@/components/app-header"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "@/components/ui/tabs"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> } from "@/components/ui/chart"
import { ArrowUp, ArrowDown, Download } from "lucide-react"

export default function AnalyticsPage() {
  const [timeRange, setTimeRange] = useState("month")

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900 flex flex-col">
      <AppHeader title="Performance Analytics" />

      <div className="p-4">
        <div className="flex items-center justify-between mb-4">
          <h2 className="text-xl font-semibold text-gray-800 dark:text-gray-200">Dashboard</h2>
          <div className="flex items-center gap-2">
            <Select defaultValue="month" onValueChange={setTimeRange}>
              <SelectTrigger className="w-[130px] dark:bg-gray-800 dark:border-gray-700">
                <SelectValue placeholder="Select period" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="week">This Week</SelectItem>
                <SelectItem value="month">This Month</SelectItem>
                <SelectItem value="quarter">This Quarter</SelectItem>
                <SelectItem value="year">This Year</SelectItem>
              </SelectContent>
            </Select>
            <Button variant="outline" size="icon" className="dark:border-gray-700">
              <Download className="h-4 w-4" />
            </Button>
          </div>
        </div>

        {/* KPI Cards */}
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-6">
          <Card className="dark:bg-gray-800">
            <CardContent className="p-4">
              <div className="flex flex-col">
                <span className="text-sm text-gray-500 dark:text-gray-400">New Leads</span>
                <div className="flex items-end justify-between mt-1">
                  <span className="text-2xl font-bold dark:text-white">127</span>
                  <div className="flex items-center text-green-600 dark:text-green-400 text-xs">
                    <ArrowUp className="h-3 w-3 mr-1" />
                    <span>12%</span>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card className="dark:bg-gray-800">
            <CardContent className="p-4">
              <div className="flex flex-col">
                <span className="text-sm text-gray-500 dark:text-gray-400">Conversions</span>
                <div className="flex items-end justify-between mt-1">
                  <span className="text-2xl font-bold dark:text-white">42</span>
                  <div className="flex items-center text-green-600 dark:text-green-400 text-xs">
                    <ArrowUp className="h-3 w-3 mr-1" />
                    <span>8%</span>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card className="dark:bg-gray-800">
            <CardContent className="p-4">
              <div className="flex flex-col">
                <span className="text-sm text-gray-500 dark:text-gray-400">Revenue</span>
                <div className="flex items-end justify-between mt-1">
                  <span className="text-2xl font-bold dark:text-white">₹1.2M</span>
                  <div className="flex items-center text-green-600 dark:text-green-400 text-xs">
                    <ArrowUp className="h-3 w-3 mr-1" />
                    <span>15%</span>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card className="dark:bg-gray-800">
            <CardContent className="p-4">
              <div className="flex flex-col">
                <span className="text-sm text-gray-500 dark:text-gray-400">Avg. Deal Size</span>
                <div className="flex items-end justify-between mt-1">
                  <span className="text-2xl font-bold dark:text-white">₹28.5K</span>
                  <div className="flex items-center text-red-600 dark:text-red-400 text-xs">
                    <ArrowDown className="h-3 w-3 mr-1" />
                    <span>3%</span>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        <Tabs defaultValue="performance" className="mb-6">
          <TabsList className="grid w-full grid-cols-3">
            <TabsTrigger value="performance">Performance</TabsTrigger>
            <TabsTrigger value="leads">Lead Sources</TabsTrigger>
            <TabsTrigger value="products">Products</TabsTrigger>
          </TabsList>

          <TabsContent value="performance" className="mt-4">
            <Card className="dark:bg-gray-800">
              <CardHeader className="pb-2">
                <CardTitle className="text-base dark:text-gray-200">Monthly Performance</CardTitle>
                <CardDescription className="dark:text-gray-400">Conversion rate and revenue over time</CardDescription>
              </CardHeader>
              <CardContent className="p-4">
                <LineChart
                  data={[
                    { name: "Jan", Leads: 65, Conversions: 28, Revenue: 45 },
                    { name: "Feb", Leads: 59, Conversions: 24, Revenue: 38 },
                    { name: "Mar", Leads: 80, Conversions: 35, Revenue: 65 },
                    { name: "Apr", Leads: 81, Conversions: 32, Revenue: 70 },
                    { name: "May", Leads: 56, Conversions: 20, Revenue: 40 },
                    { name: "Jun", Leads: 55, Conversions: 21, Revenue: 42 },
                    { name: "Jul", Leads: 40, Conversions: 15, Revenue: 30 },
                    { name: "Aug", Leads: 70, Conversions: 30, Revenue: 60 },
                    { name: "Sep", Leads: 90, Conversions: 40, Revenue: 80 },
                    { name: "Oct", Leads: 95, Conversions: 45, Revenue: 85 },
                    { name: "Nov", Leads: 100, Conversions: 50, Revenue: 90 },
                    { name: "Dec", Leads: 120, Conversions: 60, Revenue: 100 },
                  ]}
                  index="name"
                  categories={["Leads", "Conversions", "Revenue"]}
                  colors={["#4ade80", "#f87171", "#60a5fa"]}
                  valueFormatter={(value) => `${value}`}
                  yAxisWidth={40}
                  className="h-72"
                />
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="leads" className="mt-4">
            <Card className="dark:bg-gray-800">
              <CardHeader className="pb-2">
                <CardTitle className="text-base dark:text-gray-200">Lead Sources</CardTitle>
                <CardDescription className="dark:text-gray-400">Distribution of leads by source</CardDescription>
              </CardHeader>
              <CardContent className="p-4">
                <div className="h-72 flex items-center justify-center">
                  <PieChart
                    data={[
                      { name: "Referrals", value: 35 },
                      { name: "Website", value: 25 },
                      { name: "Social Media", value: 20 },
                      { name: "Direct", value: 15 },
                      { name: "Other", value: 5 },
                    ]}
                    index="name"
                    category="value"
                    valueFormatter={(value) => `${value}%`}
                    colors={["#4ade80", "#f87171", "#60a5fa", "#fbbf24", "#c084fc"]}
                    className="h-72"
                  />
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="products" className="mt-4">
            <Card className="dark:bg-gray-800">
              <CardHeader className="pb-2">
                <CardTitle className="text-base dark:text-gray-200">Product Performance</CardTitle>
                <CardDescription className="dark:text-gray-400">Sales by product category</CardDescription>
              </CardHeader>
              <CardContent className="p-4">
                <BarChart
                  data={[
                    { name: "Savings", value: 45 },
                    { name: "Fixed Deposits", value: 30 },
                    { name: "Loans", value: 60 },
                    { name: "Insurance", value: 25 },
                    { name: "Investments", value: 40 },
                  ]}
                  index="name"
                  categories={["value"]}
                  colors={["#4ade80"]}
                  valueFormatter={(value) => `${value}`}
                  yAxisWidth={40}
                  className="h-72"
                />
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <Card className="dark:bg-gray-800">
            <CardHeader className="pb-2">
              <CardTitle className="text-base dark:text-gray-200">Team Performance</CardTitle>
              <CardDescription className="dark:text-gray-400">Top performing team members</CardDescription>
            </CardHeader>
            <CardContent className="p-0">
              <div className="divide-y dark:divide-gray-700">
                {[
                  { name: "John Doe", leads: 45, conversions: 18, rate: 40 },
                  { name: "Jane Smith", leads: 38, conversions: 20, rate: 53 },
                  { name: "Alex Johnson", leads: 32, conversions: 12, rate: 38 },
                  { name: "Sarah Williams", leads: 28, conversions: 10, rate: 36 },
                ].map((member, index) => (
                  <div key={index} className="flex items-center justify-between p-4">
                    <div className="flex items-center gap-3">
                      <div className="w-8 h-8 rounded-full bg-gray-200 dark:bg-gray-700 flex items-center justify-center text-gray-600 dark:text-gray-300 font-medium">
                        {member.name.charAt(0)}
                      </div>
                      <div>
                        <p className="font-medium text-sm dark:text-gray-200">{member.name}</p>
                        <p className="text-xs text-gray-500 dark:text-gray-400">
                          {member.leads} leads, {member.conversions} conv.
                        </p>
                      </div>
                    </div>
                    <div className="text-right">
                      <p className="font-bold text-sm dark:text-gray-200">{member.rate}%</p>
                      <p className="text-xs text-gray-500 dark:text-gray-400">Conversion rate</p>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>

          <Card className="dark:bg-gray-800">
            <CardHeader className="pb-2">
              <CardTitle className="text-base dark:text-gray-200">Target Achievement</CardTitle>
              <CardDescription className="dark:text-gray-400">Progress towards monthly targets</CardDescription>
            </CardHeader>
            <CardContent className="p-0">
              <div className="divide-y dark:divide-gray-700">
                {[
                  { name: "New Clients", current: 42, target: 50, progress: 84 },
                  { name: "Revenue", current: "₹1.2M", target: "₹1.5M", progress: 80 },
                  { name: "Referrals", current: 28, target: 40, progress: 70 },
                  { name: "Cross-Selling", current: 15, target: 25, progress: 60 },
                ].map((target, index) => (
                  <div key={index} className="flex items-center justify-between p-4">
                    <div>
                      <p className="font-medium text-sm dark:text-gray-200">{target.name}</p>
                      <p className="text-xs text-gray-500 dark:text-gray-400">
                        {target.current} of {target.target}
                      </p>
                    </div>
                    <div className="flex items-center gap-2">
                      <div className="w-24 h-2 bg-gray-200 dark:bg-gray-700 rounded-full overflow-hidden">
                        <div
                          className="h-full bg-exobank-green dark:bg-green-600 rounded-full"
                          style={{ width: `${target.progress}%` }}
                        ></div>
                      </div>
                      <span className="text-xs font-medium dark:text-gray-300">{target.progress}%</span>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  )
}
