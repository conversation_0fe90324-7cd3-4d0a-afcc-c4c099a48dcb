'use client'

import React, { useState, useEffect } from 'react'
import { Calendar, ChevronDown, Clock } from 'lucide-react'
import { Button } from '@/components/ui/button'
import { Label } from '@/components/ui/label'
import { Badge } from '@/components/ui/badge'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import { fiscalYearManager, PayrollPeriod } from '@/lib/fiscal-year-manager'
import { NepaliCalendar } from '@/lib/nepali-calendar'
import { cn } from '@/lib/utils'

interface NepaliPayPeriodSelectorProps {
  value?: string
  onChange?: (period: PayrollPeriod) => void
  className?: string
  disabled?: boolean
  showFiscalYearSelector?: boolean
  historicalYearsCount?: number // Number of previous fiscal years to show
  allowHistoricalSelection?: boolean // Whether to allow selection of past periods
  showValidationWarnings?: boolean // Whether to show warnings for old periods
}

interface FiscalYearPeriod {
  fiscalYear: string
  displayName: string
  periods: PayrollPeriod[]
  isCurrent: boolean
}

export function NepaliPayPeriodSelector({
  value,
  onChange,
  className,
  disabled = false,
  showFiscalYearSelector = true,
  historicalYearsCount = 3,
  allowHistoricalSelection = true,
  showValidationWarnings = true
}: NepaliPayPeriodSelectorProps) {
  const [selectedFiscalYear, setSelectedFiscalYear] = useState<string>('')
  const [availableFiscalYears, setAvailableFiscalYears] = useState<FiscalYearPeriod[]>([])
  const [selectedPeriod, setSelectedPeriod] = useState<PayrollPeriod | null>(null)
  const [loading, setLoading] = useState(true)
  const [validationWarning, setValidationWarning] = useState<string>('')

  // Initialize fiscal years and periods
  useEffect(() => {
    const initializeFiscalYears = async () => {
      setLoading(true)
      try {
        const currentFY = fiscalYearManager.getCurrentFiscalYear()
        // Get current + historical fiscal years based on historicalYearsCount
        const fyList = fiscalYearManager.getAvailableFiscalYears(historicalYearsCount + 1)

        const fiscalYearPeriods: FiscalYearPeriod[] = []

        for (const fy of fyList) {
          const periods = await fiscalYearManager.createPayrollPeriods(fy)
          const monthlyPeriods = periods.filter(p => p.type === 'monthly')

          fiscalYearPeriods.push({
            fiscalYear: fy,
            displayName: `FY ${fy}`,
            periods: monthlyPeriods,
            isCurrent: fy === currentFY
          })
        }

        // Sort fiscal years in descending order (most recent first)
        fiscalYearPeriods.sort((a, b) => b.fiscalYear.localeCompare(a.fiscalYear))

        setAvailableFiscalYears(fiscalYearPeriods)

        // Set default fiscal year to current
        if (!selectedFiscalYear) {
          setSelectedFiscalYear(currentFY)
        }
      } catch (error) {
        console.error('Error initializing fiscal years:', error)
      } finally {
        setLoading(false)
      }
    }

    initializeFiscalYears()
  }, [historicalYearsCount])

  // Update selected period when fiscal year changes
  useEffect(() => {
    if (selectedFiscalYear && availableFiscalYears.length > 0) {
      const fyData = availableFiscalYears.find(fy => fy.fiscalYear === selectedFiscalYear)
      if (fyData && fyData.periods.length > 0) {
        // Auto-select current month if it's the current fiscal year
        if (fyData.isCurrent) {
          const currentPeriod = fiscalYearManager.getCurrentPayrollPeriod('monthly')
          if (currentPeriod && fyData.periods.find(p => p.id === currentPeriod.id)) {
            setSelectedPeriod(currentPeriod)
            onChange?.(currentPeriod)
            return
          }
        }
        
        // Otherwise select the first period
        const firstPeriod = fyData.periods[0]
        setSelectedPeriod(firstPeriod)
        onChange?.(firstPeriod)
      }
    }
  }, [selectedFiscalYear, availableFiscalYears])

  // Handle period selection with validation
  const handlePeriodChange = (periodId: string) => {
    const fyData = availableFiscalYears.find(fy => fy.fiscalYear === selectedFiscalYear)
    if (fyData) {
      const period = fyData.periods.find(p => p.id === periodId)
      if (period) {
        // Validate historical period selection
        if (showValidationWarnings && allowHistoricalSelection) {
          const warning = validateHistoricalPeriod(period)
          setValidationWarning(warning)
        } else {
          setValidationWarning('')
        }

        setSelectedPeriod(period)
        onChange?.(period)
      }
    }
  }

  // Validate if the selected period is significantly old
  const validateHistoricalPeriod = (period: PayrollPeriod): string => {
    const today = new Date()
    const periodEndDate = period.adEndDate
    const monthsDifference = (today.getFullYear() - periodEndDate.getFullYear()) * 12 +
                            (today.getMonth() - periodEndDate.getMonth())

    if (monthsDifference > 6) {
      return `⚠️ This payroll period is ${monthsDifference} months old. Please verify this is the correct period for processing.`
    } else if (monthsDifference > 3) {
      return `⚠️ This payroll period is ${monthsDifference} months old.`
    }

    return ''
  }

  // Get current fiscal year data
  const currentFYData = availableFiscalYears.find(fy => fy.fiscalYear === selectedFiscalYear)

  // Format period display name with both Nepali and English dates
  const formatPeriodName = (period: PayrollPeriod) => {
    const nepaliMonth = NepaliCalendar.getBSMonthName(period.bsStartDate.month, 'en')
    const nepaliYear = period.bsStartDate.year
    const adStart = period.adStartDate.toLocaleDateString('en-US', { month: 'short' })
    const adEnd = period.adEndDate.toLocaleDateString('en-US', { month: 'short', year: 'numeric' })

    // Add fiscal year indicator for clarity
    const fiscalYearSuffix = period.bsStartDate.year !== fiscalYearManager.getCurrentFiscalYear()
      ? ` (FY ${period.bsStartDate.year})`
      : ''

    return {
      primary: `${nepaliMonth} ${nepaliYear}${fiscalYearSuffix}`,
      secondary: `${adStart} - ${adEnd}`
    }
  }

  // Check if a period is historical (more than current month)
  const isPeriodHistorical = (period: PayrollPeriod): boolean => {
    const today = new Date()
    return period.adEndDate < today
  }

  // Get period status for display
  const getPeriodStatus = (period: PayrollPeriod) => {
    if (period.isClosed) return { label: 'Closed', variant: 'secondary' as const }
    if (period.isActive) return { label: 'Current', variant: 'default' as const }
    if (isPeriodHistorical(period)) return { label: 'Historical', variant: 'outline' as const }
    return { label: 'Future', variant: 'outline' as const }
  }

  if (loading) {
    return (
      <div className={cn("space-y-2", className)}>
        <Label>Pay Period</Label>
        <div className="h-10 bg-muted animate-pulse rounded-md" />
      </div>
    )
  }

  return (
    <div className={cn("space-y-4", className)}>
      {showFiscalYearSelector && (
        <div className="space-y-2">
          <Label>Fiscal Year</Label>
          <Select
            value={selectedFiscalYear}
            onValueChange={setSelectedFiscalYear}
            disabled={disabled}
          >
            <SelectTrigger>
              <SelectValue placeholder="Select fiscal year" />
            </SelectTrigger>
            <SelectContent>
              {availableFiscalYears.map((fy) => {
                const periodsCount = fy.periods.length
                const historicalCount = fy.periods.filter(p => isPeriodHistorical(p)).length

                return (
                  <SelectItem key={fy.fiscalYear} value={fy.fiscalYear}>
                    <div className="flex items-center justify-between w-full">
                      <div className="flex items-center gap-2">
                        <span>{fy.displayName}</span>
                        {fy.isCurrent && (
                          <Badge variant="secondary" className="text-xs">Current</Badge>
                        )}
                        {!fy.isCurrent && (
                          <Badge variant="outline" className="text-xs">Historical</Badge>
                        )}
                      </div>
                      <span className="text-xs text-muted-foreground">
                        {periodsCount} periods
                      </span>
                    </div>
                  </SelectItem>
                )
              })}
            </SelectContent>
          </Select>
        </div>
      )}

      <div className="space-y-2">
        <Label>Pay Period</Label>
        <Select
          value={selectedPeriod?.id || ''}
          onValueChange={handlePeriodChange}
          disabled={disabled || !currentFYData}
        >
          <SelectTrigger>
            <SelectValue placeholder="Select pay period">
              {selectedPeriod && (
                <div className="flex items-center gap-2">
                  <Calendar className="h-4 w-4" />
                  <div className="text-left">
                    <div className="font-medium">
                      {formatPeriodName(selectedPeriod).primary}
                    </div>
                    <div className="text-xs text-muted-foreground">
                      {formatPeriodName(selectedPeriod).secondary}
                    </div>
                  </div>
                </div>
              )}
            </SelectValue>
          </SelectTrigger>
          <SelectContent>
            {currentFYData?.periods.map((period) => {
              const formatted = formatPeriodName(period)
              const status = getPeriodStatus(period)
              const isHistorical = isPeriodHistorical(period)

              // Filter out periods that shouldn't be selectable
              if (!allowHistoricalSelection && isHistorical && !period.isActive) {
                return null
              }

              return (
                <SelectItem key={period.id} value={period.id}>
                  <div className="flex items-center justify-between w-full">
                    <div className="flex items-center gap-2">
                      <Calendar className={cn(
                        "h-4 w-4",
                        isHistorical && "text-muted-foreground",
                        period.isActive && "text-primary"
                      )} />
                      <div>
                        <div className={cn(
                          "font-medium",
                          isHistorical && "text-muted-foreground"
                        )}>
                          {formatted.primary}
                        </div>
                        <div className="text-xs text-muted-foreground">
                          {formatted.secondary}
                        </div>
                      </div>
                    </div>
                    <div className="flex items-center gap-1">
                      <Badge variant={status.variant} className="text-xs">
                        {status.label}
                      </Badge>
                      {isHistorical && showValidationWarnings && (
                        <span className="text-xs text-amber-600">⚠️</span>
                      )}
                    </div>
                  </div>
                </SelectItem>
              )
            }).filter(Boolean)}
          </SelectContent>
        </Select>
      </div>

      {selectedPeriod && (
        <div className="space-y-3">
          <div className="bg-muted/50 p-3 rounded-lg">
            <div className="text-sm space-y-1">
              <div className="flex justify-between">
                <span className="text-muted-foreground">Working Days:</span>
                <span className="font-medium">{selectedPeriod.workingDays}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-muted-foreground">Period Status:</span>
                <Badge variant={getPeriodStatus(selectedPeriod).variant} className="text-xs">
                  {getPeriodStatus(selectedPeriod).label}
                </Badge>
              </div>
              {isPeriodHistorical(selectedPeriod) && (
                <div className="flex justify-between">
                  <span className="text-muted-foreground">Period Type:</span>
                  <span className="text-xs text-amber-600 font-medium">Historical Period</span>
                </div>
              )}
            </div>
          </div>

          {validationWarning && (
            <div className="bg-amber-50 dark:bg-amber-950/20 border border-amber-200 dark:border-amber-800 p-3 rounded-lg">
              <div className="text-sm text-amber-800 dark:text-amber-200">
                {validationWarning}
              </div>
            </div>
          )}
        </div>
      )}
    </div>
  )
}
