// Enhanced Attendance-Payroll Integration
// Improved integration between attendance data and payroll calculations

import { neon } from '@neondatabase/serverless';

const sql = neon(process.env.DATABASE_URL!);

export interface EnhancedAttendanceSummary {
  user_id: string;
  fiscal_year: string;
  bs_month: string;
  ad_month_start: string;
  ad_month_end: string;
  
  // Working days calculation
  total_working_days: number;
  total_calendar_days: number;
  weekends: number;
  public_holidays: number;
  
  // Attendance breakdown
  days_present: number;
  days_absent: number;
  days_late: number;
  days_half_day: number;
  days_on_leave: number;
  
  // Hours calculation
  total_hours_worked: number;
  regular_hours: number;
  overtime_hours: number;
  break_hours: number;
  
  // Session tracking
  total_sessions: number;
  average_sessions_per_day: number;
  
  // Late tracking
  total_late_minutes: number;
  average_late_minutes: number;
  late_days_count: number;
  
  // Performance metrics
  attendance_percentage: number;
  punctuality_percentage: number;
  productivity_score: number;
  
  // Bonus eligibility
  attendance_bonus_eligible: boolean;
  punctuality_bonus_eligible: boolean;
  
  // Penalty calculations
  late_penalty_amount: number;
  absence_penalty_amount: number;
  
  // Overtime details
  overtime_eligible_hours: number;
  overtime_rate_multiplier: number;
  overtime_pay_amount: number;
}

export interface PayrollComponentCalculation {
  component_id: string;
  component_name: string;
  component_code: string;
  component_type: 'allowance' | 'deduction';
  calculation_type: 'fixed' | 'percentage' | 'formula' | 'conditional';
  
  // Base values
  base_amount?: number;
  percentage?: number;
  percentage_base?: string;
  
  // Calculated values
  calculated_amount: number;
  is_applicable: boolean;
  calculation_notes: string[];
  
  // Metadata
  is_taxable: boolean;
  is_statutory: boolean;
  effective_from: string;
  effective_to?: string;
}

export class EnhancedAttendancePayrollIntegration {
  
  // Get comprehensive attendance summary with payroll calculations
  async getEnhancedAttendanceSummary(
    userId: string, 
    year: number, 
    month: number
  ): Promise<EnhancedAttendanceSummary> {
    try {
      // Get payroll settings
      const settings = await this.getPayrollSettings();
      
      // Calculate date range
      const startDate = new Date(year, month - 1, 1);
      const endDate = new Date(year, month, 0);
      const startDateStr = startDate.toISOString().split('T')[0];
      const endDateStr = endDate.toISOString().split('T')[0];
      
      // Get attendance records with detailed breakdown
      const attendanceData = await sql`
        SELECT 
          DATE(check_in_time) as attendance_date,
          status,
          COUNT(*) as sessions_count,
          MIN(check_in_time) as first_check_in,
          MAX(check_out_time) as last_check_out,
          SUM(
            CASE WHEN check_out_time IS NOT NULL 
            THEN EXTRACT(EPOCH FROM (check_out_time - check_in_time))/3600 
            ELSE 0 END
          ) as daily_hours,
          SUM(
            CASE WHEN status = 'late' AND check_in_time IS NOT NULL
            THEN EXTRACT(EPOCH FROM (check_in_time::time - '09:00:00'::time))/60
            ELSE 0 END
          ) as daily_late_minutes
        FROM attendance 
        WHERE user_id = ${userId} 
          AND DATE(check_in_time) BETWEEN ${startDateStr} AND ${endDateStr}
        GROUP BY DATE(check_in_time), status
        ORDER BY attendance_date
      `;
      
      // Calculate working days (excluding weekends and holidays)
      const workingDaysInfo = await this.calculateWorkingDays(year, month);
      
      // Process attendance data
      let daysPresent = 0;
      let daysAbsent = 0;
      let daysLate = 0;
      let daysHalfDay = 0;
      let daysOnLeave = 0;
      let totalHours = 0;
      let regularHours = 0;
      let overtimeHours = 0;
      let totalSessions = 0;
      let totalLateMinutes = 0;
      
      const attendanceDates = new Set();
      
      for (const record of attendanceData) {
        const date = record.attendance_date;
        attendanceDates.add(date);
        
        const dailyHours = parseFloat(record.daily_hours) || 0;
        const sessions = parseInt(record.sessions_count) || 0;
        const lateMinutes = parseFloat(record.daily_late_minutes) || 0;
        
        totalHours += dailyHours;
        totalSessions += sessions;
        totalLateMinutes += lateMinutes;
        
        // Categorize attendance
        switch (record.status) {
          case 'present':
            daysPresent++;
            this.categorizeHours(dailyHours, settings, (reg, over) => {
              regularHours += reg;
              overtimeHours += over;
            });
            break;
          case 'late':
            daysLate++;
            daysPresent++; // Late is still present
            this.categorizeHours(dailyHours, settings, (reg, over) => {
              regularHours += reg;
              overtimeHours += over;
            });
            break;
          case 'half_day':
            daysHalfDay++;
            regularHours += Math.min(dailyHours, settings.default_working_hours_per_day / 2);
            break;
          case 'on_leave':
            daysOnLeave++;
            regularHours += settings.default_working_hours_per_day; // Paid leave
            break;
          default:
            // Handle other statuses
            break;
        }
      }
      
      // Calculate absent days
      daysAbsent = workingDaysInfo.total_working_days - attendanceDates.size;
      
      // Calculate performance metrics
      const attendancePercentage = (daysPresent + daysOnLeave) / workingDaysInfo.total_working_days;
      const punctualityPercentage = daysPresent > 0 ? (daysPresent - daysLate) / daysPresent : 1;
      const productivityScore = this.calculateProductivityScore(
        attendancePercentage, 
        punctualityPercentage, 
        totalHours, 
        workingDaysInfo.total_working_days * settings.default_working_hours_per_day
      );
      
      // Calculate bonus eligibility
      const attendanceBonusEligible = attendancePercentage >= (settings.attendance_bonus_threshold / 100);
      const punctualityBonusEligible = punctualityPercentage >= 0.95; // 95% punctuality
      
      // Calculate penalties
      const latePenaltyAmount = totalLateMinutes * settings.late_penalty_per_minute;
      const absencePenaltyAmount = daysAbsent * (settings.default_working_hours_per_day * settings.late_penalty_per_minute * 60);
      
      // Calculate overtime details
      const overtimeEligibleHours = Math.max(0, overtimeHours);
      const overtimeRateMultiplier = settings.default_overtime_multiplier;
      
      // Get fiscal year and BS month info
      const fiscalYearInfo = await this.getFiscalYearInfo(year, month);
      
      return {
        user_id: userId,
        fiscal_year: fiscalYearInfo.fiscal_year,
        bs_month: fiscalYearInfo.bs_month,
        ad_month_start: startDateStr,
        ad_month_end: endDateStr,
        
        total_working_days: workingDaysInfo.total_working_days,
        total_calendar_days: workingDaysInfo.total_calendar_days,
        weekends: workingDaysInfo.weekends,
        public_holidays: workingDaysInfo.public_holidays,
        
        days_present: daysPresent,
        days_absent: daysAbsent,
        days_late: daysLate,
        days_half_day: daysHalfDay,
        days_on_leave: daysOnLeave,
        
        total_hours_worked: totalHours,
        regular_hours: regularHours,
        overtime_hours: overtimeHours,
        break_hours: 0, // Can be calculated if break tracking is implemented
        
        total_sessions: totalSessions,
        average_sessions_per_day: attendanceDates.size > 0 ? totalSessions / attendanceDates.size : 0,
        
        total_late_minutes: totalLateMinutes,
        average_late_minutes: daysLate > 0 ? totalLateMinutes / daysLate : 0,
        late_days_count: daysLate,
        
        attendance_percentage: attendancePercentage * 100,
        punctuality_percentage: punctualityPercentage * 100,
        productivity_score: productivityScore,
        
        attendance_bonus_eligible: attendanceBonusEligible,
        punctuality_bonus_eligible: punctualityBonusEligible,
        
        late_penalty_amount: latePenaltyAmount,
        absence_penalty_amount: absencePenaltyAmount,
        
        overtime_eligible_hours: overtimeEligibleHours,
        overtime_rate_multiplier: overtimeRateMultiplier,
        overtime_pay_amount: 0 // Will be calculated with salary info
      };
      
    } catch (error) {
      console.error('Error getting enhanced attendance summary:', error);
      throw new Error('Failed to get attendance summary');
    }
  }
  
  // Calculate employee payroll components based on attendance and assignments
  async calculatePayrollComponents(
    userId: string, 
    attendanceSummary: EnhancedAttendanceSummary,
    baseSalary: number
  ): Promise<PayrollComponentCalculation[]> {
    try {
      // Get active component assignments for the employee
      const componentAssignments = await sql`
        SELECT 
          eca.id as assignment_id,
          eca.override_amount,
          eca.override_percentage,
          eca.override_conditions,
          pcm.id as component_id,
          pcm.name,
          pcm.code,
          pcm.type,
          pcm.category,
          pcm.calculation_type,
          pcm.fixed_amount,
          pcm.percentage,
          pcm.percentage_base,
          pcm.formula,
          pcm.conditions,
          pcm.is_taxable,
          pcm.is_statutory,
          pcm.effective_from,
          pcm.effective_to
        FROM employee_component_assignments eca
        JOIN payroll_components_master pcm ON eca.component_id = pcm.id
        WHERE eca.user_id = ${userId}
          AND eca.is_active = true
          AND eca.effective_from <= CURRENT_DATE
          AND (eca.effective_to IS NULL OR eca.effective_to >= CURRENT_DATE)
          AND pcm.is_active = true
        ORDER BY pcm.type, pcm.category, pcm.name
      `;
      
      const calculations: PayrollComponentCalculation[] = [];
      
      for (const assignment of componentAssignments) {
        const calculation = await this.calculateSingleComponent(
          assignment, 
          attendanceSummary, 
          baseSalary
        );
        calculations.push(calculation);
      }
      
      // Add automatic components (late penalty, attendance bonus)
      const automaticComponents = await this.calculateAutomaticComponents(
        attendanceSummary, 
        baseSalary
      );
      calculations.push(...automaticComponents);
      
      return calculations;
      
    } catch (error) {
      console.error('Error calculating payroll components:', error);
      throw new Error('Failed to calculate payroll components');
    }
  }
  
  // Helper method to categorize hours into regular and overtime
  private categorizeHours(
    dailyHours: number, 
    settings: any, 
    callback: (regular: number, overtime: number) => void
  ): void {
    const standardHours = settings.default_working_hours_per_day;
    if (dailyHours <= standardHours) {
      callback(dailyHours, 0);
    } else {
      callback(standardHours, dailyHours - standardHours);
    }
  }
  
  // Calculate productivity score based on various factors
  private calculateProductivityScore(
    attendancePercentage: number,
    punctualityPercentage: number,
    totalHours: number,
    expectedHours: number
  ): number {
    const attendanceScore = attendancePercentage * 40; // 40% weight
    const punctualityScore = punctualityPercentage * 30; // 30% weight
    const hoursScore = Math.min(totalHours / expectedHours, 1.2) * 30; // 30% weight, cap at 120%
    
    return Math.round(attendanceScore + punctualityScore + hoursScore);
  }
  
  // Get payroll settings from database
  private async getPayrollSettings(): Promise<any> {
    const settings = await sql`
      SELECT setting_key, setting_value, setting_type
      FROM payroll_settings
      WHERE is_system_setting = true
    `;
    
    const settingsObj: any = {};
    for (const setting of settings) {
      const key = setting.setting_key;
      let value = setting.setting_value;
      
      if (setting.setting_type === 'number') {
        value = parseFloat(value);
      } else if (setting.setting_type === 'boolean') {
        value = value === 'true';
      }
      
      settingsObj[key] = value;
    }
    
    return settingsObj;
  }
  
  // Calculate working days in a month
  private async calculateWorkingDays(year: number, month: number): Promise<any> {
    const startDate = new Date(year, month - 1, 1);
    const endDate = new Date(year, month, 0);
    
    let totalCalendarDays = endDate.getDate();
    let weekends = 0;
    let workingDays = 0;
    
    for (let day = 1; day <= totalCalendarDays; day++) {
      const date = new Date(year, month - 1, day);
      const dayOfWeek = date.getDay();
      
      if (dayOfWeek === 0 || dayOfWeek === 6) { // Sunday or Saturday
        weekends++;
      } else {
        workingDays++;
      }
    }
    
    // TODO: Subtract public holidays from working days
    const publicHolidays = 0; // This should be calculated from a holidays table
    
    return {
      total_calendar_days: totalCalendarDays,
      total_working_days: workingDays - publicHolidays,
      weekends: weekends,
      public_holidays: publicHolidays
    };
  }
  
  // Get fiscal year information
  private async getFiscalYearInfo(year: number, month: number): Promise<any> {
    // This is a simplified version - should integrate with Nepal calendar system
    const fiscalYear = month >= 4 ? `${year}-${(year + 1).toString().slice(-2)}` : `${year - 1}-${year.toString().slice(-2)}`;
    const monthNames = [
      'Baisakh', 'Jestha', 'Ashadh', 'Shrawan', 'Bhadra', 'Ashwin',
      'Kartik', 'Mangsir', 'Poush', 'Magh', 'Falgun', 'Chaitra'
    ];
    
    // Convert to Nepali month (simplified)
    const nepaliMonth = month >= 4 ? monthNames[month - 4] : monthNames[month + 8];
    const nepaliYear = month >= 4 ? year + 57 : year + 56; // Approximate BS year
    
    return {
      fiscal_year: fiscalYear,
      bs_month: `${nepaliMonth} ${nepaliYear}`
    };
  }
  
  // Calculate a single payroll component
  private async calculateSingleComponent(
    assignment: any,
    attendanceSummary: EnhancedAttendanceSummary,
    baseSalary: number
  ): Promise<PayrollComponentCalculation> {
    let calculatedAmount = 0;
    let isApplicable = true;
    const calculationNotes: string[] = [];
    
    // Use override values if available
    const amount = assignment.override_amount || assignment.fixed_amount;
    const percentage = assignment.override_percentage || assignment.percentage;
    
    switch (assignment.calculation_type) {
      case 'fixed':
        calculatedAmount = amount || 0;
        break;
        
      case 'percentage':
        const baseAmount = this.getPercentageBase(assignment.percentage_base, baseSalary, attendanceSummary);
        calculatedAmount = (baseAmount * percentage) / 100;
        calculationNotes.push(`${percentage}% of ${assignment.percentage_base}`);
        break;
        
      case 'formula':
        calculatedAmount = this.evaluateFormula(assignment.formula, baseSalary, attendanceSummary);
        calculationNotes.push(`Formula: ${assignment.formula}`);
        break;
        
      case 'conditional':
        const conditionResult = this.evaluateConditions(assignment.conditions, attendanceSummary);
        isApplicable = conditionResult.applicable;
        calculatedAmount = conditionResult.amount;
        calculationNotes.push(...conditionResult.notes);
        break;
        
      default:
        calculatedAmount = 0;
        isApplicable = false;
        calculationNotes.push('Unknown calculation type');
    }
    
    return {
      component_id: assignment.component_id,
      component_name: assignment.name,
      component_code: assignment.code,
      component_type: assignment.type,
      calculation_type: assignment.calculation_type,
      base_amount: amount,
      percentage: percentage,
      percentage_base: assignment.percentage_base,
      calculated_amount: Math.round(calculatedAmount * 100) / 100, // Round to 2 decimal places
      is_applicable: isApplicable,
      calculation_notes: calculationNotes,
      is_taxable: assignment.is_taxable,
      is_statutory: assignment.is_statutory,
      effective_from: assignment.effective_from,
      effective_to: assignment.effective_to
    };
  }
  
  // Calculate automatic components (late penalty, attendance bonus)
  private async calculateAutomaticComponents(
    attendanceSummary: EnhancedAttendanceSummary,
    baseSalary: number
  ): Promise<PayrollComponentCalculation[]> {
    const components: PayrollComponentCalculation[] = [];
    
    // Late penalty (automatic deduction)
    if (attendanceSummary.late_penalty_amount > 0) {
      components.push({
        component_id: 'auto_late_penalty',
        component_name: 'Late Penalty',
        component_code: 'LATE_PENALTY_AUTO',
        component_type: 'deduction',
        calculation_type: 'formula',
        calculated_amount: attendanceSummary.late_penalty_amount,
        is_applicable: true,
        calculation_notes: [`${attendanceSummary.total_late_minutes} minutes late`],
        is_taxable: false,
        is_statutory: false,
        effective_from: attendanceSummary.ad_month_start,
        effective_to: attendanceSummary.ad_month_end
      });
    }
    
    // Attendance bonus (automatic allowance)
    if (attendanceSummary.attendance_bonus_eligible) {
      const settings = await this.getPayrollSettings();
      components.push({
        component_id: 'auto_attendance_bonus',
        component_name: 'Attendance Bonus',
        component_code: 'ATTENDANCE_BONUS_AUTO',
        component_type: 'allowance',
        calculation_type: 'conditional',
        calculated_amount: settings.attendance_bonus_amount || 2000,
        is_applicable: true,
        calculation_notes: [`${attendanceSummary.attendance_percentage.toFixed(1)}% attendance`],
        is_taxable: false,
        is_statutory: false,
        effective_from: attendanceSummary.ad_month_start,
        effective_to: attendanceSummary.ad_month_end
      });
    }
    
    return components;
  }
  
  // Get base amount for percentage calculations
  private getPercentageBase(base: string, baseSalary: number, attendanceSummary: EnhancedAttendanceSummary): number {
    switch (base) {
      case 'base_salary':
        return baseSalary;
      case 'gross_pay':
        return baseSalary; // Simplified - should include allowances
      case 'total_earnings':
        return baseSalary; // Simplified - should include all earnings
      default:
        return baseSalary;
    }
  }
  
  // Evaluate formula-based calculations
  private evaluateFormula(formula: string, baseSalary: number, attendanceSummary: EnhancedAttendanceSummary): number {
    // This is a simplified formula evaluator
    // In production, use a proper expression parser
    try {
      // Replace variables in formula
      let evaluatedFormula = formula
        .replace(/base_salary/g, baseSalary.toString())
        .replace(/days_late/g, attendanceSummary.days_late.toString())
        .replace(/late_minutes/g, attendanceSummary.total_late_minutes.toString())
        .replace(/overtime_hours/g, attendanceSummary.overtime_hours.toString());
      
      // Basic math evaluation (be careful with eval in production)
      return eval(evaluatedFormula) || 0;
    } catch (error) {
      console.error('Error evaluating formula:', error);
      return 0;
    }
  }
  
  // Evaluate conditional calculations
  private evaluateConditions(conditions: any, attendanceSummary: EnhancedAttendanceSummary): any {
    // This should be a proper condition evaluator
    // For now, return a simple result
    return {
      applicable: true,
      amount: 0,
      notes: ['Conditional calculation not implemented']
    };
  }
}

export const enhancedAttendancePayrollIntegration = new EnhancedAttendancePayrollIntegration();
