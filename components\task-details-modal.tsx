"use client"

import { useState, useEffect } from "react"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, Dialog<PERSON>eader, DialogTitle } from "@/components/ui/dialog"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Textarea } from "@/components/ui/textarea"
import { Label } from "@/components/ui/label"
import { Badge } from "@/components/ui/badge"
import { Avatar, AvatarFallback } from "@/components/ui/avatar"
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Separator } from "@/components/ui/separator"
import { ScrollArea } from "@/components/ui/scroll-area"
import { 
  Edit, 
  Save, 
  X, 
  Users, 
  Paperclip, 
  CheckSquare, 
  Calendar, 
  Flag, 
  User,
  Plus,
  Trash2,
  Clock,
  FileText
} from "lucide-react"
import { useTask, useUpdateTask, useTaskAssignments, useSubTasks, useCreateSubTask, useUpdateSubTask } from "@/hooks/use-tasks"
import { TaskAttachments } from "@/components/task-attachments"
import { MultiUserAssignment } from "@/components/multi-user-assignment"
import { useAuth } from "@/components/auth-provider"
import { formatDistanceToNow, format } from "date-fns"
import { toast } from "@/hooks/use-toast"

interface TaskDetailsModalProps {
  isOpen: boolean
  onClose: () => void
  taskId: string | null
}

interface TaskAssignment {
  id: string
  full_name: string
  email: string
  employee_id?: string
  department?: string
  position?: string
  is_primary: boolean
  assigned_at?: string
  assigned_by_name?: string
}

interface SubTask {
  id: string
  title: string
  description?: string
  status: "todo" | "in_progress" | "completed" | "cancelled"
  assigned_to?: string
  assigned_to_name?: string
  due_date?: string
  position: number
  created_at: string
  updated_at: string
  completed_at?: string
}

export function TaskDetailsModal({ isOpen, onClose, taskId }: TaskDetailsModalProps) {
  const { user } = useAuth()
  const [isEditing, setIsEditing] = useState(false)
  const [editedTitle, setEditedTitle] = useState("")
  const [editedDescription, setEditedDescription] = useState("")
  const [editedPriority, setEditedPriority] = useState("medium")
  const [editedDueDate, setEditedDueDate] = useState("")

  // Sub-task creation state
  const [isCreatingSubTask, setIsCreatingSubTask] = useState(false)
  const [newSubTaskTitle, setNewSubTaskTitle] = useState("")
  const [newSubTaskDescription, setNewSubTaskDescription] = useState("")

  // Fetch task data
  const { data: taskResponse, isLoading: taskLoading, refetch: refetchTask } = useTask(taskId || "")
  const { data: assignmentsResponse, refetch: refetchAssignments } = useTaskAssignments(taskId || "")
  const { data: subTasksResponse, refetch: refetchSubTasks } = useSubTasks(taskId || "")
  const updateTaskMutation = useUpdateTask()
  const createSubTaskMutation = useCreateSubTask()
  const updateSubTaskMutation = useUpdateSubTask()

  const task = taskResponse?.data
  const assignments = assignmentsResponse?.data || []
  const subTasks = subTasksResponse?.data || []

  // Initialize edit form when task loads
  useEffect(() => {
    if (task && isEditing) {
      setEditedTitle(task.title || "")
      setEditedDescription(task.description || "")
      setEditedPriority(task.priority || "medium")
      setEditedDueDate(task.due_date ? task.due_date.split('T')[0] : "")
    }
  }, [task, isEditing])

  const handleSaveEdit = async () => {
    if (!task || !taskId) return

    try {
      await updateTaskMutation.mutateAsync({
        id: taskId,
        data: {
          title: editedTitle,
          description: editedDescription,
          priority: editedPriority,
          due_date: editedDueDate ? new Date(editedDueDate).toISOString() : undefined,
        }
      })
      setIsEditing(false)
      toast({
        title: "Success",
        description: "Task updated successfully",
      })
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to update task",
        variant: "destructive",
      })
    }
  }

  const handleCancelEdit = () => {
    setIsEditing(false)
    if (task) {
      setEditedTitle(task.title || "")
      setEditedDescription(task.description || "")
      setEditedPriority(task.priority || "medium")
      setEditedDueDate(task.due_date ? task.due_date.split('T')[0] : "")
    }
  }

  const getPriorityColor = (priority: string) => {
    const colors = {
      low: "bg-blue-100 text-blue-800 border-blue-200",
      medium: "bg-orange-100 text-orange-800 border-orange-200", 
      high: "bg-red-100 text-red-800 border-red-200",
      urgent: "bg-purple-100 text-purple-800 border-purple-200"
    }
    return colors[priority as keyof typeof colors] || colors.medium
  }

  const getStatusColor = (status: string) => {
    const colors = {
      todo: "bg-gray-100 text-gray-800",
      in_progress: "bg-blue-100 text-blue-800",
      completed: "bg-green-100 text-green-800",
      cancelled: "bg-red-100 text-red-800"
    }
    return colors[status as keyof typeof colors] || colors.todo
  }

  const handleCreateSubTask = async () => {
    if (!newSubTaskTitle.trim() || !taskId) return

    try {
      await createSubTaskMutation.mutateAsync({
        taskId,
        data: {
          title: newSubTaskTitle.trim(),
          description: newSubTaskDescription.trim() || undefined,
          assigned_to: user?.id, // Assign to current user by default
        }
      })

      // Reset form
      setNewSubTaskTitle("")
      setNewSubTaskDescription("")
      setIsCreatingSubTask(false)
      refetchSubTasks()

      toast({
        title: "Success",
        description: "Sub-task created successfully",
      })
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to create sub-task",
        variant: "destructive",
      })
    }
  }

  const handleSubTaskStatusChange = async (subtaskId: string, newStatus: string) => {
    if (!taskId) return

    try {
      await updateSubTaskMutation.mutateAsync({
        taskId,
        subtaskId,
        data: {
          status: newStatus as "todo" | "in_progress" | "completed" | "cancelled"
        }
      })

      refetchSubTasks()

      toast({
        title: "Success",
        description: "Sub-task status updated successfully",
      })
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to update sub-task status",
        variant: "destructive",
      })
    }
  }

  if (!isOpen || !taskId) return null

  if (taskLoading) {
    return (
      <Dialog open={isOpen} onOpenChange={onClose}>
        <DialogContent className="sm:max-w-[900px] w-[95vw] max-h-[95vh] h-[95vh]">
          <div className="flex items-center justify-center h-full">
            <div className="text-center">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900 mx-auto mb-4"></div>
              <p className="text-gray-500">Loading task details...</p>
            </div>
          </div>
        </DialogContent>
      </Dialog>
    )
  }

  if (!task) {
    return (
      <Dialog open={isOpen} onOpenChange={onClose}>
        <DialogContent className="sm:max-w-[900px] w-[95vw] max-h-[95vh] h-[95vh]">
          <div className="flex items-center justify-center h-full">
            <div className="text-center">
              <p className="text-gray-500">Task not found</p>
              <Button onClick={onClose} className="mt-4">Close</Button>
            </div>
          </div>
        </DialogContent>
      </Dialog>
    )
  }

  const canEdit = user?.role === "admin" || user?.role === "hr_manager" ||
                 task.assigned_to === user?.id || task.assigned_by === user?.id ||
                 assignments.some(a => a.id === user?.id)

  // Handle assignments refresh
  const handleAssignmentsChange = () => {
    refetchAssignments()
    refetchTask()
  }

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-[900px] w-[95vw] max-h-[95vh] h-[95vh] overflow-hidden flex flex-col p-0">
        <DialogHeader className="flex-shrink-0 px-6 pt-6 pb-4 border-b">
          <div className="flex items-center justify-between">
            <DialogTitle className="flex items-center gap-2">
              <FileText className="h-5 w-5" />
              {isEditing ? "Edit Task" : "Task Details"}
            </DialogTitle>
            <div className="flex items-center gap-2">
              {canEdit && (
                <>
                  {isEditing ? (
                    <>
                      <Button size="sm" onClick={handleSaveEdit} disabled={updateTaskMutation.isPending}>
                        <Save className="h-4 w-4 mr-1" />
                        Save
                      </Button>
                      <Button size="sm" variant="outline" onClick={handleCancelEdit}>
                        <X className="h-4 w-4 mr-1" />
                        Cancel
                      </Button>
                    </>
                  ) : (
                    <Button size="sm" variant="outline" onClick={() => setIsEditing(true)}>
                      <Edit className="h-4 w-4 mr-1" />
                      Edit
                    </Button>
                  )}
                </>
              )}
            </div>
          </div>
        </DialogHeader>

        <div className="flex-1 overflow-hidden min-h-0">
          <ScrollArea className="h-full">
            <div className="space-y-6 px-6 py-4 pb-6">
              {/* Task Header */}
              <div className="space-y-4">
                {isEditing ? (
                  <div className="space-y-4">
                    <div>
                      <Label htmlFor="title">Title</Label>
                      <Input
                        id="title"
                        value={editedTitle}
                        onChange={(e) => setEditedTitle(e.target.value)}
                        className="text-lg font-semibold"
                      />
                    </div>
                    <div>
                      <Label htmlFor="description">Description</Label>
                      <Textarea
                        id="description"
                        value={editedDescription}
                        onChange={(e) => setEditedDescription(e.target.value)}
                        rows={3}
                      />
                    </div>
                    <div className="grid grid-cols-2 gap-4">
                      <div>
                        <Label htmlFor="priority">Priority</Label>
                        <Select value={editedPriority} onValueChange={setEditedPriority}>
                          <SelectTrigger>
                            <SelectValue />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="low">Low</SelectItem>
                            <SelectItem value="medium">Medium</SelectItem>
                            <SelectItem value="high">High</SelectItem>
                            <SelectItem value="urgent">Urgent</SelectItem>
                          </SelectContent>
                        </Select>
                      </div>
                      <div>
                        <Label htmlFor="due_date">Due Date</Label>
                        <Input
                          id="due_date"
                          type="date"
                          value={editedDueDate}
                          onChange={(e) => setEditedDueDate(e.target.value)}
                        />
                      </div>
                    </div>
                  </div>
                ) : (
                  <div className="space-y-4">
                    <div className="flex items-start justify-between">
                      <h2 className="text-2xl font-bold">{task.title}</h2>
                      <div className="flex items-center gap-2">
                        <Badge className={getPriorityColor(task.priority)}>
                          <Flag className="h-3 w-3 mr-1" />
                          {task.priority}
                        </Badge>
                        <Badge className={getStatusColor(task.status)}>
                          {task.status.replace('_', ' ')}
                        </Badge>
                      </div>
                    </div>
                    
                    {task.description && (
                      <p className="text-gray-600 dark:text-gray-300">{task.description}</p>
                    )}

                    <div className="grid grid-cols-2 gap-4 text-sm text-gray-500">
                      <div className="flex items-center gap-2">
                        <Calendar className="h-4 w-4" />
                        <span>
                          Created {formatDistanceToNow(new Date(task.created_at), { addSuffix: true })}
                        </span>
                      </div>
                      {task.due_date && (
                        <div className="flex items-center gap-2">
                          <Clock className="h-4 w-4" />
                          <span>
                            Due {format(new Date(task.due_date), 'MMM d, yyyy')}
                          </span>
                        </div>
                      )}
                    </div>
                  </div>
                )}
              </div>

              <Separator />

              {/* Tabs for different sections */}
              <Tabs defaultValue="assignments" className="w-full">
                <TabsList className="grid w-full grid-cols-3">
                  <TabsTrigger value="assignments" className="flex items-center gap-2">
                    <Users className="h-4 w-4" />
                    Assignments ({assignments.length})
                  </TabsTrigger>
                  <TabsTrigger value="subtasks" className="flex items-center gap-2">
                    <CheckSquare className="h-4 w-4" />
                    Sub-tasks ({subTasks.length})
                  </TabsTrigger>
                  <TabsTrigger value="attachments" className="flex items-center gap-2">
                    <Paperclip className="h-4 w-4" />
                    Files ({task.attachment_count || 0})
                  </TabsTrigger>
                </TabsList>

                <TabsContent value="assignments" className="space-y-4">
                  <MultiUserAssignment
                    taskId={taskId!}
                    assignments={assignments}
                    canEdit={canEdit}
                    onAssignmentsChange={handleAssignmentsChange}
                  />
                </TabsContent>

                <TabsContent value="subtasks" className="space-y-4">
                  <Card>
                    <CardHeader>
                      <CardTitle className="flex items-center justify-between">
                        <div className="flex items-center gap-2">
                          <CheckSquare className="h-5 w-5" />
                          Sub-tasks ({subTasks.length})
                        </div>
                        <Button
                          size="sm"
                          onClick={() => setIsCreatingSubTask(true)}
                          className="flex items-center gap-2"
                        >
                          <Plus className="h-4 w-4" />
                          Add Sub-task
                        </Button>
                      </CardTitle>
                    </CardHeader>
                    <CardContent className="space-y-4">
                      {/* Sub-task Creation Form */}
                      {isCreatingSubTask && (
                        <div className="p-4 border rounded-lg bg-gray-50 dark:bg-gray-800 space-y-3">
                          <div className="space-y-2">
                            <Label htmlFor="subtask-title">Sub-task Title</Label>
                            <Input
                              id="subtask-title"
                              value={newSubTaskTitle}
                              onChange={(e) => setNewSubTaskTitle(e.target.value)}
                              placeholder="Enter sub-task title..."
                              autoFocus
                            />
                          </div>
                          <div className="space-y-2">
                            <Label htmlFor="subtask-description">Description (Optional)</Label>
                            <Textarea
                              id="subtask-description"
                              value={newSubTaskDescription}
                              onChange={(e) => setNewSubTaskDescription(e.target.value)}
                              placeholder="Enter sub-task description..."
                              rows={2}
                            />
                          </div>
                          <div className="flex items-center gap-2">
                            <Button
                              size="sm"
                              onClick={handleCreateSubTask}
                              disabled={!newSubTaskTitle.trim() || createSubTaskMutation.isPending}
                            >
                              {createSubTaskMutation.isPending ? "Creating..." : "Create Sub-task"}
                            </Button>
                            <Button
                              size="sm"
                              variant="outline"
                              onClick={() => {
                                setIsCreatingSubTask(false)
                                setNewSubTaskTitle("")
                                setNewSubTaskDescription("")
                              }}
                            >
                              Cancel
                            </Button>
                          </div>
                        </div>
                      )}

                      {/* Sub-tasks List */}
                      {subTasks.length > 0 ? (
                        <div className="space-y-3">
                          {subTasks.map((subtask: SubTask) => (
                            <div key={subtask.id} className="flex items-center gap-3 p-3 border rounded-lg hover:bg-gray-50 dark:hover:bg-gray-800">
                              {/* Completion Checkbox */}
                              <input
                                type="checkbox"
                                checked={subtask.status === 'completed'}
                                onChange={(e) => {
                                  const newStatus = e.target.checked ? 'completed' : 'todo'
                                  handleSubTaskStatusChange(subtask.id, newStatus)
                                }}
                                className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                              />

                              <div className="flex-1">
                                <div className="flex items-center gap-2">
                                  <span className={`font-medium ${subtask.status === 'completed' ? 'line-through text-gray-500' : ''}`}>
                                    {subtask.title}
                                  </span>
                                  <Badge className={getStatusColor(subtask.status)} variant="outline">
                                    {subtask.status.replace('_', ' ')}
                                  </Badge>
                                </div>
                                {subtask.description && (
                                  <p className="text-sm text-gray-500 mt-1">{subtask.description}</p>
                                )}
                                <div className="flex items-center gap-4 mt-1 text-xs text-gray-400">
                                  {subtask.assigned_to_name && (
                                    <span>Assigned to {subtask.assigned_to_name}</span>
                                  )}
                                  <span>
                                    {subtask.completed_at ?
                                      `Completed ${formatDistanceToNow(new Date(subtask.completed_at), { addSuffix: true })}` :
                                      `Created ${formatDistanceToNow(new Date(subtask.created_at), { addSuffix: true })}`
                                    }
                                  </span>
                                </div>
                              </div>

                              {/* Status Change Dropdown */}
                              <Select
                                value={subtask.status}
                                onValueChange={(value) => handleSubTaskStatusChange(subtask.id, value)}
                              >
                                <SelectTrigger className="w-32">
                                  <SelectValue />
                                </SelectTrigger>
                                <SelectContent>
                                  <SelectItem value="todo">To Do</SelectItem>
                                  <SelectItem value="in_progress">In Progress</SelectItem>
                                  <SelectItem value="completed">Completed</SelectItem>
                                  <SelectItem value="cancelled">Cancelled</SelectItem>
                                </SelectContent>
                              </Select>
                            </div>
                          ))}
                        </div>
                      ) : (
                        <div className="text-center py-8 text-gray-500">
                          <CheckSquare className="h-12 w-12 mx-auto mb-4 opacity-50" />
                          <p>No sub-tasks for this task</p>
                          <p className="text-sm">Click "Add Sub-task" to create your first sub-task</p>
                        </div>
                      )}
                    </CardContent>
                  </Card>
                </TabsContent>

                <TabsContent value="attachments" className="space-y-4">
                  <TaskAttachments taskId={taskId} />
                </TabsContent>
              </Tabs>
            </div>
          </ScrollArea>
        </div>
      </DialogContent>
    </Dialog>
  )
}
