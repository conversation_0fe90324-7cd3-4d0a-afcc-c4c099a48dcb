"use client"
import React, { useState } from "react"
import { KanbanColumn } from "@/components/kanban-column"
import { TaskDetailsModal } from "@/components/task-details-modal"
import { useTasks, useUpdateTaskStatus } from "@/hooks/use-tasks"
import { Loader2 } from "lucide-react"

interface Task {
  id: string
  title: string
  description?: string
  status: "todo" | "in_progress" | "completed" | "cancelled"
  priority: "low" | "medium" | "high" | "urgent"
  assigned_to?: string
  assigned_by: string
  due_date?: string
  project_id?: string
  created_at: string
  updated_at: string
  // Extended fields from joins
  assigned_to_name?: string
  assigned_to_email?: string
  project_name?: string
  project_color?: string
  comment_count?: number
  attachment_count?: number
  // New fields for enhanced functionality
  assignments?: Array<{
    id: string
    full_name: string
    email: string
    is_primary: boolean
  }>
  subtask_count?: number
  completed_subtasks?: number
}

interface KanbanBoardProps {
  onEditTask?: (task: Task) => void
  onDeleteTask?: (taskId: string) => void
  isAdmin?: boolean
  currentUserId?: string
  filters?: {
    status?: string
    priority?: string
    assigned_to?: string
    project_id?: string
    search?: string
  }
  realTimeUpdates?: boolean
}

export function KanbanBoard({
  onEditTask = () => {},
  onDeleteTask = () => {},
  isAdmin = false,
  currentUserId = "",
  filters = {},
  realTimeUpdates = true,
}: KanbanBoardProps) {
  const [selectedTaskId, setSelectedTaskId] = useState<string | null>(null)

  // Fetch tasks using React Query with optional real-time updates
  const { data: tasksResponse, isLoading, error } = useTasks(filters, { realTime: realTimeUpdates })
  const updateTaskStatusMutation = useUpdateTaskStatus()

  // Handle task status updates (drag and drop)
  const handleUpdateTaskStatus = (taskId: string, newStatus: string) => {
    updateTaskStatusMutation.mutate({ id: taskId, status: newStatus })
  }

  // Show loading state
  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-64 bg-gradient-to-br from-purple-50 to-pink-50 dark:from-purple-900/10 dark:to-pink-900/5 rounded-lg">
        <div className="flex items-center space-x-2">
          <Loader2 className="h-6 w-6 animate-spin" />
          <span>Loading tasks...</span>
        </div>
      </div>
    )
  }

  // Show error state
  if (error) {
    return (
      <div className="flex items-center justify-center h-64 bg-gradient-to-br from-purple-50 to-pink-50 dark:from-purple-900/10 dark:to-pink-900/5 rounded-lg">
        <div className="text-center">
          <p className="text-red-600 dark:text-red-400">Failed to load tasks</p>
          <p className="text-sm text-gray-500 mt-1">Please try refreshing the page</p>
        </div>
      </div>
    )
  }

  // Get tasks from response with defensive programming
  const tasksArray = React.useMemo(() => {
    // Handle different possible response structures
    if (!tasksResponse) return []

    // If tasksResponse is already an array (direct response)
    if (Array.isArray(tasksResponse)) return tasksResponse

    // If tasksResponse has data.tasks structure (API wrapper)
    if (tasksResponse.data && Array.isArray(tasksResponse.data.tasks)) {
      return tasksResponse.data.tasks
    }

    // If tasksResponse has tasks directly
    if (Array.isArray(tasksResponse.tasks)) {
      return tasksResponse.tasks
    }

    // If tasksResponse.data is an array
    if (Array.isArray(tasksResponse.data)) {
      return tasksResponse.data
    }

    // Fallback to empty array
    return []
  }, [tasksResponse])

  // Filter tasks by status - now guaranteed to be an array
  const todoTasks = tasksArray.filter((task: Task) => task.status === "todo")
  const inProgressTasks = tasksArray.filter((task: Task) => task.status === "in_progress")
  const doneTasks = tasksArray.filter((task: Task) => task.status === "completed")

  // If there are no tasks at all, show a message
  if (tasksArray.length === 0) {
    return (
      <div className="flex items-center justify-center h-40 bg-gradient-to-br from-purple-50 to-pink-50 dark:from-purple-900/10 dark:to-pink-900/5 p-4 rounded-lg">
        <p className="text-gray-500 dark:text-gray-400">No tasks available. Create a new task to get started.</p>
      </div>
    )
  }

  return (
    <>
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4 bg-gradient-to-br from-purple-50 to-pink-50 dark:from-purple-900/10 dark:to-pink-900/5 p-4 pb-2 rounded-lg">
        <KanbanColumn
          title="To Do"
          tasks={todoTasks}
          onEditTask={onEditTask}
          onDeleteTask={onDeleteTask}
          onTaskClick={(taskId) => setSelectedTaskId(taskId)}
          onUpdateTaskStatus={handleUpdateTaskStatus}
          isAdmin={isAdmin}
          currentUserId={currentUserId}
          columnId="todo"
          isUpdating={updateTaskStatusMutation.isPending}
        />
        <KanbanColumn
          title="In Progress"
          tasks={inProgressTasks}
          onEditTask={onEditTask}
          onDeleteTask={onDeleteTask}
          onTaskClick={(taskId) => setSelectedTaskId(taskId)}
          onUpdateTaskStatus={handleUpdateTaskStatus}
          isAdmin={isAdmin}
          currentUserId={currentUserId}
          columnId="in_progress"
          isUpdating={updateTaskStatusMutation.isPending}
        />
        <KanbanColumn
          title="Done"
          tasks={doneTasks}
          onEditTask={onEditTask}
          onDeleteTask={onDeleteTask}
          onTaskClick={(taskId) => setSelectedTaskId(taskId)}
          onUpdateTaskStatus={handleUpdateTaskStatus}
          isAdmin={isAdmin}
          currentUserId={currentUserId}
          columnId="completed"
          isUpdating={updateTaskStatusMutation.isPending}
        />
      </div>

      {/* Task Details Modal */}
      <TaskDetailsModal
        isOpen={!!selectedTaskId}
        onClose={() => setSelectedTaskId(null)}
        taskId={selectedTaskId}
      />
    </>
  )
}
