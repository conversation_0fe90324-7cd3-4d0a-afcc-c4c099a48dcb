"use client"

import { useState } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Badge } from "@/components/ui/badge"
import { Plus, Search, MoreHorizontal, Phone, Mail, Calendar, User } from "lucide-react"
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from "@/components/ui/dropdown-menu"
import Link from "next/link"

// Mock data for leads
const mockLeads = [
  {
    id: 1,
    name: "<PERSON>",
    email: "<EMAIL>",
    phone: "+****************",
    company: "Tech Solutions Inc",
    status: "new",
    value: "$15,000",
    source: "Website",
    lastContact: "2024-01-15",
    notes: "Interested in enterprise package",
  },
  {
    id: 2,
    name: "<PERSON>",
    email: "<EMAIL>",
    phone: "+****************",
    company: "Marketing Pro",
    status: "qualified",
    value: "$8,500",
    source: "Referral",
    lastContact: "2024-01-14",
    notes: "Ready to schedule demo",
  },
  {
    id: 3,
    name: "<PERSON> <PERSON>",
    email: "<EMAIL>",
    phone: "+****************",
    company: "Brown & Associates",
    status: "contacted",
    value: "$22,000",
    source: "LinkedIn",
    lastContact: "2024-01-13",
    notes: "Needs custom solution",
  },
  {
    id: 4,
    name: "Emily <PERSON>",
    email: "<EMAIL>",
    phone: "+****************",
    company: "StartupXYZ",
    status: "proposal",
    value: "$12,000",
    source: "Cold Email",
    lastContact: "2024-01-12",
    notes: "Reviewing proposal",
  },
]

const getStatusColor = (status: string) => {
  switch (status) {
    case "new":
      return "bg-blue-100 text-blue-800 dark:bg-blue-900/20 dark:text-blue-300"
    case "qualified":
      return "bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-300"
    case "contacted":
      return "bg-warning/10 text-warning-foreground border border-warning/20"
    case "proposal":
      return "bg-purple-100 text-purple-800 dark:bg-purple-900/20 dark:text-purple-300"
    default:
      return "bg-gray-100 text-gray-800 dark:bg-gray-900/20 dark:text-gray-300"
  }
}

export default function LeadsPage() {
  const [searchTerm, setSearchTerm] = useState("")
  const [selectedStatus, setSelectedStatus] = useState("all")

  const filteredLeads = mockLeads.filter((lead) => {
    const matchesSearch =
      lead.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      lead.company.toLowerCase().includes(searchTerm.toLowerCase()) ||
      lead.email.toLowerCase().includes(searchTerm.toLowerCase())
    const matchesStatus = selectedStatus === "all" || lead.status === selectedStatus
    return matchesSearch && matchesStatus
  })

  const leadStats = {
    total: mockLeads.length,
    new: mockLeads.filter((lead) => lead.status === "new").length,
    qualified: mockLeads.filter((lead) => lead.status === "qualified").length,
    contacted: mockLeads.filter((lead) => lead.status === "contacted").length,
    proposal: mockLeads.filter((lead) => lead.status === "proposal").length,
  }

  return (
    <div className="container mx-auto px-4 py-6 pb-20 md:pb-6">
      <div className="flex flex-col md:flex-row md:items-center justify-between mb-6">
        <div>
          <h1 className="text-2xl font-bold text-gray-900 dark:text-white">Lead Management</h1>
          <p className="text-gray-600 dark:text-gray-400">Track and manage your potential customers</p>
        </div>
        <Link href="/leads/add">
          <Button className="bg-exobank-green hover:bg-exobank-green/90 text-white mt-4 md:mt-0">
            <Plus className="h-4 w-4 mr-2" />
            Add Lead
          </Button>
        </Link>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-2 md:grid-cols-5 gap-4 mb-6">
        <Card>
          <CardContent className="p-4">
            <div className="text-center">
              <div className="text-2xl font-bold text-gray-900 dark:text-white">{leadStats.total}</div>
              <div className="text-sm text-gray-500 dark:text-gray-400">Total Leads</div>
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4">
            <div className="text-center">
              <div className="text-2xl font-bold text-blue-600">{leadStats.new}</div>
              <div className="text-sm text-gray-500 dark:text-gray-400">New</div>
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4">
            <div className="text-center">
              <div className="text-2xl font-bold text-green-600">{leadStats.qualified}</div>
              <div className="text-sm text-gray-500 dark:text-gray-400">Qualified</div>
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4">
            <div className="text-center">
              <div className="text-2xl font-bold text-yellow-600">{leadStats.contacted}</div>
              <div className="text-sm text-gray-500 dark:text-gray-400">Contacted</div>
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4">
            <div className="text-center">
              <div className="text-2xl font-bold text-purple-600">{leadStats.proposal}</div>
              <div className="text-sm text-gray-500 dark:text-gray-400">Proposal</div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Search and Filter */}
      <div className="flex flex-col md:flex-row gap-4 mb-6">
        <div className="relative flex-1">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
          <Input
            placeholder="Search leads..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="pl-10"
          />
        </div>
        <div className="flex gap-2">
          <Button
            variant={selectedStatus === "all" ? "default" : "outline"}
            onClick={() => setSelectedStatus("all")}
            size="sm"
          >
            All
          </Button>
          <Button
            variant={selectedStatus === "new" ? "default" : "outline"}
            onClick={() => setSelectedStatus("new")}
            size="sm"
          >
            New
          </Button>
          <Button
            variant={selectedStatus === "qualified" ? "default" : "outline"}
            onClick={() => setSelectedStatus("qualified")}
            size="sm"
          >
            Qualified
          </Button>
          <Button
            variant={selectedStatus === "contacted" ? "default" : "outline"}
            onClick={() => setSelectedStatus("contacted")}
            size="sm"
          >
            Contacted
          </Button>
        </div>
      </div>

      {/* Leads List */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
        {filteredLeads.map((lead) => (
          <Card key={lead.id} className="hover:shadow-md transition-shadow">
            <CardHeader className="pb-2">
              <div className="flex items-start justify-between">
                <div>
                  <CardTitle className="text-lg">{lead.name}</CardTitle>
                  <CardDescription>{lead.company}</CardDescription>
                </div>
                <DropdownMenu>
                  <DropdownMenuTrigger asChild>
                    <Button variant="ghost" size="icon">
                      <MoreHorizontal className="h-4 w-4" />
                    </Button>
                  </DropdownMenuTrigger>
                  <DropdownMenuContent align="end">
                    <DropdownMenuItem>Edit Lead</DropdownMenuItem>
                    <DropdownMenuItem>Send Email</DropdownMenuItem>
                    <DropdownMenuItem>Schedule Call</DropdownMenuItem>
                    <DropdownMenuItem className="text-red-600">Delete</DropdownMenuItem>
                  </DropdownMenuContent>
                </DropdownMenu>
              </div>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                <div className="flex items-center justify-between">
                  <Badge className={getStatusColor(lead.status)}>
                    {lead.status.charAt(0).toUpperCase() + lead.status.slice(1)}
                  </Badge>
                  <span className="font-semibold text-green-600">{lead.value}</span>
                </div>

                <div className="space-y-2 text-sm">
                  <div className="flex items-center gap-2">
                    <Mail className="h-4 w-4 text-gray-400" />
                    <span className="text-gray-600 dark:text-gray-300">{lead.email}</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <Phone className="h-4 w-4 text-gray-400" />
                    <span className="text-gray-600 dark:text-gray-300">{lead.phone}</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <Calendar className="h-4 w-4 text-gray-400" />
                    <span className="text-gray-600 dark:text-gray-300">Last contact: {lead.lastContact}</span>
                  </div>
                </div>

                <div className="pt-2 border-t">
                  <p className="text-sm text-gray-600 dark:text-gray-300">{lead.notes}</p>
                </div>

                <div className="flex gap-2 pt-2">
                  <Button size="sm" variant="outline" className="flex-1 bg-transparent">
                    <Phone className="h-4 w-4 mr-1" />
                    Call
                  </Button>
                  <Button size="sm" variant="outline" className="flex-1 bg-transparent">
                    <Mail className="h-4 w-4 mr-1" />
                    Email
                  </Button>
                </div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      {filteredLeads.length === 0 && (
        <div className="text-center py-12">
          <User className="h-12 w-12 text-gray-400 mx-auto mb-4" />
          <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2">No leads found</h3>
          <p className="text-gray-500 dark:text-gray-400 mb-4">
            {searchTerm ? "Try adjusting your search terms" : "Get started by adding your first lead"}
          </p>
          <Link href="/leads/add">
            <Button className="bg-exobank-green hover:bg-exobank-green/90 text-white">
              <Plus className="h-4 w-4 mr-2" />
              Add Lead
            </Button>
          </Link>
        </div>
      )}
    </div>
  )
}
