const { neon } = require('@neondatabase/serverless');

// Load environment variables
require('dotenv').config({ path: '.env.local' });

const sql = neon(process.env.DATABASE_URL);

async function testSubTaskSystem() {
  try {
    console.log('🧪 Testing Sub-task Management System...\n');

    // 1. Get test users and tasks
    console.log('1. Getting test data...');
    const users = await sql`
      SELECT id, full_name, email, role 
      FROM users 
      WHERE is_active = true 
      ORDER BY created_at 
      LIMIT 3
    `;
    
    const tasks = await sql`
      SELECT id, title, assigned_to, assigned_by 
      FROM tasks 
      ORDER BY created_at DESC 
      LIMIT 2
    `;
    
    if (users.length < 2 || tasks.length < 1) {
      console.log('❌ Need at least 2 users and 1 task to test sub-tasks');
      return;
    }
    
    console.log(`✅ Found ${users.length} users and ${tasks.length} tasks`);
    const testTask = tasks[0];
    console.log(`📝 Using task: "${testTask.title}" for testing`);

    // 2. Clear existing sub-tasks for clean testing
    console.log('\n2. Clearing existing sub-tasks...');
    await sql`DELETE FROM sub_tasks WHERE parent_task_id = ${testTask.id}`;
    console.log('✅ Cleared existing sub-tasks');

    // 3. Test creating sub-tasks
    console.log('\n3. Testing sub-task creation...');
    
    const subTasksToCreate = [
      {
        title: 'Research and Analysis',
        description: 'Conduct initial research and analysis for the task',
        assigned_to: users[0].id,
        position: 1
      },
      {
        title: 'Design and Planning',
        description: 'Create design mockups and project plan',
        assigned_to: users[1].id,
        position: 2
      },
      {
        title: 'Implementation',
        description: 'Implement the solution based on design',
        assigned_to: users[0].id,
        position: 3
      },
      {
        title: 'Testing and Review',
        description: 'Test the implementation and conduct code review',
        assigned_to: users[1].id,
        position: 4
      }
    ];

    const createdSubTasks = [];
    
    for (const subTaskData of subTasksToCreate) {
      const result = await sql`
        INSERT INTO sub_tasks (
          parent_task_id, title, description, assigned_to, assigned_by, position, status
        )
        VALUES (
          ${testTask.id},
          ${subTaskData.title},
          ${subTaskData.description},
          ${subTaskData.assigned_to},
          ${testTask.assigned_by},
          ${subTaskData.position},
          'todo'
        )
        RETURNING *
      `;
      
      createdSubTasks.push(result[0]);
      console.log(`   ✅ Created: "${subTaskData.title}"`);
    }

    // 4. Test retrieving sub-tasks with user details
    console.log('\n4. Testing sub-task retrieval...');
    
    const subTasksWithDetails = await sql`
      SELECT 
        st.*,
        assigned_user.full_name as assigned_to_name,
        assigned_user.email as assigned_to_email,
        assigned_user.employee_id as assigned_to_employee_id,
        assigned_by_user.full_name as assigned_by_name
      FROM sub_tasks st
      LEFT JOIN users assigned_user ON st.assigned_to = assigned_user.id
      LEFT JOIN users assigned_by_user ON st.assigned_by = assigned_by_user.id
      WHERE st.parent_task_id = ${testTask.id}
      ORDER BY st.position ASC, st.created_at ASC
    `;

    console.log(`✅ Retrieved ${subTasksWithDetails.length} sub-tasks:`);
    subTasksWithDetails.forEach((subTask, index) => {
      console.log(`   ${index + 1}. ${subTask.title}`);
      console.log(`      Status: ${subTask.status}, Position: ${subTask.position}`);
      console.log(`      Assigned to: ${subTask.assigned_to_name} (${subTask.assigned_to_email})`);
      console.log(`      Created by: ${subTask.assigned_by_name}`);
      console.log(`      Description: ${subTask.description}`);
    });

    // 5. Test updating sub-task status
    console.log('\n5. Testing sub-task status updates...');
    
    const firstSubTask = createdSubTasks[0];
    const secondSubTask = createdSubTasks[1];
    
    // Update first sub-task to in_progress
    await sql`
      UPDATE sub_tasks 
      SET status = 'in_progress', updated_at = NOW()
      WHERE id = ${firstSubTask.id}
    `;
    console.log(`   ✅ Updated "${firstSubTask.title}" to in_progress`);
    
    // Complete second sub-task
    await sql`
      UPDATE sub_tasks 
      SET status = 'completed', completed_at = NOW(), updated_at = NOW()
      WHERE id = ${secondSubTask.id}
    `;
    console.log(`   ✅ Completed "${secondSubTask.title}"`);

    // 6. Test parent task statistics calculation
    console.log('\n6. Testing parent task statistics...');
    
    const taskStats = await sql`
      SELECT 
        t.*,
        (
          SELECT COUNT(*)
          FROM sub_tasks st
          WHERE st.parent_task_id = t.id
        ) as total_subtasks,
        (
          SELECT COUNT(*)
          FROM sub_tasks st
          WHERE st.parent_task_id = t.id AND st.status = 'completed'
        ) as completed_subtasks,
        (
          SELECT ROUND(
            (COUNT(*) FILTER (WHERE st.status = 'completed') * 100.0) / 
            NULLIF(COUNT(*), 0), 2
          )
          FROM sub_tasks st
          WHERE st.parent_task_id = t.id
        ) as completion_percentage
      FROM tasks t
      WHERE t.id = ${testTask.id}
    `;

    const stats = taskStats[0];
    console.log(`✅ Parent task statistics:`);
    console.log(`   Total sub-tasks: ${stats.total_subtasks}`);
    console.log(`   Completed sub-tasks: ${stats.completed_subtasks}`);
    console.log(`   Completion percentage: ${stats.completion_percentage || 0}%`);

    // 7. Test sub-task deletion
    console.log('\n7. Testing sub-task deletion...');
    
    const lastSubTask = createdSubTasks[createdSubTasks.length - 1];
    await sql`
      DELETE FROM sub_tasks WHERE id = ${lastSubTask.id}
    `;
    console.log(`   ✅ Deleted "${lastSubTask.title}"`);
    
    // Verify deletion
    const remainingSubTasks = await sql`
      SELECT COUNT(*) as count
      FROM sub_tasks 
      WHERE parent_task_id = ${testTask.id}
    `;
    console.log(`   Remaining sub-tasks: ${remainingSubTasks[0].count}`);

    // 8. Test position reordering
    console.log('\n8. Testing position reordering...');
    
    const remainingSubTasksList = await sql`
      SELECT id, title, position
      FROM sub_tasks 
      WHERE parent_task_id = ${testTask.id}
      ORDER BY position ASC
    `;
    
    if (remainingSubTasksList.length >= 2) {
      // Swap positions of first two sub-tasks
      const first = remainingSubTasksList[0];
      const second = remainingSubTasksList[1];
      
      await sql`
        UPDATE sub_tasks 
        SET position = ${second.position}, updated_at = NOW()
        WHERE id = ${first.id}
      `;
      
      await sql`
        UPDATE sub_tasks 
        SET position = ${first.position}, updated_at = NOW()
        WHERE id = ${second.id}
      `;
      
      console.log(`   ✅ Swapped positions of "${first.title}" and "${second.title}"`);
      
      // Verify new order
      const reorderedSubTasks = await sql`
        SELECT title, position
        FROM sub_tasks 
        WHERE parent_task_id = ${testTask.id}
        ORDER BY position ASC
      `;
      
      console.log(`   New order:`);
      reorderedSubTasks.forEach((subTask, index) => {
        console.log(`     ${index + 1}. ${subTask.title} (position: ${subTask.position})`);
      });
    }

    // 9. Test access control simulation
    console.log('\n9. Testing access control logic...');
    
    const testUserId = users[1].id;
    const testSubTask = createdSubTasks[0];
    
    // Check if user has access to the sub-task
    const subTaskAccess = await sql`
      SELECT 
        st.*,
        t.assigned_to as parent_assigned_to,
        t.assigned_by as parent_assigned_by,
        (
          SELECT EXISTS (
            SELECT 1 FROM task_assignments 
            WHERE task_id = t.id AND user_id = ${testUserId}
          )
        ) as is_assigned_to_parent
      FROM sub_tasks st
      LEFT JOIN tasks t ON st.parent_task_id = t.id
      WHERE st.id = ${testSubTask.id}
    `;

    const subTask = subTaskAccess[0];
    const hasDirectAccess = subTask.assigned_to === testUserId || subTask.assigned_by === testUserId;
    const hasParentAccess = subTask.parent_assigned_to === testUserId || subTask.parent_assigned_by === testUserId;
    const hasAssignmentAccess = subTask.is_assigned_to_parent;
    const totalAccess = hasDirectAccess || hasParentAccess || hasAssignmentAccess;
    
    console.log(`✅ Access control for ${users[1].full_name}:`);
    console.log(`   Direct sub-task access: ${hasDirectAccess}`);
    console.log(`   Parent task access: ${hasParentAccess}`);
    console.log(`   Assignment access: ${hasAssignmentAccess}`);
    console.log(`   Total access: ${totalAccess}`);

    console.log('\n🎉 Sub-task Management System test completed successfully!');
    console.log('\n📋 Summary:');
    console.log(`   - Sub-task creation: ✅ Working`);
    console.log(`   - Sub-task retrieval with user details: ✅ Working`);
    console.log(`   - Status updates and completion tracking: ✅ Working`);
    console.log(`   - Parent task statistics calculation: ✅ Working`);
    console.log(`   - Sub-task deletion: ✅ Working`);
    console.log(`   - Position reordering: ✅ Working`);
    console.log(`   - Access control logic: ✅ Working`);

  } catch (error) {
    console.error('❌ Test failed:', error.message);
    console.error('Full error:', error);
  }
}

testSubTaskSystem();
