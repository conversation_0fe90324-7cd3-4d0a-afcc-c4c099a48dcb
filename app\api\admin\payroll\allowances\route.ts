// Payroll Allowances Management API
// Admin endpoints for managing employee allowances

import { NextRequest, NextResponse } from 'next/server';
import { AuthService } from '@/lib/auth-utils';
import { neon } from '@neondatabase/serverless';

const sql = neon(process.env.DATABASE_URL!);

// GET - List employee allowances or available allowance types
export async function GET(request: NextRequest) {
  try {
    const sessionToken = request.cookies.get("session-token")?.value;

    if (!sessionToken) {
      return NextResponse.json({
        success: false,
        error: 'Unauthorized'
      }, { status: 401 });
    }

    const user = await AuthService.verifySession(sessionToken);

    if (!user || !["admin", "hr_manager"].includes(user.role)) {
      return NextResponse.json({
        success: false,
        error: 'Insufficient permissions'
      }, { status: 403 });
    }

    const { searchParams } = new URL(request.url);
    const action = searchParams.get('action') || 'list_assignments';
    const userId = searchParams.get('userId');
    const componentId = searchParams.get('componentId');

    if (action === 'available_allowances') {
      // Get all available allowance components
      const allowances = await sql`
        SELECT 
          id, name, code, category, calculation_type,
          fixed_amount, percentage, percentage_base,
          description, is_active, effective_from, effective_to
        FROM payroll_components_master 
        WHERE type = 'allowance' AND is_active = true
        ORDER BY category, name
      `;

      return NextResponse.json({
        success: true,
        data: allowances,
        message: 'Available allowances retrieved successfully'
      });

    } else if (action === 'employee_allowances' && userId) {
      // Get allowances assigned to specific employee
      const employeeAllowances = await sql`
        SELECT 
          eca.id as assignment_id,
          eca.user_id,
          eca.component_id,
          eca.is_active,
          eca.effective_from,
          eca.effective_to,
          eca.override_amount,
          eca.override_percentage,
          eca.assigned_by,
          eca.approved_by,
          eca.approval_date,
          eca.notes,
          eca.created_at,
          pcm.name,
          pcm.code,
          pcm.category,
          pcm.calculation_type,
          pcm.fixed_amount,
          pcm.percentage,
          pcm.percentage_base,
          pcm.description,
          u.full_name as employee_name,
          assigned_user.full_name as assigned_by_name,
          approved_user.full_name as approved_by_name
        FROM employee_component_assignments eca
        JOIN payroll_components_master pcm ON eca.component_id = pcm.id
        JOIN users u ON eca.user_id = u.id
        LEFT JOIN users assigned_user ON eca.assigned_by = assigned_user.id
        LEFT JOIN users approved_user ON eca.approved_by = approved_user.id
        WHERE eca.user_id = ${userId} 
          AND pcm.type = 'allowance'
          AND (eca.effective_to IS NULL OR eca.effective_to >= CURRENT_DATE)
        ORDER BY eca.created_at DESC
      `;

      return NextResponse.json({
        success: true,
        data: employeeAllowances,
        message: 'Employee allowances retrieved successfully'
      });

    } else if (action === 'all_assignments') {
      // Get all allowance assignments with pagination
      const limit = parseInt(searchParams.get('limit') || '50');
      const offset = parseInt(searchParams.get('offset') || '0');

      const assignments = await sql`
        SELECT 
          eca.id as assignment_id,
          eca.user_id,
          eca.component_id,
          eca.is_active,
          eca.effective_from,
          eca.effective_to,
          eca.override_amount,
          eca.override_percentage,
          eca.created_at,
          pcm.name as allowance_name,
          pcm.code,
          pcm.category,
          pcm.calculation_type,
          u.full_name as employee_name,
          u.employee_id,
          u.department
        FROM employee_component_assignments eca
        JOIN payroll_components_master pcm ON eca.component_id = pcm.id
        JOIN users u ON eca.user_id = u.id
        WHERE pcm.type = 'allowance'
        ORDER BY eca.created_at DESC
        LIMIT ${limit} OFFSET ${offset}
      `;

      const totalCount = await sql`
        SELECT COUNT(*) as count
        FROM employee_component_assignments eca
        JOIN payroll_components_master pcm ON eca.component_id = pcm.id
        WHERE pcm.type = 'allowance'
      `;

      return NextResponse.json({
        success: true,
        data: {
          assignments,
          pagination: {
            total: totalCount[0].count,
            limit,
            offset,
            hasMore: totalCount[0].count > offset + limit
          }
        },
        message: 'All allowance assignments retrieved successfully'
      });

    } else {
      return NextResponse.json({
        success: false,
        error: 'Invalid action or missing parameters'
      }, { status: 400 });
    }

  } catch (error) {
    console.error('Error in allowances GET:', error);
    return NextResponse.json({
      success: false,
      error: error.message || 'Internal server error'
    }, { status: 500 });
  }
}

// POST - Create new allowance assignment
export async function POST(request: NextRequest) {
  try {
    const sessionToken = request.cookies.get("session-token")?.value;

    if (!sessionToken) {
      return NextResponse.json({
        success: false,
        error: 'Unauthorized'
      }, { status: 401 });
    }

    const user = await AuthService.verifySession(sessionToken);

    if (!user || !["admin", "hr_manager"].includes(user.role)) {
      return NextResponse.json({
        success: false,
        error: 'Insufficient permissions'
      }, { status: 403 });
    }

    const body = await request.json();
    const {
      userId,
      componentId,
      effectiveFrom,
      effectiveTo,
      overrideAmount,
      overridePercentage,
      notes,
      requiresApproval = false
    } = body;

    // Validate required fields
    if (!userId || !componentId || !effectiveFrom) {
      return NextResponse.json({
        success: false,
        error: 'User ID, component ID, and effective from date are required'
      }, { status: 400 });
    }

    // Verify the component exists and is an allowance
    const component = await sql`
      SELECT id, name, type, is_active 
      FROM payroll_components_master 
      WHERE id = ${componentId} AND type = 'allowance' AND is_active = true
    `;

    if (component.length === 0) {
      return NextResponse.json({
        success: false,
        error: 'Invalid or inactive allowance component'
      }, { status: 400 });
    }

    // Verify the user exists
    const employee = await sql`
      SELECT id, full_name, employment_status 
      FROM users 
      WHERE id = ${userId} AND employment_status = 'active'
    `;

    if (employee.length === 0) {
      return NextResponse.json({
        success: false,
        error: 'Employee not found or inactive'
      }, { status: 400 });
    }

    // Check for overlapping assignments
    const overlapping = await sql`
      SELECT id 
      FROM employee_component_assignments 
      WHERE user_id = ${userId} 
        AND component_id = ${componentId}
        AND is_active = true
        AND (
          (effective_to IS NULL) OR 
          (${effectiveFrom} <= effective_to AND (${effectiveTo} IS NULL OR ${effectiveTo} >= effective_from))
        )
    `;

    if (overlapping.length > 0) {
      return NextResponse.json({
        success: false,
        error: 'Overlapping allowance assignment already exists for this employee'
      }, { status: 400 });
    }

    // Create the assignment
    const assignment = await sql`
      INSERT INTO employee_component_assignments (
        user_id, component_id, is_active, effective_from, effective_to,
        override_amount, override_percentage, assigned_by, 
        approved_by, approval_date, notes
      )
      VALUES (
        ${userId}, ${componentId}, true, ${effectiveFrom}, ${effectiveTo || null},
        ${overrideAmount || null}, ${overridePercentage || null}, ${user.id},
        ${requiresApproval ? null : user.id}, 
        ${requiresApproval ? null : new Date().toISOString()},
        ${notes || null}
      )
      RETURNING *
    `;

    // Get the complete assignment details
    const assignmentDetails = await sql`
      SELECT 
        eca.*,
        pcm.name as allowance_name,
        pcm.code,
        u.full_name as employee_name
      FROM employee_component_assignments eca
      JOIN payroll_components_master pcm ON eca.component_id = pcm.id
      JOIN users u ON eca.user_id = u.id
      WHERE eca.id = ${assignment[0].id}
    `;

    return NextResponse.json({
      success: true,
      data: assignmentDetails[0],
      message: `Allowance assignment created successfully${requiresApproval ? ' (pending approval)' : ''}`
    });

  } catch (error) {
    console.error('Error in allowances POST:', error);
    return NextResponse.json({
      success: false,
      error: error.message || 'Internal server error'
    }, { status: 500 });
  }
}

// PUT - Update allowance assignment
export async function PUT(request: NextRequest) {
  try {
    const sessionToken = request.cookies.get("session-token")?.value;

    if (!sessionToken) {
      return NextResponse.json({
        success: false,
        error: 'Unauthorized'
      }, { status: 401 });
    }

    const user = await AuthService.verifySession(sessionToken);

    if (!user || !["admin", "hr_manager"].includes(user.role)) {
      return NextResponse.json({
        success: false,
        error: 'Insufficient permissions'
      }, { status: 403 });
    }

    const body = await request.json();
    const {
      assignmentId,
      effectiveFrom,
      effectiveTo,
      overrideAmount,
      overridePercentage,
      notes,
      isActive,
      action = 'update'
    } = body;

    if (!assignmentId) {
      return NextResponse.json({
        success: false,
        error: 'Assignment ID is required'
      }, { status: 400 });
    }

    if (action === 'approve') {
      // Approve the assignment
      const updated = await sql`
        UPDATE employee_component_assignments 
        SET 
          approved_by = ${user.id},
          approval_date = NOW(),
          updated_at = NOW()
        WHERE id = ${assignmentId} AND approved_by IS NULL
        RETURNING *
      `;

      if (updated.length === 0) {
        return NextResponse.json({
          success: false,
          error: 'Assignment not found or already approved'
        }, { status: 404 });
      }

      return NextResponse.json({
        success: true,
        data: updated[0],
        message: 'Allowance assignment approved successfully'
      });

    } else {
      // Update the assignment
      const updated = await sql`
        UPDATE employee_component_assignments 
        SET 
          effective_from = COALESCE(${effectiveFrom}, effective_from),
          effective_to = ${effectiveTo},
          override_amount = ${overrideAmount},
          override_percentage = ${overridePercentage},
          notes = COALESCE(${notes}, notes),
          is_active = COALESCE(${isActive}, is_active),
          updated_at = NOW()
        WHERE id = ${assignmentId}
        RETURNING *
      `;

      if (updated.length === 0) {
        return NextResponse.json({
          success: false,
          error: 'Assignment not found'
        }, { status: 404 });
      }

      return NextResponse.json({
        success: true,
        data: updated[0],
        message: 'Allowance assignment updated successfully'
      });
    }

  } catch (error) {
    console.error('Error in allowances PUT:', error);
    return NextResponse.json({
      success: false,
      error: error.message || 'Internal server error'
    }, { status: 500 });
  }
}

// DELETE - Remove allowance assignment
export async function DELETE(request: NextRequest) {
  try {
    const sessionToken = request.cookies.get("session-token")?.value;

    if (!sessionToken) {
      return NextResponse.json({
        success: false,
        error: 'Unauthorized'
      }, { status: 401 });
    }

    const user = await AuthService.verifySession(sessionToken);

    if (!user || user.role !== "admin") {
      return NextResponse.json({
        success: false,
        error: 'Admin access required'
      }, { status: 403 });
    }

    const { searchParams } = new URL(request.url);
    const assignmentId = searchParams.get('assignmentId');

    if (!assignmentId) {
      return NextResponse.json({
        success: false,
        error: 'Assignment ID is required'
      }, { status: 400 });
    }

    // Soft delete by setting effective_to to current date and is_active to false
    const deleted = await sql`
      UPDATE employee_component_assignments 
      SET 
        is_active = false,
        effective_to = CURRENT_DATE,
        updated_at = NOW()
      WHERE id = ${assignmentId}
      RETURNING *
    `;

    if (deleted.length === 0) {
      return NextResponse.json({
        success: false,
        error: 'Assignment not found'
      }, { status: 404 });
    }

    return NextResponse.json({
      success: true,
      data: deleted[0],
      message: 'Allowance assignment removed successfully'
    });

  } catch (error) {
    console.error('Error in allowances DELETE:', error);
    return NextResponse.json({
      success: false,
      error: error.message || 'Internal server error'
    }, { status: 500 });
  }
}
