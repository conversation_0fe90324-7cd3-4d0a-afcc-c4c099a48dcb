#!/usr/bin/env node

require('dotenv').config({ path: '.env.local' });
const { neon } = require('@neondatabase/serverless');

async function validateTaskFix() {
  console.log('🎯 VALIDATING TASK MANAGEMENT FIX');
  console.log('=================================');
  
  if (!process.env.DATABASE_URL) {
    console.error('❌ DATABASE_URL environment variable is not set');
    process.exit(1);
  }
  
  try {
    const sql = neon(process.env.DATABASE_URL);
    
    // Test 1: Verify consistent user counts across all systems
    console.log('🔄 Testing user count consistency...');
    
    const userCounts = {
      total: await sql`SELECT COUNT(*) as count FROM users`,
      active: await sql`SELECT COUNT(*) as count FROM users WHERE is_active = true`,
      byRole: await sql`
        SELECT role, COUNT(*) as count 
        FROM users 
        WHERE is_active = true 
        GROUP BY role 
        ORDER BY role
      `
    };
    
    console.log(`   Total users: ${userCounts.total[0].count}`);
    console.log(`   Active users: ${userCounts.active[0].count}`);
    console.log('   Users by role:');
    userCounts.byRole.forEach(role => {
      console.log(`     ${role.role}: ${role.count}`);
    });
    
    // Test 2: Test the new /api/users endpoint query
    console.log('\n🔄 Testing /api/users endpoint query...');

    // Simulate the query that the new endpoint uses
    const usersApiResult = await sql`
      SELECT
        id,
        email,
        full_name,
        role,
        department,
        position,
        employee_id,
        is_active,
        created_at
      FROM users
      WHERE is_active = true
      ORDER BY full_name
      LIMIT 100
    `;

    console.log(`✅ /api/users query returns ${usersApiResult.length} users`);
    
    // Test 3: Test the /api/admin/users endpoint query
    console.log('\n🔄 Testing /api/admin/users endpoint query...');
    
    const adminUsersResult = await sql`
      SELECT id, email, full_name, role, employee_id, department, position,
             employment_type, employment_status, hire_date, salary, phone, is_active, created_at
      FROM users
      WHERE is_active = true
      ORDER BY full_name
      LIMIT 100
    `;
    console.log(`✅ /api/admin/users query returns ${adminUsersResult.length} users`);
    
    // Test 4: Verify task assignment queries work
    console.log('\n🔄 Testing task assignment queries...');
    
    // Test query for getting tasks with user information
    const tasksWithUsers = await sql`
      SELECT 
        t.id,
        t.title,
        t.status,
        t.assigned_to,
        u.full_name as assigned_to_name,
        u.email as assigned_to_email
      FROM tasks t
      LEFT JOIN users u ON t.assigned_to = u.id
      ORDER BY t.created_at DESC
      LIMIT 5
    `;
    
    console.log(`✅ Found ${tasksWithUsers.length} tasks with user relationships`);
    
    if (tasksWithUsers.length > 0) {
      console.log('   Sample tasks:');
      tasksWithUsers.forEach(task => {
        const assignee = task.assigned_to_name || 'Unassigned';
        console.log(`     • ${task.title} → ${assignee}`);
      });
    }
    
    // Test 5: Verify data consistency between attendance and task systems
    console.log('\n🔄 Testing data consistency...');
    
    // Get users from attendance perspective
    const attendanceUsers = await sql`
      SELECT DISTINCT u.id, u.full_name, u.email
      FROM users u
      WHERE u.is_active = true
      ORDER BY u.full_name
    `;
    
    // Get users from task perspective  
    const taskUsers = await sql`
      SELECT DISTINCT u.id, u.full_name, u.email
      FROM users u
      WHERE u.is_active = true
      ORDER BY u.full_name
    `;
    
    const attendanceCount = attendanceUsers.length;
    const taskCount = taskUsers.length;
    
    if (attendanceCount === taskCount) {
      console.log(`✅ Consistent user count: ${attendanceCount} users in both systems`);
    } else {
      console.log(`❌ Inconsistent user count: Attendance(${attendanceCount}) vs Tasks(${taskCount})`);
    }
    
    // Test 6: Check for any orphaned data
    console.log('\n🔄 Checking for orphaned data...');
    
    const orphanedTasks = await sql`
      SELECT COUNT(*) as count
      FROM tasks t
      LEFT JOIN users u ON t.assigned_to = u.id
      WHERE t.assigned_to IS NOT NULL AND u.id IS NULL
    `;
    
    const orphanedAttendance = await sql`
      SELECT COUNT(*) as count
      FROM attendance a
      LEFT JOIN users u ON a.user_id = u.id
      WHERE u.id IS NULL
    `;
    
    console.log(`   Orphaned tasks: ${orphanedTasks[0].count}`);
    console.log(`   Orphaned attendance: ${orphanedAttendance[0].count}`);
    
    // Test 7: Final validation summary
    console.log('\n🎯 VALIDATION RESULTS');
    console.log('====================');
    
    const validationResults = {
      userCount: userCounts.active[0].count,
      usersApiWorks: usersApiResult.length > 0,
      adminUsersApiWorks: adminUsersResult.length > 0,
      taskAssignmentWorks: tasksWithUsers.length >= 0,
      dataConsistent: attendanceCount === taskCount,
      noOrphanedData: orphanedTasks[0].count === 0 && orphanedAttendance[0].count === 0
    };
    
    console.log(`✅ Active users: ${validationResults.userCount}`);
    console.log(`✅ /api/users endpoint: ${validationResults.usersApiWorks ? 'Working' : 'Failed'}`);
    console.log(`✅ /api/admin/users endpoint: ${validationResults.adminUsersApiWorks ? 'Working' : 'Failed'}`);
    console.log(`✅ Task assignment: ${validationResults.taskAssignmentWorks ? 'Working' : 'Failed'}`);
    console.log(`✅ Data consistency: ${validationResults.dataConsistent ? 'Good' : 'Issues found'}`);
    console.log(`✅ Data integrity: ${validationResults.noOrphanedData ? 'Good' : 'Orphaned data found'}`);
    
    // Final recommendation
    console.log('\n📋 FINAL STATUS:');
    
    const allTestsPassed = Object.values(validationResults).every(result => 
      typeof result === 'boolean' ? result : result >= 9
    );
    
    if (allTestsPassed && validationResults.userCount >= 9) {
      console.log('🎉 SUCCESS: Task management system is now properly integrated!');
      console.log('');
      console.log('✅ All 9 users are available for task assignment');
      console.log('✅ Both UserSelector and TaskModal will show all users');
      console.log('✅ Task assignment functionality works with real user data');
      console.log('✅ Data is consistent across attendance and task systems');
      console.log('');
      console.log('🚀 The task assignment dropdown should now show all 9 users instead of 4 demo users!');
    } else {
      console.log('⚠️  Some issues were found. Please review the results above.');
    }
    
  } catch (error) {
    console.error('❌ Validation failed:', error.message);
    process.exit(1);
  }
}

validateTaskFix();
