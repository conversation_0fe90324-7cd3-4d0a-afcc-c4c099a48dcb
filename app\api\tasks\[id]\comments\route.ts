import { NextRequest, NextResponse } from "next/server"
import { AuthService } from "@/lib/auth-utils"
import { serverDb } from "@/lib/server-db"
import { z } from "zod"

// Validation schema for comment creation
const createCommentSchema = z.object({
  content: z.string().min(1, "Comment content is required").max(1000, "Comment too long"),
})

// GET /api/tasks/[id]/comments - Get task comments
export async function GET(request: NextRequest, { params }: { params: { id: string } }) {
  try {
    const sessionToken = request.cookies.get("session-token")?.value

    if (!sessionToken) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 })
    }

    const user = await AuthService.verifySession(sessionToken)

    if (!user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 })
    }

    const { id: taskId } = params

    // Check if task exists and user has access
    const taskResult = await serverDb.sql`
      SELECT * FROM tasks WHERE id = ${taskId}
    `

    if (taskResult.length === 0) {
      return NextResponse.json({ error: "Task not found" }, { status: 404 })
    }

    const task = taskResult[0]

    // Check access permissions
    const hasAccess = 
      ["admin", "manager"].includes(user.role) ||
      task.assigned_to === user.id ||
      task.assigned_by === user.id

    if (!hasAccess) {
      return NextResponse.json({ error: "Access denied" }, { status: 403 })
    }

    // Get comments with user information
    const comments = await serverDb.sql`
      SELECT 
        c.*,
        u.full_name as user_name,
        u.email as user_email
      FROM task_comments c
      LEFT JOIN users u ON c.user_id = u.id
      WHERE c.task_id = ${taskId}
      ORDER BY c.created_at ASC
    `

    return NextResponse.json({
      success: true,
      data: comments
    })

  } catch (error) {
    console.error("Get task comments error:", error)
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    )
  }
}

// POST /api/tasks/[id]/comments - Add comment to task
export async function POST(request: NextRequest, { params }: { params: { id: string } }) {
  try {
    const sessionToken = request.cookies.get("session-token")?.value

    if (!sessionToken) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 })
    }

    const user = await AuthService.verifySession(sessionToken)

    if (!user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 })
    }

    const { id: taskId } = params

    // Check if task exists and user has access
    const taskResult = await serverDb.sql`
      SELECT * FROM tasks WHERE id = ${taskId}
    `

    if (taskResult.length === 0) {
      return NextResponse.json({ error: "Task not found" }, { status: 404 })
    }

    const task = taskResult[0]

    // Check access permissions
    const hasAccess = 
      ["admin", "manager"].includes(user.role) ||
      task.assigned_to === user.id ||
      task.assigned_by === user.id

    if (!hasAccess) {
      return NextResponse.json({ error: "Access denied" }, { status: 403 })
    }

    // Parse and validate request body
    const body = await request.json()
    const { content } = createCommentSchema.parse(body)

    // Create the comment
    const commentResult = await serverDb.sql`
      INSERT INTO task_comments (task_id, user_id, content)
      VALUES (${taskId}, ${user.id}, ${content})
      RETURNING *
    `

    const newComment = commentResult[0]

    // Get the complete comment data with user info
    const completeCommentResult = await serverDb.sql`
      SELECT 
        c.*,
        u.full_name as user_name,
        u.email as user_email
      FROM task_comments c
      LEFT JOIN users u ON c.user_id = u.id
      WHERE c.id = ${newComment.id}
    `

    return NextResponse.json({
      success: true,
      data: completeCommentResult[0],
      message: "Comment added successfully"
    }, { status: 201 })

  } catch (error) {
    console.error("Create comment error:", error)
    
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: "Invalid comment data", details: error.errors },
        { status: 400 }
      )
    }

    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    )
  }
}
