#!/usr/bin/env node

/**
 * <PERSON><PERSON>t to diagnose and fix attendance check-in/check-out button state management
 * This script will:
 * 1. Check database schema for required fields
 * 2. Test attendance status API
 * 3. Fix any schema issues
 * 4. Verify the fix works
 */

const { neon } = require('@neondatabase/serverless');

// Load environment variables
require('dotenv').config({ path: '.env.local' });

const sql = neon(process.env.DATABASE_URL);

console.log('🔧 Fixing Attendance State Management Issues');
console.log('==========================================\n');

async function checkDatabaseSchema() {
  console.log('1. 📋 Checking database schema...');
  
  try {
    // Check if attendance table exists and get its structure
    const tableInfo = await sql`
      SELECT column_name, data_type, is_nullable, column_default
      FROM information_schema.columns 
      WHERE table_name = 'attendance' 
      ORDER BY ordinal_position
    `;
    
    console.log('   ✅ Attendance table structure:');
    tableInfo.forEach(col => {
      console.log(`      - ${col.column_name}: ${col.data_type} ${col.is_nullable === 'NO' ? 'NOT NULL' : 'NULL'}`);
    });
    
    // Check for required fields
    const requiredFields = ['is_active', 'daily_sequence', 'entry_type'];
    const existingFields = tableInfo.map(col => col.column_name);
    const missingFields = requiredFields.filter(field => !existingFields.includes(field));
    
    if (missingFields.length > 0) {
      console.log(`   ⚠️  Missing required fields: ${missingFields.join(', ')}`);
      return { needsSchemaFix: true, missingFields };
    } else {
      console.log('   ✅ All required fields present');
      return { needsSchemaFix: false, missingFields: [] };
    }
    
  } catch (error) {
    console.error('   ❌ Error checking schema:', error.message);
    return { needsSchemaFix: true, missingFields: [], error: error.message };
  }
}

async function fixDatabaseSchema(missingFields) {
  console.log('\n2. 🔨 Fixing database schema...');
  
  try {
    // Add missing fields
    for (const field of missingFields) {
      console.log(`   Adding ${field} column...`);
      
      if (field === 'is_active') {
        await sql`
          ALTER TABLE attendance 
          ADD COLUMN IF NOT EXISTS is_active BOOLEAN DEFAULT FALSE
        `;
        console.log('   ✅ Added is_active column');
      }
      
      if (field === 'daily_sequence') {
        await sql`
          ALTER TABLE attendance 
          ADD COLUMN IF NOT EXISTS daily_sequence INTEGER DEFAULT 1
        `;
        console.log('   ✅ Added daily_sequence column');
      }
      
      if (field === 'entry_type') {
        await sql`
          ALTER TABLE attendance 
          ADD COLUMN IF NOT EXISTS entry_type VARCHAR(20) DEFAULT 'regular' 
          CHECK (entry_type IN ('regular', 'break', 'meeting', 'overtime'))
        `;
        console.log('   ✅ Added entry_type column');
      }
    }
    
    // Update existing records to have proper values
    console.log('   Updating existing records...');
    await sql`
      UPDATE attendance 
      SET 
        daily_sequence = COALESCE(daily_sequence, 1),
        entry_type = COALESCE(entry_type, 'regular'),
        is_active = COALESCE(is_active, 
          CASE 
            WHEN check_in_time IS NOT NULL AND check_out_time IS NULL THEN TRUE 
            ELSE FALSE 
          END
        )
      WHERE daily_sequence IS NULL OR entry_type IS NULL OR is_active IS NULL
    `;
    console.log('   ✅ Updated existing records');
    
    // Remove the old unique constraint if it exists
    try {
      await sql`
        ALTER TABLE attendance
        DROP CONSTRAINT IF EXISTS attendance_user_id_date_key
      `;
      console.log('   ✅ Removed old unique constraint');
    } catch (error) {
      console.log('   ⚠️  Old constraint might not exist:', error.message);
    }

    return true;
  } catch (error) {
    console.error('   ❌ Error fixing schema:', error.message);
    return false;
  }
}

async function removeUniqueConstraint() {
  console.log('\n2. 🔨 Removing unique constraint...');

  try {
    // Check if the constraint exists
    const constraints = await sql`
      SELECT constraint_name
      FROM information_schema.table_constraints
      WHERE table_name = 'attendance'
      AND constraint_type = 'UNIQUE'
    `;

    console.log('   Current constraints:', constraints.map(c => c.constraint_name));

    // Remove the problematic unique constraint
    for (const constraint of constraints) {
      if (constraint.constraint_name.includes('user_id_date')) {
        // Use dynamic SQL for constraint name
        await sql.query(`ALTER TABLE attendance DROP CONSTRAINT ${constraint.constraint_name}`);
        console.log(`   ✅ Removed constraint: ${constraint.constraint_name}`);
      }
    }
    
    return true;
  } catch (error) {
    console.error('   ❌ Error fixing schema:', error.message);
    return false;
  }
}

async function testAttendanceStatus() {
  console.log('\n3. 🧪 Testing attendance status logic...');
  
  try {
    // Get a test user
    const users = await sql`
      SELECT id, email, full_name FROM users 
      WHERE role = 'admin' 
      LIMIT 1
    `;
    
    if (users.length === 0) {
      console.log('   ⚠️  No test user found');
      return false;
    }
    
    const testUser = users[0];
    console.log(`   Using test user: ${testUser.full_name} (${testUser.email})`);
    
    // Test getCurrentAttendanceStatus logic
    const today = new Date().toISOString().split("T")[0];
    
    // Get today's entries
    const todayEntries = await sql`
      SELECT * FROM attendance
      WHERE user_id = ${testUser.id} AND date = ${today}
      ORDER BY daily_sequence ASC
    `;
    
    console.log(`   Today's entries: ${todayEntries.length}`);
    
    // Find active session
    const activeSession = todayEntries.find(entry =>
      entry.check_in_time && !entry.check_out_time && entry.is_active
    );
    
    const isCheckedIn = !!activeSession;
    console.log(`   Is checked in: ${isCheckedIn}`);
    console.log(`   Active session: ${activeSession ? 'Yes' : 'No'}`);
    
    // Calculate remaining limits
    const checkInsToday = todayEntries.filter(entry => entry.check_in_time).length;
    const checkOutsToday = todayEntries.filter(entry => entry.check_out_time).length;
    
    console.log(`   Check-ins today: ${checkInsToday}/5`);
    console.log(`   Check-outs today: ${checkOutsToday}/5`);
    console.log(`   Remaining check-ins: ${Math.max(0, 5 - checkInsToday)}`);
    console.log(`   Remaining check-outs: ${Math.max(0, 5 - checkOutsToday)}`);
    
    return {
      isCheckedIn,
      activeSession,
      checkInsToday,
      checkOutsToday,
      canCheckIn: !isCheckedIn && checkInsToday < 5,
      canCheckOut: isCheckedIn && checkOutsToday < 5
    };
    
  } catch (error) {
    console.error('   ❌ Error testing attendance status:', error.message);
    return false;
  }
}

async function simulateCheckInOut() {
  console.log('\n4. 🎭 Simulating check-in/check-out flow...');
  
  try {
    // Get test user
    const users = await sql`
      SELECT id, email, full_name FROM users 
      WHERE role = 'admin' 
      LIMIT 1
    `;
    
    if (users.length === 0) {
      console.log('   ⚠️  No test user found');
      return false;
    }
    
    const testUser = users[0];
    const today = new Date().toISOString().split("T")[0];
    const now = new Date();
    
    // Clean up any existing active sessions for testing
    await sql`
      UPDATE attendance 
      SET is_active = FALSE, check_out_time = NOW()
      WHERE user_id = ${testUser.id} 
      AND date = ${today} 
      AND is_active = TRUE
    `;
    
    console.log('   Cleaned up existing active sessions');
    
    // Test check-in
    console.log('   Testing check-in...');
    
    // Get next sequence number
    const maxSequence = await sql`
      SELECT COALESCE(MAX(daily_sequence), 0) as max_seq
      FROM attendance
      WHERE user_id = ${testUser.id} AND date = ${today}
    `;
    const nextSequence = Number(maxSequence[0]?.max_seq || 0) + 1;
    
    // Create check-in record
    const checkInResult = await sql`
      INSERT INTO attendance (
        user_id, date, check_in_time, status, notes,
        entry_type, daily_sequence, is_active
      )
      VALUES (
        ${testUser.id}, ${today}, ${now.toISOString()}, 'present', 'Test check-in',
        'regular', ${nextSequence}, TRUE
      )
      RETURNING *
    `;
    
    console.log(`   ✅ Check-in successful: Sequence ${checkInResult[0].daily_sequence}`);
    
    // Test status after check-in
    const statusAfterCheckIn = await testAttendanceStatus();
    console.log(`   Status after check-in: isCheckedIn=${statusAfterCheckIn.isCheckedIn}, canCheckOut=${statusAfterCheckIn.canCheckOut}`);
    
    // Test check-out
    console.log('   Testing check-out...');
    
    const checkOutResult = await sql`
      UPDATE attendance
      SET check_out_time = ${new Date().toISOString()},
          hours_worked = 1.0,
          is_active = FALSE,
          updated_at = NOW()
      WHERE id = ${checkInResult[0].id}
      RETURNING *
    `;
    
    console.log(`   ✅ Check-out successful: ${checkOutResult[0].hours_worked}h worked`);
    
    // Test status after check-out
    const statusAfterCheckOut = await testAttendanceStatus();
    console.log(`   Status after check-out: isCheckedIn=${statusAfterCheckOut.isCheckedIn}, canCheckIn=${statusAfterCheckOut.canCheckIn}`);
    
    return true;
    
  } catch (error) {
    console.error('   ❌ Error simulating check-in/out:', error.message);
    return false;
  }
}

async function main() {
  try {
    // Step 1: Check schema
    const schemaCheck = await checkDatabaseSchema();

    // Step 2: Remove unique constraint (always do this)
    const constraintRemoved = await removeUniqueConstraint();
    if (!constraintRemoved) {
      console.log('\n❌ Failed to remove unique constraint');
      process.exit(1);
    }

    // Step 3: Fix schema if needed
    if (schemaCheck.needsSchemaFix) {
      const schemaFixed = await fixDatabaseSchema(schemaCheck.missingFields);
      if (!schemaFixed) {
        console.log('\n❌ Failed to fix database schema');
        process.exit(1);
      }
    }
    
    // Step 4: Test attendance status
    const statusTest = await testAttendanceStatus();
    if (!statusTest) {
      console.log('\n❌ Failed to test attendance status');
      process.exit(1);
    }

    // Step 5: Simulate check-in/out flow
    const simulationTest = await simulateCheckInOut();
    if (!simulationTest) {
      console.log('\n❌ Failed to simulate check-in/out flow');
      process.exit(1);
    }
    
    console.log('\n🎉 Attendance state management fix completed successfully!');
    console.log('\nNext steps:');
    console.log('1. Start the development server: npm run dev');
    console.log('2. Test the check-in/check-out buttons in the dashboard');
    console.log('3. Verify button states change correctly');
    
  } catch (error) {
    console.error('\n❌ Unexpected error:', error);
    process.exit(1);
  }
}

// Run the fix
if (require.main === module) {
  main();
}

module.exports = { main };
