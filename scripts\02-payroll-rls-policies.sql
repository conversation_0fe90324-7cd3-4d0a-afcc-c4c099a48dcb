-- ============================================================================
-- PAYROLL SYSTEM ROW LEVEL SECURITY (RLS) POLICIES
-- Task 1.1: Implement comprehensive RLS policies for payroll data access
-- ============================================================================

-- ============================================================================
-- Step 1: Create helper function for role checking
-- ============================================================================

-- Function to get current user's role
CREATE OR REPLACE FUNCTION get_current_user_role()
RETURNS TEXT AS $$
DECLARE
    user_role TEXT;
BEGIN
    SELECT role INTO user_role 
    FROM users 
    WHERE id = auth.uid();
    
    RETURN COALESCE(user_role, 'guest');
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to check if user is admin or HR manager
CREATE OR REPLACE FUNCTION is_admin_or_hr()
RETURNS BOOLEAN AS $$
BEGIN
    RETURN get_current_user_role() IN ('admin', 'hr_manager');
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- ============================================================================
-- Step 2: RLS Policies for USERS table
-- ============================================================================

-- Users can view their own profile and basic info of colleagues
-- Admins and HR can view all user data
CREATE POLICY users_select_policy ON users
    FOR SELECT USING (
        id = auth.uid() OR 
        is_admin_or_hr() OR
        -- Allow viewing basic info for all authenticated users
        auth.uid() IS NOT NULL
    );

-- Only admins and HR can insert new users
CREATE POLICY users_insert_policy ON users
    FOR INSERT WITH CHECK (is_admin_or_hr());

-- Users can update their own basic info, admins/HR can update all
CREATE POLICY users_update_policy ON users
    FOR UPDATE USING (
        id = auth.uid() OR 
        is_admin_or_hr()
    );

-- Only admins can delete users (soft delete via is_active)
CREATE POLICY users_delete_policy ON users
    FOR DELETE USING (get_current_user_role() = 'admin');

-- ============================================================================
-- Step 3: RLS Policies for PAYROLL table
-- ============================================================================

-- Employees can only view their own payroll data
-- Admins and HR managers can view all payroll data
CREATE POLICY payroll_select_policy ON payroll
    FOR SELECT USING (
        user_id = auth.uid() OR 
        is_admin_or_hr()
    );

-- Only admins and HR managers can insert payroll records
CREATE POLICY payroll_insert_policy ON payroll
    FOR INSERT WITH CHECK (is_admin_or_hr());

-- Only admins and HR managers can update payroll records
CREATE POLICY payroll_update_policy ON payroll
    FOR UPDATE USING (is_admin_or_hr());

-- Only admins can delete payroll records
CREATE POLICY payroll_delete_policy ON payroll
    FOR DELETE USING (get_current_user_role() = 'admin');

-- ============================================================================
-- Step 4: RLS Policies for ATTENDANCE table
-- ============================================================================

-- Employees can view their own attendance, admins/HR can view all
CREATE POLICY attendance_select_policy ON attendance
    FOR SELECT USING (
        user_id = auth.uid() OR 
        is_admin_or_hr()
    );

-- Employees can insert their own attendance, admins/HR can insert for anyone
CREATE POLICY attendance_insert_policy ON attendance
    FOR INSERT WITH CHECK (
        user_id = auth.uid() OR 
        is_admin_or_hr()
    );

-- Employees can update their own attendance (within limits), admins/HR can update all
CREATE POLICY attendance_update_policy ON attendance
    FOR UPDATE USING (
        user_id = auth.uid() OR 
        is_admin_or_hr()
    );

-- Only admins and HR can delete attendance records
CREATE POLICY attendance_delete_policy ON attendance
    FOR DELETE USING (is_admin_or_hr());

-- ============================================================================
-- Step 5: RLS Policies for PAYROLL_APPROVALS table
-- ============================================================================

-- Enable RLS on payroll_approvals
ALTER TABLE payroll_approvals ENABLE ROW LEVEL SECURITY;

-- Approvers can view approvals assigned to them, admins/HR can view all
CREATE POLICY payroll_approvals_select_policy ON payroll_approvals
    FOR SELECT USING (
        approver_id = auth.uid() OR 
        is_admin_or_hr() OR
        -- Allow employees to view approvals for their own payroll
        EXISTS (
            SELECT 1 FROM payroll p 
            WHERE p.id = payroll_id AND p.user_id = auth.uid()
        )
    );

-- Only admins and HR can create approval records
CREATE POLICY payroll_approvals_insert_policy ON payroll_approvals
    FOR INSERT WITH CHECK (is_admin_or_hr());

-- Approvers can update their own approvals, admins/HR can update all
CREATE POLICY payroll_approvals_update_policy ON payroll_approvals
    FOR UPDATE USING (
        approver_id = auth.uid() OR 
        is_admin_or_hr()
    );

-- Only admins can delete approval records
CREATE POLICY payroll_approvals_delete_policy ON payroll_approvals
    FOR DELETE USING (get_current_user_role() = 'admin');

-- ============================================================================
-- Step 6: RLS Policies for PAYROLL_DISBURSEMENTS table
-- ============================================================================

-- Enable RLS on payroll_disbursements
ALTER TABLE payroll_disbursements ENABLE ROW LEVEL SECURITY;

-- Employees can view disbursements for their own payroll, admins/HR can view all
CREATE POLICY payroll_disbursements_select_policy ON payroll_disbursements
    FOR SELECT USING (
        is_admin_or_hr() OR
        EXISTS (
            SELECT 1 FROM payroll p 
            WHERE p.id = payroll_id AND p.user_id = auth.uid()
        )
    );

-- Only admins and HR can manage disbursements
CREATE POLICY payroll_disbursements_insert_policy ON payroll_disbursements
    FOR INSERT WITH CHECK (is_admin_or_hr());

CREATE POLICY payroll_disbursements_update_policy ON payroll_disbursements
    FOR UPDATE USING (is_admin_or_hr());

CREATE POLICY payroll_disbursements_delete_policy ON payroll_disbursements
    FOR DELETE USING (get_current_user_role() = 'admin');

-- ============================================================================
-- Step 7: RLS Policies for PAYROLL_AUDIT_LOG table
-- ============================================================================

-- Enable RLS on payroll_audit_log
ALTER TABLE payroll_audit_log ENABLE ROW LEVEL SECURITY;

-- Only admins can view audit logs
CREATE POLICY payroll_audit_log_select_policy ON payroll_audit_log
    FOR SELECT USING (get_current_user_role() = 'admin');

-- System can insert audit logs (no user restriction)
CREATE POLICY payroll_audit_log_insert_policy ON payroll_audit_log
    FOR INSERT WITH CHECK (true);

-- No updates or deletes allowed on audit logs
CREATE POLICY payroll_audit_log_no_update ON payroll_audit_log
    FOR UPDATE USING (false);

CREATE POLICY payroll_audit_log_no_delete ON payroll_audit_log
    FOR DELETE USING (false);

-- ============================================================================
-- Step 8: RLS Policies for PAYROLL_COMPONENTS_MASTER table
-- ============================================================================

-- Enable RLS on payroll_components_master
ALTER TABLE payroll_components_master ENABLE ROW LEVEL SECURITY;

-- All authenticated users can view active components
CREATE POLICY payroll_components_master_select_policy ON payroll_components_master
    FOR SELECT USING (
        auth.uid() IS NOT NULL AND (
            is_active = true OR 
            is_admin_or_hr()
        )
    );

-- Only admins and HR can manage components
CREATE POLICY payroll_components_master_insert_policy ON payroll_components_master
    FOR INSERT WITH CHECK (is_admin_or_hr());

CREATE POLICY payroll_components_master_update_policy ON payroll_components_master
    FOR UPDATE USING (is_admin_or_hr());

CREATE POLICY payroll_components_master_delete_policy ON payroll_components_master
    FOR DELETE USING (get_current_user_role() = 'admin');

-- ============================================================================
-- Step 9: RLS Policies for EMPLOYEE_COMPONENT_ASSIGNMENTS table
-- ============================================================================

-- Enable RLS on employee_component_assignments
ALTER TABLE employee_component_assignments ENABLE ROW LEVEL SECURITY;

-- Employees can view their own assignments, admins/HR can view all
CREATE POLICY employee_component_assignments_select_policy ON employee_component_assignments
    FOR SELECT USING (
        user_id = auth.uid() OR 
        is_admin_or_hr()
    );

-- Only admins and HR can manage assignments
CREATE POLICY employee_component_assignments_insert_policy ON employee_component_assignments
    FOR INSERT WITH CHECK (is_admin_or_hr());

CREATE POLICY employee_component_assignments_update_policy ON employee_component_assignments
    FOR UPDATE USING (is_admin_or_hr());

CREATE POLICY employee_component_assignments_delete_policy ON employee_component_assignments
    FOR DELETE USING (is_admin_or_hr());

-- ============================================================================
-- Step 10: RLS Policies for MONTHLY_PAYROLL_SUMMARY table
-- ============================================================================

-- Enable RLS on monthly_payroll_summary
ALTER TABLE monthly_payroll_summary ENABLE ROW LEVEL SECURITY;

-- Employees can view their own summary, admins/HR can view all
CREATE POLICY monthly_payroll_summary_select_policy ON monthly_payroll_summary
    FOR SELECT USING (
        user_id = auth.uid() OR 
        is_admin_or_hr()
    );

-- Only admins and HR can manage summaries
CREATE POLICY monthly_payroll_summary_insert_policy ON monthly_payroll_summary
    FOR INSERT WITH CHECK (is_admin_or_hr());

CREATE POLICY monthly_payroll_summary_update_policy ON monthly_payroll_summary
    FOR UPDATE USING (is_admin_or_hr());

CREATE POLICY monthly_payroll_summary_delete_policy ON monthly_payroll_summary
    FOR DELETE USING (get_current_user_role() = 'admin');

-- ============================================================================
-- Step 11: RLS Policies for PAYROLL_PERIODS and PAYROLL_SETTINGS tables
-- ============================================================================

-- Enable RLS on payroll_periods
ALTER TABLE payroll_periods ENABLE ROW LEVEL SECURITY;

-- All authenticated users can view periods
CREATE POLICY payroll_periods_select_policy ON payroll_periods
    FOR SELECT USING (auth.uid() IS NOT NULL);

-- Only admins and HR can manage periods
CREATE POLICY payroll_periods_insert_policy ON payroll_periods
    FOR INSERT WITH CHECK (is_admin_or_hr());

CREATE POLICY payroll_periods_update_policy ON payroll_periods
    FOR UPDATE USING (is_admin_or_hr());

CREATE POLICY payroll_periods_delete_policy ON payroll_periods
    FOR DELETE USING (get_current_user_role() = 'admin');

-- Enable RLS on payroll_settings
ALTER TABLE payroll_settings ENABLE ROW LEVEL SECURITY;

-- All authenticated users can view non-sensitive settings
CREATE POLICY payroll_settings_select_policy ON payroll_settings
    FOR SELECT USING (
        auth.uid() IS NOT NULL AND (
            is_system_setting = false OR 
            is_admin_or_hr()
        )
    );

-- Only admins can manage settings
CREATE POLICY payroll_settings_insert_policy ON payroll_settings
    FOR INSERT WITH CHECK (get_current_user_role() = 'admin');

CREATE POLICY payroll_settings_update_policy ON payroll_settings
    FOR UPDATE USING (get_current_user_role() = 'admin');

CREATE POLICY payroll_settings_delete_policy ON payroll_settings
    FOR DELETE USING (get_current_user_role() = 'admin');

-- ============================================================================
-- Step 12: Grant necessary permissions
-- ============================================================================

-- Grant usage on auth schema for RLS functions
GRANT USAGE ON SCHEMA auth TO authenticated;

-- Grant execute permissions on helper functions
GRANT EXECUTE ON FUNCTION get_current_user_role() TO authenticated;
GRANT EXECUTE ON FUNCTION is_admin_or_hr() TO authenticated;

-- ============================================================================
-- SUCCESS VERIFICATION
-- ============================================================================

-- Verify RLS is enabled on all tables
SELECT 
    schemaname, 
    tablename, 
    rowsecurity 
FROM pg_tables 
WHERE tablename IN (
    'users', 'payroll', 'attendance', 'payroll_approvals', 
    'payroll_disbursements', 'payroll_audit_log', 'payroll_components_master',
    'employee_component_assignments', 'monthly_payroll_summary', 
    'payroll_periods', 'payroll_settings'
) 
ORDER BY tablename;
