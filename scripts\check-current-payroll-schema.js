require('dotenv').config({ path: '.env.local' });
const { neon } = require('@neondatabase/serverless');

async function checkPayrollSchema() {
  try {
    const sql = neon(process.env.DATABASE_URL);
    
    console.log('🔍 Checking payroll table structure...');
    const payrollColumns = await sql`
      SELECT column_name, data_type, is_nullable, column_default
      FROM information_schema.columns 
      WHERE table_name = 'payroll'
      ORDER BY ordinal_position
    `;
    
    console.log('Payroll table structure:');
    payrollColumns.forEach(col => {
      console.log(`  - ${col.column_name}: ${col.data_type} (${col.is_nullable === 'YES' ? 'nullable' : 'not null'})`);
    });
    
    console.log('\n🔍 Checking for existing RLS policies...');
    const policies = await sql`
      SELECT schemaname, tablename, policyname, permissive, roles, cmd, qual
      FROM pg_policies 
      WHERE tablename IN ('payroll', 'users', 'attendance')
      ORDER BY tablename, policyname
    `;
    
    console.log('Existing RLS policies:');
    if (policies.length === 0) {
      console.log('  - No RLS policies found');
    } else {
      policies.forEach(policy => {
        console.log(`  - ${policy.tablename}.${policy.policyname}: ${policy.cmd} for ${policy.roles}`);
      });
    }
    
    console.log('\n🔍 Checking for payroll-related tables...');
    const payrollTables = await sql`
      SELECT table_name 
      FROM information_schema.tables 
      WHERE table_name LIKE '%payroll%' OR table_name LIKE '%allowance%' OR table_name LIKE '%deduction%'
      ORDER BY table_name
    `;
    
    console.log('Payroll-related tables:');
    payrollTables.forEach(table => {
      console.log(`  - ${table.table_name}`);
    });
    
  } catch (error) {
    console.error('❌ Error:', error.message);
  }
}

checkPayrollSchema();
