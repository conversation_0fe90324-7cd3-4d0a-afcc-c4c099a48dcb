"use client"

import type React from "react"

import { <PERSON>, CardContent } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { But<PERSON> } from "@/components/ui/button"
import { Avatar, AvatarFallback } from "@/components/ui/avatar"
import { Edit, Trash2, Users, Paperclip, CheckSquare, Calendar, Clock } from "lucide-react"
import { format, isAfter } from "date-fns"

interface Task {
  id: string
  title: string
  description?: string
  status: "todo" | "in_progress" | "completed" | "cancelled"
  priority: "low" | "medium" | "high" | "urgent"
  assigned_to?: string
  assigned_by: string
  due_date?: string
  created_at: string
  updated_at: string
  // Extended fields from joins
  assigned_to_name?: string
  assigned_to_email?: string
  project_name?: string
  project_color?: string
  // New fields for enhanced functionality
  assignments?: Array<{
    id: string
    full_name: string
    email: string
    is_primary: boolean
  }>
  subtask_count?: number
  completed_subtasks?: number
  attachment_count?: number
}

interface TaskCardProps {
  task: Task
  onEdit?: () => void
  onDelete?: () => void
  onClick?: () => void
  isAdmin?: boolean
  isOwnedByCurrentUser?: boolean
}

export function TaskCard({ task, onEdit, onDelete, onClick, isAdmin = false, isOwnedByCurrentUser = false }: TaskCardProps) {
  const handleDragStart = (e: React.DragEvent) => {
    e.dataTransfer.setData("taskId", task.id)
  }

  // Determine if the current user can edit/delete this task
  const canModify = isAdmin || isOwnedByCurrentUser

  // Priority colors
  const priorityColors = {
    high: "bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-300",
    medium: "bg-orange-100 text-orange-800 dark:bg-orange-900/20 dark:text-orange-300",
    low: "bg-blue-100 text-blue-800 dark:bg-blue-900/20 dark:text-blue-300",
  }

  const priorityColor = task.priority
    ? priorityColors[task.priority as keyof typeof priorityColors]
    : "bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-300"

  const borderColor = {
    high: "border-l-red-500",
    medium: "border-l-orange-500",
    low: "border-l-blue-500",
  }

  const taskBorder = task.priority ? borderColor[task.priority as keyof typeof borderColor] : "border-l-gray-300"

  const isOverdue = task.due_date && isAfter(new Date(), new Date(task.due_date)) && task.status !== 'completed'
  const assignedUsers = task.assignments || []
  const primaryUser = assignedUsers.find(a => a.is_primary) || assignedUsers[0]

  return (
    <Card
      className={`group cursor-pointer bg-white dark:bg-gray-700 shadow-sm border-l-4 ${taskBorder} hover:shadow-md transition-shadow`}
      draggable
      onDragStart={handleDragStart}
      onClick={(e) => {
        // Only trigger click if not dragging
        if (onClick && !e.defaultPrevented) {
          onClick()
        }
      }}
    >
      <CardContent className="p-3">
        <div className="space-y-2">
          {/* Header with title and priority */}
          <div className="flex justify-between items-start">
            <h4 className="font-medium text-sm mb-1 dark:text-white line-clamp-2">{task.title}</h4>
            {task.priority && <Badge className={`text-xs ${priorityColor} flex-shrink-0`}>{task.priority}</Badge>}
          </div>

          {/* Description */}
          {task.description && (
            <p className="text-xs text-gray-500 dark:text-gray-400 line-clamp-2">{task.description}</p>
          )}

          {/* Due date with overdue warning */}
          {task.due_date && (
            <div className={`flex items-center gap-1 text-xs ${isOverdue ? 'text-red-600' : 'text-gray-500'}`}>
              <Calendar className="h-3 w-3" />
              <span>
                {isOverdue ? 'Overdue: ' : 'Due: '}
                {format(new Date(task.due_date), 'MMM d')}
              </span>
            </div>
          )}

          {/* Assigned users */}
          {assignedUsers.length > 0 && (
            <div className="flex items-center gap-2">
              <div className="flex -space-x-1">
                {assignedUsers.slice(0, 3).map((user, index) => (
                  <Avatar key={user.id} className="h-5 w-5 border border-white">
                    <AvatarFallback className="text-xs">
                      {user.full_name.split(' ').map(n => n[0]).join('')}
                    </AvatarFallback>
                  </Avatar>
                ))}
                {assignedUsers.length > 3 && (
                  <div className="h-5 w-5 rounded-full bg-gray-200 border border-white flex items-center justify-center">
                    <span className="text-xs text-gray-600">+{assignedUsers.length - 3}</span>
                  </div>
                )}
              </div>
              {primaryUser && (
                <span className="text-xs text-gray-500 truncate">
                  {primaryUser.full_name}
                </span>
              )}
            </div>
          )}

          {/* Progress indicators */}
          <div className="flex items-center justify-between text-xs text-gray-500">
            <div className="flex items-center gap-3">
              {/* Sub-tasks progress */}
              {(task.subtask_count || 0) > 0 && (
                <div className="flex items-center gap-1">
                  <CheckSquare className="h-3 w-3" />
                  <span>{task.completed_subtasks || 0}/{task.subtask_count}</span>
                </div>
              )}

              {/* Attachments count */}
              {(task.attachment_count || 0) > 0 && (
                <div className="flex items-center gap-1">
                  <Paperclip className="h-3 w-3" />
                  <span>{task.attachment_count}</span>
                </div>
              )}

              {/* Multiple assignees indicator */}
              {assignedUsers.length > 1 && (
                <div className="flex items-center gap-1">
                  <Users className="h-3 w-3" />
                  <span>{assignedUsers.length}</span>
                </div>
              )}
            </div>

            {/* Edit/Delete buttons for admin/owners (smaller and less prominent) */}
            {canModify && onEdit && onDelete && (
              <div className="flex gap-1 opacity-0 group-hover:opacity-100 transition-opacity">
                <Button
                  variant="ghost"
                  size="sm"
                  className="h-5 w-5 p-0 text-gray-400 hover:text-green-600"
                  onClick={(e) => {
                    e.stopPropagation()
                    onEdit()
                  }}
                >
                  <Edit className="h-3 w-3" />
                </Button>
                <Button
                  variant="ghost"
                  size="sm"
                  className="h-5 w-5 p-0 text-gray-400 hover:text-red-600"
                  onClick={(e) => {
                    e.stopPropagation()
                    onDelete()
                  }}
                >
                  <Trash2 className="h-3 w-3" />
                </Button>
              </div>
            )}
          </div>
        </div>
      </CardContent>
    </Card>
  )
}
