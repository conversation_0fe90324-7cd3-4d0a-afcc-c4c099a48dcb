-- ============================================================================
-- PAYROLL SYSTEM DATABASE SCHEMA ENHANCEMENT
-- Phase 1: Database Schema Analysis & Enhancement
-- ============================================================================

-- Step 1: Check current payroll table structure
SELECT 
    column_name, 
    data_type, 
    is_nullable,
    column_default
FROM information_schema.columns 
WHERE table_name = 'payroll' 
ORDER BY ordinal_position;

-- ============================================================================
-- Step 2: Enhance existing payroll table with Nepal-specific fields
-- ============================================================================

-- Add Nepal-specific columns to existing payroll table
ALTER TABLE payroll ADD COLUMN IF NOT EXISTS nepali_fiscal_year VARCHAR(10);
ALTER TABLE payroll ADD COLUMN IF NOT EXISTS bs_pay_period_start VARCHAR(10); -- Bikram Sambat date
ALTER TABLE payroll ADD COLUMN IF NOT EXISTS bs_pay_period_end VARCHAR(10);   -- Bikram Sambat date
ALTER TABLE payroll ADD COLUMN IF NOT EXISTS working_days INTEGER DEFAULT 0;
ALTER TABLE payroll ADD COLUMN IF NOT EXISTS public_holidays INTEGER DEFAULT 0;
ALTER TABLE payroll ADD COLUMN IF NOT EXISTS total_attendance_hours DECIMAL(6,2) DEFAULT 0;
ALTER TABLE payroll ADD COLUMN IF NOT EXISTS regular_hours DECIMAL(6,2) DEFAULT 0;
ALTER TABLE payroll ADD COLUMN IF NOT EXISTS overtime_hours_calculated DECIMAL(6,2) DEFAULT 0;
ALTER TABLE payroll ADD COLUMN IF NOT EXISTS pay_structure_type VARCHAR(20) DEFAULT 'monthly' 
    CHECK (pay_structure_type IN ('hourly', 'daily', 'monthly', 'project_based'));
ALTER TABLE payroll ADD COLUMN IF NOT EXISTS hourly_rate DECIMAL(8,2) DEFAULT 0;
ALTER TABLE payroll ADD COLUMN IF NOT EXISTS daily_rate DECIMAL(8,2) DEFAULT 0;
ALTER TABLE payroll ADD COLUMN IF NOT EXISTS provident_fund DECIMAL(10,2) DEFAULT 0;
ALTER TABLE payroll ADD COLUMN IF NOT EXISTS insurance_deduction DECIMAL(10,2) DEFAULT 0;
ALTER TABLE payroll ADD COLUMN IF NOT EXISTS other_allowances DECIMAL(10,2) DEFAULT 0;
ALTER TABLE payroll ADD COLUMN IF NOT EXISTS attendance_bonus DECIMAL(10,2) DEFAULT 0;
ALTER TABLE payroll ADD COLUMN IF NOT EXISTS late_penalty DECIMAL(10,2) DEFAULT 0;

-- Add updated_at column if it doesn't exist
ALTER TABLE payroll ADD COLUMN IF NOT EXISTS updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW();

-- ============================================================================
-- Step 3: Create payroll_components table for flexible deductions/allowances
-- ============================================================================

CREATE TABLE IF NOT EXISTS payroll_components (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    payroll_id UUID NOT NULL REFERENCES payroll(id) ON DELETE CASCADE,
    component_type VARCHAR(50) NOT NULL CHECK (component_type IN ('allowance', 'deduction', 'tax', 'bonus', 'penalty')),
    component_name VARCHAR(100) NOT NULL,
    amount DECIMAL(10,2) NOT NULL DEFAULT 0,
    is_percentage BOOLEAN DEFAULT FALSE,
    percentage_base VARCHAR(50) DEFAULT 'base_salary' CHECK (percentage_base IN ('base_salary', 'gross_pay', 'net_pay')),
    is_taxable BOOLEAN DEFAULT TRUE,
    description TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- ============================================================================
-- Step 4: Create nepali_calendar_config table for BS/AD conversion
-- ============================================================================

CREATE TABLE IF NOT EXISTS nepali_calendar_config (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    bs_year INTEGER NOT NULL,
    bs_month INTEGER NOT NULL CHECK (bs_month BETWEEN 1 AND 12),
    bs_day INTEGER NOT NULL CHECK (bs_day BETWEEN 1 AND 32),
    ad_date DATE NOT NULL UNIQUE,
    is_holiday BOOLEAN DEFAULT FALSE,
    holiday_name VARCHAR(200),
    holiday_type VARCHAR(50) CHECK (holiday_type IN ('public', 'festival', 'observance')),
    is_working_day BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- ============================================================================
-- Step 5: Create payroll_periods table for fiscal year management
-- ============================================================================

CREATE TABLE IF NOT EXISTS payroll_periods (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    period_name VARCHAR(100) NOT NULL, -- e.g., "Shrawan 2081", "Q1 2024-25"
    period_type VARCHAR(20) NOT NULL CHECK (period_type IN ('monthly', 'quarterly', 'yearly')),
    fiscal_year VARCHAR(10) NOT NULL, -- e.g., "2081-82"
    bs_start_date VARCHAR(10) NOT NULL, -- Bikram Sambat format
    bs_end_date VARCHAR(10) NOT NULL,   -- Bikram Sambat format
    ad_start_date DATE NOT NULL,
    ad_end_date DATE NOT NULL,
    working_days INTEGER DEFAULT 0,
    public_holidays INTEGER DEFAULT 0,
    is_current_period BOOLEAN DEFAULT FALSE,
    is_closed BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(period_name, fiscal_year)
);

-- ============================================================================
-- Step 6: Create payroll_settings table for system configuration
-- ============================================================================

CREATE TABLE IF NOT EXISTS payroll_settings (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    setting_key VARCHAR(100) NOT NULL UNIQUE,
    setting_value TEXT NOT NULL,
    setting_type VARCHAR(20) NOT NULL CHECK (setting_type IN ('string', 'number', 'boolean', 'json')),
    description TEXT,
    is_system_setting BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- ============================================================================
-- Step 7: Create employee_pay_structure table for individual pay configurations
-- ============================================================================

CREATE TABLE IF NOT EXISTS employee_pay_structure (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    pay_structure_type VARCHAR(20) NOT NULL DEFAULT 'monthly' 
        CHECK (pay_structure_type IN ('hourly', 'daily', 'monthly', 'project_based')),
    base_salary DECIMAL(10,2) DEFAULT 0,
    hourly_rate DECIMAL(8,2) DEFAULT 0,
    daily_rate DECIMAL(8,2) DEFAULT 0,
    overtime_rate_multiplier DECIMAL(3,2) DEFAULT 1.5, -- 1.5x for overtime
    provident_fund_percentage DECIMAL(5,2) DEFAULT 10.0, -- 10% PF
    insurance_amount DECIMAL(10,2) DEFAULT 0,
    effective_from DATE NOT NULL,
    effective_to DATE,
    is_active BOOLEAN DEFAULT TRUE,
    created_by UUID REFERENCES users(id),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(user_id, effective_from)
);

-- ============================================================================
-- Step 8: Create indexes for performance optimization
-- ============================================================================

-- Payroll table indexes
CREATE INDEX IF NOT EXISTS idx_payroll_fiscal_year ON payroll(nepali_fiscal_year);
CREATE INDEX IF NOT EXISTS idx_payroll_status ON payroll(status);
CREATE INDEX IF NOT EXISTS idx_payroll_pay_structure ON payroll(pay_structure_type);

-- Payroll components indexes
CREATE INDEX IF NOT EXISTS idx_payroll_components_payroll_id ON payroll_components(payroll_id);
CREATE INDEX IF NOT EXISTS idx_payroll_components_type ON payroll_components(component_type);

-- Nepali calendar indexes
CREATE INDEX IF NOT EXISTS idx_nepali_calendar_ad_date ON nepali_calendar_config(ad_date);
CREATE INDEX IF NOT EXISTS idx_nepali_calendar_bs_date ON nepali_calendar_config(bs_year, bs_month, bs_day);
CREATE INDEX IF NOT EXISTS idx_nepali_calendar_holidays ON nepali_calendar_config(is_holiday, ad_date);

-- Payroll periods indexes
CREATE INDEX IF NOT EXISTS idx_payroll_periods_fiscal_year ON payroll_periods(fiscal_year);
CREATE INDEX IF NOT EXISTS idx_payroll_periods_current ON payroll_periods(is_current_period) WHERE is_current_period = TRUE;
CREATE INDEX IF NOT EXISTS idx_payroll_periods_ad_dates ON payroll_periods(ad_start_date, ad_end_date);

-- Employee pay structure indexes
CREATE INDEX IF NOT EXISTS idx_employee_pay_structure_user ON employee_pay_structure(user_id);
CREATE INDEX IF NOT EXISTS idx_employee_pay_structure_active ON employee_pay_structure(user_id, is_active) WHERE is_active = TRUE;
CREATE INDEX IF NOT EXISTS idx_employee_pay_structure_effective ON employee_pay_structure(effective_from, effective_to);

-- ============================================================================
-- Step 9: Insert default payroll settings
-- ============================================================================

INSERT INTO payroll_settings (setting_key, setting_value, setting_type, description, is_system_setting) VALUES
('default_working_hours_per_day', '8', 'number', 'Standard working hours per day', TRUE),
('overtime_threshold_hours', '8', 'number', 'Hours after which overtime applies', TRUE),
('default_overtime_multiplier', '1.5', 'number', 'Default overtime rate multiplier', TRUE),
('provident_fund_rate', '10', 'number', 'Default provident fund percentage', TRUE),
('tax_threshold_annual', '500000', 'number', 'Annual income tax threshold in NPR', TRUE),
('currency_symbol', 'NPR', 'string', 'Currency symbol for display', TRUE),
('fiscal_year_start_month', '4', 'number', 'Fiscal year start month (Shrawan = 4)', TRUE),
('working_days_per_week', '6', 'number', 'Standard working days per week', TRUE),
('late_penalty_per_minute', '10', 'number', 'Penalty amount per minute of lateness', TRUE),
('attendance_bonus_threshold', '95', 'number', 'Attendance percentage for bonus eligibility', TRUE)
ON CONFLICT (setting_key) DO NOTHING;

-- ============================================================================
-- Step 10: Create updated_at triggers
-- ============================================================================

-- Create trigger function if it doesn't exist
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Add triggers for updated_at columns
DROP TRIGGER IF EXISTS update_payroll_updated_at ON payroll;
CREATE TRIGGER update_payroll_updated_at 
    BEFORE UPDATE ON payroll 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

DROP TRIGGER IF EXISTS update_payroll_components_updated_at ON payroll_components;
CREATE TRIGGER update_payroll_components_updated_at 
    BEFORE UPDATE ON payroll_components 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

DROP TRIGGER IF EXISTS update_payroll_periods_updated_at ON payroll_periods;
CREATE TRIGGER update_payroll_periods_updated_at 
    BEFORE UPDATE ON payroll_periods 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

DROP TRIGGER IF EXISTS update_payroll_settings_updated_at ON payroll_settings;
CREATE TRIGGER update_payroll_settings_updated_at 
    BEFORE UPDATE ON payroll_settings 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

DROP TRIGGER IF EXISTS update_employee_pay_structure_updated_at ON employee_pay_structure;
CREATE TRIGGER update_employee_pay_structure_updated_at 
    BEFORE UPDATE ON employee_pay_structure 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- ============================================================================
-- Step 11: Verification queries
-- ============================================================================

-- Verify enhanced payroll table structure
SELECT 
    column_name, 
    data_type, 
    is_nullable,
    column_default
FROM information_schema.columns 
WHERE table_name = 'payroll' 
ORDER BY ordinal_position;

-- Verify new tables were created
SELECT table_name 
FROM information_schema.tables 
WHERE table_name IN ('payroll_components', 'nepali_calendar_config', 'payroll_periods', 'payroll_settings', 'employee_pay_structure')
ORDER BY table_name;

-- Check payroll settings
SELECT setting_key, setting_value, description FROM payroll_settings ORDER BY setting_key;

-- ============================================================================
-- Step 12: Create deductions and allowances master tables
-- ============================================================================

CREATE TABLE IF NOT EXISTS payroll_components_master (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name VARCHAR(100) NOT NULL,
    code VARCHAR(50) NOT NULL UNIQUE,
    type VARCHAR(20) NOT NULL CHECK (type IN ('deduction', 'allowance')),
    category VARCHAR(30) NOT NULL CHECK (category IN ('statutory', 'voluntary', 'company_policy', 'custom')),
    calculation_type VARCHAR(20) NOT NULL CHECK (calculation_type IN ('fixed', 'percentage', 'formula', 'conditional')),

    -- Calculation parameters
    fixed_amount DECIMAL(10,2),
    percentage DECIMAL(5,2),
    percentage_base VARCHAR(30) CHECK (percentage_base IN ('base_salary', 'gross_pay', 'net_pay', 'total_earnings')),
    formula TEXT,
    conditions JSONB,

    -- Tax and compliance
    is_taxable BOOLEAN DEFAULT TRUE,
    is_statutory BOOLEAN DEFAULT FALSE,
    affects_provident_fund BOOLEAN DEFAULT TRUE,
    affects_gratuity BOOLEAN DEFAULT TRUE,

    -- Applicability
    applicable_to_pay_structures JSONB DEFAULT '[]',
    applicable_to_employee_categories JSONB DEFAULT '[]',
    applicable_to_departments JSONB DEFAULT '[]',

    -- Limits and validations
    minimum_amount DECIMAL(10,2),
    maximum_amount DECIMAL(10,2),
    minimum_salary_threshold DECIMAL(10,2),
    maximum_salary_threshold DECIMAL(10,2),

    -- Metadata
    description TEXT,
    is_active BOOLEAN DEFAULT TRUE,
    effective_from DATE NOT NULL,
    effective_to DATE,
    created_by UUID REFERENCES users(id),
    approved_by UUID REFERENCES users(id),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

CREATE TABLE IF NOT EXISTS employee_component_assignments (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    component_id UUID NOT NULL REFERENCES payroll_components_master(id) ON DELETE CASCADE,
    is_active BOOLEAN DEFAULT TRUE,
    effective_from DATE NOT NULL,
    effective_to DATE,

    -- Override values
    override_amount DECIMAL(10,2),
    override_percentage DECIMAL(5,2),
    override_conditions JSONB,

    -- Approval
    assigned_by UUID REFERENCES users(id),
    approved_by UUID REFERENCES users(id),
    approval_date DATE,
    notes TEXT,

    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),

    UNIQUE(user_id, component_id, effective_from)
);

-- ============================================================================
-- Step 13: Create indexes for deductions and allowances tables
-- ============================================================================

-- Payroll components master indexes
CREATE INDEX IF NOT EXISTS idx_payroll_components_master_code ON payroll_components_master(code);
CREATE INDEX IF NOT EXISTS idx_payroll_components_master_type ON payroll_components_master(type);
CREATE INDEX IF NOT EXISTS idx_payroll_components_master_category ON payroll_components_master(category);
CREATE INDEX IF NOT EXISTS idx_payroll_components_master_active ON payroll_components_master(is_active) WHERE is_active = TRUE;
CREATE INDEX IF NOT EXISTS idx_payroll_components_master_effective ON payroll_components_master(effective_from, effective_to);

-- Employee component assignments indexes
CREATE INDEX IF NOT EXISTS idx_employee_component_assignments_user ON employee_component_assignments(user_id);
CREATE INDEX IF NOT EXISTS idx_employee_component_assignments_component ON employee_component_assignments(component_id);
CREATE INDEX IF NOT EXISTS idx_employee_component_assignments_active ON employee_component_assignments(user_id, is_active) WHERE is_active = TRUE;
CREATE INDEX IF NOT EXISTS idx_employee_component_assignments_effective ON employee_component_assignments(effective_from, effective_to);

-- ============================================================================
-- Step 14: Add triggers for deductions and allowances tables
-- ============================================================================

DROP TRIGGER IF EXISTS update_payroll_components_master_updated_at ON payroll_components_master;
CREATE TRIGGER update_payroll_components_master_updated_at
    BEFORE UPDATE ON payroll_components_master
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

DROP TRIGGER IF EXISTS update_employee_component_assignments_updated_at ON employee_component_assignments;
CREATE TRIGGER update_employee_component_assignments_updated_at
    BEFORE UPDATE ON employee_component_assignments
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- ============================================================================
-- SUCCESS CRITERIA
-- ============================================================================
-- After successful completion:
-- ✅ payroll table enhanced with Nepal-specific fields
-- ✅ payroll_components table created for flexible deductions/allowances
-- ✅ nepali_calendar_config table created for BS/AD conversion
-- ✅ payroll_periods table created for fiscal year management
-- ✅ payroll_settings table created with default configurations
-- ✅ employee_pay_structure table created for individual pay configs
-- ✅ payroll_components_master table created for component definitions
-- ✅ employee_component_assignments table created for employee assignments
-- ✅ All necessary indexes created for performance
-- ✅ Updated_at triggers configured for all tables
-- ✅ Default payroll settings inserted
