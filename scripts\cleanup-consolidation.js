#!/usr/bin/env node

const fs = require('fs');
const path = require('path');

function cleanupConsolidation() {
  console.log('🧹 CLEANING UP CONSOLIDATION FILES');
  console.log('==================================');
  
  // Files that can be safely removed after consolidation
  const filesToRemove = [
    'scripts/analyze-both-databases.js',
    'scripts/detailed-db-analysis.js',
    'scripts/fixed-db-analysis.js',
    'scripts/simple-db-test.js',
    'scripts/export-secondary-data.js',
    'scripts/import-to-primary.js',
    'scripts/smart-import.js',
    'scripts/compare-schemas.js',
    'scripts/schema-comparison.js',
    'scripts/secondary-db-export.json',
    'scripts/update-hardcoded-connections.js'
  ];
  
  // Files to keep for reference
  const filesToKeep = [
    'scripts/test-consolidation.js',
    'scripts/test-api-consistency.js',
    'scripts/cleanup-consolidation.js'
  ];
  
  let removedCount = 0;
  let keptCount = 0;
  
  console.log('\n🗑️  Removing temporary consolidation files:');
  
  for (const file of filesToRemove) {
    if (fs.existsSync(file)) {
      try {
        fs.unlinkSync(file);
        console.log(`   ✅ Removed: ${file}`);
        removedCount++;
      } catch (error) {
        console.log(`   ❌ Failed to remove: ${file} - ${error.message}`);
      }
    } else {
      console.log(`   ⚠️  Not found: ${file}`);
    }
  }
  
  console.log('\n📋 Keeping reference files:');
  for (const file of filesToKeep) {
    if (fs.existsSync(file)) {
      console.log(`   📄 Kept: ${file}`);
      keptCount++;
    }
  }
  
  console.log('\n📊 CLEANUP SUMMARY:');
  console.log(`   Removed: ${removedCount} files`);
  console.log(`   Kept: ${keptCount} reference files`);
  
  console.log('\n✅ Cleanup completed successfully!');
  console.log('\n📋 CONSOLIDATION COMPLETE:');
  console.log('- All data is now in the primary database');
  console.log('- All scripts use environment variables');
  console.log('- Employee counts are consistent across the application');
  console.log('- Temporary files have been cleaned up');
}

cleanupConsolidation();
