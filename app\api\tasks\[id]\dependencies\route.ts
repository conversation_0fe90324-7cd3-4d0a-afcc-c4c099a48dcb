import { NextRequest, NextResponse } from "next/server"
import { AuthService } from "@/lib/auth-utils"
import { serverDb } from "@/lib/server-db"
import { z } from "zod"

// Validation schema for dependency creation
const createDependencySchema = z.object({
  depends_on_task_id: z.string().uuid("Invalid task ID"),
  dependency_type: z.enum(["blocks", "relates_to"]).default("blocks"),
})

// GET /api/tasks/[id]/dependencies - Get task dependencies
export async function GET(request: NextRequest, { params }: { params: { id: string } }) {
  try {
    const sessionToken = request.cookies.get("session-token")?.value

    if (!sessionToken) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 })
    }

    const user = await AuthService.verifySession(sessionToken)

    if (!user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 })
    }

    const { id: taskId } = params

    // Check if task exists and user has access
    const taskResult = await serverDb.sql`
      SELECT * FROM tasks WHERE id = ${taskId}
    `

    if (taskResult.length === 0) {
      return NextResponse.json({ error: "Task not found" }, { status: 404 })
    }

    const task = taskResult[0]

    // Check access permissions
    const hasAccess = 
      ["admin", "manager"].includes(user.role) ||
      task.assigned_to === user.id ||
      task.assigned_by === user.id

    if (!hasAccess) {
      return NextResponse.json({ error: "Access denied" }, { status: 403 })
    }

    // Get dependencies (tasks this task depends on)
    const dependencies = await serverDb.sql`
      SELECT 
        d.*,
        t.title as depends_on_title,
        t.status as depends_on_status,
        t.priority as depends_on_priority,
        u.full_name as created_by_name
      FROM task_dependencies d
      LEFT JOIN tasks t ON d.depends_on_task_id = t.id
      LEFT JOIN users u ON d.created_by = u.id
      WHERE d.task_id = ${taskId}
      ORDER BY d.created_at DESC
    `

    // Get dependents (tasks that depend on this task)
    const dependents = await serverDb.sql`
      SELECT 
        d.*,
        t.title as dependent_title,
        t.status as dependent_status,
        t.priority as dependent_priority,
        u.full_name as created_by_name
      FROM task_dependencies d
      LEFT JOIN tasks t ON d.task_id = t.id
      LEFT JOIN users u ON d.created_by = u.id
      WHERE d.depends_on_task_id = ${taskId}
      ORDER BY d.created_at DESC
    `

    return NextResponse.json({
      success: true,
      data: {
        dependencies,
        dependents,
      }
    })

  } catch (error) {
    console.error("Get task dependencies error:", error)
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    )
  }
}

// POST /api/tasks/[id]/dependencies - Add dependency to task
export async function POST(request: NextRequest, { params }: { params: { id: string } }) {
  try {
    const sessionToken = request.cookies.get("session-token")?.value

    if (!sessionToken) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 })
    }

    const user = await AuthService.verifySession(sessionToken)

    if (!user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 })
    }

    const { id: taskId } = params

    // Check if task exists and user has access
    const taskResult = await serverDb.sql`
      SELECT * FROM tasks WHERE id = ${taskId}
    `

    if (taskResult.length === 0) {
      return NextResponse.json({ error: "Task not found" }, { status: 404 })
    }

    const task = taskResult[0]

    // Check access permissions
    const hasAccess = 
      ["admin", "manager"].includes(user.role) ||
      task.assigned_to === user.id ||
      task.assigned_by === user.id

    if (!hasAccess) {
      return NextResponse.json({ error: "Access denied" }, { status: 403 })
    }

    // Parse and validate request body
    const body = await request.json()
    const { depends_on_task_id, dependency_type } = createDependencySchema.parse(body)

    // Check if the dependency task exists
    const dependencyTaskResult = await serverDb.sql`
      SELECT * FROM tasks WHERE id = ${depends_on_task_id}
    `

    if (dependencyTaskResult.length === 0) {
      return NextResponse.json({ error: "Dependency task not found" }, { status: 404 })
    }

    // Check for circular dependencies
    const circularCheck = await serverDb.sql`
      WITH RECURSIVE dependency_chain AS (
        SELECT task_id, depends_on_task_id, 1 as depth
        FROM task_dependencies
        WHERE depends_on_task_id = ${taskId}
        
        UNION ALL
        
        SELECT d.task_id, d.depends_on_task_id, dc.depth + 1
        FROM task_dependencies d
        INNER JOIN dependency_chain dc ON d.depends_on_task_id = dc.task_id
        WHERE dc.depth < 10
      )
      SELECT 1 FROM dependency_chain WHERE task_id = ${depends_on_task_id}
    `

    if (circularCheck.length > 0) {
      return NextResponse.json({ 
        error: "Cannot create dependency: would create circular dependency" 
      }, { status: 400 })
    }

    // Create the dependency
    const dependencyResult = await serverDb.sql`
      INSERT INTO task_dependencies (task_id, depends_on_task_id, dependency_type, created_by)
      VALUES (${taskId}, ${depends_on_task_id}, ${dependency_type}, ${user.id})
      ON CONFLICT (task_id, depends_on_task_id) DO NOTHING
      RETURNING *
    `

    if (dependencyResult.length === 0) {
      return NextResponse.json({ 
        error: "Dependency already exists" 
      }, { status: 409 })
    }

    const newDependency = dependencyResult[0]

    // Get the complete dependency data
    const completeDependencyResult = await serverDb.sql`
      SELECT 
        d.*,
        t.title as depends_on_title,
        t.status as depends_on_status,
        t.priority as depends_on_priority,
        u.full_name as created_by_name
      FROM task_dependencies d
      LEFT JOIN tasks t ON d.depends_on_task_id = t.id
      LEFT JOIN users u ON d.created_by = u.id
      WHERE d.id = ${newDependency.id}
    `

    return NextResponse.json({
      success: true,
      data: completeDependencyResult[0],
      message: "Dependency created successfully"
    }, { status: 201 })

  } catch (error) {
    console.error("Create dependency error:", error)
    
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: "Invalid dependency data", details: error.errors },
        { status: 400 }
      )
    }

    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    )
  }
}
