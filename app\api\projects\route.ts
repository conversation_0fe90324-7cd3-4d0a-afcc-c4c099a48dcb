import { NextRequest, NextResponse } from "next/server"
import { AuthService } from "@/lib/auth-utils"
import { serverDb } from "@/lib/server-db"
import { z } from "zod"

// Validation schema for project creation
const createProjectSchema = z.object({
  name: z.string().min(1, "Name is required").max(255, "Name too long"),
  description: z.string().optional(),
  color: z.string().regex(/^#[0-9A-F]{6}$/i, "Invalid color format").optional(),
})

// GET /api/projects - List projects
export async function GET(request: NextRequest) {
  try {
    const sessionToken = request.cookies.get("session-token")?.value

    if (!sessionToken) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 })
    }

    const user = await AuthService.verifySession(sessionToken)

    if (!user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 })
    }

    // Get projects with task counts
    const projects = await serverDb.sql`
      SELECT 
        p.*,
        u.full_name as created_by_name,
        COUNT(t.id) as task_count,
        COUNT(CASE WHEN t.status = 'completed' THEN 1 END) as completed_tasks,
        COUNT(CASE WHEN t.status != 'completed' THEN 1 END) as active_tasks
      FROM task_projects p
      LEFT JOIN users u ON p.created_by = u.id
      LEFT JOIN tasks t ON p.id = t.project_id
      WHERE p.is_active = true
      AND (
        p.created_by = ${user.id} OR
        EXISTS (
          SELECT 1 FROM tasks 
          WHERE project_id = p.id 
          AND (assigned_to = ${user.id} OR assigned_by = ${user.id})
        ) OR
        ${["admin", "manager"].includes(user.role)}
      )
      GROUP BY p.id, u.full_name
      ORDER BY p.created_at DESC
    `

    return NextResponse.json({
      success: true,
      data: projects
    })

  } catch (error) {
    console.error("Projects API error:", error)
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    )
  }
}

// POST /api/projects - Create new project
export async function POST(request: NextRequest) {
  try {
    const sessionToken = request.cookies.get("session-token")?.value

    if (!sessionToken) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 })
    }

    const user = await AuthService.verifySession(sessionToken)

    if (!user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 })
    }

    // Parse and validate request body
    const body = await request.json()
    const validatedData = createProjectSchema.parse(body)

    // Create the project
    const projectResult = await serverDb.sql`
      INSERT INTO task_projects (name, description, color, created_by)
      VALUES (
        ${validatedData.name},
        ${validatedData.description || null},
        ${validatedData.color || '#3B82F6'},
        ${user.id}
      )
      RETURNING *
    `

    const newProject = projectResult[0]

    // Get the complete project data with creator info
    const completeProjectResult = await serverDb.sql`
      SELECT 
        p.*,
        u.full_name as created_by_name,
        0 as task_count,
        0 as completed_tasks,
        0 as active_tasks
      FROM task_projects p
      LEFT JOIN users u ON p.created_by = u.id
      WHERE p.id = ${newProject.id}
    `

    return NextResponse.json({
      success: true,
      data: completeProjectResult[0],
      message: "Project created successfully"
    }, { status: 201 })

  } catch (error) {
    console.error("Create project error:", error)
    
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: "Invalid project data", details: error.errors },
        { status: 400 }
      )
    }

    // Handle unique constraint violation
    if (error.code === '23505') {
      return NextResponse.json(
        { error: "A project with this name already exists" },
        { status: 409 }
      )
    }

    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    )
  }
}
