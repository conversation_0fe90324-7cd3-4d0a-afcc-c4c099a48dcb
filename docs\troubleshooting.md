# Troubleshooting Guide - Nepal Payroll Management System

## Common Issues and Solutions

### 1. MODULE_NOT_FOUND Errors

#### Problem: @sentry/nextjs module not found
```
Error: Cannot find module '@sentry/nextjs'
```

**Solution Options:**

**Option A: Install Sentry (Recommended for Production)**
```bash
npm install @sentry/nextjs
# or
pnpm add @sentry/nextjs
# or
yarn add @sentry/nextjs
```

**Option B: Disable Sentry (For Development)**
- Remove or comment out `SENTRY_DSN` from your `.env` file
- The updated `next.config.js` will automatically skip Sentry configuration

**Option C: Use Setup Script**
```bash
npm run setup
```

#### Problem: next-pwa module not found
```
Error: Cannot find module 'next-pwa'
```

**Solution:**
```bash
npm install next-pwa
# or disable PWA by removing ENABLE_PWA from .env
```

#### Problem: @next/bundle-analyzer module not found
```
Error: Cannot find module '@next/bundle-analyzer'
```

**Solution:**
```bash
npm install @next/bundle-analyzer
# or disable by removing ANALYZE=true from environment
```

### 2. Environment Configuration Issues

#### Problem: Missing environment variables
```
Error: Environment variable DATABASE_URL is required
```

**Solution:**
1. Copy the example environment file:
```bash
cp .env.example .env.local
```

2. Fill in required variables:
```bash
DATABASE_URL="your-database-url"
NEXTAUTH_SECRET="your-secret-key"
NEXTAUTH_URL="http://localhost:3000"
```

#### Problem: Invalid database URL format
```
Error: Invalid database URL format
```

**Solution:**
Ensure your DATABASE_URL follows this format:
```
postgresql://username:password@host:port/database
```

For Neon:
```
postgresql://username:<EMAIL>/database?sslmode=require
```

### 3. Build and Development Issues

#### Problem: TypeScript errors during build
```
Error: Type errors found
```

**Solution:**
```bash
# Check types without building
npm run type-check

# Fix common issues
npm run lint:fix
```

#### Problem: Next.js build fails
```
Error: Build failed
```

**Solution:**
1. Clear Next.js cache:
```bash
npm run clean
rm -rf node_modules
npm install
```

2. Check for syntax errors:
```bash
npm run lint
```

3. Verify environment variables are set correctly

#### Problem: Development server won't start
```
Error: Port 3000 is already in use
```

**Solution:**
```bash
# Kill process on port 3000
npx kill-port 3000

# Or use different port
PORT=3001 npm run dev
```

### 4. Database Issues

#### Problem: Database connection failed
```
Error: connect ECONNREFUSED
```

**Solution:**
1. Verify database URL is correct
2. Check if database server is running
3. Verify network connectivity
4. For Neon, check project status in console

#### Problem: Migration errors
```
Error: Migration failed
```

**Solution:**
```bash
# Reset database (development only)
npm run db:reset

# Or run migrations manually
npm run db:migrate
```

### 5. Authentication Issues

#### Problem: NextAuth configuration error
```
Error: NEXTAUTH_SECRET is not set
```

**Solution:**
Generate a secret and add to `.env.local`:
```bash
# Generate secret
openssl rand -base64 32

# Add to .env.local
NEXTAUTH_SECRET="generated-secret-here"
```

#### Problem: Authentication callback error
```
Error: Invalid callback URL
```

**Solution:**
Ensure NEXTAUTH_URL matches your domain:
```bash
# Development
NEXTAUTH_URL="http://localhost:3000"

# Production
NEXTAUTH_URL="https://your-domain.com"
```

### 6. Performance Issues

#### Problem: Slow page loads
**Solution:**
1. Enable caching:
```bash
ENABLE_CACHING="true"
REDIS_URL="redis://localhost:6379"
```

2. Optimize images and assets
3. Check database query performance

#### Problem: Memory issues
**Solution:**
1. Increase Node.js memory limit:
```bash
NODE_OPTIONS="--max-old-space-size=4096" npm run dev
```

2. Check for memory leaks in custom code

### 7. Deployment Issues

#### Problem: Vercel deployment fails
**Solution:**
1. Check build logs in Vercel dashboard
2. Verify environment variables are set in Vercel
3. Ensure all dependencies are in package.json

#### Problem: Docker build fails
**Solution:**
1. Check Dockerfile syntax
2. Verify all files are copied correctly
3. Check for missing dependencies

### 8. Nepal Localization Issues

#### Problem: Calendar conversion errors
```
Error: Invalid BS date
```

**Solution:**
1. Verify date format (YYYY-MM-DD for AD, {year, month, day} for BS)
2. Check if date is within supported range
3. Validate input data

#### Problem: Currency formatting issues
```
Error: Invalid currency amount
```

**Solution:**
1. Ensure amount is a valid number
2. Check for null/undefined values
3. Verify currency configuration

### 9. Quick Fixes

#### Clear all caches and restart:
```bash
npm run clean
rm -rf node_modules package-lock.json
npm install
npm run dev
```

#### Reset development environment:
```bash
# Stop all processes
pkill -f "next"

# Clear caches
npm run clean

# Reinstall dependencies
rm -rf node_modules
npm install

# Copy environment file
cp .env.example .env.local

# Start fresh
npm run dev
```

#### Check system requirements:
```bash
# Check Node.js version (should be 18+)
node --version

# Check npm version
npm --version

# Check available memory
free -h  # Linux
vm_stat  # macOS
```

### 10. Getting Help

#### Check logs:
```bash
# Application logs
tail -f logs/combined.log

# Next.js logs
npm run dev -- --debug

# Database logs (if using local PostgreSQL)
tail -f /var/log/postgresql/postgresql-*.log
```

#### Debug mode:
```bash
# Enable debug logging
DEBUG=* npm run dev

# Or specific modules
DEBUG=payroll:* npm run dev
```

#### Report issues:
1. Check existing issues in project repository
2. Provide error logs and environment details
3. Include steps to reproduce the problem

### 11. Prevention Tips

1. **Always use the setup script** for new installations:
```bash
npm run setup
```

2. **Keep dependencies updated**:
```bash
npm audit
npm update
```

3. **Use environment files** for configuration:
- `.env.local` for development
- `.env.production` for production
- Never commit sensitive data

4. **Regular backups**:
```bash
npm run db:backup
```

5. **Monitor system health**:
- Check logs regularly
- Monitor database performance
- Watch for compliance violations

---

## Still Having Issues?

If you're still experiencing problems:

1. **Check the documentation**:
   - `docs/user-guide.md` for usage instructions
   - `docs/deployment-guide.md` for deployment help
   - `docs/api-documentation.md` for API reference

2. **Run the setup script**:
```bash
npm run setup
```

3. **Contact support** with:
   - Error messages and logs
   - Environment details (Node.js version, OS, etc.)
   - Steps to reproduce the issue
   - Configuration files (without sensitive data)
