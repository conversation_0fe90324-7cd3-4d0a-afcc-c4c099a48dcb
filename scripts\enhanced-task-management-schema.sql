-- Enhanced Task Management System Database Schema
-- This script creates comprehensive schema for multi-user assignments, sub-tasks, and file attachments

-- Create task_assignments table for many-to-many user assignments
CREATE TABLE IF NOT EXISTS task_assignments (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    task_id UUID NOT NULL REFERENCES tasks(id) ON DELETE CASCADE,
    user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    assigned_by UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    assigned_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    is_primary BOOLEAN DEFAULT false, -- One primary assignee per task
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    CONSTRAINT task_assignments_unique UNIQUE(task_id, user_id)
);

-- Create sub_tasks table for hierarchical task management
CREATE TABLE IF NOT EXISTS sub_tasks (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    parent_task_id UUID NOT NULL REFERENCES tasks(id) ON DELETE CASCADE,
    title VARCHAR(255) NOT NULL,
    description TEXT,
    status VARCHAR(20) NOT NULL DEFAULT 'todo' CHECK (status IN ('todo', 'in_progress', 'completed', 'cancelled')),
    assigned_to UUID REFERENCES users(id) ON DELETE SET NULL,
    assigned_by UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    due_date TIMESTAMP WITH TIME ZONE,
    position INTEGER DEFAULT 0, -- For ordering sub-tasks
    completed_at TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Enhance task_attachments table to integrate with existing file management
DROP TABLE IF EXISTS task_attachments;
CREATE TABLE task_attachments (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    task_id UUID NOT NULL REFERENCES tasks(id) ON DELETE CASCADE,
    file_id UUID NOT NULL REFERENCES user_files(id) ON DELETE CASCADE,
    uploaded_by UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    attachment_type VARCHAR(50) DEFAULT 'document' CHECK (attachment_type IN ('document', 'image', 'video', 'other')),
    description TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    CONSTRAINT task_attachments_unique UNIQUE(task_id, file_id)
);

-- Add missing columns to existing tasks table
DO $$ 
BEGIN
    -- Add actual_hours column if it doesn't exist
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns 
                   WHERE table_name = 'tasks' AND column_name = 'actual_hours') THEN
        ALTER TABLE tasks ADD COLUMN actual_hours DECIMAL(5,2) DEFAULT 0;
    END IF;
    
    -- Add tags column for better categorization
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns 
                   WHERE table_name = 'tasks' AND column_name = 'tags') THEN
        ALTER TABLE tasks ADD COLUMN tags TEXT[];
    END IF;
    
    -- Add completion_percentage for progress tracking
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns 
                   WHERE table_name = 'tasks' AND column_name = 'completion_percentage') THEN
        ALTER TABLE tasks ADD COLUMN completion_percentage INTEGER DEFAULT 0 CHECK (completion_percentage >= 0 AND completion_percentage <= 100);
    END IF;
END $$;

-- Create indexes for performance
CREATE INDEX IF NOT EXISTS idx_task_assignments_task_id ON task_assignments(task_id);
CREATE INDEX IF NOT EXISTS idx_task_assignments_user_id ON task_assignments(user_id);
CREATE INDEX IF NOT EXISTS idx_task_assignments_primary ON task_assignments(task_id, is_primary) WHERE is_primary = true;

CREATE INDEX IF NOT EXISTS idx_sub_tasks_parent_task_id ON sub_tasks(parent_task_id);
CREATE INDEX IF NOT EXISTS idx_sub_tasks_assigned_to ON sub_tasks(assigned_to);
CREATE INDEX IF NOT EXISTS idx_sub_tasks_status ON sub_tasks(status);
CREATE INDEX IF NOT EXISTS idx_sub_tasks_position ON sub_tasks(parent_task_id, position);

CREATE INDEX IF NOT EXISTS idx_task_attachments_task_id ON task_attachments(task_id);
CREATE INDEX IF NOT EXISTS idx_task_attachments_file_id ON task_attachments(file_id);

-- Create triggers for automatic timestamp updates
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Apply triggers to sub_tasks table
DROP TRIGGER IF EXISTS update_sub_tasks_updated_at ON sub_tasks;
CREATE TRIGGER update_sub_tasks_updated_at 
    BEFORE UPDATE ON sub_tasks 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Function to automatically update task completion percentage based on sub-tasks
CREATE OR REPLACE FUNCTION update_task_completion_percentage()
RETURNS TRIGGER AS $$
DECLARE
    total_subtasks INTEGER;
    completed_subtasks INTEGER;
    completion_pct INTEGER;
BEGIN
    -- Get the parent task ID
    IF TG_OP = 'DELETE' THEN
        -- Count sub-tasks for the deleted sub-task's parent
        SELECT COUNT(*), COUNT(*) FILTER (WHERE status = 'completed')
        INTO total_subtasks, completed_subtasks
        FROM sub_tasks 
        WHERE parent_task_id = OLD.parent_task_id;
        
        -- Calculate completion percentage
        IF total_subtasks > 0 THEN
            completion_pct = ROUND((completed_subtasks::DECIMAL / total_subtasks) * 100);
        ELSE
            completion_pct = 0;
        END IF;
        
        -- Update parent task
        UPDATE tasks 
        SET completion_percentage = completion_pct,
            updated_at = NOW()
        WHERE id = OLD.parent_task_id;
        
        RETURN OLD;
    ELSE
        -- Count sub-tasks for the current parent task
        SELECT COUNT(*), COUNT(*) FILTER (WHERE status = 'completed')
        INTO total_subtasks, completed_subtasks
        FROM sub_tasks 
        WHERE parent_task_id = NEW.parent_task_id;
        
        -- Calculate completion percentage
        IF total_subtasks > 0 THEN
            completion_pct = ROUND((completed_subtasks::DECIMAL / total_subtasks) * 100);
        ELSE
            completion_pct = 0;
        END IF;
        
        -- Update parent task
        UPDATE tasks 
        SET completion_percentage = completion_pct,
            updated_at = NOW()
        WHERE id = NEW.parent_task_id;
        
        RETURN NEW;
    END IF;
END;
$$ language 'plpgsql';

-- Apply trigger to automatically update completion percentage
DROP TRIGGER IF EXISTS update_completion_percentage_trigger ON sub_tasks;
CREATE TRIGGER update_completion_percentage_trigger 
    AFTER INSERT OR UPDATE OR DELETE ON sub_tasks 
    FOR EACH ROW EXECUTE FUNCTION update_task_completion_percentage();

-- Function to ensure only one primary assignee per task
CREATE OR REPLACE FUNCTION ensure_single_primary_assignee()
RETURNS TRIGGER AS $$
BEGIN
    -- If setting a new primary assignee, remove primary flag from others
    IF NEW.is_primary = true THEN
        UPDATE task_assignments 
        SET is_primary = false 
        WHERE task_id = NEW.task_id AND user_id != NEW.user_id;
    END IF;
    
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Apply trigger to ensure single primary assignee
DROP TRIGGER IF EXISTS ensure_single_primary_trigger ON task_assignments;
CREATE TRIGGER ensure_single_primary_trigger 
    BEFORE INSERT OR UPDATE ON task_assignments 
    FOR EACH ROW EXECUTE FUNCTION ensure_single_primary_assignee();

-- Create RLS policies for new tables
ALTER TABLE task_assignments ENABLE ROW LEVEL SECURITY;
ALTER TABLE sub_tasks ENABLE ROW LEVEL SECURITY;
ALTER TABLE task_attachments ENABLE ROW LEVEL SECURITY;

-- RLS policies for task_assignments
CREATE POLICY "Users can view task assignments for accessible tasks" ON task_assignments
    FOR SELECT USING (
        user_id = auth.uid() OR
        assigned_by = auth.uid() OR
        EXISTS (
            SELECT 1 FROM tasks 
            WHERE tasks.id = task_assignments.task_id 
            AND (tasks.assigned_to = auth.uid() OR tasks.assigned_by = auth.uid())
        ) OR
        EXISTS (
            SELECT 1 FROM users 
            WHERE id = auth.uid() 
            AND role IN ('admin', 'hr_manager')
        )
    );

CREATE POLICY "Authorized users can manage task assignments" ON task_assignments
    FOR ALL USING (
        assigned_by = auth.uid() OR
        EXISTS (
            SELECT 1 FROM tasks 
            WHERE tasks.id = task_assignments.task_id 
            AND tasks.assigned_by = auth.uid()
        ) OR
        EXISTS (
            SELECT 1 FROM users 
            WHERE id = auth.uid() 
            AND role IN ('admin', 'hr_manager')
        )
    );

-- RLS policies for sub_tasks
CREATE POLICY "Users can view sub-tasks for accessible parent tasks" ON sub_tasks
    FOR SELECT USING (
        assigned_to = auth.uid() OR
        assigned_by = auth.uid() OR
        EXISTS (
            SELECT 1 FROM tasks 
            WHERE tasks.id = sub_tasks.parent_task_id 
            AND (tasks.assigned_to = auth.uid() OR tasks.assigned_by = auth.uid())
        ) OR
        EXISTS (
            SELECT 1 FROM task_assignments 
            WHERE task_assignments.task_id = sub_tasks.parent_task_id 
            AND task_assignments.user_id = auth.uid()
        ) OR
        EXISTS (
            SELECT 1 FROM users 
            WHERE id = auth.uid() 
            AND role IN ('admin', 'hr_manager')
        )
    );

CREATE POLICY "Authorized users can manage sub-tasks" ON sub_tasks
    FOR ALL USING (
        assigned_by = auth.uid() OR
        assigned_to = auth.uid() OR
        EXISTS (
            SELECT 1 FROM tasks 
            WHERE tasks.id = sub_tasks.parent_task_id 
            AND (tasks.assigned_to = auth.uid() OR tasks.assigned_by = auth.uid())
        ) OR
        EXISTS (
            SELECT 1 FROM task_assignments 
            WHERE task_assignments.task_id = sub_tasks.parent_task_id 
            AND task_assignments.user_id = auth.uid()
        ) OR
        EXISTS (
            SELECT 1 FROM users 
            WHERE id = auth.uid() 
            AND role IN ('admin', 'hr_manager')
        )
    );

-- RLS policies for task_attachments
CREATE POLICY "Users can view attachments for accessible tasks" ON task_attachments
    FOR SELECT USING (
        uploaded_by = auth.uid() OR
        EXISTS (
            SELECT 1 FROM tasks 
            WHERE tasks.id = task_attachments.task_id 
            AND (tasks.assigned_to = auth.uid() OR tasks.assigned_by = auth.uid())
        ) OR
        EXISTS (
            SELECT 1 FROM task_assignments 
            WHERE task_assignments.task_id = task_attachments.task_id 
            AND task_assignments.user_id = auth.uid()
        ) OR
        EXISTS (
            SELECT 1 FROM users 
            WHERE id = auth.uid() 
            AND role IN ('admin', 'hr_manager')
        )
    );

CREATE POLICY "Authorized users can manage task attachments" ON task_attachments
    FOR ALL USING (
        uploaded_by = auth.uid() OR
        EXISTS (
            SELECT 1 FROM tasks 
            WHERE tasks.id = task_attachments.task_id 
            AND (tasks.assigned_to = auth.uid() OR tasks.assigned_by = auth.uid())
        ) OR
        EXISTS (
            SELECT 1 FROM task_assignments 
            WHERE task_assignments.task_id = task_attachments.task_id 
            AND task_assignments.user_id = auth.uid()
        ) OR
        EXISTS (
            SELECT 1 FROM users 
            WHERE id = auth.uid() 
            AND role IN ('admin', 'hr_manager')
        )
    );

COMMIT;
