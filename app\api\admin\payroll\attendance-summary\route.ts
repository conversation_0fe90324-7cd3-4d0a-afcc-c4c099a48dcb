// Attendance Summary API for Payroll Calculations
// Fetches attendance data for selected pay periods to enable attendance-based salary calculations

import { NextRequest, NextResponse } from 'next/server';
import { AuthService } from '@/lib/auth-utils';
import { serverDb } from '@/lib/server-db';

interface AttendanceSummary {
  employee_id: string;
  employee_name: string;
  employee_email: string;
  pay_period_start: string;
  pay_period_end: string;
  fiscal_year: string;
  bs_month: number;
  bs_month_name: string;
  total_working_days: number;
  days_present: number;
  days_absent: number;
  days_late: number;
  days_half_day: number;
  days_on_leave: number;
  total_hours_worked: number;
  regular_hours: number;
  overtime_hours: number;
  attendance_percentage: number;
  daily_breakdown: {
    [date: string]: {
      status: "present" | "absent" | "late" | "half_day" | "on_leave";
      check_in_time?: string;
      check_out_time?: string;
      total_hours?: number;
      sessions_count: number;
    }
  };
}

// GET - Fetch attendance summary for payroll calculation
export async function GET(request: NextRequest) {
  try {
    const sessionToken = request.cookies.get("session-token")?.value;

    if (!sessionToken) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const user = await AuthService.verifySession(sessionToken);

    if (!user || !["admin", "hr_manager"].includes(user.role)) {
      return NextResponse.json({ error: "Forbidden" }, { status: 403 });
    }

    const { searchParams } = new URL(request.url);
    const employeeId = searchParams.get("employee_id");
    const startDate = searchParams.get("start_date");
    const endDate = searchParams.get("end_date");
    const fiscalYear = searchParams.get("fiscal_year");
    const bsMonth = searchParams.get("bs_month");

    // Validation
    if (!employeeId) {
      return NextResponse.json(
        { success: false, error: 'Employee ID is required' },
        { status: 400 }
      );
    }

    if (!startDate || !endDate) {
      return NextResponse.json(
        { success: false, error: 'Start date and end date are required' },
        { status: 400 }
      );
    }

    // Get employee information
    const employee = await serverDb.sql`
      SELECT id, full_name, email, department, position
      FROM users
      WHERE id = ${employeeId} AND role != 'admin'
    `;

    if (employee.length === 0) {
      return NextResponse.json(
        { success: false, error: 'Employee not found' },
        { status: 404 }
      );
    }

    // Get working days configuration for the period
    let workingDaysConfig = null;
    if (fiscalYear && bsMonth) {
      const configResult = await serverDb.sql`
        SELECT * FROM working_days_configuration
        WHERE fiscal_year = ${fiscalYear} AND bs_month = ${parseInt(bsMonth)}
      `;
      workingDaysConfig = configResult[0] || null;
    }

    // Get attendance data for the period
    const attendanceData = await serverDb.sql`
      SELECT
        a.date,
        a.check_in_time,
        a.check_out_time,
        a.status,
        a.total_hours,
        COUNT(*) OVER (PARTITION BY a.date) as sessions_count
      FROM attendance a
      WHERE a.user_id = ${employeeId}
      AND a.date >= ${startDate}
      AND a.date <= ${endDate}
      ORDER BY a.date, a.created_at
    `;

    // Calculate date range
    const start = new Date(startDate);
    const end = new Date(endDate);
    const totalDays = Math.ceil((end.getTime() - start.getTime()) / (1000 * 60 * 60 * 24)) + 1;

    // Initialize counters
    let daysPresent = 0;
    let daysAbsent = 0;
    let daysLate = 0;
    let daysHalfDay = 0;
    let daysOnLeave = 0;
    let totalHoursWorked = 0;
    let regularHours = 0;
    let overtimeHours = 0;

    // Process daily attendance
    const dailyBreakdown: { [date: string]: any } = {};
    
    // Initialize all days in the period
    for (let day = 0; day < totalDays; day++) {
      const currentDate = new Date(start);
      currentDate.setDate(start.getDate() + day);
      const dateStr = currentDate.toISOString().split('T')[0];
      
      dailyBreakdown[dateStr] = {
        status: "absent" as const,
        sessions_count: 0
      };
    }

    // Process actual attendance data
    const attendanceByDate = new Map();
    attendanceData.forEach(record => {
      const dateStr = record.date;
      if (!attendanceByDate.has(dateStr)) {
        attendanceByDate.set(dateStr, []);
      }
      attendanceByDate.get(dateStr).push(record);
    });

    // Calculate daily summaries
    attendanceByDate.forEach((dayRecords, dateStr) => {
      const primaryRecord = dayRecords[0]; // Use first record for status
      const totalDayHours = dayRecords.reduce((sum, record) => sum + (record.total_hours || 0), 0);
      
      dailyBreakdown[dateStr] = {
        status: primaryRecord.status,
        check_in_time: primaryRecord.check_in_time,
        check_out_time: dayRecords[dayRecords.length - 1].check_out_time, // Last check out
        total_hours: totalDayHours,
        sessions_count: dayRecords.length
      };

      // Update counters based on status
      switch (primaryRecord.status) {
        case 'present':
          daysPresent++;
          break;
        case 'late':
          daysLate++;
          daysPresent++; // Late is still present
          break;
        case 'half_day':
          daysHalfDay++;
          break;
        case 'on_leave':
          daysOnLeave++;
          break;
        default:
          // absent - already initialized
          break;
      }

      // Calculate hours
      totalHoursWorked += totalDayHours;
      
      // Assume 8 hours is regular, anything above is overtime
      const standardHours = 8;
      if (totalDayHours <= standardHours) {
        regularHours += totalDayHours;
      } else {
        regularHours += standardHours;
        overtimeHours += (totalDayHours - standardHours);
      }
    });

    // Calculate absent days (days not in attendance records)
    daysAbsent = totalDays - daysPresent - daysHalfDay - daysOnLeave;

    // Get working days from configuration or use default calculation
    const totalWorkingDays = workingDaysConfig?.working_days || Math.max(totalDays - Math.floor(totalDays / 7), 20);

    // Calculate attendance percentage
    const attendedDays = daysPresent + daysOnLeave + (daysHalfDay * 0.5);
    const attendancePercentage = totalWorkingDays > 0 ? (attendedDays / totalWorkingDays) * 100 : 0;

    const summary: AttendanceSummary = {
      employee_id: employeeId,
      employee_name: employee[0].full_name,
      employee_email: employee[0].email,
      pay_period_start: startDate,
      pay_period_end: endDate,
      fiscal_year: fiscalYear || '',
      bs_month: bsMonth ? parseInt(bsMonth) : 0,
      bs_month_name: workingDaysConfig?.bs_month_name || '',
      total_working_days: totalWorkingDays,
      days_present: daysPresent,
      days_absent: daysAbsent,
      days_late: daysLate,
      days_half_day: daysHalfDay,
      days_on_leave: daysOnLeave,
      total_hours_worked: Math.round(totalHoursWorked * 100) / 100,
      regular_hours: Math.round(regularHours * 100) / 100,
      overtime_hours: Math.round(overtimeHours * 100) / 100,
      attendance_percentage: Math.round(attendancePercentage * 100) / 100,
      daily_breakdown: dailyBreakdown
    };

    return NextResponse.json({
      success: true,
      data: summary,
      working_days_config: workingDaysConfig,
      period_info: {
        total_days: totalDays,
        start_date: startDate,
        end_date: endDate
      }
    });

  } catch (error) {
    console.error('Error fetching attendance summary:', error);
    return NextResponse.json(
      { 
        success: false, 
        error: 'Failed to fetch attendance summary',
        details: error.message 
      },
      { status: 500 }
    );
  }
}

// POST - Calculate salary based on attendance
export async function POST(request: NextRequest) {
  try {
    const sessionToken = request.cookies.get("session-token")?.value;

    if (!sessionToken) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const user = await AuthService.verifySession(sessionToken);

    if (!user || !["admin", "hr_manager"].includes(user.role)) {
      return NextResponse.json({ error: "Forbidden" }, { status: 403 });
    }

    const body = await request.json();
    const { 
      base_salary, 
      working_days, 
      days_present, 
      days_late = 0, 
      days_half_day = 0, 
      days_on_leave = 0,
      fiscal_year,
      bs_month
    } = body;

    // Validation
    if (!base_salary || !working_days || days_present === undefined) {
      return NextResponse.json(
        { success: false, error: 'Base salary, working days, and days present are required' },
        { status: 400 }
      );
    }

    // Use database function to calculate salary
    const result = await serverDb.sql`
      SELECT calculate_attendance_based_salary(
        ${base_salary}::DECIMAL(10,2),
        ${working_days}::INTEGER,
        ${days_present}::INTEGER,
        ${days_late}::INTEGER,
        ${days_half_day}::INTEGER,
        ${days_on_leave}::INTEGER
      ) as calculated_salary
    `;

    const calculatedSalary = result[0]?.calculated_salary || 0;

    // Calculate breakdown
    const dailyRate = base_salary / working_days;
    const breakdown = {
      base_salary: base_salary,
      daily_rate: Math.round(dailyRate * 100) / 100,
      working_days: working_days,
      days_present: days_present,
      days_late: days_late,
      days_half_day: days_half_day,
      days_on_leave: days_on_leave,
      present_salary: Math.round(dailyRate * days_present * 100) / 100,
      leave_salary: Math.round(dailyRate * days_on_leave * 100) / 100,
      half_day_salary: Math.round(dailyRate * 0.5 * days_half_day * 100) / 100,
      late_penalty: Math.round(dailyRate * 0.5 * days_late * 100) / 100,
      calculated_salary: Math.round(calculatedSalary * 100) / 100
    };

    return NextResponse.json({
      success: true,
      data: breakdown
    });

  } catch (error) {
    console.error('Error calculating attendance-based salary:', error);
    return NextResponse.json(
      { 
        success: false, 
        error: 'Failed to calculate salary',
        details: error.message 
      },
      { status: 500 }
    );
  }
}
