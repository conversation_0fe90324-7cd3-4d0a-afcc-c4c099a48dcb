"use client"

import type React from "react"

import { AppHeader } from "@/components/app-header"
import { RequireAuth } from "@/components/require-auth"

export default function EmployeeLayout({
  children,
}: {
  children: React.ReactNode
}) {
  return (
    <RequireAuth>
      <div className="flex flex-col min-h-screen">
        <AppHeader />
        <div className="flex-1 p-4">{children}</div>
      </div>
    </RequireAuth>
  )
}
