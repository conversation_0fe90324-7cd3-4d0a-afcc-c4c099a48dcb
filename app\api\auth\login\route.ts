import { type NextRequest, NextResponse } from "next/server"
import { AuthService } from "@/lib/auth-utils"

export async function POST(request: NextRequest) {
  try {
    console.log("Login API: Starting login request")

    const { email, password } = await request.json()
    console.log(`Login API: Attempting login for email: ${email}`)

    if (!email || !password) {
      console.log("Login API: Missing email or password")
      return NextResponse.json({ success: false, error: "Email and password are required" }, { status: 400 })
    }

    console.log("Login API: Calling AuthService.login")
    const result = await AuthService.login(email, password)
    console.log(`Login API: AuthService.login result: ${result.success}`)

    if (result.success && result.sessionToken) {
      console.log("Login API: Login successful, setting cookie")
      const response = NextResponse.json({
        success: true,
        user: result.user,
      })

      // Set session cookie
      response.cookies.set("session-token", result.sessionToken, {
        httpOnly: true,
        secure: process.env.NODE_ENV === "production",
        sameSite: "lax",
        maxAge: 7 * 24 * 60 * 60, // 7 days
        path: "/",
      })

      console.log("Login API: Returning successful response")
      return response
    } else {
      console.log(`Login API: Login failed: ${result.error}`)
      return NextResponse.json({ success: false, error: result.error }, { status: 401 })
    }
  } catch (error) {
    console.error("Login API: Unexpected error:", error)
    console.error("Login API: Error stack:", error.stack)
    return NextResponse.json({
      success: false,
      error: "Internal server error",
      details: process.env.NODE_ENV === "development" ? error.message : undefined
    }, { status: 500 })
  }
}
