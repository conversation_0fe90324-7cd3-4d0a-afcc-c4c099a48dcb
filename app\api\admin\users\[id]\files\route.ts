import { NextRequest, NextResponse } from "next/server"
import { serverDb } from "@/lib/server-db"
import { AuthService } from "@/lib/auth-utils"

// GET /api/admin/users/[id]/files - Get user files
export async function GET(request: NextRequest, { params }: { params: { id: string } }) {
  try {
    const sessionToken = request.cookies.get("session-token")?.value

    if (!sessionToken) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 })
    }

    const currentUser = await AuthService.verifySession(sessionToken)

    if (!currentUser || !["admin", "hr_manager"].includes(currentUser.role)) {
      return NextResponse.json({ error: "Forbidden" }, { status: 403 })
    }

    const { searchParams } = new URL(request.url)
    const fileType = searchParams.get('type')

    const files = await serverDb.getUserFiles(params.id, fileType || undefined)

    return NextResponse.json({ files })
  } catch (error) {
    console.error("Get user files API error:", error)
    return NextResponse.json({ error: "Internal server error" }, { status: 500 })
  }
}

// POST /api/admin/users/[id]/files - Upload file for user
export async function POST(request: NextRequest, { params }: { params: { id: string } }) {
  try {
    const sessionToken = request.cookies.get("session-token")?.value

    if (!sessionToken) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 })
    }

    const currentUser = await AuthService.verifySession(sessionToken)

    if (!currentUser || !["admin", "hr_manager"].includes(currentUser.role)) {
      return NextResponse.json({ error: "Forbidden" }, { status: 403 })
    }

    // Parse multipart form data
    const formData = await request.formData()
    const file = formData.get('file') as File
    const fileType = formData.get('fileType') as string

    if (!file) {
      return NextResponse.json({ error: "No file provided" }, { status: 400 })
    }

    if (!fileType || !['profile_picture', 'document', 'contract', 'signature'].includes(fileType)) {
      return NextResponse.json({ error: "Invalid file type" }, { status: 400 })
    }

    // Convert file to buffer
    const arrayBuffer = await file.arrayBuffer()
    const fileContent = Buffer.from(arrayBuffer)

    // Get client IP and user agent
    const clientIP = request.headers.get('x-forwarded-for') || request.headers.get('x-real-ip') || 'unknown'
    const userAgent = request.headers.get('user-agent') || 'unknown'

    // Upload file
    const uploadedFile = await serverDb.uploadFile({
      userId: params.id,
      fileType: fileType as any,
      originalFilename: file.name,
      fileContent,
      mimeType: file.type,
      uploadedBy: currentUser.id,
      uploadIpAddress: clientIP,
      uploadUserAgent: userAgent
    })

    return NextResponse.json({ 
      message: "File uploaded successfully",
      file: uploadedFile
    })
  } catch (error) {
    console.error("Upload file API error:", error)
    return NextResponse.json({ 
      error: error instanceof Error ? error.message : "Internal server error" 
    }, { status: 500 })
  }
}
