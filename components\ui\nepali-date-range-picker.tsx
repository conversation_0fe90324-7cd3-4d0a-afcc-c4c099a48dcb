// Nepali Date Range Picker Component
// Phase 3: Nepal Localization Implementation - Calendar Integration

"use client"

import React, { useState, useEffect } from 'react'
import { Calendar, CalendarDays, ArrowRight } from 'lucide-react'
import { Button } from '@/components/ui/button'
import { Label } from '@/components/ui/label'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Badge } from '@/components/ui/badge'
import { NepaliDatePicker } from './nepali-date-picker'
import { NepaliCalendar, BSDate } from '@/lib/nepali-calendar'
import { nepalConfig } from '@/lib/nepal-config'
import { cn } from '@/lib/utils'

interface DateRange {
  from: Date | undefined
  to: Date | undefined
}

interface NepaliDateRangePickerProps {
  value?: DateRange
  onChange?: (range: DateRange | undefined) => void
  label?: string
  showPresets?: boolean
  showFiscalYearPresets?: boolean
  showPayrollPeriodPresets?: boolean
  minDate?: Date
  maxDate?: Date
  required?: boolean
  disabled?: boolean
  className?: string
  error?: string
}

interface DateRangePreset {
  label: string
  value: string
  range: () => DateRange
  description?: string
}

export function NepaliDateRangePicker({
  value,
  onChange,
  label = "Date Range",
  showPresets = true,
  showFiscalYearPresets = true,
  showPayrollPeriodPresets = true,
  minDate,
  maxDate,
  required = false,
  disabled = false,
  className,
  error
}: NepaliDateRangePickerProps) {
  const [selectedPreset, setSelectedPreset] = useState<string>('')

  // Generate date range presets
  const generatePresets = (): DateRangePreset[] => {
    const presets: DateRangePreset[] = []
    const today = new Date()
    const currentBSDate = NepaliCalendar.adToBS(today)

    // Basic presets
    if (showPresets) {
      presets.push(
        {
          label: 'Today',
          value: 'today',
          range: () => ({ from: today, to: today }),
          description: 'Current day'
        },
        {
          label: 'Yesterday',
          value: 'yesterday',
          range: () => {
            const yesterday = new Date(today)
            yesterday.setDate(yesterday.getDate() - 1)
            return { from: yesterday, to: yesterday }
          },
          description: 'Previous day'
        },
        {
          label: 'This Week',
          value: 'this-week',
          range: () => {
            const startOfWeek = new Date(today)
            startOfWeek.setDate(today.getDate() - today.getDay())
            const endOfWeek = new Date(startOfWeek)
            endOfWeek.setDate(startOfWeek.getDate() + 6)
            return { from: startOfWeek, to: endOfWeek }
          },
          description: 'Current week (Sunday to Saturday)'
        },
        {
          label: 'Last Week',
          value: 'last-week',
          range: () => {
            const startOfLastWeek = new Date(today)
            startOfLastWeek.setDate(today.getDate() - today.getDay() - 7)
            const endOfLastWeek = new Date(startOfLastWeek)
            endOfLastWeek.setDate(startOfLastWeek.getDate() + 6)
            return { from: startOfLastWeek, to: endOfLastWeek }
          },
          description: 'Previous week'
        },
        {
          label: 'This Month',
          value: 'this-month',
          range: () => {
            const startOfMonth = new Date(today.getFullYear(), today.getMonth(), 1)
            const endOfMonth = new Date(today.getFullYear(), today.getMonth() + 1, 0)
            return { from: startOfMonth, to: endOfMonth }
          },
          description: 'Current calendar month'
        },
        {
          label: 'Last Month',
          value: 'last-month',
          range: () => {
            const startOfLastMonth = new Date(today.getFullYear(), today.getMonth() - 1, 1)
            const endOfLastMonth = new Date(today.getFullYear(), today.getMonth(), 0)
            return { from: startOfLastMonth, to: endOfLastMonth }
          },
          description: 'Previous calendar month'
        }
      )
    }

    // Fiscal year presets
    if (showFiscalYearPresets) {
      const currentFiscalYear = nepalConfig.getCurrentFiscalYear()
      const fiscalYearInfo = NepaliCalendar.getFiscalYearDates(currentFiscalYear)
      
      presets.push(
        {
          label: `Current Fiscal Year (${currentFiscalYear})`,
          value: 'current-fy',
          range: () => ({
            from: NepaliCalendar.bsToAD(fiscalYearInfo.start),
            to: NepaliCalendar.bsToAD(fiscalYearInfo.end)
          }),
          description: `Nepali fiscal year ${currentFiscalYear}`
        }
      )

      // Previous fiscal year
      const prevFYParts = currentFiscalYear.split('-')
      const prevFY = `${parseInt(prevFYParts[0]) - 1}-${(parseInt(prevFYParts[1]) - 1).toString().padStart(2, '0')}`
      const prevFiscalYearInfo = NepaliCalendar.getFiscalYearDates(prevFY)
      
      presets.push({
        label: `Previous Fiscal Year (${prevFY})`,
        value: 'previous-fy',
        range: () => ({
          from: NepaliCalendar.bsToAD(prevFiscalYearInfo.start),
          to: NepaliCalendar.bsToAD(prevFiscalYearInfo.end)
        }),
        description: `Previous Nepali fiscal year ${prevFY}`
      })
    }

    // Payroll period presets
    if (showPayrollPeriodPresets) {
      // Current BS month
      const currentBSMonthStart = { ...currentBSDate, day: 1 }
      const currentBSMonthEnd = { 
        ...currentBSDate, 
        day: NepaliCalendar.getDaysInBSMonth(currentBSDate.year, currentBSDate.month) 
      }
      
      presets.push({
        label: `Current BS Month (${NepaliCalendar.getBSMonthName(currentBSDate.month)})`,
        value: 'current-bs-month',
        range: () => ({
          from: NepaliCalendar.bsToAD(currentBSMonthStart),
          to: NepaliCalendar.bsToAD(currentBSMonthEnd)
        }),
        description: `Current Bikram Sambat month`
      })

      // Previous BS month
      const prevBSMonth = currentBSDate.month === 1 ? 12 : currentBSDate.month - 1
      const prevBSYear = currentBSDate.month === 1 ? currentBSDate.year - 1 : currentBSDate.year
      const prevBSMonthStart = { year: prevBSYear, month: prevBSMonth, day: 1 }
      const prevBSMonthEnd = { 
        year: prevBSYear, 
        month: prevBSMonth, 
        day: NepaliCalendar.getDaysInBSMonth(prevBSYear, prevBSMonth) 
      }
      
      presets.push({
        label: `Previous BS Month (${NepaliCalendar.getBSMonthName(prevBSMonth)})`,
        value: 'previous-bs-month',
        range: () => ({
          from: NepaliCalendar.bsToAD(prevBSMonthStart),
          to: NepaliCalendar.bsToAD(prevBSMonthEnd)
        }),
        description: `Previous Bikram Sambat month`
      })

      // Quarterly presets
      const currentQuarter = Math.ceil(currentBSDate.month / 3)
      const quarterStartMonth = (currentQuarter - 1) * 3 + 1
      const quarterEndMonth = currentQuarter * 3
      
      presets.push({
        label: `Current Quarter (Q${currentQuarter})`,
        value: 'current-quarter',
        range: () => ({
          from: NepaliCalendar.bsToAD({ year: currentBSDate.year, month: quarterStartMonth, day: 1 }),
          to: NepaliCalendar.bsToAD({ 
            year: currentBSDate.year, 
            month: quarterEndMonth, 
            day: NepaliCalendar.getDaysInBSMonth(currentBSDate.year, quarterEndMonth) 
          })
        }),
        description: `Current fiscal quarter`
      })
    }

    return presets
  }

  const presets = generatePresets()

  const handlePresetSelect = (presetValue: string) => {
    setSelectedPreset(presetValue)
    const preset = presets.find(p => p.value === presetValue)
    if (preset) {
      const range = preset.range()
      onChange?.(range)
    }
  }

  const handleFromDateChange = (date: Date | undefined) => {
    onChange?.({
      from: date,
      to: value?.to
    })
    setSelectedPreset('') // Clear preset when manually changing dates
  }

  const handleToDateChange = (date: Date | undefined) => {
    onChange?.({
      from: value?.from,
      to: date
    })
    setSelectedPreset('') // Clear preset when manually changing dates
  }

  const calculateRangeStats = () => {
    if (!value?.from || !value?.to) return null

    const workingDays = nepalConfig.getWorkingDaysInPeriod(
      value.from.toISOString().split('T')[0],
      value.to.toISOString().split('T')[0]
    )

    const holidays = nepalConfig.getHolidaysInRange(
      value.from.toISOString().split('T')[0],
      value.to.toISOString().split('T')[0]
    )

    const totalDays = Math.ceil((value.to.getTime() - value.from.getTime()) / (1000 * 60 * 60 * 24)) + 1

    return {
      totalDays,
      workingDays,
      holidays: holidays.length,
      weekends: totalDays - workingDays - holidays.length
    }
  }

  const rangeStats = calculateRangeStats()

  const formatDateRange = () => {
    if (!value?.from || !value?.to) return "Select date range"

    const fromBS = NepaliCalendar.adToBS(value.from)
    const toBS = NepaliCalendar.adToBS(value.to)

    const fromFormatted = value.from.toLocaleDateString('en-US', { month: 'short', day: 'numeric' })
    const toFormatted = value.to.toLocaleDateString('en-US', { month: 'short', day: 'numeric', year: 'numeric' })
    
    const fromBSFormatted = `${NepaliCalendar.getBSMonthName(fromBS.month).slice(0, 3)} ${fromBS.day}`
    const toBSFormatted = `${NepaliCalendar.getBSMonthName(toBS.month).slice(0, 3)} ${toBS.day}, ${toBS.year}`

    return `${fromFormatted} - ${toFormatted} (${fromBSFormatted} - ${toBSFormatted})`
  }

  return (
    <Card className={cn("w-full", className)}>
      <CardHeader className="pb-4">
        <CardTitle className="text-lg font-semibold flex items-center gap-2">
          <Calendar className="h-5 w-5" />
          {label}
          {required && <span className="text-red-500 ml-1">*</span>}
        </CardTitle>
      </CardHeader>
      
      <CardContent className="space-y-6">
        {/* Preset selection */}
        {(showPresets || showFiscalYearPresets || showPayrollPeriodPresets) && (
          <div className="space-y-2">
            <Label className="text-sm font-medium">Quick Select</Label>
            <Select value={selectedPreset} onValueChange={handlePresetSelect}>
              <SelectTrigger>
                <SelectValue placeholder="Choose a preset range" />
              </SelectTrigger>
              <SelectContent>
                {presets.map((preset) => (
                  <SelectItem key={preset.value} value={preset.value}>
                    <div>
                      <div className="font-medium">{preset.label}</div>
                      {preset.description && (
                        <div className="text-xs text-muted-foreground">{preset.description}</div>
                      )}
                    </div>
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
        )}

        {/* Manual date selection */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <NepaliDatePicker
            value={value?.from}
            onChange={handleFromDateChange}
            label="From Date"
            placeholder="Start date"
            showBothCalendars={true}
            highlightHolidays={true}
            highlightWorkingDays={true}
            minDate={minDate}
            maxDate={value?.to || maxDate}
            disabled={disabled}
          />
          
          <NepaliDatePicker
            value={value?.to}
            onChange={handleToDateChange}
            label="To Date"
            placeholder="End date"
            showBothCalendars={true}
            highlightHolidays={true}
            highlightWorkingDays={true}
            minDate={value?.from || minDate}
            maxDate={maxDate}
            disabled={disabled}
          />
        </div>

        {/* Selected range display */}
        {value?.from && value?.to && (
          <div className="space-y-3 p-4 bg-muted rounded-lg">
            <div className="flex items-center gap-2 text-sm font-medium">
              <CalendarDays className="h-4 w-4" />
              Selected Range
            </div>
            
            <div className="text-sm text-muted-foreground">
              {formatDateRange()}
            </div>

            {/* Range statistics */}
            {rangeStats && (
              <div className="flex flex-wrap gap-2">
                <Badge variant="outline">
                  {rangeStats.totalDays} Total Days
                </Badge>
                <Badge variant="default">
                  {rangeStats.workingDays} Working Days
                </Badge>
                {rangeStats.holidays > 0 && (
                  <Badge variant="destructive">
                    {rangeStats.holidays} Holidays
                  </Badge>
                )}
                {rangeStats.weekends > 0 && (
                  <Badge variant="secondary">
                    {rangeStats.weekends} Weekends
                  </Badge>
                )}
              </div>
            )}
          </div>
        )}

        {error && (
          <p className="text-sm text-red-500">{error}</p>
        )}
      </CardContent>
    </Card>
  )
}

export default NepaliDateRangePicker
