import { NextRequest, NextResponse } from "next/server"
import { AuthService } from "@/lib/auth-utils"
import { serverDb } from "@/lib/server-db"
import { z } from "zod"

// Validation schemas
const createSubTaskSchema = z.object({
  title: z.string().min(1, "Title is required").max(255, "Title too long"),
  description: z.string().optional(),
  assigned_to: z.string().uuid().optional(),
  due_date: z.string().datetime().optional(),
  position: z.number().int().min(0).optional(),
})

const updateSubTaskSchema = z.object({
  title: z.string().min(1, "Title is required").max(255, "Title too long").optional(),
  description: z.string().optional(),
  assigned_to: z.string().uuid().optional(),
  status: z.enum(["todo", "in_progress", "completed", "cancelled"]).optional(),
  due_date: z.string().datetime().optional(),
  position: z.number().int().min(0).optional(),
})

// GET /api/tasks/[id]/subtasks - Get sub-tasks for a task
export async function GET(request: NextRequest, { params }: { params: { id: string } }) {
  try {
    const sessionToken = request.cookies.get("session-token")?.value

    if (!sessionToken) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 })
    }

    const user = await AuthService.verifySession(sessionToken)

    if (!user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 })
    }

    const { id: taskId } = params

    // Check if task exists and user has access
    const taskResult = await serverDb.sql`
      SELECT * FROM tasks WHERE id = ${taskId}
    `

    if (taskResult.length === 0) {
      return NextResponse.json({ error: "Task not found" }, { status: 404 })
    }

    const task = taskResult[0]

    // Check if user is assigned to this task via task_assignments
    const isAssigned = await serverDb.sql`
      SELECT EXISTS (
        SELECT 1 FROM task_assignments 
        WHERE task_id = ${taskId} AND user_id = ${user.id}
      )
    `

    // Check access permissions
    const hasAccess = 
      ["admin", "hr_manager"].includes(user.role) ||
      task.assigned_to === user.id ||
      task.assigned_by === user.id ||
      isAssigned[0].exists

    if (!hasAccess) {
      return NextResponse.json({ error: "Access denied" }, { status: 403 })
    }

    // Get sub-tasks with assignee details
    const subTasks = await serverDb.sql`
      SELECT 
        st.*,
        assigned_user.full_name as assigned_to_name,
        assigned_user.email as assigned_to_email,
        assigned_user.employee_id as assigned_to_employee_id,
        assigned_by_user.full_name as assigned_by_name
      FROM sub_tasks st
      LEFT JOIN users assigned_user ON st.assigned_to = assigned_user.id
      LEFT JOIN users assigned_by_user ON st.assigned_by = assigned_by_user.id
      WHERE st.parent_task_id = ${taskId}
      ORDER BY st.position ASC, st.created_at ASC
    `

    return NextResponse.json({
      success: true,
      data: subTasks
    })

  } catch (error) {
    console.error("Get sub-tasks error:", error)
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    )
  }
}

// POST /api/tasks/[id]/subtasks - Create a new sub-task
export async function POST(request: NextRequest, { params }: { params: { id: string } }) {
  try {
    const sessionToken = request.cookies.get("session-token")?.value

    if (!sessionToken) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 })
    }

    const user = await AuthService.verifySession(sessionToken)

    if (!user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 })
    }

    const { id: taskId } = params

    // Check if task exists
    const taskResult = await serverDb.sql`
      SELECT * FROM tasks WHERE id = ${taskId}
    `

    if (taskResult.length === 0) {
      return NextResponse.json({ error: "Task not found" }, { status: 404 })
    }

    const task = taskResult[0]

    // Check if user is assigned to this task via task_assignments
    const isAssigned = await serverDb.sql`
      SELECT EXISTS (
        SELECT 1 FROM task_assignments 
        WHERE task_id = ${taskId} AND user_id = ${user.id}
      )
    `

    // Check permissions - only assigned users, task creator, or admin/hr_manager can create sub-tasks
    const canCreate = 
      ["admin", "hr_manager"].includes(user.role) ||
      task.assigned_to === user.id ||
      task.assigned_by === user.id ||
      isAssigned[0].exists

    if (!canCreate) {
      return NextResponse.json({ 
        error: "You don't have permission to create sub-tasks for this task" 
      }, { status: 403 })
    }

    // Parse and validate request body
    const body = await request.json()
    const validatedData = createSubTaskSchema.parse(body)

    // Check if user can assign sub-tasks to others
    if (validatedData.assigned_to && validatedData.assigned_to !== user.id) {
      if (!["admin", "hr_manager"].includes(user.role)) {
        return NextResponse.json(
          { error: "You can only assign sub-tasks to yourself" },
          { status: 403 }
        )
      }

      // Validate that the assigned user exists and is active
      const assignedUserResult = await serverDb.sql`
        SELECT id FROM users 
        WHERE id = ${validatedData.assigned_to} AND is_active = true
      `

      if (assignedUserResult.length === 0) {
        return NextResponse.json(
          { error: "Assigned user not found or inactive" },
          { status: 400 }
        )
      }
    }

    // Get the next position if not specified
    let position = validatedData.position
    if (position === undefined) {
      const positionResult = await serverDb.sql`
        SELECT COALESCE(MAX(position), 0) + 1 as next_position
        FROM sub_tasks
        WHERE parent_task_id = ${taskId}
      `
      position = positionResult[0]?.next_position || 1
    }

    // Create the sub-task
    const subTaskResult = await serverDb.sql`
      INSERT INTO sub_tasks (
        parent_task_id, title, description, assigned_to, assigned_by, 
        due_date, position, status
      )
      VALUES (
        ${taskId},
        ${validatedData.title},
        ${validatedData.description || null},
        ${validatedData.assigned_to || user.id},
        ${user.id},
        ${validatedData.due_date || null},
        ${position},
        'todo'
      )
      RETURNING *
    `

    const newSubTask = subTaskResult[0]

    // Get the complete sub-task data with user details
    const completeSubTaskResult = await serverDb.sql`
      SELECT 
        st.*,
        assigned_user.full_name as assigned_to_name,
        assigned_user.email as assigned_to_email,
        assigned_user.employee_id as assigned_to_employee_id,
        assigned_by_user.full_name as assigned_by_name
      FROM sub_tasks st
      LEFT JOIN users assigned_user ON st.assigned_to = assigned_user.id
      LEFT JOIN users assigned_by_user ON st.assigned_by = assigned_by_user.id
      WHERE st.id = ${newSubTask.id}
    `

    return NextResponse.json({
      success: true,
      data: completeSubTaskResult[0],
      message: "Sub-task created successfully"
    }, { status: 201 })

  } catch (error) {
    console.error("Create sub-task error:", error)
    
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: "Invalid sub-task data", details: error.errors },
        { status: 400 }
      )
    }

    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    )
  }
}
