// Allowances Management API Endpoints
// Admin endpoints for managing allowances and employee assignments

import { NextRequest, NextResponse } from 'next/server';
import { allowancesManager } from '@/lib/allowances-manager';
import { AuthService } from '@/lib/auth-utils';

// GET - Get allowance types or employee allowances summary
export async function GET(request: NextRequest) {
  try {
    const sessionToken = request.cookies.get("session-token")?.value;

    if (!sessionToken) {
      return NextResponse.json({
        success: false,
        error: 'Unauthorized'
      }, { status: 401 });
    }

    const user = await AuthService.verifySession(sessionToken);

    if (!user || (user.role !== 'admin' && user.role !== 'hr_manager')) {
      return NextResponse.json({
        success: false,
        error: 'Insufficient permissions'
      }, { status: 403 });
    }

    const { searchParams } = new URL(request.url);
    const action = searchParams.get('action');
    const userId = searchParams.get('userId');

    if (action === 'types') {
      // Get all allowance types
      const allowanceTypes = await allowancesManager.getAllowanceTypes();
      return NextResponse.json({
        success: true,
        data: allowanceTypes
      });
    } else if (action === 'employee' && userId) {
      // Get specific employee allowances
      const allowances = await allowancesManager.getEmployeeAllowances(userId);
      return NextResponse.json({
        success: true,
        data: allowances
      });
    } else if (action === 'summary') {
      // Get all employees allowance summary
      const summary = await allowancesManager.getAllEmployeesAllowanceSummary();
      return NextResponse.json({
        success: true,
        data: summary
      });
    } else if (action === 'statistics') {
      // Get allowance statistics
      const statistics = await allowancesManager.getAllowanceStatistics();
      return NextResponse.json({
        success: true,
        data: statistics
      });
    } else {
      return NextResponse.json({
        success: false,
        error: 'Invalid action parameter'
      }, { status: 400 });
    }

  } catch (error) {
    console.error('Error in allowances GET:', error);
    return NextResponse.json({
      success: false,
      error: 'Internal server error'
    }, { status: 500 });
  }
}

// POST - Create new allowance type or assign allowance to employee
export async function POST(request: NextRequest) {
  try {
    const sessionToken = request.cookies.get("session-token")?.value;

    if (!sessionToken) {
      return NextResponse.json({
        success: false,
        error: 'Unauthorized'
      }, { status: 401 });
    }

    const user = await AuthService.verifySession(sessionToken);

    if (!user || (user.role !== 'admin' && user.role !== 'hr_manager')) {
      return NextResponse.json({
        success: false,
        error: 'Insufficient permissions'
      }, { status: 403 });
    }

    const body = await request.json();
    const { action } = body;

    if (action === 'create_type') {
      // Create new allowance type
      const { allowanceType } = body;

      if (!allowanceType || !allowanceType.name || !allowanceType.code) {
        return NextResponse.json({
          success: false,
          error: 'Allowance name and code are required'
        }, { status: 400 });
      }

      // Validate calculation_type
      if (!['fixed', 'percentage', 'formula'].includes(allowanceType.calculation_type)) {
        return NextResponse.json({
          success: false,
          error: 'Invalid calculation type'
        }, { status: 400 });
      }

      // Validate category
      if (!['statutory', 'voluntary', 'company_policy', 'custom'].includes(allowanceType.category)) {
        return NextResponse.json({
          success: false,
          error: 'Invalid category'
        }, { status: 400 });
      }

      const allowanceId = await allowancesManager.createAllowanceType({
        name: allowanceType.name,
        code: allowanceType.code.toUpperCase(),
        type: 'allowance',
        category: allowanceType.category,
        calculation_type: allowanceType.calculation_type,
        fixed_amount: allowanceType.fixed_amount || null,
        percentage: allowanceType.percentage || null,
        percentage_base: allowanceType.percentage_base || null,
        is_taxable: allowanceType.is_taxable !== false,
        is_statutory: allowanceType.is_statutory === true,
        description: allowanceType.description || null,
        is_active: true,
        effective_from: allowanceType.effective_from || new Date().toISOString().split('T')[0],
        effective_to: allowanceType.effective_to || null
      });

      return NextResponse.json({
        success: true,
        data: { allowanceId },
        message: 'Allowance type created successfully'
      });

    } else if (action === 'assign') {
      // Assign allowance to employee
      const { userId, allowanceConfig } = body;

      if (!userId || !allowanceConfig) {
        return NextResponse.json({
          success: false,
          error: 'User ID and allowance configuration are required'
        }, { status: 400 });
      }

      // Validate allowance_type
      if (!['travelling', 'phone', 'meal', 'transport', 'medical', 'education', 'custom'].includes(allowanceConfig.allowance_type)) {
        return NextResponse.json({
          success: false,
          error: 'Invalid allowance type'
        }, { status: 400 });
      }

      // Validate calculation_type
      if (!['fixed', 'percentage'].includes(allowanceConfig.calculation_type)) {
        return NextResponse.json({
          success: false,
          error: 'Invalid calculation type'
        }, { status: 400 });
      }

      // Validate required fields based on calculation type
      if (allowanceConfig.calculation_type === 'fixed' && !allowanceConfig.amount) {
        return NextResponse.json({
          success: false,
          error: 'Amount is required for fixed calculation type'
        }, { status: 400 });
      }

      if (allowanceConfig.calculation_type === 'percentage' && (!allowanceConfig.percentage || !allowanceConfig.percentage_base)) {
        return NextResponse.json({
          success: false,
          error: 'Percentage and percentage base are required for percentage calculation type'
        }, { status: 400 });
      }

      const assignmentId = await allowancesManager.assignAllowanceToEmployee(
        userId,
        allowanceConfig,
        user.id
      );

      return NextResponse.json({
        success: true,
        data: { assignmentId },
        message: 'Allowance assigned to employee successfully'
      });

    } else {
      return NextResponse.json({
        success: false,
        error: 'Invalid action'
      }, { status: 400 });
    }

  } catch (error) {
    console.error('Error in allowances POST:', error);
    return NextResponse.json({
      success: false,
      error: error.message || 'Internal server error'
    }, { status: 500 });
  }
}

// PUT - Update allowance type or employee allowance assignment
export async function PUT(request: NextRequest) {
  try {
    const sessionToken = request.cookies.get("session-token")?.value;

    if (!sessionToken) {
      return NextResponse.json({
        success: false,
        error: 'Unauthorized'
      }, { status: 401 });
    }

    const user = await AuthService.verifySession(sessionToken);

    if (!user || (user.role !== 'admin' && user.role !== 'hr_manager')) {
      return NextResponse.json({
        success: false,
        error: 'Insufficient permissions'
      }, { status: 403 });
    }

    const body = await request.json();
    const { action } = body;

    if (action === 'update_type') {
      // Update allowance type
      const { allowanceId, updates } = body;

      if (!allowanceId || !updates) {
        return NextResponse.json({
          success: false,
          error: 'Allowance ID and updates are required'
        }, { status: 400 });
      }

      const success = await allowancesManager.updateAllowanceType(allowanceId, updates);

      if (success) {
        return NextResponse.json({
          success: true,
          message: 'Allowance type updated successfully'
        });
      } else {
        return NextResponse.json({
          success: false,
          error: 'Failed to update allowance type'
        }, { status: 500 });
      }

    } else if (action === 'update_assignment') {
      // Update employee allowance assignment
      const { assignmentId, updates } = body;

      if (!assignmentId || !updates) {
        return NextResponse.json({
          success: false,
          error: 'Assignment ID and updates are required'
        }, { status: 400 });
      }

      const success = await allowancesManager.updateEmployeeAllowance(assignmentId, updates);

      if (success) {
        return NextResponse.json({
          success: true,
          message: 'Employee allowance updated successfully'
        });
      } else {
        return NextResponse.json({
          success: false,
          error: 'Failed to update employee allowance'
        }, { status: 500 });
      }

    } else {
      return NextResponse.json({
        success: false,
        error: 'Invalid action'
      }, { status: 400 });
    }

  } catch (error) {
    console.error('Error in allowances PUT:', error);
    return NextResponse.json({
      success: false,
      error: 'Internal server error'
    }, { status: 500 });
  }
}

// DELETE - Remove allowance assignment from employee
export async function DELETE(request: NextRequest) {
  try {
    const sessionToken = request.cookies.get("session-token")?.value;

    if (!sessionToken) {
      return NextResponse.json({
        success: false,
        error: 'Unauthorized'
      }, { status: 401 });
    }

    const user = await AuthService.verifySession(sessionToken);

    if (!user || (user.role !== 'admin' && user.role !== 'hr_manager')) {
      return NextResponse.json({
        success: false,
        error: 'Insufficient permissions'
      }, { status: 403 });
    }

    const { searchParams } = new URL(request.url);
    const assignmentId = searchParams.get('assignmentId');

    if (!assignmentId) {
      return NextResponse.json({
        success: false,
        error: 'Assignment ID is required'
      }, { status: 400 });
    }

    const success = await allowancesManager.removeEmployeeAllowance(assignmentId);

    if (success) {
      return NextResponse.json({
        success: true,
        message: 'Allowance removed from employee successfully'
      });
    } else {
      return NextResponse.json({
        success: false,
        error: 'Failed to remove allowance from employee'
      }, { status: 500 });
    }

  } catch (error) {
    console.error('Error in allowances DELETE:', error);
    return NextResponse.json({
      success: false,
      error: 'Internal server error'
    }, { status: 500 });
  }
}
