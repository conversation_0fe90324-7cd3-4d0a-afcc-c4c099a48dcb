#!/usr/bin/env node

// Quick database fix for attendance table
const { neon } = require('@neondatabase/serverless');

// Read environment variables manually
const fs = require('fs');
const path = require('path');

function loadEnvFile() {
  try {
    const envPath = path.join(process.cwd(), '.env.local');
    const envContent = fs.readFileSync(envPath, 'utf8');

    envContent.split('\n').forEach(line => {
      const trimmed = line.trim();
      if (trimmed && !trimmed.startsWith('#')) {
        const [key, ...valueParts] = trimmed.split('=');
        if (key && valueParts.length > 0) {
          const value = valueParts.join('=').replace(/^["']|["']$/g, '');
          process.env[key] = value;
        }
      }
    });
  } catch (error) {
    console.log('⚠️  Could not read .env.local file');
  }
}

loadEnvFile();

async function fixDatabase() {
  console.log('🔧 Quick Fix: Updating attendance table schema...\n');
  
  if (!process.env.DATABASE_URL) {
    console.error('❌ DATABASE_URL not found in environment');
    console.log('📝 Please check your .env.local file');
    process.exit(1);
  }
  
  const sql = neon(process.env.DATABASE_URL);
  
  try {
    console.log('🔍 Checking current table structure...');
    
    // Check current column types
    const currentColumns = await sql`
      SELECT column_name, data_type 
      FROM information_schema.columns 
      WHERE table_name = 'attendance' 
      AND column_name IN ('check_in_time', 'check_out_time')
      ORDER BY column_name;
    `;
    
    console.log('📊 Current columns:');
    currentColumns.forEach(col => {
      console.log(`  - ${col.column_name}: ${col.data_type}`);
    });
    
    if (currentColumns.length === 0) {
      console.log('❌ Attendance table not found');
      process.exit(1);
    }
    
    // Check if migration is needed
    const needsMigration = currentColumns.some(col => col.data_type === 'time without time zone');
    
    if (!needsMigration) {
      console.log('✅ Columns are already correct type!');
      return;
    }
    
    console.log('\n🔄 Running migration...');
    
    // Step 1: Add new columns
    console.log('  1. Adding temporary columns...');
    await sql`ALTER TABLE attendance ADD COLUMN IF NOT EXISTS check_in_time_new TIMESTAMP WITH TIME ZONE`;
    await sql`ALTER TABLE attendance ADD COLUMN IF NOT EXISTS check_out_time_new TIMESTAMP WITH TIME ZONE`;
    
    // Step 2: Migrate existing data
    console.log('  2. Migrating existing data...');
    await sql`
      UPDATE attendance 
      SET 
        check_in_time_new = CASE 
          WHEN check_in_time IS NOT NULL THEN 
            (date + check_in_time)::TIMESTAMP WITH TIME ZONE
          ELSE NULL 
        END,
        check_out_time_new = CASE 
          WHEN check_out_time IS NOT NULL THEN 
            (date + check_out_time)::TIMESTAMP WITH TIME ZONE
          ELSE NULL 
        END
      WHERE check_in_time_new IS NULL OR check_out_time_new IS NULL
    `;
    
    // Step 3: Drop old columns
    console.log('  3. Dropping old columns...');
    await sql`ALTER TABLE attendance DROP COLUMN IF EXISTS check_in_time`;
    await sql`ALTER TABLE attendance DROP COLUMN IF EXISTS check_out_time`;
    
    // Step 4: Rename new columns
    console.log('  4. Renaming new columns...');
    await sql`ALTER TABLE attendance RENAME COLUMN check_in_time_new TO check_in_time`;
    await sql`ALTER TABLE attendance RENAME COLUMN check_out_time_new TO check_out_time`;
    
    // Step 5: Verify
    console.log('  5. Verifying changes...');
    const updatedColumns = await sql`
      SELECT column_name, data_type 
      FROM information_schema.columns 
      WHERE table_name = 'attendance' 
      AND column_name IN ('check_in_time', 'check_out_time')
      ORDER BY column_name;
    `;
    
    console.log('\n📊 Updated columns:');
    updatedColumns.forEach(col => {
      const status = col.data_type === 'timestamp with time zone' ? '✅' : '❌';
      console.log(`  ${status} ${col.column_name}: ${col.data_type}`);
    });
    
    const allCorrect = updatedColumns.every(col => col.data_type === 'timestamp with time zone');
    
    if (allCorrect) {
      console.log('\n🎉 Migration completed successfully!');
      console.log('✅ Clock-in functionality should now work correctly');
      console.log('\n📝 Test the fix:');
      console.log('  1. Go to /employee/attendance');
      console.log('  2. Try clicking "Check In"');
      console.log('  3. Verify no more "invalid input syntax for type time" errors');
    } else {
      console.log('\n❌ Migration verification failed');
      process.exit(1);
    }
    
  } catch (error) {
    console.error('❌ Migration failed:', error);
    console.log('\n🔧 Manual fix:');
    console.log('  1. Open your Neon database console');
    console.log('  2. Run the SQL from scripts/fix-attendance-schema.sql');
    process.exit(1);
  }
}

fixDatabase().catch(console.error);
