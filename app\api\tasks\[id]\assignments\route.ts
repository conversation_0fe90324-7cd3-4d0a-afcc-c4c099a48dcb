import { NextRequest, NextResponse } from "next/server"
import { AuthService } from "@/lib/auth-utils"
import { serverDb } from "@/lib/server-db"
import { z } from "zod"

// Validation schemas
const createAssignmentSchema = z.object({
  user_ids: z.array(z.string().uuid()).min(1, "At least one user must be assigned"),
  is_primary: z.string().uuid().optional(), // ID of the primary assignee
})

const updateAssignmentSchema = z.object({
  is_primary: z.boolean().optional(),
})

// GET /api/tasks/[id]/assignments - Get task assignments
export async function GET(request: NextRequest, { params }: { params: { id: string } }) {
  try {
    const sessionToken = request.cookies.get("session-token")?.value

    if (!sessionToken) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 })
    }

    const user = await AuthService.verifySession(sessionToken)

    if (!user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 })
    }

    const { id: taskId } = params

    // Check if task exists and user has access
    const taskResult = await serverDb.sql`
      SELECT * FROM tasks WHERE id = ${taskId}
    `

    if (taskResult.length === 0) {
      return NextResponse.json({ error: "Task not found" }, { status: 404 })
    }

    const task = taskResult[0]

    // Check access permissions
    const hasAccess = 
      ["admin", "hr_manager"].includes(user.role) ||
      task.assigned_to === user.id ||
      task.assigned_by === user.id ||
      // Check if user is assigned to this task
      await serverDb.sql`
        SELECT EXISTS (
          SELECT 1 FROM task_assignments 
          WHERE task_id = ${taskId} AND user_id = ${user.id}
        )
      `.then(result => result[0].exists)

    if (!hasAccess) {
      return NextResponse.json({ error: "Access denied" }, { status: 403 })
    }

    // Get task assignments with user details
    const assignments = await serverDb.sql`
      SELECT 
        ta.*,
        u.full_name,
        u.email,
        u.employee_id,
        u.department,
        u.position,
        assigned_by_user.full_name as assigned_by_name
      FROM task_assignments ta
      JOIN users u ON ta.user_id = u.id
      LEFT JOIN users assigned_by_user ON ta.assigned_by = assigned_by_user.id
      WHERE ta.task_id = ${taskId}
      ORDER BY ta.is_primary DESC, ta.assigned_at ASC
    `

    return NextResponse.json({
      success: true,
      data: assignments
    })

  } catch (error) {
    console.error("Get task assignments error:", error)
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    )
  }
}

// POST /api/tasks/[id]/assignments - Add multiple users to task
export async function POST(request: NextRequest, { params }: { params: { id: string } }) {
  try {
    const sessionToken = request.cookies.get("session-token")?.value

    if (!sessionToken) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 })
    }

    const user = await AuthService.verifySession(sessionToken)

    if (!user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 })
    }

    const { id: taskId } = params

    // Check if task exists
    const taskResult = await serverDb.sql`
      SELECT * FROM tasks WHERE id = ${taskId}
    `

    if (taskResult.length === 0) {
      return NextResponse.json({ error: "Task not found" }, { status: 404 })
    }

    const task = taskResult[0]

    // Check permissions - only admin, hr_manager, or task creator can assign users
    const canAssign = 
      ["admin", "hr_manager"].includes(user.role) ||
      task.assigned_by === user.id

    if (!canAssign) {
      return NextResponse.json({ 
        error: "You don't have permission to assign users to this task" 
      }, { status: 403 })
    }

    // Parse and validate request body
    const body = await request.json()
    console.log("Assignment API - Request body:", JSON.stringify(body, null, 2))

    try {
      const { user_ids, is_primary } = createAssignmentSchema.parse(body)
      console.log("Assignment API - Parsed data:", { user_ids, is_primary })
    } catch (validationError) {
      console.error("Assignment API - Validation error:", validationError)
      return NextResponse.json({
        error: "Invalid request data",
        details: validationError instanceof z.ZodError ? validationError.errors : validationError
      }, { status: 400 })
    }

    const { user_ids, is_primary } = createAssignmentSchema.parse(body)

    // Validate that all user IDs exist and are active
    const usersResult = await serverDb.sql`
      SELECT id, full_name, role FROM users
      WHERE id = ANY(${user_ids}) AND is_active = true
    `

    console.log("Assignment API - Users validation:", {
      requested_user_ids: user_ids,
      found_users: usersResult.map(u => ({ id: u.id, name: u.full_name, role: u.role })),
      current_user: { id: user.id, role: user.role }
    })

    if (usersResult.length !== user_ids.length) {
      console.log("Assignment API - User validation failed:", {
        requested_count: user_ids.length,
        found_count: usersResult.length,
        missing_users: user_ids.filter(id => !usersResult.some(u => u.id === id))
      })
      return NextResponse.json({
        error: "One or more users not found or inactive",
        details: {
          requested: user_ids.length,
          found: usersResult.length,
          missing: user_ids.filter(id => !usersResult.some(u => u.id === id))
        }
      }, { status: 400 })
    }

    // Check role-based assignment restrictions
    const regularUsers = usersResult.filter(u => !["admin", "hr_manager"].includes(u.role))
    console.log("Assignment API - Role check:", {
      current_user_role: user.role,
      regular_users: regularUsers.map(u => ({ id: u.id, name: u.full_name, role: u.role })),
      can_assign: ["admin", "hr_manager"].includes(user.role)
    })

    if (regularUsers.length > 0 && !["admin", "hr_manager"].includes(user.role)) {
      console.log("Assignment API - Role restriction triggered")
      return NextResponse.json({
        error: "Only admin and HR managers can assign tasks to other users",
        details: {
          current_user_role: user.role,
          regular_users_being_assigned: regularUsers.map(u => u.full_name)
        }
      }, { status: 403 })
    }

    // Start transaction
    await serverDb.sql`BEGIN`

    try {
      // Remove existing assignments for this task
      await serverDb.sql`
        DELETE FROM task_assignments WHERE task_id = ${taskId}
      `

      // Create new assignments
      const assignments = []
      for (const userId of user_ids) {
        const isPrimary = is_primary === userId
        
        const assignmentResult = await serverDb.sql`
          INSERT INTO task_assignments (task_id, user_id, assigned_by, is_primary)
          VALUES (${taskId}, ${userId}, ${user.id}, ${isPrimary})
          RETURNING *
        `
        assignments.push(assignmentResult[0])
      }

      // Update the main task's assigned_to field with the primary assignee
      const primaryAssignee = is_primary || user_ids[0]
      await serverDb.sql`
        UPDATE tasks 
        SET assigned_to = ${primaryAssignee}, updated_at = NOW()
        WHERE id = ${taskId}
      `

      await serverDb.sql`COMMIT`

      // Get the complete assignment data with user details
      const completeAssignments = await serverDb.sql`
        SELECT 
          ta.*,
          u.full_name,
          u.email,
          u.employee_id,
          u.department,
          u.position
        FROM task_assignments ta
        JOIN users u ON ta.user_id = u.id
        WHERE ta.task_id = ${taskId}
        ORDER BY ta.is_primary DESC, ta.assigned_at ASC
      `

      return NextResponse.json({
        success: true,
        data: completeAssignments,
        message: "Task assignments updated successfully"
      })

    } catch (error) {
      await serverDb.sql`ROLLBACK`
      throw error
    }

  } catch (error) {
    console.error("Create task assignments error:", error)
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    )
  }
}

// DELETE /api/tasks/[id]/assignments - Remove user from task
export async function DELETE(request: NextRequest, { params }: { params: { id: string } }) {
  try {
    const sessionToken = request.cookies.get("session-token")?.value

    if (!sessionToken) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 })
    }

    const user = await AuthService.verifySession(sessionToken)

    if (!user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 })
    }

    const { id: taskId } = params
    const { searchParams } = new URL(request.url)
    const userIdToRemove = searchParams.get('user_id')

    if (!userIdToRemove) {
      return NextResponse.json({ error: "user_id parameter is required" }, { status: 400 })
    }

    // Check if task exists
    const taskResult = await serverDb.sql`
      SELECT * FROM tasks WHERE id = ${taskId}
    `

    if (taskResult.length === 0) {
      return NextResponse.json({ error: "Task not found" }, { status: 404 })
    }

    const task = taskResult[0]

    // Check permissions
    const canRemove = 
      ["admin", "hr_manager"].includes(user.role) ||
      task.assigned_by === user.id ||
      userIdToRemove === user.id // Users can remove themselves

    if (!canRemove) {
      return NextResponse.json({ 
        error: "You don't have permission to remove this assignment" 
      }, { status: 403 })
    }

    // Remove the assignment
    const deleteResult = await serverDb.sql`
      DELETE FROM task_assignments 
      WHERE task_id = ${taskId} AND user_id = ${userIdToRemove}
      RETURNING *
    `

    if (deleteResult.length === 0) {
      return NextResponse.json({ error: "Assignment not found" }, { status: 404 })
    }

    // If we removed the primary assignee, make the first remaining assignee primary
    if (deleteResult[0].is_primary) {
      const remainingAssignments = await serverDb.sql`
        SELECT * FROM task_assignments 
        WHERE task_id = ${taskId}
        ORDER BY assigned_at ASC
        LIMIT 1
      `

      if (remainingAssignments.length > 0) {
        await serverDb.sql`
          UPDATE task_assignments 
          SET is_primary = true 
          WHERE id = ${remainingAssignments[0].id}
        `

        await serverDb.sql`
          UPDATE tasks 
          SET assigned_to = ${remainingAssignments[0].user_id}, updated_at = NOW()
          WHERE id = ${taskId}
        `
      } else {
        // No more assignees, set task as unassigned
        await serverDb.sql`
          UPDATE tasks 
          SET assigned_to = NULL, updated_at = NOW()
          WHERE id = ${taskId}
        `
      }
    }

    return NextResponse.json({
      success: true,
      message: "Assignment removed successfully"
    })

  } catch (error) {
    console.error("Remove task assignment error:", error)
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    )
  }
}
