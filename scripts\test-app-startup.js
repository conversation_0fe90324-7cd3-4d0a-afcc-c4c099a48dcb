#!/usr/bin/env node

// Test application startup and basic functionality
const { spawn } = require('child_process');
const http = require('http');

async function testAppStartup() {
  console.log('🚀 Testing Next.js application startup...\n');
  
  // Check if .env.local exists
  const fs = require('fs');
  if (!fs.existsSync('.env.local')) {
    console.log('⚠️  .env.local not found - this is expected if DATABASE_URL not set yet');
  } else {
    console.log('✅ .env.local found');
  }
  
  console.log('🔄 Starting Next.js development server...');
  console.log('📝 This will test if the application can start without errors\n');
  
  // Start Next.js dev server
  const nextProcess = spawn('npm', ['run', 'dev'], {
    stdio: ['pipe', 'pipe', 'pipe'],
    shell: true
  });
  
  let serverReady = false;
  let serverError = false;
  
  // Monitor stdout for server ready message
  nextProcess.stdout.on('data', (data) => {
    const output = data.toString();
    console.log('📤 Next.js:', output.trim());
    
    if (output.includes('Ready') || output.includes('started server') || output.includes('Local:')) {
      serverReady = true;
      console.log('\n✅ Next.js server started successfully!');
      testEndpoints();
    }
  });
  
  // Monitor stderr for errors
  nextProcess.stderr.on('data', (data) => {
    const error = data.toString();
    console.error('❌ Next.js Error:', error.trim());
    
    if (error.includes('DATABASE_URL')) {
      console.log('\n💡 Database connection error detected');
      console.log('   This is expected if you haven\'t set up your Neon database yet');
    }
    
    if (error.includes('EADDRINUSE')) {
      console.log('\n💡 Port already in use - another instance may be running');
    }
    
    serverError = true;
  });
  
  // Test basic endpoints once server is ready
  async function testEndpoints() {
    console.log('\n🔄 Testing application endpoints...');
    
    // Wait a moment for server to fully initialize
    await new Promise(resolve => setTimeout(resolve, 2000));
    
    const endpoints = [
      { path: '/', description: 'Home page (should redirect to login)' },
      { path: '/auth/login', description: 'Login page' },
      { path: '/api/auth/me', description: 'Auth API endpoint' }
    ];
    
    for (const endpoint of endpoints) {
      try {
        console.log(`🔄 Testing ${endpoint.path}...`);
        
        const response = await makeRequest('localhost', 3000, endpoint.path);
        
        if (response.statusCode === 200 || response.statusCode === 302 || response.statusCode === 401) {
          console.log(`✅ ${endpoint.path}: ${response.statusCode} (${endpoint.description})`);
        } else {
          console.log(`⚠️  ${endpoint.path}: ${response.statusCode} - ${response.statusMessage}`);
        }
        
      } catch (error) {
        console.log(`❌ ${endpoint.path}: ${error.message}`);
      }
    }
    
    console.log('\n🎉 Application startup test completed!');
    console.log('\n📋 Next Steps:');
    console.log('1. Set up your Neon database connection in .env.local');
    console.log('2. Run: node scripts/setup-database.js');
    console.log('3. Run: node scripts/test-auth-system.js');
    console.log('4. Access the app at http://localhost:3000');
    
    // Stop the server
    console.log('\n🛑 Stopping test server...');
    nextProcess.kill('SIGTERM');
    process.exit(0);
  }
  
  // Handle process exit
  nextProcess.on('close', (code) => {
    if (code !== 0 && !serverReady) {
      console.log(`\n❌ Next.js process exited with code ${code}`);
      
      if (!serverError) {
        console.log('\n💡 Possible issues:');
        console.log('- Dependencies not installed (run: npm install)');
        console.log('- Port 3000 already in use');
        console.log('- Configuration errors');
      }
    }
  });
  
  // Timeout after 30 seconds
  setTimeout(() => {
    if (!serverReady) {
      console.log('\n⏰ Timeout: Server did not start within 30 seconds');
      nextProcess.kill('SIGTERM');
      process.exit(1);
    }
  }, 30000);
}

// Helper function to make HTTP requests
function makeRequest(hostname, port, path) {
  return new Promise((resolve, reject) => {
    const options = {
      hostname,
      port,
      path,
      method: 'GET',
      timeout: 5000
    };
    
    const req = http.request(options, (res) => {
      resolve({
        statusCode: res.statusCode,
        statusMessage: res.statusMessage,
        headers: res.headers
      });
    });
    
    req.on('error', reject);
    req.on('timeout', () => reject(new Error('Request timeout')));
    req.end();
  });
}

// Handle Ctrl+C gracefully
process.on('SIGINT', () => {
  console.log('\n\n🛑 Test interrupted by user');
  process.exit(0);
});

testAppStartup();
