#!/usr/bin/env node

// Create all demo users
let neon, bcrypt, dotenv;

try {
  ({ neon } = require('@neondatabase/serverless'));
  bcrypt = require('bcryptjs');
  dotenv = require('dotenv');
  dotenv.config({ path: '.env.local' });
} catch (error) {
  console.log('⚠️  Required packages not installed yet.');
  process.exit(1);
}

async function createDemoUsers() {
  console.log('👥 Creating demo users...\n');
  
  if (!process.env.DATABASE_URL) {
    console.error('❌ DATABASE_URL not set');
    process.exit(1);
  }
  
  try {
    const sql = neon(process.env.DATABASE_URL);
    
    // Hash password once for all users
    console.log('🔄 Hashing password...');
    const passwordHash = await bcrypt.hash('admin123', 12);
    console.log('✅ Password hashed');
    
    const demoUsers = [
      {
        email: '<EMAIL>',
        full_name: 'System Administrator',
        role: 'admin',
        department: 'IT',
        position: 'System Administrator'
      },
      {
        email: '<EMAIL>',
        full_name: 'HR Manager',
        role: 'hr_manager',
        department: 'Human Resources',
        position: 'HR Manager'
      },
      {
        email: '<EMAIL>',
        full_name: 'Department Manager',
        role: 'manager',
        department: 'Operations',
        position: 'Operations Manager'
      },
      {
        email: '<EMAIL>',
        full_name: 'Staff Member',
        role: 'staff',
        department: 'Operations',
        position: 'Staff Member'
      }
    ];
    
    console.log('🔄 Creating demo users...');
    
    for (const user of demoUsers) {
      try {
        await sql`
          INSERT INTO users (
            email, password_hash, full_name, role, department, position, is_active, email_verified
          ) VALUES (
            ${user.email}, ${passwordHash}, ${user.full_name}, ${user.role}, 
            ${user.department}, ${user.position}, true, true
          )
          ON CONFLICT (email) DO NOTHING
        `;
        console.log(`✅ Created: ${user.email} (${user.role})`);
      } catch (error) {
        if (error.message.includes('duplicate key')) {
          console.log(`ℹ️  Already exists: ${user.email}`);
        } else {
          console.error(`❌ Error creating ${user.email}:`, error.message);
        }
      }
    }
    
    // Verify users were created
    console.log('\n🔍 Verifying created users...');
    const users = await sql`SELECT email, role, is_active FROM users ORDER BY role`;
    
    console.log(`📊 Total users: ${users.length}`);
    users.forEach(user => {
      console.log(`   - ${user.email} (${user.role}) ${user.is_active ? '✅' : '❌'}`);
    });
    
    console.log('\n🎉 Demo users setup complete!');
    console.log('\n📋 Login Credentials (all use password: admin123):');
    users.forEach(user => {
      console.log(`   ${user.email} / admin123`);
    });
    
  } catch (error) {
    console.error('❌ Error:', error.message);
  }
}

createDemoUsers();
