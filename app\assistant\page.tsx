"use client"

import type React from "react"

import { useState } from "react"
import { <PERSON><PERSON><PERSON><PERSON><PERSON> } from "@/components/app-header"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Avatar } from "@/components/ui/avatar"
import { Tabs, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Bot, Mic, Send, Paperclip, User, Lightbulb, MessageSquare } from "lucide-react"

export default function AssistantPage() {
  const [message, setMessage] = useState("")
  const [chatHistory, setChatHistory] = useState([
    { role: "assistant", content: "Hello! I'm your AI assistant. How can I help you today?" },
  ])

  const handleSendMessage = () => {
    if (!message.trim()) return

    // Add user message to chat
    setChatHistory([...chatHistory, { role: "user", content: message }])

    // Simulate AI response (in a real app, this would call an AI API)
    setTimeout(() => {
      setChatHistory((prev) => [
        ...prev,
        {
          role: "assistant",
          content:
            "I understand you need help with that. I can provide information about our financial products, help prepare for client meetings, or suggest responses to common objections. What specific assistance do you need?",
        },
      ])
    }, 1000)

    setMessage("")
  }

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === "Enter" && !e.shiftKey) {
      e.preventDefault()
      handleSendMessage()
    }
  }

  const suggestions = [
    "Help me prepare for a client meeting with John Smith",
    "What are the key features of our new investment product?",
    "How should I respond to objections about high interest rates?",
    "Generate a follow-up email template for a lead",
  ]

  return (
    <div className="min-h-screen bg-gray-50 flex flex-col">
      <AppHeader title="AI Assistant" />

      <div className="p-4 flex-1 flex flex-col">
        <Tabs defaultValue="chat" className="mb-4">
          <TabsList className="grid w-full grid-cols-3">
            <TabsTrigger value="chat">
              <MessageSquare className="h-4 w-4 mr-2" />
              Chat
            </TabsTrigger>
            <TabsTrigger value="meeting">
              <User className="h-4 w-4 mr-2" />
              Meeting Prep
            </TabsTrigger>
            <TabsTrigger value="knowledge">
              <Lightbulb className="h-4 w-4 mr-2" />
              Knowledge
            </TabsTrigger>
          </TabsList>
        </Tabs>

        <Card className="flex-1 flex flex-col mb-4">
          <CardHeader className="pb-2">
            <div className="flex items-center gap-2">
              <Avatar className="h-8 w-8 bg-exobank-green text-white">
                <Bot className="h-5 w-5" />
              </Avatar>
              <div>
                <CardTitle className="text-base">exoBank Assistant</CardTitle>
                <CardDescription className="text-xs">AI-powered support for marketing staff</CardDescription>
              </div>
            </div>
          </CardHeader>
          <CardContent className="flex-1 overflow-y-auto p-4 space-y-4">
            {chatHistory.map((msg, index) => (
              <div key={index} className={`flex ${msg.role === "user" ? "justify-end" : "justify-start"}`}>
                <div
                  className={`max-w-[80%] rounded-lg p-3 ${
                    msg.role === "user"
                      ? "bg-exobank-green text-white rounded-tr-none"
                      : "bg-gray-100 text-gray-800 rounded-tl-none"
                  }`}
                >
                  {msg.content}
                </div>
              </div>
            ))}
          </CardContent>
          <CardFooter className="border-t p-3">
            <div className="flex items-center gap-2 w-full">
              <Button variant="outline" size="icon" className="rounded-full">
                <Paperclip className="h-4 w-4" />
              </Button>
              <Button variant="outline" size="icon" className="rounded-full">
                <Mic className="h-4 w-4" />
              </Button>
              <Input
                placeholder="Type your message..."
                value={message}
                onChange={(e) => setMessage(e.target.value)}
                onKeyDown={handleKeyDown}
                className="flex-1"
              />
              <Button
                onClick={handleSendMessage}
                disabled={!message.trim()}
                size="icon"
                className="rounded-full bg-exobank-green hover:bg-exobank-green/90 text-white"
              >
                <Send className="h-4 w-4" />
              </Button>
            </div>
          </CardFooter>
        </Card>

        <div className="space-y-2">
          <h3 className="text-sm font-medium text-gray-500">Suggested Queries</h3>
          <div className="flex flex-wrap gap-2">
            {suggestions.map((suggestion, index) => (
              <Button
                key={index}
                variant="outline"
                size="sm"
                className="text-xs"
                onClick={() => {
                  setMessage(suggestion)
                }}
              >
                {suggestion}
              </Button>
            ))}
          </div>
        </div>
      </div>
    </div>
  )
}
