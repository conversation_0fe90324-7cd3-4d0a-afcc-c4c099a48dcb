// Bulk Payroll Processing API
// Admin endpoints for bulk payroll operations

import { NextRequest, NextResponse } from 'next/server';
import { AuthService } from '@/lib/auth-utils';
import { nepalPayrollProcessor } from '@/lib/nepal-payroll-processor';
import { nepalTaxCalculator } from '@/lib/nepal-tax-calculator';
import { neon } from '@neondatabase/serverless';

const sql = neon(process.env.DATABASE_URL!);

// GET - Check bulk processing status or get processing history
export async function GET(request: NextRequest) {
  try {
    const sessionToken = request.cookies.get("session-token")?.value;

    if (!sessionToken) {
      return NextResponse.json({
        success: false,
        error: 'Unauthorized'
      }, { status: 401 });
    }

    const user = await AuthService.verifySession(sessionToken);

    if (!user || !["admin", "hr_manager"].includes(user.role)) {
      return NextResponse.json({
        success: false,
        error: 'Insufficient permissions'
      }, { status: 403 });
    }

    const { searchParams } = new URL(request.url);
    const action = searchParams.get('action') || 'processing_history';
    const year = searchParams.get('year');
    const month = searchParams.get('month');

    if (action === 'processing_status') {
      // Get current processing status for a period
      if (!year || !month) {
        return NextResponse.json({
          success: false,
          error: 'Year and month are required for processing status'
        }, { status: 400 });
      }

      const fiscalYear = nepalTaxCalculator.getCurrentFiscalYear();
      const payrollPeriod = nepalTaxCalculator.getMonthlyPayrollPeriod(parseInt(year), parseInt(month));

      // Get processing status for all employees
      const processingStatus = await sql`
        SELECT 
          u.id as user_id,
          u.full_name,
          u.employee_id,
          u.department,
          u.salary,
          u.employment_status,
          mps.id as payroll_summary_id,
          mps.status as payroll_status,
          mps.calculated_at,
          mps.approved_at,
          mps.net_pay,
          CASE 
            WHEN mps.id IS NULL THEN 'not_processed'
            WHEN mps.status = 'draft' THEN 'calculated'
            WHEN mps.status = 'approved' THEN 'approved'
            WHEN mps.status = 'processed' THEN 'processed'
            WHEN mps.status = 'paid' THEN 'paid'
            ELSE 'unknown'
          END as processing_stage
        FROM users u
        LEFT JOIN monthly_payroll_summary mps ON u.id = mps.user_id 
          AND mps.fiscal_year = ${fiscalYear.fiscal_year}
          AND mps.bs_month = ${payrollPeriod.bs_month_name}
        WHERE u.role != 'admin' AND u.employment_status = 'active'
        ORDER BY u.department, u.full_name
      `;

      const summary = {
        total_employees: processingStatus.length,
        not_processed: processingStatus.filter(p => p.processing_stage === 'not_processed').length,
        calculated: processingStatus.filter(p => p.processing_stage === 'calculated').length,
        approved: processingStatus.filter(p => p.processing_stage === 'approved').length,
        processed: processingStatus.filter(p => p.processing_stage === 'processed').length,
        paid: processingStatus.filter(p => p.processing_stage === 'paid').length
      };

      return NextResponse.json({
        success: true,
        data: {
          employees: processingStatus,
          summary,
          period: payrollPeriod,
          fiscal_year: fiscalYear
        },
        message: 'Processing status retrieved successfully'
      });

    } else if (action === 'processing_history') {
      // Get bulk processing history
      const limit = parseInt(searchParams.get('limit') || '20');
      const offset = parseInt(searchParams.get('offset') || '0');

      const history = await sql`
        SELECT 
          mps.fiscal_year,
          mps.bs_month,
          mps.ad_month_start,
          mps.ad_month_end,
          COUNT(*) as total_employees,
          COUNT(CASE WHEN mps.status = 'paid' THEN 1 END) as paid_employees,
          COUNT(CASE WHEN mps.status = 'processed' THEN 1 END) as processed_employees,
          COUNT(CASE WHEN mps.status = 'approved' THEN 1 END) as approved_employees,
          SUM(mps.net_pay) as total_net_pay,
          MAX(mps.calculated_at) as last_calculated,
          MAX(mps.approved_at) as last_approved
        FROM monthly_payroll_summary mps
        GROUP BY mps.fiscal_year, mps.bs_month, mps.ad_month_start, mps.ad_month_end
        ORDER BY mps.ad_month_start DESC
        LIMIT ${limit} OFFSET ${offset}
      `;

      return NextResponse.json({
        success: true,
        data: history,
        message: 'Processing history retrieved successfully'
      });

    } else if (action === 'eligible_employees') {
      // Get employees eligible for payroll processing
      const eligibleEmployees = await sql`
        SELECT 
          u.id,
          u.full_name,
          u.employee_id,
          u.department,
          u.position,
          u.salary,
          u.hire_date,
          u.employment_type,
          CASE 
            WHEN u.salary IS NULL OR u.salary = 0 THEN false
            ELSE true
          END as has_salary_configured
        FROM users u
        WHERE u.role != 'admin' 
          AND u.employment_status = 'active'
          AND u.hire_date IS NOT NULL
        ORDER BY u.department, u.full_name
      `;

      const eligibleCount = eligibleEmployees.filter(e => e.has_salary_configured).length;
      const ineligibleCount = eligibleEmployees.filter(e => !e.has_salary_configured).length;

      return NextResponse.json({
        success: true,
        data: {
          employees: eligibleEmployees,
          summary: {
            total: eligibleEmployees.length,
            eligible: eligibleCount,
            ineligible: ineligibleCount
          }
        },
        message: 'Eligible employees retrieved successfully'
      });

    } else {
      return NextResponse.json({
        success: false,
        error: 'Invalid action'
      }, { status: 400 });
    }

  } catch (error) {
    console.error('Error in bulk-process GET:', error);
    return NextResponse.json({
      success: false,
      error: error.message || 'Internal server error'
    }, { status: 500 });
  }
}

// POST - Start bulk payroll processing
export async function POST(request: NextRequest) {
  try {
    const sessionToken = request.cookies.get("session-token")?.value;

    if (!sessionToken) {
      return NextResponse.json({
        success: false,
        error: 'Unauthorized'
      }, { status: 401 });
    }

    const user = await AuthService.verifySession(sessionToken);

    if (!user || !["admin", "hr_manager"].includes(user.role)) {
      return NextResponse.json({
        success: false,
        error: 'Insufficient permissions'
      }, { status: 403 });
    }

    const body = await request.json();
    const { 
      action, 
      year, 
      month, 
      userIds, 
      departments,
      includeInactive = false,
      dryRun = false 
    } = body;

    if (action === 'process_monthly_all') {
      // Process monthly payroll for all eligible employees
      if (!year || !month) {
        return NextResponse.json({
          success: false,
          error: 'Year and month are required'
        }, { status: 400 });
      }

      if (dryRun) {
        // Dry run - calculate but don't save
        const fiscalYear = nepalTaxCalculator.getCurrentFiscalYear();
        const payrollPeriod = nepalTaxCalculator.getMonthlyPayrollPeriod(year, month);

        const employees = await sql`
          SELECT id, full_name, salary, department
          FROM users 
          WHERE role != 'admin' 
            AND employment_status = 'active'
            AND salary IS NOT NULL 
            AND salary > 0
          ORDER BY department, full_name
        `;

        const dryRunResults = [];
        for (const employee of employees) {
          try {
            // This would be the actual calculation without saving
            const calculation = await nepalPayrollProcessor.calculateEmployeePayroll(
              employee.id, year, month, fiscalYear, payrollPeriod
            );
            dryRunResults.push({
              employee_id: employee.id,
              employee_name: employee.full_name,
              department: employee.department,
              base_salary: employee.salary,
              calculated_net_pay: calculation.net_pay,
              status: 'success'
            });
          } catch (error) {
            dryRunResults.push({
              employee_id: employee.id,
              employee_name: employee.full_name,
              department: employee.department,
              status: 'error',
              error: error.message
            });
          }
        }

        return NextResponse.json({
          success: true,
          data: {
            dry_run: true,
            results: dryRunResults,
            summary: {
              total: dryRunResults.length,
              successful: dryRunResults.filter(r => r.status === 'success').length,
              failed: dryRunResults.filter(r => r.status === 'error').length,
              total_net_pay: dryRunResults
                .filter(r => r.status === 'success')
                .reduce((sum, r) => sum + (r.calculated_net_pay || 0), 0)
            }
          },
          message: 'Dry run completed successfully'
        });
      } else {
        // Actual processing
        const result = await nepalPayrollProcessor.processMonthlyPayroll(year, month, user.id);

        return NextResponse.json({
          success: true,
          data: result,
          message: `Monthly payroll processed: ${result.processed_count} employees processed, ${result.failed_count} failed`
        });
      }

    } else if (action === 'process_selected_employees') {
      // Process payroll for selected employees
      if (!year || !month || !userIds || !Array.isArray(userIds)) {
        return NextResponse.json({
          success: false,
          error: 'Year, month, and user IDs array are required'
        }, { status: 400 });
      }

      const fiscalYear = nepalTaxCalculator.getCurrentFiscalYear();
      const payrollPeriod = nepalTaxCalculator.getMonthlyPayrollPeriod(year, month);

      const results = [];
      const errors = [];

      for (const userId of userIds) {
        try {
          const payrollRecord = await nepalPayrollProcessor.processEmployeePayroll(
            userId, year, month, fiscalYear, payrollPeriod, user.id
          );
          results.push(payrollRecord);
        } catch (error) {
          errors.push({
            user_id: userId,
            error: error.message
          });
        }
      }

      return NextResponse.json({
        success: true,
        data: {
          processed: results,
          errors: errors,
          summary: {
            total_requested: userIds.length,
            successful: results.length,
            failed: errors.length
          }
        },
        message: `Bulk processing completed: ${results.length} successful, ${errors.length} failed`
      });

    } else if (action === 'process_by_department') {
      // Process payroll by departments
      if (!year || !month || !departments || !Array.isArray(departments)) {
        return NextResponse.json({
          success: false,
          error: 'Year, month, and departments array are required'
        }, { status: 400 });
      }

      const fiscalYear = nepalTaxCalculator.getCurrentFiscalYear();
      const payrollPeriod = nepalTaxCalculator.getMonthlyPayrollPeriod(year, month);

      // Get employees from specified departments
      const employees = await sql`
        SELECT id, full_name, department
        FROM users 
        WHERE role != 'admin' 
          AND employment_status = 'active'
          AND department = ANY(${departments})
          AND salary IS NOT NULL 
          AND salary > 0
        ORDER BY department, full_name
      `;

      const results = [];
      const errors = [];

      for (const employee of employees) {
        try {
          const payrollRecord = await nepalPayrollProcessor.processEmployeePayroll(
            employee.id, year, month, fiscalYear, payrollPeriod, user.id
          );
          results.push({
            ...payrollRecord,
            employee_name: employee.full_name,
            department: employee.department
          });
        } catch (error) {
          errors.push({
            user_id: employee.id,
            employee_name: employee.full_name,
            department: employee.department,
            error: error.message
          });
        }
      }

      return NextResponse.json({
        success: true,
        data: {
          processed: results,
          errors: errors,
          departments: departments,
          summary: {
            total_employees: employees.length,
            successful: results.length,
            failed: errors.length,
            departments_processed: departments.length
          }
        },
        message: `Department payroll processing completed: ${results.length} successful, ${errors.length} failed`
      });

    } else {
      return NextResponse.json({
        success: false,
        error: 'Invalid action'
      }, { status: 400 });
    }

  } catch (error) {
    console.error('Error in bulk-process POST:', error);
    return NextResponse.json({
      success: false,
      error: error.message || 'Internal server error'
    }, { status: 500 });
  }
}

// PUT - Update bulk processing status or approve bulk payroll
export async function PUT(request: NextRequest) {
  try {
    const sessionToken = request.cookies.get("session-token")?.value;

    if (!sessionToken) {
      return NextResponse.json({
        success: false,
        error: 'Unauthorized'
      }, { status: 401 });
    }

    const user = await AuthService.verifySession(sessionToken);

    if (!user || !["admin", "hr_manager"].includes(user.role)) {
      return NextResponse.json({
        success: false,
        error: 'Insufficient permissions'
      }, { status: 403 });
    }

    const body = await request.json();
    const { action, year, month, userIds, newStatus } = body;

    if (action === 'bulk_approve') {
      // Bulk approve payroll records
      if (!year || !month) {
        return NextResponse.json({
          success: false,
          error: 'Year and month are required'
        }, { status: 400 });
      }

      const fiscalYear = nepalTaxCalculator.getCurrentFiscalYear();
      const payrollPeriod = nepalTaxCalculator.getMonthlyPayrollPeriod(year, month);

      let whereClause = sql`
        WHERE fiscal_year = ${fiscalYear.fiscal_year}
          AND bs_month = ${payrollPeriod.bs_month_name}
          AND status = 'calculated'
      `;

      if (userIds && Array.isArray(userIds) && userIds.length > 0) {
        whereClause = sql`
          WHERE fiscal_year = ${fiscalYear.fiscal_year}
            AND bs_month = ${payrollPeriod.bs_month_name}
            AND status = 'calculated'
            AND user_id = ANY(${userIds})
        `;
      }

      const approved = await sql`
        UPDATE monthly_payroll_summary 
        SET 
          status = 'approved',
          approved_by = ${user.id},
          approved_at = NOW(),
          updated_at = NOW()
        ${whereClause}
        RETURNING user_id, net_pay
      `;

      return NextResponse.json({
        success: true,
        data: {
          approved_count: approved.length,
          total_amount: approved.reduce((sum, record) => sum + record.net_pay, 0),
          approved_records: approved
        },
        message: `Bulk approval completed: ${approved.length} payroll records approved`
      });

    } else if (action === 'bulk_status_update') {
      // Update status for multiple payroll records
      if (!userIds || !Array.isArray(userIds) || !newStatus) {
        return NextResponse.json({
          success: false,
          error: 'User IDs array and new status are required'
        }, { status: 400 });
      }

      const validStatuses = ['draft', 'calculated', 'approved', 'processed', 'paid'];
      if (!validStatuses.includes(newStatus)) {
        return NextResponse.json({
          success: false,
          error: 'Invalid status'
        }, { status: 400 });
      }

      const updated = await sql`
        UPDATE monthly_payroll_summary 
        SET 
          status = ${newStatus},
          updated_at = NOW()
        WHERE user_id = ANY(${userIds})
        RETURNING user_id, status
      `;

      return NextResponse.json({
        success: true,
        data: {
          updated_count: updated.length,
          new_status: newStatus,
          updated_records: updated
        },
        message: `Bulk status update completed: ${updated.length} records updated to ${newStatus}`
      });

    } else {
      return NextResponse.json({
        success: false,
        error: 'Invalid action'
      }, { status: 400 });
    }

  } catch (error) {
    console.error('Error in bulk-process PUT:', error);
    return NextResponse.json({
      success: false,
      error: error.message || 'Internal server error'
    }, { status: 500 });
  }
}
