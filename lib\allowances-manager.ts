// Allowances Management System
// Complete allowances management with travelling, phone, and custom allowances

import { db } from './neon';

export interface AllowanceType {
  id: string;
  name: string;
  code: string;
  type: 'allowance';
  category: 'statutory' | 'voluntary' | 'company_policy' | 'custom';
  calculation_type: 'fixed' | 'percentage' | 'formula';
  fixed_amount?: number;
  percentage?: number;
  percentage_base?: 'base_salary' | 'gross_pay' | 'net_pay';
  is_taxable: boolean;
  is_statutory: boolean;
  description?: string;
  is_active: boolean;
  effective_from: string;
  effective_to?: string;
}

export interface AllowanceAssignment {
  id: string;
  user_id: string;
  allowance_type: 'travelling' | 'phone' | 'meal' | 'transport' | 'medical' | 'education' | 'custom';
  allowance_name: string;
  calculation_type: 'fixed' | 'percentage';
  amount: number;
  percentage?: number;
  percentage_base?: 'base_salary' | 'gross_pay';
  is_taxable: boolean;
  effective_from: string;
  effective_to?: string;
  is_active: boolean;
  assigned_by?: string;
  notes?: string;
  created_at: string;
  updated_at: string;
}

export interface AllowanceConfiguration {
  allowance_type: 'travelling' | 'phone' | 'meal' | 'transport' | 'medical' | 'education' | 'custom';
  allowance_name: string;
  calculation_type: 'fixed' | 'percentage';
  amount?: number;
  percentage?: number;
  percentage_base?: 'base_salary' | 'gross_pay';
  is_taxable: boolean;
  requires_receipt?: boolean;
  monthly_limit?: number;
  description?: string;
}

export interface EmployeeAllowanceSummary {
  user_id: string;
  full_name: string;
  email: string;
  department?: string;
  position?: string;
  base_salary?: number;
  total_allowances: number;
  allowance_count: number;
  allowances: AllowanceAssignment[];
}

export class AllowancesManager {
  
  // Get all allowance types from master table
  async getAllowanceTypes(): Promise<AllowanceType[]> {
    try {
      const allowanceTypes = await db.sql`
        SELECT * FROM payroll_components_master 
        WHERE type = 'allowance' AND is_active = TRUE
        ORDER BY category, name
      `;
      
      return allowanceTypes;
      
    } catch (error) {
      console.error('Error getting allowance types:', error);
      throw new Error('Failed to get allowance types');
    }
  }
  
  // Create new allowance type
  async createAllowanceType(allowanceType: Omit<AllowanceType, 'id' | 'created_at' | 'updated_at'>): Promise<string> {
    try {
      const result = await db.sql`
        INSERT INTO payroll_components_master (
          name, code, type, category, calculation_type, fixed_amount, percentage,
          percentage_base, is_taxable, is_statutory, description, is_active, effective_from, effective_to
        ) VALUES (
          ${allowanceType.name}, ${allowanceType.code}, 'allowance', ${allowanceType.category},
          ${allowanceType.calculation_type}, ${allowanceType.fixed_amount || null}, ${allowanceType.percentage || null},
          ${allowanceType.percentage_base || null}, ${allowanceType.is_taxable}, ${allowanceType.is_statutory},
          ${allowanceType.description || null}, ${allowanceType.is_active}, ${allowanceType.effective_from},
          ${allowanceType.effective_to || null}
        ) RETURNING id
      `;
      
      return result[0].id;
      
    } catch (error) {
      console.error('Error creating allowance type:', error);
      if (error.message?.includes('duplicate key')) {
        throw new Error('Allowance code already exists');
      }
      throw new Error('Failed to create allowance type');
    }
  }
  
  // Update allowance type
  async updateAllowanceType(id: string, updates: Partial<AllowanceType>): Promise<boolean> {
    try {
      const updateFields = [];
      const values = [];
      let paramIndex = 1;
      
      const allowedFields = [
        'name', 'category', 'calculation_type', 'fixed_amount', 'percentage',
        'percentage_base', 'is_taxable', 'is_statutory', 'description', 'is_active', 'effective_to'
      ];
      
      for (const field of allowedFields) {
        if (updates[field] !== undefined) {
          updateFields.push(`${field} = $${paramIndex}`);
          values.push(updates[field]);
          paramIndex++;
        }
      }
      
      if (updateFields.length === 0) {
        return true;
      }
      
      updateFields.push(`updated_at = NOW()`);
      values.push(id);
      
      const query = `
        UPDATE payroll_components_master 
        SET ${updateFields.join(', ')}
        WHERE id = $${paramIndex}
      `;
      
      await db.sql([query, ...values]);
      return true;
      
    } catch (error) {
      console.error('Error updating allowance type:', error);
      throw new Error('Failed to update allowance type');
    }
  }
  
  // Assign allowance to employee
  async assignAllowanceToEmployee(
    userId: string, 
    allowanceConfig: AllowanceConfiguration,
    assignedBy: string
  ): Promise<string> {
    try {
      const result = await db.sql`
        INSERT INTO allowance_assignments (
          user_id, allowance_type, allowance_name, calculation_type, amount, percentage,
          percentage_base, is_taxable, effective_from, is_active, assigned_by, notes
        ) VALUES (
          ${userId}, ${allowanceConfig.allowance_type}, ${allowanceConfig.allowance_name},
          ${allowanceConfig.calculation_type}, ${allowanceConfig.amount || 0}, ${allowanceConfig.percentage || null},
          ${allowanceConfig.percentage_base || null}, ${allowanceConfig.is_taxable}, CURRENT_DATE,
          TRUE, ${assignedBy}, ${allowanceConfig.description || null}
        ) RETURNING id
      `;
      
      return result[0].id;
      
    } catch (error) {
      console.error('Error assigning allowance to employee:', error);
      throw new Error('Failed to assign allowance to employee');
    }
  }
  
  // Update employee allowance assignment
  async updateEmployeeAllowance(assignmentId: string, updates: Partial<AllowanceAssignment>): Promise<boolean> {
    try {
      const updateFields = [];
      const values = [];
      let paramIndex = 1;
      
      const allowedFields = [
        'allowance_name', 'calculation_type', 'amount', 'percentage', 'percentage_base',
        'is_taxable', 'effective_to', 'is_active', 'notes'
      ];
      
      for (const field of allowedFields) {
        if (updates[field] !== undefined) {
          updateFields.push(`${field} = $${paramIndex}`);
          values.push(updates[field]);
          paramIndex++;
        }
      }
      
      if (updateFields.length === 0) {
        return true;
      }
      
      updateFields.push(`updated_at = NOW()`);
      values.push(assignmentId);
      
      const query = `
        UPDATE allowance_assignments 
        SET ${updateFields.join(', ')}
        WHERE id = $${paramIndex}
      `;
      
      await db.sql([query, ...values]);
      return true;
      
    } catch (error) {
      console.error('Error updating employee allowance:', error);
      throw new Error('Failed to update employee allowance');
    }
  }
  
  // Remove allowance from employee (deactivate)
  async removeEmployeeAllowance(assignmentId: string): Promise<boolean> {
    try {
      await db.sql`
        UPDATE allowance_assignments 
        SET is_active = FALSE, effective_to = CURRENT_DATE, updated_at = NOW()
        WHERE id = ${assignmentId}
      `;
      
      return true;
      
    } catch (error) {
      console.error('Error removing employee allowance:', error);
      throw new Error('Failed to remove employee allowance');
    }
  }
  
  // Get employee allowances
  async getEmployeeAllowances(userId: string): Promise<AllowanceAssignment[]> {
    try {
      const allowances = await db.sql`
        SELECT * FROM allowance_assignments 
        WHERE user_id = ${userId} AND is_active = TRUE
        ORDER BY created_at DESC
      `;
      
      return allowances;
      
    } catch (error) {
      console.error('Error getting employee allowances:', error);
      throw new Error('Failed to get employee allowances');
    }
  }
  
  // Get all employees with allowance summary
  async getAllEmployeesAllowanceSummary(): Promise<EmployeeAllowanceSummary[]> {
    try {
      const employees = await db.sql`
        SELECT 
          u.id as user_id,
          u.full_name,
          u.email,
          u.department,
          u.position,
          u.salary as base_salary,
          COALESCE(SUM(CASE 
            WHEN aa.calculation_type = 'fixed' THEN aa.amount
            WHEN aa.calculation_type = 'percentage' AND aa.percentage_base = 'base_salary' THEN (u.salary * aa.percentage / 100)
            ELSE 0
          END), 0) as total_allowances,
          COUNT(aa.id) as allowance_count
        FROM users u
        LEFT JOIN allowance_assignments aa ON u.id = aa.user_id AND aa.is_active = TRUE
        WHERE u.role != 'admin'
        GROUP BY u.id, u.full_name, u.email, u.department, u.position, u.salary
        ORDER BY u.full_name
      `;
      
      // Get detailed allowances for each employee
      const result = [];
      for (const emp of employees) {
        const allowances = await this.getEmployeeAllowances(emp.user_id);
        result.push({
          ...emp,
          allowances
        });
      }
      
      return result;
      
    } catch (error) {
      console.error('Error getting employees allowance summary:', error);
      throw new Error('Failed to get employees allowance summary');
    }
  }
  
  // Calculate allowance amount for employee
  async calculateAllowanceAmount(
    userId: string, 
    allowanceConfig: AllowanceConfiguration
  ): Promise<number> {
    try {
      if (allowanceConfig.calculation_type === 'fixed') {
        return allowanceConfig.amount || 0;
      }
      
      if (allowanceConfig.calculation_type === 'percentage') {
        // Get employee's base salary
        const employee = await db.sql`
          SELECT salary FROM users WHERE id = ${userId}
        `;
        
        if (employee.length === 0) {
          throw new Error('Employee not found');
        }
        
        const baseSalary = employee[0].salary || 0;
        const percentage = allowanceConfig.percentage || 0;
        
        return (baseSalary * percentage) / 100;
      }
      
      return 0;
      
    } catch (error) {
      console.error('Error calculating allowance amount:', error);
      throw new Error('Failed to calculate allowance amount');
    }
  }
  
  // Get allowance statistics
  async getAllowanceStatistics(): Promise<{
    totalAllowanceTypes: number;
    totalActiveAssignments: number;
    totalAllowanceAmount: number;
    mostUsedAllowanceType: string;
    averageAllowancePerEmployee: number;
  }> {
    try {
      // Get total allowance types
      const allowanceTypes = await db.sql`
        SELECT COUNT(*) as count FROM payroll_components_master 
        WHERE type = 'allowance' AND is_active = TRUE
      `;
      
      // Get total active assignments
      const activeAssignments = await db.sql`
        SELECT COUNT(*) as count FROM allowance_assignments 
        WHERE is_active = TRUE
      `;
      
      // Get total allowance amount (approximation for fixed amounts)
      const totalAmount = await db.sql`
        SELECT 
          COALESCE(SUM(CASE 
            WHEN aa.calculation_type = 'fixed' THEN aa.amount
            WHEN aa.calculation_type = 'percentage' AND aa.percentage_base = 'base_salary' THEN (u.salary * aa.percentage / 100)
            ELSE 0
          END), 0) as total
        FROM allowance_assignments aa
        JOIN users u ON aa.user_id = u.id
        WHERE aa.is_active = TRUE
      `;
      
      // Get most used allowance type
      const mostUsed = await db.sql`
        SELECT allowance_type, COUNT(*) as count
        FROM allowance_assignments 
        WHERE is_active = TRUE
        GROUP BY allowance_type
        ORDER BY count DESC
        LIMIT 1
      `;
      
      // Get average allowance per employee
      const employeeCount = await db.sql`
        SELECT COUNT(DISTINCT user_id) as count 
        FROM allowance_assignments 
        WHERE is_active = TRUE
      `;
      
      const totalEmployees = employeeCount[0]?.count || 1;
      const averageAllowance = totalAmount[0]?.total / totalEmployees || 0;
      
      return {
        totalAllowanceTypes: allowanceTypes[0]?.count || 0,
        totalActiveAssignments: activeAssignments[0]?.count || 0,
        totalAllowanceAmount: totalAmount[0]?.total || 0,
        mostUsedAllowanceType: mostUsed[0]?.allowance_type || 'None',
        averageAllowancePerEmployee: averageAllowance
      };
      
    } catch (error) {
      console.error('Error getting allowance statistics:', error);
      throw new Error('Failed to get allowance statistics');
    }
  }
}

export const allowancesManager = new AllowancesManager();
