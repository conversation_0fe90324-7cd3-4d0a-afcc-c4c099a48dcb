import { type NextRequest, NextResponse } from "next/server"
import { AuthService } from "@/lib/auth-utils"

export async function POST(request: NextRequest) {
  try {
    const userData = await request.json()

    const result = await AuthService.register(userData)

    if (result.success) {
      return NextResponse.json({
        success: true,
        user: result.user,
      })
    } else {
      return NextResponse.json({ success: false, error: result.error }, { status: 400 })
    }
  } catch (error) {
    console.error("Register API error:", error)
    return NextResponse.json({ success: false, error: "Internal server error" }, { status: 500 })
  }
}
