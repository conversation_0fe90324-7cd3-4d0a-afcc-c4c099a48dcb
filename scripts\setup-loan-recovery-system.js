#!/usr/bin/env node

/**
 * Loan Recovery System Setup Script
 * This script creates the database schema and populates sample data
 */

import { neon } from '@neondatabase/serverless';
import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Load environment variables
import dotenv from 'dotenv';
dotenv.config({ path: '.env.local' });

if (!process.env.DATABASE_URL) {
  console.error('❌ DATABASE_URL environment variable is required');
  process.exit(1);
}

const sql = neon(process.env.DATABASE_URL);

async function setupLoanRecoverySystem() {
  try {
    console.log('🚀 Setting up Loan Recovery Management System...\n');

    // Step 1: Create the schema
    console.log('📋 Creating database schema...');
    const schemaSQL = fs.readFileSync(
      path.join(__dirname, 'create-loan-recovery-schema.sql'),
      'utf8'
    );
    
    await sql(schemaSQL);
    console.log('✅ Database schema created successfully\n');

    // Step 2: Get admin user for sample data
    console.log('👤 Finding admin user for sample data...');
    const adminUsers = await sql`
      SELECT id, full_name FROM users 
      WHERE role = 'admin' 
      ORDER BY created_at 
      LIMIT 1
    `;

    if (adminUsers.length === 0) {
      console.log('⚠️  No admin user found. Creating sample admin user...');
      const newAdmin = await sql`
        INSERT INTO users (email, password_hash, full_name, role, is_active)
        VALUES ('<EMAIL>', '$2b$10$dummy.hash.for.demo', 'System Admin', 'admin', true)
        RETURNING id, full_name
      `;
      adminUser = newAdmin[0];
    } else {
      adminUser = adminUsers[0];
    }

    console.log(`✅ Using admin user: ${adminUser.full_name} (${adminUser.id})\n`);

    // Step 3: Create sample customers
    console.log('👥 Creating sample customers...');
    const customers = [
      {
        name: 'राम बहादुर शेर्पा',
        phone: '9841234567',
        email: '<EMAIL>',
        address: 'काठमाडौं, नेपाल'
      },
      {
        name: 'सीता देवी पौडेल',
        phone: '9851234567',
        email: '<EMAIL>',
        address: 'पोखरा, नेपाल'
      },
      {
        name: 'हरि यादव',
        phone: '9861234567',
        email: '<EMAIL>',
        address: 'जनकपुर, नेपाल'
      },
      {
        name: 'गीता तामाङ',
        phone: '9871234567',
        email: '<EMAIL>',
        address: 'धरान, नेपाल'
      },
      {
        name: 'सुरेश गुरुङ',
        phone: '9881234567',
        email: '<EMAIL>',
        address: 'बुटवल, नेपाल'
      },
      {
        name: 'कमला खत्री',
        phone: '9891234567',
        email: '<EMAIL>',
        address: 'भैरहवा, नेपाल'
      }
    ];

    const createdCustomers = [];
    for (const customer of customers) {
      const result = await sql`
        INSERT INTO loan_recovery_customers (name, phone, email, address, created_by)
        VALUES (${customer.name}, ${customer.phone}, ${customer.email}, ${customer.address}, ${adminUser.id})
        RETURNING id, name
      `;
      createdCustomers.push(result[0]);
      console.log(`   ✓ Created customer: ${result[0].name}`);
    }

    console.log(`✅ Created ${createdCustomers.length} sample customers\n`);

    // Step 4: Create sample loan records with different stages
    console.log('💰 Creating sample loan records...');
    const loanData = [
      {
        customer_idx: 0,
        loan_amount: 350000.00,
        amount_paid: 50000.00,
        due_date: '2024-06-15',
        due_date_bs: '2081-03-01',
        current_stage: 'early',
        stage_order: 1
      },
      {
        customer_idx: 1,
        loan_amount: 49800.00,
        amount_paid: 200.00,
        due_date: '2025-07-09',
        due_date_bs: '2082-03-25',
        current_stage: 'assertive',
        stage_order: 1
      },
      {
        customer_idx: 2,
        loan_amount: 800000.00,
        amount_paid: 0.00,
        due_date: '2023-12-20',
        due_date_bs: '2080-09-05',
        current_stage: 'escalation',
        stage_order: 1
      },
      {
        customer_idx: 3,
        loan_amount: 650000.00,
        amount_paid: 0.00,
        due_date: '2023-10-15',
        due_date_bs: '2080-06-30',
        current_stage: 'legal_recovery',
        stage_order: 1
      },
      {
        customer_idx: 4,
        loan_amount: 250000.00,
        amount_paid: 50000.00,
        due_date: '2024-08-20',
        due_date_bs: '2081-05-05',
        current_stage: 'early',
        stage_order: 2
      },
      {
        customer_idx: 5,
        loan_amount: 650000.00,
        amount_paid: 0.00,
        due_date: '2024-05-15',
        due_date_bs: '2081-02-01',
        current_stage: 'escalation',
        stage_order: 2
      }
    ];

    const createdLoans = [];
    for (const loan of loanData) {
      const customer = createdCustomers[loan.customer_idx];
      const result = await sql`
        INSERT INTO loan_records (
          customer_id, loan_amount, amount_paid, due_date, due_date_bs, 
          current_stage, stage_order, created_by, updated_by
        )
        VALUES (
          ${customer.id}, ${loan.loan_amount}, ${loan.amount_paid}, 
          ${loan.due_date}, ${loan.due_date_bs}, ${loan.current_stage}, 
          ${loan.stage_order}, ${adminUser.id}, ${adminUser.id}
        )
        RETURNING id, loan_amount, current_stage
      `;
      createdLoans.push(result[0]);
      console.log(`   ✓ Created ${loan.current_stage} loan: Rs. ${loan.loan_amount.toLocaleString()}`);
    }

    console.log(`✅ Created ${createdLoans.length} sample loan records\n`);

    // Step 5: Add sample conversation notes
    console.log('📝 Adding sample conversation notes...');
    const sampleNotes = [
      {
        loan_idx: 1,
        note_type: 'call',
        content: 'Customer को साथ फोन कुराकानी। भुक्तानी गर्ने वाचा गरे।'
      },
      {
        loan_idx: 2,
        note_type: 'meeting',
        content: 'आइतबार भेट्ने'
      },
      {
        loan_idx: 0,
        note_type: 'general',
        content: 'First contact made. Customer acknowledged debt.'
      }
    ];

    for (const note of sampleNotes) {
      const loan = createdLoans[note.loan_idx];
      await sql`
        INSERT INTO loan_conversation_notes (loan_id, note_type, content, created_by)
        VALUES (${loan.id}, ${note.note_type}, ${note.content}, ${adminUser.id})
      `;
      console.log(`   ✓ Added ${note.note_type} note for loan ${loan.id.substring(0, 8)}...`);
    }

    console.log('✅ Added sample conversation notes\n');

    // Step 6: Add sample reminders
    console.log('⏰ Adding sample reminders...');
    const sampleReminders = [
      {
        loan_idx: 2,
        title: 'Call on Sunday for meeting.',
        reminder_date: '2025-07-27',
        reminder_date_bs: '2082-04-12'
      },
      {
        loan_idx: 1,
        title: 'Follow up on payment promise',
        reminder_date: '2025-07-30',
        reminder_date_bs: '2082-04-15'
      }
    ];

    for (const reminder of sampleReminders) {
      const loan = createdLoans[reminder.loan_idx];
      await sql`
        INSERT INTO loan_reminders (
          loan_id, title, reminder_date, reminder_date_bs, created_by
        )
        VALUES (
          ${loan.id}, ${reminder.title}, ${reminder.reminder_date}, 
          ${reminder.reminder_date_bs}, ${adminUser.id}
        )
      `;
      console.log(`   ✓ Added reminder: ${reminder.title}`);
    }

    console.log('✅ Added sample reminders\n');

    // Step 7: Verify the setup
    console.log('🔍 Verifying setup...');
    const stats = await sql`
      SELECT 
        (SELECT COUNT(*) FROM loan_recovery_customers) as customers,
        (SELECT COUNT(*) FROM loan_records) as loans,
        (SELECT COUNT(*) FROM loan_conversation_notes) as notes,
        (SELECT COUNT(*) FROM loan_reminders) as reminders,
        (SELECT COUNT(*) FROM loan_stage_transitions) as transitions
    `;

    console.log('📊 System Statistics:');
    console.log(`   • Customers: ${stats[0].customers}`);
    console.log(`   • Loan Records: ${stats[0].loans}`);
    console.log(`   • Conversation Notes: ${stats[0].notes}`);
    console.log(`   • Reminders: ${stats[0].reminders}`);
    console.log(`   • Stage Transitions: ${stats[0].transitions}`);

    console.log('\n🎉 Loan Recovery Management System setup completed successfully!');
    console.log('\n📋 Next Steps:');
    console.log('   1. Run the application: npm run dev');
    console.log('   2. Navigate to /recovery-flow (once implemented)');
    console.log('   3. Test the Kanban board functionality');

  } catch (error) {
    console.error('❌ Setup failed:', error);
    process.exit(1);
  }
}

// Run the setup
setupLoanRecoverySystem();
