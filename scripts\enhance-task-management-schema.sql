-- Task Management System Database Schema Enhancement
-- This script enhances the existing tasks table with additional features
-- for a comprehensive task management system

-- First, ensure the existing tasks table has all required columns
-- (This is safe to run multiple times)

-- Add missing columns to tasks table if they don't exist
DO $$ 
BEGIN
    -- Add project_id column if it doesn't exist
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns 
                   WHERE table_name = 'tasks' AND column_name = 'project_id') THEN
        ALTER TABLE tasks ADD COLUMN project_id UUID REFERENCES task_projects(id);
    END IF;
    
    -- Add position column for kanban ordering
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns 
                   WHERE table_name = 'tasks' AND column_name = 'position') THEN
        ALTER TABLE tasks ADD COLUMN position INTEGER DEFAULT 0;
    END IF;
    
    -- Add estimated_hours column
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns 
                   WHERE table_name = 'tasks' AND column_name = 'estimated_hours') THEN
        ALTER TABLE tasks ADD COLUMN estimated_hours DECIMAL(5,2);
    END IF;
    
    -- Add actual_hours column
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns 
                   WHERE table_name = 'tasks' AND column_name = 'actual_hours') THEN
        ALTER TABLE tasks ADD COLUMN actual_hours DECIMAL(5,2) DEFAULT 0;
    END IF;
END $$;

-- Create task_projects table for organizing tasks
CREATE TABLE IF NOT EXISTS task_projects (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name VARCHAR(255) NOT NULL,
    description TEXT,
    color VARCHAR(7) DEFAULT '#3B82F6', -- Default blue color
    created_by UUID NOT NULL REFERENCES users(id),
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    CONSTRAINT task_projects_name_unique UNIQUE(name, created_by)
);

-- Create task_comments table for collaboration
CREATE TABLE IF NOT EXISTS task_comments (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    task_id UUID NOT NULL REFERENCES tasks(id) ON DELETE CASCADE,
    user_id UUID NOT NULL REFERENCES users(id),
    content TEXT NOT NULL,
    is_edited BOOLEAN DEFAULT false,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create task_attachments table for file management
CREATE TABLE IF NOT EXISTS task_attachments (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    task_id UUID NOT NULL REFERENCES tasks(id) ON DELETE CASCADE,
    filename VARCHAR(255) NOT NULL,
    original_filename VARCHAR(255) NOT NULL,
    file_path TEXT NOT NULL,
    file_size INTEGER NOT NULL,
    mime_type VARCHAR(100) NOT NULL,
    uploaded_by UUID NOT NULL REFERENCES users(id),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create task_time_logs table for time tracking
CREATE TABLE IF NOT EXISTS task_time_logs (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    task_id UUID NOT NULL REFERENCES tasks(id) ON DELETE CASCADE,
    user_id UUID NOT NULL REFERENCES users(id),
    start_time TIMESTAMP WITH TIME ZONE NOT NULL,
    end_time TIMESTAMP WITH TIME ZONE,
    duration_minutes INTEGER,
    description TEXT,
    is_active BOOLEAN DEFAULT false, -- For currently running timers
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create task_dependencies table for task relationships
CREATE TABLE IF NOT EXISTS task_dependencies (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    task_id UUID NOT NULL REFERENCES tasks(id) ON DELETE CASCADE,
    depends_on_task_id UUID NOT NULL REFERENCES tasks(id) ON DELETE CASCADE,
    dependency_type VARCHAR(20) DEFAULT 'blocks' CHECK (dependency_type IN ('blocks', 'relates_to')),
    created_by UUID NOT NULL REFERENCES users(id),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    CONSTRAINT task_dependencies_unique UNIQUE(task_id, depends_on_task_id),
    CONSTRAINT task_dependencies_no_self_reference CHECK (task_id != depends_on_task_id)
);

-- Create task_activity_logs table for audit trail
CREATE TABLE IF NOT EXISTS task_activity_logs (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    task_id UUID NOT NULL REFERENCES tasks(id) ON DELETE CASCADE,
    user_id UUID NOT NULL REFERENCES users(id),
    action VARCHAR(50) NOT NULL, -- 'created', 'updated', 'status_changed', 'assigned', etc.
    field_name VARCHAR(100), -- Which field was changed
    old_value TEXT, -- Previous value
    new_value TEXT, -- New value
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create custom_kanban_columns table for flexible workflows
CREATE TABLE IF NOT EXISTS custom_kanban_columns (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name VARCHAR(100) NOT NULL,
    status_value VARCHAR(50) NOT NULL, -- Maps to task.status
    position INTEGER NOT NULL,
    color VARCHAR(7) DEFAULT '#6B7280',
    is_default BOOLEAN DEFAULT false,
    created_by UUID NOT NULL REFERENCES users(id),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    CONSTRAINT custom_kanban_columns_unique UNIQUE(status_value, created_by)
);

-- Create indexes for performance optimization
CREATE INDEX IF NOT EXISTS idx_tasks_status ON tasks(status);
CREATE INDEX IF NOT EXISTS idx_tasks_assigned_to ON tasks(assigned_to);
CREATE INDEX IF NOT EXISTS idx_tasks_assigned_by ON tasks(assigned_by);
CREATE INDEX IF NOT EXISTS idx_tasks_project_id ON tasks(project_id);
CREATE INDEX IF NOT EXISTS idx_tasks_due_date ON tasks(due_date);
CREATE INDEX IF NOT EXISTS idx_tasks_created_at ON tasks(created_at);
CREATE INDEX IF NOT EXISTS idx_tasks_position ON tasks(status, position);

CREATE INDEX IF NOT EXISTS idx_task_comments_task_id ON task_comments(task_id);
CREATE INDEX IF NOT EXISTS idx_task_comments_created_at ON task_comments(created_at);

CREATE INDEX IF NOT EXISTS idx_task_attachments_task_id ON task_attachments(task_id);

CREATE INDEX IF NOT EXISTS idx_task_time_logs_task_id ON task_time_logs(task_id);
CREATE INDEX IF NOT EXISTS idx_task_time_logs_user_id ON task_time_logs(user_id);
CREATE INDEX IF NOT EXISTS idx_task_time_logs_active ON task_time_logs(is_active) WHERE is_active = true;

CREATE INDEX IF NOT EXISTS idx_task_dependencies_task_id ON task_dependencies(task_id);
CREATE INDEX IF NOT EXISTS idx_task_dependencies_depends_on ON task_dependencies(depends_on_task_id);

CREATE INDEX IF NOT EXISTS idx_task_activity_logs_task_id ON task_activity_logs(task_id);
CREATE INDEX IF NOT EXISTS idx_task_activity_logs_created_at ON task_activity_logs(created_at);

-- Create full-text search index for tasks
CREATE INDEX IF NOT EXISTS idx_tasks_search ON tasks USING gin(to_tsvector('english', title || ' ' || COALESCE(description, '')));

-- Create triggers for automatic timestamp updates
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Apply triggers to relevant tables
DROP TRIGGER IF EXISTS update_tasks_updated_at ON tasks;
CREATE TRIGGER update_tasks_updated_at 
    BEFORE UPDATE ON tasks 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

DROP TRIGGER IF EXISTS update_task_projects_updated_at ON task_projects;
CREATE TRIGGER update_task_projects_updated_at 
    BEFORE UPDATE ON task_projects 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

DROP TRIGGER IF EXISTS update_task_comments_updated_at ON task_comments;
CREATE TRIGGER update_task_comments_updated_at 
    BEFORE UPDATE ON task_comments 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

DROP TRIGGER IF EXISTS update_task_time_logs_updated_at ON task_time_logs;
CREATE TRIGGER update_task_time_logs_updated_at 
    BEFORE UPDATE ON task_time_logs 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Create function to automatically calculate duration for time logs
CREATE OR REPLACE FUNCTION calculate_time_log_duration()
RETURNS TRIGGER AS $$
BEGIN
    IF NEW.end_time IS NOT NULL AND NEW.start_time IS NOT NULL THEN
        NEW.duration_minutes = EXTRACT(EPOCH FROM (NEW.end_time - NEW.start_time)) / 60;
        NEW.is_active = false;
    END IF;
    RETURN NEW;
END;
$$ language 'plpgsql';

DROP TRIGGER IF EXISTS calculate_duration_trigger ON task_time_logs;
CREATE TRIGGER calculate_duration_trigger 
    BEFORE INSERT OR UPDATE ON task_time_logs 
    FOR EACH ROW EXECUTE FUNCTION calculate_time_log_duration();

-- Create function to log task activities automatically
CREATE OR REPLACE FUNCTION log_task_activity()
RETURNS TRIGGER AS $$
BEGIN
    -- Log task creation
    IF TG_OP = 'INSERT' THEN
        INSERT INTO task_activity_logs (task_id, user_id, action, new_value)
        VALUES (NEW.id, NEW.assigned_by, 'created', NEW.title);
        RETURN NEW;
    END IF;
    
    -- Log task updates
    IF TG_OP = 'UPDATE' THEN
        -- Log status changes
        IF OLD.status != NEW.status THEN
            INSERT INTO task_activity_logs (task_id, user_id, action, field_name, old_value, new_value)
            VALUES (NEW.id, NEW.assigned_by, 'status_changed', 'status', OLD.status, NEW.status);
        END IF;
        
        -- Log assignment changes
        IF OLD.assigned_to IS DISTINCT FROM NEW.assigned_to THEN
            INSERT INTO task_activity_logs (task_id, user_id, action, field_name, old_value, new_value)
            VALUES (NEW.id, NEW.assigned_by, 'assigned', 'assigned_to', 
                   COALESCE(OLD.assigned_to::text, 'unassigned'), 
                   COALESCE(NEW.assigned_to::text, 'unassigned'));
        END IF;
        
        -- Log priority changes
        IF OLD.priority != NEW.priority THEN
            INSERT INTO task_activity_logs (task_id, user_id, action, field_name, old_value, new_value)
            VALUES (NEW.id, NEW.assigned_by, 'priority_changed', 'priority', OLD.priority, NEW.priority);
        END IF;
        
        RETURN NEW;
    END IF;
    
    RETURN NULL;
END;
$$ language 'plpgsql';

DROP TRIGGER IF EXISTS log_task_activity_trigger ON tasks;
CREATE TRIGGER log_task_activity_trigger 
    AFTER INSERT OR UPDATE ON tasks 
    FOR EACH ROW EXECUTE FUNCTION log_task_activity();

-- Insert default kanban columns
INSERT INTO custom_kanban_columns (name, status_value, position, color, is_default, created_by)
SELECT 'To Do', 'todo', 1, '#6B7280', true, id FROM users WHERE role = 'admin' LIMIT 1
ON CONFLICT (status_value, created_by) DO NOTHING;

INSERT INTO custom_kanban_columns (name, status_value, position, color, is_default, created_by)
SELECT 'In Progress', 'in_progress', 2, '#F59E0B', true, id FROM users WHERE role = 'admin' LIMIT 1
ON CONFLICT (status_value, created_by) DO NOTHING;

INSERT INTO custom_kanban_columns (name, status_value, position, color, is_default, created_by)
SELECT 'Done', 'completed', 3, '#10B981', true, id FROM users WHERE role = 'admin' LIMIT 1
ON CONFLICT (status_value, created_by) DO NOTHING;

-- Create RLS policies for new tables
ALTER TABLE task_projects ENABLE ROW LEVEL SECURITY;
ALTER TABLE task_comments ENABLE ROW LEVEL SECURITY;
ALTER TABLE task_attachments ENABLE ROW LEVEL SECURITY;
ALTER TABLE task_time_logs ENABLE ROW LEVEL SECURITY;
ALTER TABLE task_dependencies ENABLE ROW LEVEL SECURITY;
ALTER TABLE task_activity_logs ENABLE ROW LEVEL SECURITY;
ALTER TABLE custom_kanban_columns ENABLE ROW LEVEL SECURITY;

-- RLS policies for task_projects
CREATE POLICY "Users can view projects they created or are assigned to" ON task_projects
    FOR SELECT USING (
        created_by = auth.uid() OR
        EXISTS (
            SELECT 1 FROM tasks 
            WHERE tasks.project_id = task_projects.id 
            AND (tasks.assigned_to = auth.uid() OR tasks.assigned_by = auth.uid())
        ) OR
        EXISTS (
            SELECT 1 FROM users 
            WHERE id = auth.uid() 
            AND role IN ('admin', 'manager')
        )
    );

CREATE POLICY "Users can manage their own projects" ON task_projects
    FOR ALL USING (
        created_by = auth.uid() OR
        EXISTS (
            SELECT 1 FROM users 
            WHERE id = auth.uid() 
            AND role IN ('admin', 'manager')
        )
    );

-- RLS policies for task_comments
CREATE POLICY "Users can view comments on tasks they can access" ON task_comments
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM tasks 
            WHERE tasks.id = task_comments.task_id 
            AND (tasks.assigned_to = auth.uid() OR tasks.assigned_by = auth.uid())
        ) OR
        EXISTS (
            SELECT 1 FROM users 
            WHERE id = auth.uid() 
            AND role IN ('admin', 'manager')
        )
    );

CREATE POLICY "Users can manage comments on accessible tasks" ON task_comments
    FOR ALL USING (
        user_id = auth.uid() OR
        EXISTS (
            SELECT 1 FROM tasks 
            WHERE tasks.id = task_comments.task_id 
            AND (tasks.assigned_to = auth.uid() OR tasks.assigned_by = auth.uid())
        ) OR
        EXISTS (
            SELECT 1 FROM users 
            WHERE id = auth.uid() 
            AND role IN ('admin', 'manager')
        )
    );

-- Similar policies for other tables...
-- (Additional RLS policies would be added here for completeness)

COMMIT;
