// Overtime Calculation Engine with Nepal Labor Law Compliance
// Phase 2: Core Payroll Engine Development - Overtime Calculations

import { nepalConfig, NepalLaborLawConfig } from './nepal-config';
import { NepaliCalendar } from './nepali-calendar';

export interface OvertimeCalculationInput {
  userId: string;
  date: string;
  totalHoursWorked: number;
  regularHours: number;
  sessionBreakdown: OvertimeSession[];
  isHoliday: boolean;
  isWeeklyOff: boolean;
  payStructure: {
    type: 'hourly' | 'daily' | 'monthly' | 'project_based';
    hourlyRate: number;
    dailyRate: number;
    baseSalary: number;
    overtimeMultiplier: number;
  };
  employeeCategory: 'regular' | 'contract' | 'probation' | 'intern';
  overtimePreApproved: boolean;
}

export interface OvertimeSession {
  startTime: string;
  endTime: string;
  duration: number;
  sessionType: 'regular' | 'overtime' | 'holiday' | 'emergency';
  isPreApproved: boolean;
  approvedBy?: string;
  reason?: string;
}

export interface OvertimeCalculationResult {
  userId: string;
  date: string;
  bsDate: string;
  
  // Time breakdown
  totalHoursWorked: number;
  regularHours: number;
  overtimeHours: number;
  holidayHours: number;
  weeklyOffHours: number;
  emergencyOvertimeHours: number;
  
  // Rate calculations
  regularHourlyRate: number;
  overtimeHourlyRate: number;
  holidayHourlyRate: number;
  weeklyOffHourlyRate: number;
  emergencyOvertimeRate: number;
  
  // Pay calculations
  regularPay: number;
  overtimePay: number;
  holidayPay: number;
  weeklyOffPay: number;
  emergencyOvertimePay: number;
  totalOvertimePay: number;
  
  // Compliance checks
  isCompliant: boolean;
  complianceIssues: string[];
  maxOvertimeExceeded: boolean;
  requiresApproval: boolean;
  
  // Nepal-specific
  laborLawCompliance: NepalLaborLawCompliance;
  calculationMethod: string;
  calculatedAt: string;
}

export interface NepalLaborLawCompliance {
  maxDailyHoursCompliant: boolean;
  maxWeeklyHoursCompliant: boolean;
  maxMonthlyOvertimeCompliant: boolean;
  restPeriodCompliant: boolean;
  holidayWorkCompliant: boolean;
  femaleWorkerCompliant: boolean;
  minorWorkerCompliant: boolean;
  complianceNotes: string[];
}

export class OvertimeCalculator {
  private nepalConfig = nepalConfig;
  private laborLaw: NepalLaborLawConfig;

  constructor() {
    this.laborLaw = this.nepalConfig.getLaborLawConfig();
  }

  /**
   * Calculate overtime pay according to Nepal Labor Law
   */
  async calculateOvertime(input: OvertimeCalculationInput): Promise<OvertimeCalculationResult> {
    try {
      // Validate input
      this.validateInput(input);

      // Get base hourly rate
      const regularHourlyRate = this.calculateRegularHourlyRate(input.payStructure);

      // Calculate time breakdown
      const timeBreakdown = this.calculateTimeBreakdown(input);

      // Calculate rates for different types of overtime
      const rates = this.calculateOvertimeRates(regularHourlyRate, input);

      // Calculate pay amounts
      const payCalculations = this.calculateOvertimePay(timeBreakdown, rates);

      // Check compliance with Nepal Labor Law
      const compliance = await this.checkLaborLawCompliance(input, timeBreakdown);

      // Get BS date
      const bsDate = NepaliCalendar.formatBSDate(NepaliCalendar.adToBS(input.date));

      return {
        userId: input.userId,
        date: input.date,
        bsDate,
        
        // Time breakdown
        totalHoursWorked: timeBreakdown.totalHours,
        regularHours: timeBreakdown.regularHours,
        overtimeHours: timeBreakdown.overtimeHours,
        holidayHours: timeBreakdown.holidayHours,
        weeklyOffHours: timeBreakdown.weeklyOffHours,
        emergencyOvertimeHours: timeBreakdown.emergencyOvertimeHours,
        
        // Rates
        regularHourlyRate,
        overtimeHourlyRate: rates.overtimeRate,
        holidayHourlyRate: rates.holidayRate,
        weeklyOffHourlyRate: rates.weeklyOffRate,
        emergencyOvertimeRate: rates.emergencyRate,
        
        // Pay calculations
        regularPay: payCalculations.regularPay,
        overtimePay: payCalculations.overtimePay,
        holidayPay: payCalculations.holidayPay,
        weeklyOffPay: payCalculations.weeklyOffPay,
        emergencyOvertimePay: payCalculations.emergencyOvertimePay,
        totalOvertimePay: payCalculations.totalOvertimePay,
        
        // Compliance
        isCompliant: compliance.isCompliant,
        complianceIssues: compliance.issues,
        maxOvertimeExceeded: compliance.maxOvertimeExceeded,
        requiresApproval: compliance.requiresApproval,
        
        laborLawCompliance: compliance.laborLawCompliance,
        calculationMethod: 'nepal_labor_law_2074',
        calculatedAt: new Date().toISOString()
      };
    } catch (error) {
      console.error('Error calculating overtime:', error);
      throw new Error(`Overtime calculation failed: ${error.message}`);
    }
  }

  /**
   * Calculate regular hourly rate from pay structure
   */
  private calculateRegularHourlyRate(payStructure: any): number {
    switch (payStructure.type) {
      case 'hourly':
        return payStructure.hourlyRate;
      case 'daily':
        return payStructure.dailyRate / this.laborLaw.standardWorkingHours;
      case 'monthly':
        // 26 working days * 8 hours = 208 hours per month
        return payStructure.baseSalary / 208;
      case 'project_based':
        // Estimate based on monthly equivalent
        return payStructure.baseSalary / 208;
      default:
        throw new Error('Invalid pay structure type');
    }
  }

  /**
   * Calculate time breakdown for different types of work
   */
  private calculateTimeBreakdown(input: OvertimeCalculationInput): {
    totalHours: number;
    regularHours: number;
    overtimeHours: number;
    holidayHours: number;
    weeklyOffHours: number;
    emergencyOvertimeHours: number;
  } {
    const standardHours = this.laborLaw.standardWorkingHours;
    let regularHours = 0;
    let overtimeHours = 0;
    let holidayHours = 0;
    let weeklyOffHours = 0;
    let emergencyOvertimeHours = 0;

    if (input.isHoliday) {
      // All hours on holidays are considered holiday hours
      holidayHours = input.totalHoursWorked;
    } else if (input.isWeeklyOff) {
      // All hours on weekly off are considered weekly off hours
      weeklyOffHours = input.totalHoursWorked;
    } else {
      // Regular working day
      regularHours = Math.min(input.totalHoursWorked, standardHours);
      overtimeHours = Math.max(0, input.totalHoursWorked - standardHours);

      // Check for emergency overtime
      const emergencySessions = input.sessionBreakdown.filter(s => s.sessionType === 'emergency');
      emergencyOvertimeHours = emergencySessions.reduce((sum, s) => sum + s.duration, 0);
      
      // Adjust regular overtime if there's emergency overtime
      overtimeHours = Math.max(0, overtimeHours - emergencyOvertimeHours);
    }

    return {
      totalHours: input.totalHoursWorked,
      regularHours,
      overtimeHours,
      holidayHours,
      weeklyOffHours,
      emergencyOvertimeHours
    };
  }

  /**
   * Calculate overtime rates according to Nepal Labor Law
   */
  private calculateOvertimeRates(regularRate: number, input: OvertimeCalculationInput): {
    overtimeRate: number;
    holidayRate: number;
    weeklyOffRate: number;
    emergencyRate: number;
  } {
    // Nepal Labor Law overtime rates
    const overtimeMultiplier = input.payStructure.overtimeMultiplier || 1.5; // 1.5x for regular overtime
    const holidayMultiplier = 2.0; // 2x for holiday work
    const weeklyOffMultiplier = 2.0; // 2x for weekly off work
    const emergencyMultiplier = 2.5; // 2.5x for emergency overtime

    return {
      overtimeRate: regularRate * overtimeMultiplier,
      holidayRate: regularRate * holidayMultiplier,
      weeklyOffRate: regularRate * weeklyOffMultiplier,
      emergencyRate: regularRate * emergencyMultiplier
    };
  }

  /**
   * Calculate overtime pay amounts
   */
  private calculateOvertimePay(timeBreakdown: any, rates: any): {
    regularPay: number;
    overtimePay: number;
    holidayPay: number;
    weeklyOffPay: number;
    emergencyOvertimePay: number;
    totalOvertimePay: number;
  } {
    const regularPay = 0; // Regular pay is calculated separately
    const overtimePay = timeBreakdown.overtimeHours * rates.overtimeRate;
    const holidayPay = timeBreakdown.holidayHours * rates.holidayRate;
    const weeklyOffPay = timeBreakdown.weeklyOffHours * rates.weeklyOffRate;
    const emergencyOvertimePay = timeBreakdown.emergencyOvertimeHours * rates.emergencyRate;

    const totalOvertimePay = overtimePay + holidayPay + weeklyOffPay + emergencyOvertimePay;

    return {
      regularPay,
      overtimePay: Math.round(overtimePay * 100) / 100,
      holidayPay: Math.round(holidayPay * 100) / 100,
      weeklyOffPay: Math.round(weeklyOffPay * 100) / 100,
      emergencyOvertimePay: Math.round(emergencyOvertimePay * 100) / 100,
      totalOvertimePay: Math.round(totalOvertimePay * 100) / 100
    };
  }

  /**
   * Check compliance with Nepal Labor Law
   */
  private async checkLaborLawCompliance(input: OvertimeCalculationInput, timeBreakdown: any): Promise<{
    isCompliant: boolean;
    issues: string[];
    maxOvertimeExceeded: boolean;
    requiresApproval: boolean;
    laborLawCompliance: NepalLaborLawCompliance;
  }> {
    const issues: string[] = [];
    const complianceNotes: string[] = [];
    let isCompliant = true;
    let maxOvertimeExceeded = false;
    let requiresApproval = false;

    // Check maximum daily working hours (12 hours including overtime)
    const maxDailyHours = this.laborLaw.maxWorkingHoursPerDay;
    const maxDailyHoursCompliant = timeBreakdown.totalHours <= maxDailyHours;
    if (!maxDailyHoursCompliant) {
      issues.push(`Daily working hours (${timeBreakdown.totalHours}) exceed maximum allowed (${maxDailyHours})`);
      isCompliant = false;
      maxOvertimeExceeded = true;
    }

    // Check maximum overtime hours per day (4 hours)
    const maxOvertimeHours = this.laborLaw.maxOvertimeHoursPerDay;
    const totalOvertimeHours = timeBreakdown.overtimeHours + timeBreakdown.emergencyOvertimeHours;
    if (totalOvertimeHours > maxOvertimeHours) {
      issues.push(`Overtime hours (${totalOvertimeHours}) exceed maximum allowed (${maxOvertimeHours})`);
      isCompliant = false;
      maxOvertimeExceeded = true;
    }

    // Check if overtime requires pre-approval
    if (totalOvertimeHours > 2 && !input.overtimePreApproved) {
      issues.push('Overtime exceeding 2 hours requires pre-approval');
      requiresApproval = true;
    }

    // Check rest period compliance (minimum 8 hours between shifts)
    // This would require checking previous day's end time
    complianceNotes.push('Rest period compliance requires previous shift data');

    // Holiday work compliance
    const holidayWorkCompliant = !input.isHoliday || input.overtimePreApproved;
    if (input.isHoliday && !input.overtimePreApproved) {
      issues.push('Holiday work requires pre-approval');
      requiresApproval = true;
    }

    // Employee category specific checks
    if (input.employeeCategory === 'probation' && totalOvertimeHours > 2) {
      issues.push('Probation employees limited to 2 hours overtime per day');
      isCompliant = false;
    }

    if (input.employeeCategory === 'intern' && totalOvertimeHours > 0) {
      issues.push('Interns are not eligible for overtime work');
      isCompliant = false;
    }

    const laborLawCompliance: NepalLaborLawCompliance = {
      maxDailyHoursCompliant,
      maxWeeklyHoursCompliant: true, // Would need weekly data
      maxMonthlyOvertimeCompliant: true, // Would need monthly data
      restPeriodCompliant: true, // Would need previous shift data
      holidayWorkCompliant,
      femaleWorkerCompliant: true, // Would need gender and time restrictions
      minorWorkerCompliant: true, // Would need age verification
      complianceNotes
    };

    return {
      isCompliant,
      issues,
      maxOvertimeExceeded,
      requiresApproval,
      laborLawCompliance
    };
  }

  /**
   * Get overtime eligibility for employee
   */
  async getOvertimeEligibility(userId: string, date: string): Promise<{
    isEligible: boolean;
    maxOvertimeHours: number;
    requiresApproval: boolean;
    restrictions: string[];
  }> {
    try {
      // Get employee data
      const employee = await this.getEmployeeData(userId);
      
      const restrictions: string[] = [];
      let isEligible = true;
      let maxOvertimeHours = this.laborLaw.maxOvertimeHoursPerDay;
      let requiresApproval = false;

      // Check employee category restrictions
      switch (employee.category) {
        case 'intern':
          isEligible = false;
          restrictions.push('Interns are not eligible for overtime');
          break;
        case 'probation':
          maxOvertimeHours = Math.min(maxOvertimeHours, 2);
          restrictions.push('Probation employees limited to 2 hours overtime');
          break;
        case 'contract':
          requiresApproval = true;
          restrictions.push('Contract employees require approval for overtime');
          break;
      }

      // Check if it's a holiday or weekly off
      const isHoliday = this.nepalConfig.isHoliday(date);
      const isWeeklyOff = this.nepalConfig.isWorkingDay(new Date(date));
      
      if (isHoliday || !isWeeklyOff) {
        requiresApproval = true;
        restrictions.push('Holiday/weekly off work requires pre-approval');
      }

      return {
        isEligible,
        maxOvertimeHours,
        requiresApproval,
        restrictions
      };
    } catch (error) {
      console.error('Error checking overtime eligibility:', error);
      return {
        isEligible: false,
        maxOvertimeHours: 0,
        requiresApproval: true,
        restrictions: ['Error checking eligibility']
      };
    }
  }

  /**
   * Validate overtime calculation input
   */
  private validateInput(input: OvertimeCalculationInput): void {
    if (!input.userId) throw new Error('User ID is required');
    if (!input.date) throw new Error('Date is required');
    if (input.totalHoursWorked < 0) throw new Error('Total hours worked cannot be negative');
    if (!input.payStructure) throw new Error('Pay structure is required');
    if (!input.payStructure.type) throw new Error('Pay structure type is required');
  }

  /**
   * Get employee data (placeholder - would integrate with actual employee system)
   */
  private async getEmployeeData(userId: string): Promise<any> {
    // This would integrate with the actual employee data system
    return {
      id: userId,
      category: 'regular',
      gender: 'male',
      age: 25,
      department: 'IT'
    };
  }
}

// Export singleton instance
export const overtimeCalculator = new OvertimeCalculator();
