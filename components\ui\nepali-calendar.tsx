// Nepali Calendar UI Component
// Phase 3: Nepal Localization Implementation - Calendar Integration

"use client"

import React, { useState, useEffect } from 'react'
import { Calendar, ChevronLeft, ChevronRight, CalendarDays } from 'lucide-react'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { NepaliCalendar, BSDate } from '@/lib/nepali-calendar'
import { nepalConfig } from '@/lib/nepal-config'
import { cn } from '@/lib/utils'

interface NepaliCalendarProps {
  selectedDate?: Date
  onDateSelect?: (date: Date, bsDate: BSDate) => void
  showDualCalendar?: boolean
  highlightHolidays?: boolean
  highlightWorkingDays?: boolean
  minDate?: Date
  maxDate?: Date
  className?: string
}

interface CalendarDay {
  date: Date
  bsDate: BSDate
  isCurrentMonth: boolean
  isToday: boolean
  isSelected: boolean
  isHoliday: boolean
  isWorkingDay: boolean
  holidayName?: string
  dayOfWeek: number
}

export function NepaliCalendarComponent({
  selectedDate,
  onDateSelect,
  showDualCalendar = true,
  highlightHolidays = true,
  highlightWorkingDays = true,
  minDate,
  maxDate,
  className
}: NepaliCalendarProps) {
  const [currentDate, setCurrentDate] = useState(new Date())
  const [currentBSDate, setCurrentBSDate] = useState<BSDate>(NepaliCalendar.adToBS(new Date()))
  const [calendarDays, setCalendarDays] = useState<CalendarDay[]>([])
  const [viewMode, setViewMode] = useState<'bs' | 'ad'>('bs')

  // Update BS date when current date changes
  useEffect(() => {
    setCurrentBSDate(NepaliCalendar.adToBS(currentDate))
  }, [currentDate])

  // Generate calendar days
  useEffect(() => {
    generateCalendarDays()
  }, [currentDate, selectedDate, highlightHolidays, highlightWorkingDays])

  const generateCalendarDays = () => {
    const days: CalendarDay[] = []
    const year = currentDate.getFullYear()
    const month = currentDate.getMonth()
    
    // Get first day of the month and calculate start of calendar
    const firstDayOfMonth = new Date(year, month, 1)
    const startOfCalendar = new Date(firstDayOfMonth)
    startOfCalendar.setDate(startOfCalendar.getDate() - firstDayOfMonth.getDay())
    
    // Generate 42 days (6 weeks)
    for (let i = 0; i < 42; i++) {
      const date = new Date(startOfCalendar)
      date.setDate(startOfCalendar.getDate() + i)
      
      const bsDate = NepaliCalendar.adToBS(date)
      const isCurrentMonth = date.getMonth() === month
      const isToday = date.toDateString() === new Date().toDateString()
      const isSelected = selectedDate ? date.toDateString() === selectedDate.toDateString() : false
      const isHoliday = highlightHolidays ? nepalConfig.isHoliday(date.toISOString().split('T')[0]) : false
      const isWorkingDay = highlightWorkingDays ? nepalConfig.isWorkingDay(date) : true
      const holidayInfo = nepalConfig.getHolidaysInRange(
        date.toISOString().split('T')[0], 
        date.toISOString().split('T')[0]
      )[0]

      days.push({
        date,
        bsDate,
        isCurrentMonth,
        isToday,
        isSelected,
        isHoliday,
        isWorkingDay,
        holidayName: holidayInfo?.name,
        dayOfWeek: date.getDay()
      })
    }
    
    setCalendarDays(days)
  }

  const navigateMonth = (direction: 'prev' | 'next') => {
    const newDate = new Date(currentDate)
    if (direction === 'prev') {
      newDate.setMonth(newDate.getMonth() - 1)
    } else {
      newDate.setMonth(newDate.getMonth() + 1)
    }
    setCurrentDate(newDate)
  }

  const navigateBSMonth = (direction: 'prev' | 'next') => {
    const newBSDate = { ...currentBSDate }
    if (direction === 'prev') {
      newBSDate.month -= 1
      if (newBSDate.month < 1) {
        newBSDate.month = 12
        newBSDate.year -= 1
      }
    } else {
      newBSDate.month += 1
      if (newBSDate.month > 12) {
        newBSDate.month = 1
        newBSDate.year += 1
      }
    }
    
    // Convert back to AD and update
    const adDate = NepaliCalendar.bsToAD({ ...newBSDate, day: 1 })
    setCurrentDate(adDate)
  }

  const handleDateClick = (day: CalendarDay) => {
    if (minDate && day.date < minDate) return
    if (maxDate && day.date > maxDate) return
    
    onDateSelect?.(day.date, day.bsDate)
  }

  const getDayClassName = (day: CalendarDay) => {
    return cn(
      "h-9 w-9 p-0 font-normal aria-selected:opacity-100 hover:bg-accent hover:text-accent-foreground cursor-pointer transition-colors",
      !day.isCurrentMonth && "text-muted-foreground opacity-50",
      day.isToday && "bg-accent text-accent-foreground font-semibold",
      day.isSelected && "bg-primary text-primary-foreground hover:bg-primary hover:text-primary-foreground",
      day.isHoliday && highlightHolidays && "bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200",
      !day.isWorkingDay && highlightWorkingDays && "bg-gray-100 text-gray-600 dark:bg-gray-800 dark:text-gray-400",
      (minDate && day.date < minDate) || (maxDate && day.date > maxDate) ? "opacity-25 cursor-not-allowed" : ""
    )
  }

  const monthNames = nepalConfig.getConfig().calendar.monthNames
  const monthNamesNepali = nepalConfig.getConfig().calendar.monthNamesNepali
  const dayNames = nepalConfig.getConfig().calendar.dayNames

  return (
    <Card className={cn("w-full max-w-md", className)}>
      <CardHeader className="pb-4">
        <div className="flex items-center justify-between">
          <CardTitle className="text-lg font-semibold flex items-center gap-2">
            <Calendar className="h-5 w-5" />
            {showDualCalendar ? 'Dual Calendar' : 'Nepali Calendar'}
          </CardTitle>
          
          {showDualCalendar && (
            <div className="flex gap-1">
              <Button
                variant={viewMode === 'bs' ? 'default' : 'outline'}
                size="sm"
                onClick={() => setViewMode('bs')}
              >
                BS
              </Button>
              <Button
                variant={viewMode === 'ad' ? 'default' : 'outline'}
                size="sm"
                onClick={() => setViewMode('ad')}
              >
                AD
              </Button>
            </div>
          )}
        </div>
        
        <div className="flex items-center justify-between">
          <Button
            variant="outline"
            size="sm"
            onClick={() => viewMode === 'bs' ? navigateBSMonth('prev') : navigateMonth('prev')}
          >
            <ChevronLeft className="h-4 w-4" />
          </Button>
          
          <div className="text-center">
            {viewMode === 'bs' ? (
              <div>
                <div className="font-semibold">
                  {monthNamesNepali[currentBSDate.month - 1]} {currentBSDate.year}
                </div>
                <div className="text-sm text-muted-foreground">
                  {monthNames[currentBSDate.month - 1]} {currentBSDate.year}
                </div>
              </div>
            ) : (
              <div>
                <div className="font-semibold">
                  {currentDate.toLocaleDateString('en-US', { month: 'long', year: 'numeric' })}
                </div>
                <div className="text-sm text-muted-foreground">
                  {monthNamesNepali[currentBSDate.month - 1]} {currentBSDate.year}
                </div>
              </div>
            )}
          </div>
          
          <Button
            variant="outline"
            size="sm"
            onClick={() => viewMode === 'bs' ? navigateBSMonth('next') : navigateMonth('next')}
          >
            <ChevronRight className="h-4 w-4" />
          </Button>
        </div>
      </CardHeader>
      
      <CardContent className="p-4 pt-0">
        {/* Day headers */}
        <div className="grid grid-cols-7 gap-1 mb-2">
          {dayNames.map((day, index) => (
            <div key={index} className="h-9 w-9 flex items-center justify-center text-sm font-medium text-muted-foreground">
              {day.slice(0, 3)}
            </div>
          ))}
        </div>
        
        {/* Calendar days */}
        <div className="grid grid-cols-7 gap-1">
          {calendarDays.map((day, index) => (
            <div key={index} className="relative">
              <Button
                variant="ghost"
                className={getDayClassName(day)}
                onClick={() => handleDateClick(day)}
                disabled={(minDate && day.date < minDate) || (maxDate && day.date > maxDate)}
              >
                {viewMode === 'bs' ? day.bsDate.day : day.date.getDate()}
              </Button>
              
              {/* Holiday indicator */}
              {day.isHoliday && highlightHolidays && (
                <div className="absolute -top-1 -right-1">
                  <div className="h-2 w-2 bg-red-500 rounded-full"></div>
                </div>
              )}
            </div>
          ))}
        </div>
        
        {/* Legend */}
        {(highlightHolidays || highlightWorkingDays) && (
          <div className="mt-4 pt-4 border-t">
            <div className="flex flex-wrap gap-2 text-xs">
              {highlightHolidays && (
                <div className="flex items-center gap-1">
                  <div className="h-3 w-3 bg-red-100 border border-red-300 rounded"></div>
                  <span>Holiday</span>
                </div>
              )}
              {highlightWorkingDays && (
                <div className="flex items-center gap-1">
                  <div className="h-3 w-3 bg-gray-100 border border-gray-300 rounded"></div>
                  <span>Weekly Off</span>
                </div>
              )}
              <div className="flex items-center gap-1">
                <div className="h-3 w-3 bg-accent border border-accent-foreground rounded"></div>
                <span>Today</span>
              </div>
            </div>
          </div>
        )}
        
        {/* Selected date info */}
        {selectedDate && (
          <div className="mt-4 pt-4 border-t">
            <div className="text-sm">
              <div className="font-medium">Selected Date:</div>
              <div className="text-muted-foreground">
                AD: {selectedDate.toLocaleDateString('en-US', { 
                  weekday: 'long', 
                  year: 'numeric', 
                  month: 'long', 
                  day: 'numeric' 
                })}
              </div>
              <div className="text-muted-foreground">
                BS: {NepaliCalendar.formatBSDate(NepaliCalendar.adToBS(selectedDate))}
              </div>
              
              {/* Holiday info */}
              {(() => {
                const holidayInfo = nepalConfig.getHolidaysInRange(
                  selectedDate.toISOString().split('T')[0],
                  selectedDate.toISOString().split('T')[0]
                )[0]
                
                if (holidayInfo) {
                  return (
                    <Badge variant="destructive" className="mt-1">
                      {holidayInfo.name}
                    </Badge>
                  )
                }
                
                if (!nepalConfig.isWorkingDay(selectedDate)) {
                  return (
                    <Badge variant="secondary" className="mt-1">
                      Weekly Off
                    </Badge>
                  )
                }
                
                return (
                  <Badge variant="default" className="mt-1">
                    Working Day
                  </Badge>
                )
              })()}
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  )
}

export default NepaliCalendarComponent
