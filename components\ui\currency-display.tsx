// NPR Currency Display Components
// Phase 3: Nepal Localization Implementation - Currency Formatting

"use client"

import React from 'react'
import { Badge } from '@/components/ui/badge'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Toolt<PERSON>, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip'
import { Info, TrendingUp, TrendingDown } from 'lucide-react'
import { nprFormatter, CurrencyFormatOptions, CurrencyBreakdown } from '@/lib/currency-formatter'
import { cn } from '@/lib/utils'

interface CurrencyDisplayProps {
  amount: number
  options?: CurrencyFormatOptions
  showBreakdown?: boolean
  showTrend?: boolean
  previousAmount?: number
  className?: string
  size?: 'sm' | 'md' | 'lg'
  variant?: 'default' | 'positive' | 'negative' | 'neutral'
}

interface CurrencyBreakdownProps {
  amount: number
  showInWords?: boolean
  className?: string
}

interface CurrencyCardProps {
  title: string
  amount: number
  subtitle?: string
  trend?: {
    value: number
    period: string
  }
  icon?: React.ReactNode
  variant?: 'default' | 'success' | 'warning' | 'destructive'
  className?: string
}

export function CurrencyDisplay({
  amount,
  options = {},
  showBreakdown = false,
  showTrend = false,
  previousAmount,
  className,
  size = 'md',
  variant = 'default'
}: CurrencyDisplayProps) {
  const defaultOptions: CurrencyFormatOptions = {
    showSymbol: true,
    useIndianNumbering: true,
    decimalPlaces: 2,
    ...options
  }

  const formattedAmount = nprFormatter.formatCurrency(amount, defaultOptions)
  const breakdown = showBreakdown ? nprFormatter.getCurrencyBreakdown(amount) : null

  // Calculate trend
  let trendData = null
  if (showTrend && previousAmount !== undefined) {
    const difference = amount - previousAmount
    const percentageChange = previousAmount !== 0 ? (difference / Math.abs(previousAmount)) * 100 : 0
    trendData = {
      difference,
      percentage: percentageChange,
      isPositive: difference >= 0
    }
  }

  const sizeClasses = {
    sm: 'text-sm',
    md: 'text-base',
    lg: 'text-lg font-semibold'
  }

  const variantClasses = {
    default: 'text-foreground',
    positive: 'text-green-600 dark:text-green-400',
    negative: 'text-red-600 dark:text-red-400',
    neutral: 'text-muted-foreground'
  }

  return (
    <div className={cn("space-y-1", className)}>
      <div className={cn(
        "font-mono",
        sizeClasses[size],
        variantClasses[variant]
      )}>
        {formattedAmount}
      </div>

      {/* Trend indicator */}
      {trendData && (
        <div className="flex items-center gap-1 text-xs">
          {trendData.isPositive ? (
            <TrendingUp className="h-3 w-3 text-green-500" />
          ) : (
            <TrendingDown className="h-3 w-3 text-red-500" />
          )}
          <span className={cn(
            "font-medium",
            trendData.isPositive ? "text-green-600" : "text-red-600"
          )}>
            {trendData.isPositive ? '+' : ''}{nprFormatter.formatCurrency(trendData.difference, { showSymbol: true, decimalPlaces: 0 })}
          </span>
          <span className="text-muted-foreground">
            ({trendData.percentage.toFixed(1)}%)
          </span>
        </div>
      )}

      {/* Breakdown tooltip */}
      {breakdown && (
        <TooltipProvider>
          <Tooltip>
            <TooltipTrigger asChild>
              <div className="flex items-center gap-1 text-xs text-muted-foreground cursor-help">
                <Info className="h-3 w-3" />
                <span>Breakdown</span>
              </div>
            </TooltipTrigger>
            <TooltipContent>
              <CurrencyBreakdownDisplay amount={amount} showInWords={true} />
            </TooltipContent>
          </Tooltip>
        </TooltipProvider>
      )}
    </div>
  )
}

export function CurrencyBreakdownDisplay({
  amount,
  showInWords = false,
  className
}: CurrencyBreakdownProps) {
  const breakdown = nprFormatter.getCurrencyBreakdown(amount)

  return (
    <div className={cn("space-y-2 text-sm", className)}>
      <div className="font-medium">Amount Breakdown:</div>
      
      <div className="space-y-1">
        {breakdown.crores > 0 && (
          <div className="flex justify-between">
            <span>Crores:</span>
            <span className="font-mono">{breakdown.crores}</span>
          </div>
        )}
        {breakdown.lakhs > 0 && (
          <div className="flex justify-between">
            <span>Lakhs:</span>
            <span className="font-mono">{breakdown.lakhs}</span>
          </div>
        )}
        {breakdown.thousands > 0 && (
          <div className="flex justify-between">
            <span>Thousands:</span>
            <span className="font-mono">{breakdown.thousands}</span>
          </div>
        )}
        {breakdown.hundreds > 0 && (
          <div className="flex justify-between">
            <span>Hundreds:</span>
            <span className="font-mono">{breakdown.hundreds}</span>
          </div>
        )}
        {breakdown.remainder > 0 && (
          <div className="flex justify-between">
            <span>Remainder:</span>
            <span className="font-mono">{breakdown.remainder}</span>
          </div>
        )}
      </div>

      {showInWords && (
        <div className="pt-2 border-t">
          <div className="text-xs text-muted-foreground">In words:</div>
          <div className="text-xs italic">{breakdown.inWords}</div>
        </div>
      )}
    </div>
  )
}

export function CurrencyCard({
  title,
  amount,
  subtitle,
  trend,
  icon,
  variant = 'default',
  className
}: CurrencyCardProps) {
  const variantStyles = {
    default: 'border-border',
    success: 'border-green-200 bg-green-50 dark:border-green-800 dark:bg-green-950',
    warning: 'border-yellow-200 bg-yellow-50 dark:border-yellow-800 dark:bg-yellow-950',
    destructive: 'border-red-200 bg-red-50 dark:border-red-800 dark:bg-red-950'
  }

  return (
    <Card className={cn(variantStyles[variant], className)}>
      <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
        <CardTitle className="text-sm font-medium">{title}</CardTitle>
        {icon}
      </CardHeader>
      <CardContent>
        <div className="space-y-1">
          <CurrencyDisplay
            amount={amount}
            size="lg"
            variant={variant === 'success' ? 'positive' : variant === 'destructive' ? 'negative' : 'default'}
            options={{ showSymbol: true, useIndianNumbering: true, decimalPlaces: 2 }}
          />
          
          {subtitle && (
            <p className="text-xs text-muted-foreground">{subtitle}</p>
          )}
          
          {trend && (
            <div className="flex items-center gap-1 text-xs">
              {trend.value >= 0 ? (
                <TrendingUp className="h-3 w-3 text-green-500" />
              ) : (
                <TrendingDown className="h-3 w-3 text-red-500" />
              )}
              <span className={cn(
                "font-medium",
                trend.value >= 0 ? "text-green-600" : "text-red-600"
              )}>
                {trend.value >= 0 ? '+' : ''}{trend.value.toFixed(1)}%
              </span>
              <span className="text-muted-foreground">from {trend.period}</span>
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  )
}

export function CurrencyComparison({
  amounts,
  labels,
  title,
  className
}: {
  amounts: number[]
  labels: string[]
  title?: string
  className?: string
}) {
  const maxAmount = Math.max(...amounts)

  return (
    <div className={cn("space-y-4", className)}>
      {title && (
        <h3 className="text-lg font-semibold">{title}</h3>
      )}
      
      <div className="space-y-3">
        {amounts.map((amount, index) => {
          const percentage = maxAmount > 0 ? (amount / maxAmount) * 100 : 0
          
          return (
            <div key={index} className="space-y-2">
              <div className="flex justify-between items-center">
                <span className="text-sm font-medium">{labels[index]}</span>
                <CurrencyDisplay
                  amount={amount}
                  size="sm"
                  options={{ showSymbol: true, useIndianNumbering: true, decimalPlaces: 0 }}
                />
              </div>
              
              <div className="w-full bg-gray-200 rounded-full h-2 dark:bg-gray-700">
                <div
                  className="bg-blue-600 h-2 rounded-full transition-all duration-300"
                  style={{ width: `${percentage}%` }}
                />
              </div>
            </div>
          )
        })}
      </div>
    </div>
  )
}

export function CurrencyBadge({
  amount,
  variant = 'default',
  size = 'default',
  compact = false,
  className
}: {
  amount: number
  variant?: 'default' | 'secondary' | 'destructive' | 'outline'
  size?: 'default' | 'sm' | 'lg'
  compact?: boolean
  className?: string
}) {
  const formattedAmount = compact 
    ? nprFormatter.formatCompact(amount)
    : nprFormatter.formatCurrency(amount, { 
        showSymbol: true, 
        useIndianNumbering: true, 
        decimalPlaces: compact ? 1 : 2 
      })

  return (
    <Badge variant={variant} className={cn("font-mono", className)}>
      {formattedAmount}
    </Badge>
  )
}
