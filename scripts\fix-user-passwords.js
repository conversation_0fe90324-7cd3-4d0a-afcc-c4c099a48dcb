#!/usr/bin/env node

/**
 * Fix user passwords by generating correct hashes for admin123
 */

const { neon } = require('@neondatabase/serverless');
const bcrypt = require('bcryptjs');
require('dotenv').config({ path: '.env.local' });

async function fixUserPasswords() {
  console.log('🔧 Fixing user passwords...\n');
  
  if (!process.env.DATABASE_URL) {
    console.error('❌ ERROR: DATABASE_URL environment variable is not set');
    process.exit(1);
  }
  
  try {
    const sql = neon(process.env.DATABASE_URL);
    
    const password = 'admin123';
    
    console.log('🔄 Generating correct password hash for "admin123"...');
    const correctHash = await bcrypt.hash(password, 12);
    console.log('✅ Password hash generated');
    console.log(`Hash: ${correctHash.substring(0, 20)}...\n`);
    
    // Verify the hash works
    console.log('🔄 Verifying the new hash...');
    const isValid = await bcrypt.compare(password, correctHash);
    console.log(`✅ Hash verification: ${isValid ? 'VALID' : 'INVALID'}\n`);
    
    if (!isValid) {
      console.log('❌ Generated hash is invalid, aborting');
      return;
    }
    
    // Get all users
    console.log('🔄 Getting all users...');
    const users = await sql`
      SELECT id, email, full_name FROM users
    `;
    
    console.log(`📊 Found ${users.length} users to update\n`);
    
    // Update each user's password
    for (const user of users) {
      console.log(`🔄 Updating password for ${user.email}...`);
      
      try {
        await sql`
          UPDATE users 
          SET password_hash = ${correctHash}, updated_at = NOW()
          WHERE id = ${user.id}
        `;
        console.log(`✅ Updated ${user.email}`);
      } catch (error) {
        console.log(`❌ Failed to update ${user.email}:`, error.message);
      }
    }
    
    console.log('\n🔍 Verifying updates...');
    
    // Test login for admin user
    const testUser = await sql`
      SELECT email, password_hash FROM users WHERE email = '<EMAIL>'
    `;
    
    if (testUser.length > 0) {
      const testResult = await bcrypt.compare(password, testUser[0].password_hash);
      console.log(`✅ Admin login test: ${testResult ? 'PASS' : 'FAIL'}`);
      
      if (testResult) {
        console.log('\n🎉 Password fix completed successfully!');
        console.log('\n📋 All users now have password: admin123');
        
        users.forEach(user => {
          console.log(`   ${user.email} / admin123`);
        });
        
        console.log('\n🚀 You can now login with any of these credentials!');
      } else {
        console.log('\n❌ Password fix failed - login test still fails');
      }
    }
    
  } catch (error) {
    console.error('❌ Error:', error.message);
    console.error('Full error:', error);
  }
}

fixUserPasswords();
