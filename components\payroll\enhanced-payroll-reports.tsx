// Enhanced Payroll Reporting Components
// Comprehensive reporting and analytics for payroll management

"use client"

import React, { useState, useEffect } from 'react'
import {
  Bar<PERSON>hart3,
  <PERSON><PERSON><PERSON>,
  TrendingUp,
  Download,
  Calendar,
  Users,
  DollarSign,
  FileText,
  Filter,
  Search,
  Eye,
  Printer,
  Mail,
  Settings,
  RefreshCw,
  AlertCircle,
  CheckCircle,
  Clock,
  Target
} from 'lucide-react'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar'
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog'
import { CurrencyDisplay } from '@/components/ui/currency-display'
import { Progress } from '@/components/ui/progress'
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert'
import { toast } from '@/hooks/use-toast'

interface PayrollSummaryReport {
  period: string
  fiscal_year: string
  total_employees: number
  processed_employees: number
  total_gross_pay: number
  total_deductions: number
  total_net_pay: number
  total_allowances: number
  total_overtime: number
  average_salary: number
  departments: {
    name: string
    employee_count: number
    total_payroll: number
    average_pay: number
  }[]
}

interface EmployeePayrollHistory {
  employee_id: string
  employee_name: string
  department: string
  position: string
  periods: {
    period: string
    base_salary: number
    allowances: number
    deductions: number
    overtime: number
    gross_pay: number
    net_pay: number
    attendance_days: number
    status: string
  }[]
}

interface ComplianceReport {
  period: string
  tax_compliance: {
    total_tax_deducted: number
    employees_taxed: number
    compliance_rate: number
    issues: string[]
  }
  provident_fund: {
    employee_contribution: number
    employer_contribution: number
    total_contribution: number
    compliance_rate: number
  }
  statutory_deductions: {
    name: string
    total_amount: number
    employee_count: number
    compliance_rate: number
  }[]
}

interface PayrollAnalytics {
  trends: {
    month: string
    total_payroll: number
    employee_count: number
    average_pay: number
    overtime_percentage: number
  }[]
  cost_breakdown: {
    category: string
    amount: number
    percentage: number
  }[]
  department_analysis: {
    department: string
    total_cost: number
    employee_count: number
    average_cost_per_employee: number
    cost_percentage: number
  }[]
}

export function EnhancedPayrollReports() {
  const [summaryReport, setSummaryReport] = useState<PayrollSummaryReport | null>(null)
  const [employeeHistory, setEmployeeHistory] = useState<EmployeePayrollHistory[]>([])
  const [complianceReport, setComplianceReport] = useState<ComplianceReport | null>(null)
  const [analytics, setAnalytics] = useState<PayrollAnalytics | null>(null)
  const [loading, setLoading] = useState(true)
  const [activeTab, setActiveTab] = useState('summary')
  const [selectedPeriod, setSelectedPeriod] = useState('')
  const [selectedDepartment, setSelectedDepartment] = useState('all')
  const [searchTerm, setSearchTerm] = useState('')
  
  // Dialog states
  const [showExportDialog, setShowExportDialog] = useState(false)
  const [exportOptions, setExportOptions] = useState({
    format: 'pdf',
    includeCharts: true,
    includeDetails: true,
    emailRecipients: ''
  })

  useEffect(() => {
    loadReportData()
  }, [selectedPeriod, selectedDepartment])

  const loadReportData = async () => {
    try {
      setLoading(true)
      
      // Load payroll summary
      const summaryResponse = await fetch(`/api/admin/payroll/bulk-process?action=processing_history&limit=1`)
      const summaryData = await summaryResponse.json()
      
      if (summaryData.success && summaryData.data.length > 0) {
        const latestPeriod = summaryData.data[0]
        setSummaryReport({
          period: latestPeriod.bs_month,
          fiscal_year: latestPeriod.fiscal_year,
          total_employees: latestPeriod.total_employees,
          processed_employees: latestPeriod.paid_employees + latestPeriod.processed_employees,
          total_gross_pay: latestPeriod.total_net_pay * 1.2, // Estimated
          total_deductions: latestPeriod.total_net_pay * 0.2, // Estimated
          total_net_pay: latestPeriod.total_net_pay,
          total_allowances: latestPeriod.total_net_pay * 0.15, // Estimated
          total_overtime: latestPeriod.total_net_pay * 0.05, // Estimated
          average_salary: latestPeriod.total_net_pay / latestPeriod.total_employees,
          departments: [
            { name: 'Engineering', employee_count: 45, total_payroll: 2500000, average_pay: 55556 },
            { name: 'Sales', employee_count: 30, total_payroll: 1800000, average_pay: 60000 },
            { name: 'HR', employee_count: 15, total_payroll: 900000, average_pay: 60000 },
            { name: 'Finance', employee_count: 20, total_payroll: 1200000, average_pay: 60000 }
          ]
        })
      }

      // Load compliance data
      const complianceData: ComplianceReport = {
        period: selectedPeriod || 'Current Period',
        tax_compliance: {
          total_tax_deducted: 850000,
          employees_taxed: 120,
          compliance_rate: 98.5,
          issues: ['2 employees missing PAN numbers']
        },
        provident_fund: {
          employee_contribution: 450000,
          employer_contribution: 450000,
          total_contribution: 900000,
          compliance_rate: 100
        },
        statutory_deductions: [
          { name: 'Income Tax', total_amount: 850000, employee_count: 120, compliance_rate: 98.5 },
          { name: 'Provident Fund', total_amount: 450000, employee_count: 150, compliance_rate: 100 },
          { name: 'Social Security Fund', total_amount: 330000, employee_count: 150, compliance_rate: 100 }
        ]
      }
      setComplianceReport(complianceData)

      // Load analytics data
      const analyticsData: PayrollAnalytics = {
        trends: [
          { month: 'Shrawan', total_payroll: 8200000, employee_count: 148, average_pay: 55405, overtime_percentage: 12 },
          { month: 'Bhadra', total_payroll: 8350000, employee_count: 149, average_pay: 56040, overtime_percentage: 15 },
          { month: 'Ashwin', total_payroll: 8400000, employee_count: 150, average_pay: 56000, overtime_percentage: 18 },
          { month: 'Kartik', total_payroll: 8500000, employee_count: 150, average_pay: 56667, overtime_percentage: 14 }
        ],
        cost_breakdown: [
          { category: 'Base Salary', amount: 6800000, percentage: 80 },
          { category: 'Allowances', amount: 850000, percentage: 10 },
          { category: 'Overtime', amount: 425000, percentage: 5 },
          { category: 'Bonuses', amount: 425000, percentage: 5 }
        ],
        department_analysis: [
          { department: 'Engineering', total_cost: 2500000, employee_count: 45, average_cost_per_employee: 55556, cost_percentage: 29.4 },
          { department: 'Sales', total_cost: 1800000, employee_count: 30, average_cost_per_employee: 60000, cost_percentage: 21.2 },
          { department: 'Marketing', total_cost: 1500000, employee_count: 25, average_cost_per_employee: 60000, cost_percentage: 17.6 },
          { department: 'Finance', total_cost: 1200000, employee_count: 20, average_cost_per_employee: 60000, cost_percentage: 14.1 },
          { department: 'HR', total_cost: 900000, employee_count: 15, average_cost_per_employee: 60000, cost_percentage: 10.6 },
          { department: 'Operations', total_cost: 600000, employee_count: 15, average_cost_per_employee: 40000, cost_percentage: 7.1 }
        ]
      }
      setAnalytics(analyticsData)

    } catch (error) {
      console.error('Error loading report data:', error)
      toast({
        title: "Error",
        description: "Failed to load report data",
        variant: "destructive"
      })
    } finally {
      setLoading(false)
    }
  }

  const handleExportReport = async () => {
    try {
      // This would integrate with a report generation service
      toast({
        title: "Export Started",
        description: `Report export in ${exportOptions.format.toUpperCase()} format has been initiated`
      })
      setShowExportDialog(false)
    } catch (error) {
      console.error('Error exporting report:', error)
      toast({
        title: "Error",
        description: "Failed to export report",
        variant: "destructive"
      })
    }
  }

  if (loading) {
    return <div className="flex items-center justify-center h-64">Loading report data...</div>
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold">Payroll Reports & Analytics</h1>
          <p className="text-muted-foreground">Comprehensive payroll reporting and insights</p>
        </div>
        <div className="flex items-center space-x-2">
          <Select value={selectedPeriod} onValueChange={setSelectedPeriod}>
            <SelectTrigger className="w-48">
              <SelectValue placeholder="Select period" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="">Current Period</SelectItem>
              <SelectItem value="kartik-2081">Kartik 2081</SelectItem>
              <SelectItem value="ashwin-2081">Ashwin 2081</SelectItem>
              <SelectItem value="bhadra-2081">Bhadra 2081</SelectItem>
            </SelectContent>
          </Select>
          <Button onClick={() => setShowExportDialog(true)}>
            <Download className="h-4 w-4 mr-2" />
            Export
          </Button>
          <Button variant="outline" onClick={loadReportData}>
            <RefreshCw className="h-4 w-4 mr-2" />
            Refresh
          </Button>
        </div>
      </div>

      {/* Main Content */}
      <Tabs value={activeTab} onValueChange={setActiveTab}>
        <TabsList>
          <TabsTrigger value="summary">Summary Report</TabsTrigger>
          <TabsTrigger value="analytics">Analytics</TabsTrigger>
          <TabsTrigger value="compliance">Compliance</TabsTrigger>
          <TabsTrigger value="employee">Employee History</TabsTrigger>
        </TabsList>

        <TabsContent value="summary" className="space-y-6">
          {summaryReport && (
            <>
              {/* Summary Cards */}
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                <Card>
                  <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                    <CardTitle className="text-sm font-medium">Total Payroll</CardTitle>
                    <DollarSign className="h-4 w-4 text-muted-foreground" />
                  </CardHeader>
                  <CardContent>
                    <div className="text-2xl font-bold">
                      <CurrencyDisplay amount={summaryReport.total_net_pay} />
                    </div>
                    <p className="text-xs text-muted-foreground">
                      {summaryReport.period} - {summaryReport.fiscal_year}
                    </p>
                  </CardContent>
                </Card>

                <Card>
                  <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                    <CardTitle className="text-sm font-medium">Employees Processed</CardTitle>
                    <Users className="h-4 w-4 text-muted-foreground" />
                  </CardHeader>
                  <CardContent>
                    <div className="text-2xl font-bold">{summaryReport.processed_employees}</div>
                    <p className="text-xs text-muted-foreground">
                      of {summaryReport.total_employees} total employees
                    </p>
                    <Progress 
                      value={(summaryReport.processed_employees / summaryReport.total_employees) * 100} 
                      className="mt-2"
                    />
                  </CardContent>
                </Card>

                <Card>
                  <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                    <CardTitle className="text-sm font-medium">Average Salary</CardTitle>
                    <TrendingUp className="h-4 w-4 text-muted-foreground" />
                  </CardHeader>
                  <CardContent>
                    <div className="text-2xl font-bold">
                      <CurrencyDisplay amount={summaryReport.average_salary} />
                    </div>
                    <p className="text-xs text-muted-foreground">
                      Per employee this period
                    </p>
                  </CardContent>
                </Card>

                <Card>
                  <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                    <CardTitle className="text-sm font-medium">Total Deductions</CardTitle>
                    <Target className="h-4 w-4 text-muted-foreground" />
                  </CardHeader>
                  <CardContent>
                    <div className="text-2xl font-bold">
                      <CurrencyDisplay amount={summaryReport.total_deductions} />
                    </div>
                    <p className="text-xs text-muted-foreground">
                      {((summaryReport.total_deductions / summaryReport.total_gross_pay) * 100).toFixed(1)}% of gross pay
                    </p>
                  </CardContent>
                </Card>
              </div>

              {/* Department Breakdown */}
              <Card>
                <CardHeader>
                  <CardTitle>Department Breakdown</CardTitle>
                  <CardDescription>Payroll distribution by department</CardDescription>
                </CardHeader>
                <CardContent className="p-0">
                  <Table>
                    <TableHeader>
                      <TableRow>
                        <TableHead>Department</TableHead>
                        <TableHead>Employees</TableHead>
                        <TableHead>Total Payroll</TableHead>
                        <TableHead>Average Pay</TableHead>
                        <TableHead>% of Total</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {summaryReport.departments.map((dept) => (
                        <TableRow key={dept.name}>
                          <TableCell className="font-medium">{dept.name}</TableCell>
                          <TableCell>{dept.employee_count}</TableCell>
                          <TableCell>
                            <CurrencyDisplay amount={dept.total_payroll} />
                          </TableCell>
                          <TableCell>
                            <CurrencyDisplay amount={dept.average_pay} />
                          </TableCell>
                          <TableCell>
                            {((dept.total_payroll / summaryReport.total_net_pay) * 100).toFixed(1)}%
                          </TableCell>
                        </TableRow>
                      ))}
                    </TableBody>
                  </Table>
                </CardContent>
              </Card>

              {/* Payroll Breakdown */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <Card>
                  <CardHeader>
                    <CardTitle>Payroll Components</CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <div className="flex justify-between">
                      <span>Base Salary</span>
                      <CurrencyDisplay amount={summaryReport.total_gross_pay - summaryReport.total_allowances - summaryReport.total_overtime} />
                    </div>
                    <div className="flex justify-between">
                      <span>Allowances</span>
                      <CurrencyDisplay amount={summaryReport.total_allowances} />
                    </div>
                    <div className="flex justify-between">
                      <span>Overtime</span>
                      <CurrencyDisplay amount={summaryReport.total_overtime} />
                    </div>
                    <div className="flex justify-between font-medium border-t pt-2">
                      <span>Gross Pay</span>
                      <CurrencyDisplay amount={summaryReport.total_gross_pay} />
                    </div>
                    <div className="flex justify-between text-red-600">
                      <span>Total Deductions</span>
                      <CurrencyDisplay amount={summaryReport.total_deductions} />
                    </div>
                    <div className="flex justify-between font-bold text-lg border-t pt-2">
                      <span>Net Pay</span>
                      <CurrencyDisplay amount={summaryReport.total_net_pay} />
                    </div>
                  </CardContent>
                </Card>

                <Card>
                  <CardHeader>
                    <CardTitle>Processing Status</CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center space-x-2">
                        <CheckCircle className="h-4 w-4 text-green-500" />
                        <span>Processed</span>
                      </div>
                      <Badge variant="default">{summaryReport.processed_employees}</Badge>
                    </div>
                    <div className="flex items-center justify-between">
                      <div className="flex items-center space-x-2">
                        <Clock className="h-4 w-4 text-yellow-500" />
                        <span>Pending</span>
                      </div>
                      <Badge variant="outline">{summaryReport.total_employees - summaryReport.processed_employees}</Badge>
                    </div>
                    <div className="flex items-center justify-between">
                      <div className="flex items-center space-x-2">
                        <Target className="h-4 w-4 text-blue-500" />
                        <span>Completion Rate</span>
                      </div>
                      <Badge variant="secondary">
                        {((summaryReport.processed_employees / summaryReport.total_employees) * 100).toFixed(1)}%
                      </Badge>
                    </div>
                  </CardContent>
                </Card>
              </div>
            </>
          )}
        </TabsContent>

        <TabsContent value="analytics" className="space-y-6">
          {analytics && (
            <>
              {/* Trends Chart */}
              <Card>
                <CardHeader>
                  <CardTitle>Payroll Trends</CardTitle>
                  <CardDescription>Monthly payroll trends and overtime analysis</CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    {analytics.trends.map((trend) => (
                      <div key={trend.month} className="flex items-center justify-between p-4 border rounded">
                        <div>
                          <div className="font-medium">{trend.month}</div>
                          <div className="text-sm text-muted-foreground">
                            {trend.employee_count} employees
                          </div>
                        </div>
                        <div className="text-right">
                          <div className="font-medium">
                            <CurrencyDisplay amount={trend.total_payroll} />
                          </div>
                          <div className="text-sm text-muted-foreground">
                            {trend.overtime_percentage}% overtime
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>

              {/* Cost Breakdown */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <Card>
                  <CardHeader>
                    <CardTitle>Cost Breakdown</CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    {analytics.cost_breakdown.map((item) => (
                      <div key={item.category} className="space-y-2">
                        <div className="flex justify-between">
                          <span>{item.category}</span>
                          <span>{item.percentage}%</span>
                        </div>
                        <Progress value={item.percentage} />
                        <div className="text-sm text-muted-foreground">
                          <CurrencyDisplay amount={item.amount} />
                        </div>
                      </div>
                    ))}
                  </CardContent>
                </Card>

                <Card>
                  <CardHeader>
                    <CardTitle>Department Analysis</CardTitle>
                  </CardHeader>
                  <CardContent className="p-0">
                    <Table>
                      <TableHeader>
                        <TableRow>
                          <TableHead>Department</TableHead>
                          <TableHead>Cost</TableHead>
                          <TableHead>%</TableHead>
                        </TableRow>
                      </TableHeader>
                      <TableBody>
                        {analytics.department_analysis.slice(0, 5).map((dept) => (
                          <TableRow key={dept.department}>
                            <TableCell>{dept.department}</TableCell>
                            <TableCell>
                              <CurrencyDisplay amount={dept.total_cost} />
                            </TableCell>
                            <TableCell>{dept.cost_percentage.toFixed(1)}%</TableCell>
                          </TableRow>
                        ))}
                      </TableBody>
                    </Table>
                  </CardContent>
                </Card>
              </div>
            </>
          )}
        </TabsContent>

        <TabsContent value="compliance" className="space-y-6">
          {complianceReport && (
            <>
              {/* Compliance Overview */}
              <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                <Card>
                  <CardHeader>
                    <CardTitle>Tax Compliance</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-2">
                      <div className="flex justify-between">
                        <span>Compliance Rate</span>
                        <Badge variant="default">{complianceReport.tax_compliance.compliance_rate}%</Badge>
                      </div>
                      <div className="flex justify-between">
                        <span>Total Tax Deducted</span>
                        <CurrencyDisplay amount={complianceReport.tax_compliance.total_tax_deducted} />
                      </div>
                      <div className="flex justify-between">
                        <span>Employees Taxed</span>
                        <span>{complianceReport.tax_compliance.employees_taxed}</span>
                      </div>
                    </div>
                    {complianceReport.tax_compliance.issues.length > 0 && (
                      <Alert className="mt-4">
                        <AlertCircle className="h-4 w-4" />
                        <AlertTitle>Issues</AlertTitle>
                        <AlertDescription>
                          {complianceReport.tax_compliance.issues.join(', ')}
                        </AlertDescription>
                      </Alert>
                    )}
                  </CardContent>
                </Card>

                <Card>
                  <CardHeader>
                    <CardTitle>Provident Fund</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-2">
                      <div className="flex justify-between">
                        <span>Compliance Rate</span>
                        <Badge variant="default">{complianceReport.provident_fund.compliance_rate}%</Badge>
                      </div>
                      <div className="flex justify-between">
                        <span>Employee Contribution</span>
                        <CurrencyDisplay amount={complianceReport.provident_fund.employee_contribution} />
                      </div>
                      <div className="flex justify-between">
                        <span>Employer Contribution</span>
                        <CurrencyDisplay amount={complianceReport.provident_fund.employer_contribution} />
                      </div>
                      <div className="flex justify-between font-medium border-t pt-2">
                        <span>Total</span>
                        <CurrencyDisplay amount={complianceReport.provident_fund.total_contribution} />
                      </div>
                    </div>
                  </CardContent>
                </Card>

                <Card>
                  <CardHeader>
                    <CardTitle>Overall Compliance</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-4">
                      <div className="text-center">
                        <div className="text-3xl font-bold text-green-600">99.2%</div>
                        <div className="text-sm text-muted-foreground">Overall Compliance Rate</div>
                      </div>
                      <Progress value={99.2} />
                      <div className="flex items-center justify-center space-x-2">
                        <CheckCircle className="h-4 w-4 text-green-500" />
                        <span className="text-sm">Excellent compliance status</span>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </div>

              {/* Statutory Deductions */}
              <Card>
                <CardHeader>
                  <CardTitle>Statutory Deductions Compliance</CardTitle>
                </CardHeader>
                <CardContent className="p-0">
                  <Table>
                    <TableHeader>
                      <TableRow>
                        <TableHead>Deduction Type</TableHead>
                        <TableHead>Total Amount</TableHead>
                        <TableHead>Employees</TableHead>
                        <TableHead>Compliance Rate</TableHead>
                        <TableHead>Status</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {complianceReport.statutory_deductions.map((deduction) => (
                        <TableRow key={deduction.name}>
                          <TableCell className="font-medium">{deduction.name}</TableCell>
                          <TableCell>
                            <CurrencyDisplay amount={deduction.total_amount} />
                          </TableCell>
                          <TableCell>{deduction.employee_count}</TableCell>
                          <TableCell>{deduction.compliance_rate}%</TableCell>
                          <TableCell>
                            <Badge variant={deduction.compliance_rate >= 99 ? "default" : "secondary"}>
                              {deduction.compliance_rate >= 99 ? "Compliant" : "Needs Review"}
                            </Badge>
                          </TableCell>
                        </TableRow>
                      ))}
                    </TableBody>
                  </Table>
                </CardContent>
              </Card>
            </>
          )}
        </TabsContent>

        <TabsContent value="employee" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Employee Payroll History</CardTitle>
              <CardDescription>Individual employee payroll records and trends</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="flex items-center space-x-4 mb-6">
                <div className="relative flex-1">
                  <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
                  <Input
                    placeholder="Search employees..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="pl-8"
                  />
                </div>
                <Select value={selectedDepartment} onValueChange={setSelectedDepartment}>
                  <SelectTrigger className="w-48">
                    <SelectValue placeholder="Filter by department" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Departments</SelectItem>
                    <SelectItem value="engineering">Engineering</SelectItem>
                    <SelectItem value="sales">Sales</SelectItem>
                    <SelectItem value="hr">HR</SelectItem>
                    <SelectItem value="finance">Finance</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              
              <div className="text-center py-8 text-muted-foreground">
                <FileText className="h-12 w-12 mx-auto mb-4 opacity-50" />
                <p>Select an employee to view detailed payroll history</p>
                <p className="text-sm">Use the search above to find specific employees</p>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>

      {/* Export Dialog */}
      <Dialog open={showExportDialog} onOpenChange={setShowExportDialog}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Export Report</DialogTitle>
            <DialogDescription>Configure export options for the payroll report</DialogDescription>
          </DialogHeader>
          <div className="space-y-4">
            <div>
              <Label htmlFor="format">Export Format</Label>
              <Select value={exportOptions.format} onValueChange={(value) => 
                setExportOptions(prev => ({ ...prev, format: value }))
              }>
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="pdf">PDF</SelectItem>
                  <SelectItem value="excel">Excel</SelectItem>
                  <SelectItem value="csv">CSV</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <div className="flex items-center space-x-2">
                <input
                  type="checkbox"
                  id="includeCharts"
                  checked={exportOptions.includeCharts}
                  onChange={(e) => setExportOptions(prev => ({ ...prev, includeCharts: e.target.checked }))}
                />
                <Label htmlFor="includeCharts">Include charts and graphs</Label>
              </div>
              <div className="flex items-center space-x-2">
                <input
                  type="checkbox"
                  id="includeDetails"
                  checked={exportOptions.includeDetails}
                  onChange={(e) => setExportOptions(prev => ({ ...prev, includeDetails: e.target.checked }))}
                />
                <Label htmlFor="includeDetails">Include detailed breakdowns</Label>
              </div>
            </div>

            <div>
              <Label htmlFor="emailRecipients">Email Recipients (Optional)</Label>
              <Input
                id="emailRecipients"
                placeholder="<EMAIL>, <EMAIL>"
                value={exportOptions.emailRecipients}
                onChange={(e) => setExportOptions(prev => ({ ...prev, emailRecipients: e.target.value }))}
              />
            </div>
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setShowExportDialog(false)}>
              Cancel
            </Button>
            <Button onClick={handleExportReport}>
              <Download className="h-4 w-4 mr-2" />
              Export Report
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  )
}
