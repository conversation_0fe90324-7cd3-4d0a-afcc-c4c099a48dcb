'use client'

import { useQuery } from '@tanstack/react-query'

export interface EmployeePayrollProfile {
  id: string
  email: string
  full_name: string
  role: string
  department?: string
  position?: string
  phone?: string
  hire_date?: string
  salary?: number
  
  // Enhanced payroll fields
  employee_type: 'full_time' | 'part_time' | 'contract' | 'intern' | 'consultant'
  employee_category: 'regular' | 'probation' | 'temporary' | 'seasonal' | 'project_based'
  tax_identification_number?: string
  citizenship_number?: string
  pan_number?: string
  employment_status: 'active' | 'inactive' | 'terminated' | 'resigned' | 'retired'
  pay_grade?: string
  joining_bonus?: number
  
  // Bank account information
  bank_accounts: BankAccount[]
  
  // Allowances and deductions
  allowances: AllowanceAssignment[]
  deductions: DeductionApproval[]
}

export interface BankAccount {
  id: string
  bank_name: string
  bank_branch?: string
  account_number: string
  account_holder_name: string
  account_type: 'savings' | 'current' | 'salary'
  is_primary: boolean
  is_active: boolean
}

export interface AllowanceAssignment {
  id: string
  allowance_id: string
  allowance_name: string
  allowance_type: 'fixed' | 'percentage'
  amount: number
  is_percentage: boolean
  percentage_base?: 'base_salary' | 'gross_pay'
  is_taxable: boolean
  effective_from: string
  effective_to?: string
  is_active: boolean
}

export interface DeductionApproval {
  id: string
  deduction_id: string
  deduction_name: string
  deduction_type: 'fixed' | 'percentage'
  amount: number
  is_percentage: boolean
  percentage_base?: 'base_salary' | 'gross_pay' | 'net_pay'
  is_taxable: boolean
  effective_from: string
  effective_to?: string
  is_active: boolean
}

interface EmployeePayrollResponse {
  success: boolean
  data: EmployeePayrollProfile
  error?: string
}

interface EmployeesPayrollSummaryResponse {
  success: boolean
  data: EmployeePayrollProfile[]
  error?: string
}

// API functions
const employeePayrollApi = {
  async getEmployeePayrollProfile(employeeId: string): Promise<EmployeePayrollResponse> {
    const response = await fetch(`/api/admin/employees/payroll-profile?employeeId=${employeeId}`, {
      credentials: 'include',
    })

    if (!response.ok) {
      throw new Error('Failed to fetch employee payroll profile')
    }

    return response.json()
  },

  async getAllEmployeesPayrollSummary(): Promise<EmployeesPayrollSummaryResponse> {
    const response = await fetch('/api/admin/employees/payroll-profile', {
      credentials: 'include',
    })

    if (!response.ok) {
      throw new Error('Failed to fetch employees payroll summary')
    }

    return response.json()
  }
}

// React Query hooks
export function useEmployeePayrollProfile(employeeId: string | null) {
  return useQuery({
    queryKey: ['employee-payroll-profile', employeeId],
    queryFn: () => employeePayrollApi.getEmployeePayrollProfile(employeeId!),
    enabled: !!employeeId,
    staleTime: 5 * 60 * 1000, // 5 minutes
  })
}

export function useAllEmployeesPayrollSummary() {
  return useQuery({
    queryKey: ['employees-payroll-summary'],
    queryFn: () => employeePayrollApi.getAllEmployeesPayrollSummary(),
    staleTime: 5 * 60 * 1000, // 5 minutes
  })
}

// Helper function to calculate total allowances
export function calculateTotalAllowances(allowances: any[], baseSalary: number = 0): number {
  return allowances
    .filter(a => a.is_active !== false) // Handle both undefined and true as active
    .reduce((total, allowance) => {
      // Handle both database field names and interface field names
      const amount = allowance.amount || allowance.allowance_amount || 0
      const isPercentage = allowance.is_percentage || false
      const percentageBase = allowance.percentage_base || 'base_salary'

      if (isPercentage) {
        const base = percentageBase === 'base_salary' ? baseSalary : 0
        return total + (base * amount / 100)
      }
      return total + parseFloat(amount)
    }, 0)
}

// Helper function to calculate total deductions
export function calculateTotalDeductions(deductions: any[], baseSalary: number = 0, grossPay: number = 0): number {
  return deductions
    .filter(d => d.is_active !== false) // Handle both undefined and true as active
    .reduce((total, deduction) => {
      // Handle both database field names and interface field names
      const amount = deduction.amount || deduction.deduction_amount || 0
      const isPercentage = deduction.is_percentage || false
      const percentageBase = deduction.percentage_base || 'base_salary'

      if (isPercentage) {
        let base = 0
        switch (percentageBase) {
          case 'base_salary':
            base = baseSalary
            break
          case 'gross_pay':
            base = grossPay
            break
          default:
            base = baseSalary
        }
        return total + (base * amount / 100)
      }
      return total + parseFloat(amount)
    }, 0)
}

// Helper function to get primary bank account
export function getPrimaryBankAccount(bankAccounts: BankAccount[]): BankAccount | null {
  return bankAccounts.find(account => account.is_primary && account.is_active) || null
}

// Helper function to format employee display name
export function formatEmployeeDisplayName(profile: EmployeePayrollProfile): string {
  return `${profile.full_name} (${profile.employee_id || 'No ID'})`
}
