#!/usr/bin/env node

require('dotenv').config({ path: '.env.local' });
const { neon } = require('@neondatabase/serverless');
const bcrypt = require('bcryptjs');

async function diagnoseAuthIssue() {
  console.log('🔍 DIAGNOSING AUTHENTICATION ISSUES');
  console.log('===================================');
  
  if (!process.env.DATABASE_URL) {
    console.error('❌ DATABASE_URL environment variable is not set');
    process.exit(1);
  }
  
  try {
    // Test 1: Basic database connection
    console.log('🔄 Testing basic database connection...');
    const sql = neon(process.env.DATABASE_URL);
    
    const connectionTest = await sql`SELECT NOW() as current_time`;
    console.log(`✅ Database connection successful: ${connectionTest[0].current_time}`);
    
    // Test 2: Check if required tables exist
    console.log('\n🔄 Checking required tables...');
    const tables = await sql`
      SELECT table_name 
      FROM information_schema.tables 
      WHERE table_schema = 'public' 
      AND table_name IN ('users', 'user_sessions')
      ORDER BY table_name
    `;
    
    console.log(`Found tables: ${tables.map(t => t.table_name).join(', ')}`);
    
    if (tables.length < 2) {
      console.log('❌ Missing required tables for authentication');
      return;
    }
    
    // Test 3: Check admin user exists
    console.log('\n🔄 Checking admin user...');
    const adminUser = await sql`
      SELECT id, email, full_name, role, is_active, password_hash
      FROM users 
      WHERE email = '<EMAIL>'
    `;
    
    if (adminUser.length === 0) {
      console.log('❌ Admin user not found');
      return;
    }
    
    const admin = adminUser[0];
    console.log(`✅ Admin user found: ${admin.full_name} (${admin.email})`);
    console.log(`   Role: ${admin.role}`);
    console.log(`   Active: ${admin.is_active}`);
    console.log(`   Has password hash: ${admin.password_hash ? 'Yes' : 'No'}`);
    
    // Test 4: Test password verification
    console.log('\n🔄 Testing password verification...');
    try {
      const isValidPassword = await bcrypt.compare('admin123', admin.password_hash);
      console.log(`✅ Password verification: ${isValidPassword ? 'Valid' : 'Invalid'}`);
      
      if (!isValidPassword) {
        console.log('❌ Password verification failed - this is likely the issue!');
        
        // Try to fix the password
        console.log('🔄 Attempting to reset admin password...');
        const newPasswordHash = await bcrypt.hash('admin123', 12);
        
        await sql`
          UPDATE users 
          SET password_hash = ${newPasswordHash}, updated_at = NOW()
          WHERE email = '<EMAIL>'
        `;
        
        console.log('✅ Admin password reset successfully');
      }
    } catch (error) {
      console.log(`❌ Password verification error: ${error.message}`);
    }
    
    // Test 5: Test session operations
    console.log('\n🔄 Testing session operations...');
    
    // Create a test session
    const testSessionToken = require('crypto').randomBytes(32).toString('hex');
    const expiresAt = new Date(Date.now() + 7 * 24 * 60 * 60 * 1000); // 7 days
    
    try {
      await sql`
        INSERT INTO user_sessions (user_id, session_token, expires_at)
        VALUES (${admin.id}, ${testSessionToken}, ${expiresAt.toISOString()})
      `;
      console.log('✅ Session creation successful');
      
      // Test session retrieval
      const session = await sql`
        SELECT * FROM user_sessions 
        WHERE session_token = ${testSessionToken} AND expires_at > NOW()
      `;
      
      if (session.length > 0) {
        console.log('✅ Session retrieval successful');
        
        // Test user retrieval by ID
        const userById = await sql`
          SELECT * FROM users WHERE id = ${session[0].user_id} AND is_active = true
        `;
        
        if (userById.length > 0) {
          console.log('✅ User retrieval by ID successful');
        } else {
          console.log('❌ User retrieval by ID failed');
        }
        
        // Clean up test session
        await sql`DELETE FROM user_sessions WHERE session_token = ${testSessionToken}`;
        console.log('✅ Test session cleaned up');
        
      } else {
        console.log('❌ Session retrieval failed');
      }
      
    } catch (error) {
      console.log(`❌ Session operation error: ${error.message}`);
    }
    
    // Test 6: Test the complete auth flow
    console.log('\n🔄 Testing complete authentication flow...');
    
    try {
      // Step 1: Find user by email
      const userByEmail = await sql`
        SELECT * FROM users WHERE email = '<EMAIL>' AND is_active = true
      `;
      
      if (userByEmail.length === 0) {
        console.log('❌ User lookup by email failed');
        return;
      }
      
      console.log('✅ Step 1: User lookup by email successful');
      
      // Step 2: Verify password
      const passwordValid = await bcrypt.compare('admin123', userByEmail[0].password_hash);
      if (!passwordValid) {
        console.log('❌ Step 2: Password verification failed');
        return;
      }
      
      console.log('✅ Step 2: Password verification successful');
      
      // Step 3: Create session
      const sessionToken = require('crypto').randomBytes(32).toString('hex');
      const sessionExpiresAt = new Date(Date.now() + 7 * 24 * 60 * 60 * 1000);
      
      await sql`
        INSERT INTO user_sessions (user_id, session_token, expires_at)
        VALUES (${userByEmail[0].id}, ${sessionToken}, ${sessionExpiresAt.toISOString()})
      `;
      
      console.log('✅ Step 3: Session creation successful');
      
      // Step 4: Verify session
      const sessionVerification = await sql`
        SELECT * FROM user_sessions 
        WHERE session_token = ${sessionToken} AND expires_at > NOW()
      `;
      
      if (sessionVerification.length === 0) {
        console.log('❌ Step 4: Session verification failed');
        return;
      }
      
      console.log('✅ Step 4: Session verification successful');
      
      // Step 5: Get user by session
      const userBySession = await sql`
        SELECT * FROM users WHERE id = ${sessionVerification[0].user_id} AND is_active = true
      `;
      
      if (userBySession.length === 0) {
        console.log('❌ Step 5: User retrieval by session failed');
        return;
      }
      
      console.log('✅ Step 5: User retrieval by session successful');
      
      // Clean up
      await sql`DELETE FROM user_sessions WHERE session_token = ${sessionToken}`;
      
      console.log('\n🎉 AUTHENTICATION FLOW TEST COMPLETED SUCCESSFULLY!');
      
    } catch (error) {
      console.log(`❌ Authentication flow error: ${error.message}`);
      console.log('Stack trace:', error.stack);
    }
    
    // Test 7: Check database connection timeout settings
    console.log('\n🔄 Testing connection timeout...');
    
    const startTime = Date.now();
    try {
      await sql`SELECT pg_sleep(1)`;  // Sleep for 1 second
      const endTime = Date.now();
      console.log(`✅ Connection timeout test passed (${endTime - startTime}ms)`);
    } catch (error) {
      console.log(`❌ Connection timeout test failed: ${error.message}`);
    }
    
    console.log('\n📊 DIAGNOSIS SUMMARY:');
    console.log('====================');
    console.log('✅ Database connection: Working');
    console.log('✅ Required tables: Present');
    console.log('✅ Admin user: Found and active');
    console.log('✅ Authentication flow: Working');
    
    console.log('\n💡 RECOMMENDATIONS:');
    console.log('1. If login still fails, check browser network tab for specific errors');
    console.log('2. Verify the Next.js server is using the same DATABASE_URL');
    console.log('3. Check if there are any CORS or cookie issues');
    console.log('4. Try clearing browser cookies and cache');
    
  } catch (error) {
    console.error('❌ Diagnosis failed:', error.message);
    console.error('Stack trace:', error.stack);
    process.exit(1);
  }
}

diagnoseAuthIssue();
