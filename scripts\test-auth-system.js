#!/usr/bin/env node

// Comprehensive authentication system test
const fs = require('fs');
const path = require('path');

let neon, bcrypt, dotenv;

try {
  ({ neon } = require('@neondatabase/serverless'));
  bcrypt = require('bcryptjs');
  dotenv = require('dotenv');
  dotenv.config({ path: '.env.local' });
} catch (error) {
  console.log('⚠️  Required packages not installed yet. Please run: npm install');
  process.exit(1);
}

async function testAuthSystem() {
  console.log('🔐 Testing Authentication System...\n');
  
  if (!process.env.DATABASE_URL) {
    console.error('❌ DATABASE_URL not set. Please update .env.local');
    process.exit(1);
  }
  
  try {
    const sql = neon(process.env.DATABASE_URL);
    
    console.log('🔄 Testing database connection...');
    await sql`SELECT 1`;
    console.log('✅ Database connection successful\n');
    
    // Test 1: Check if required tables exist
    console.log('🔄 Checking database schema...');
    const tables = await sql`
      SELECT table_name 
      FROM information_schema.tables 
      WHERE table_schema = 'public' 
      AND table_name IN ('users', 'user_sessions', 'permissions', 'role_permissions')
      ORDER BY table_name
    `;
    
    const requiredTables = ['users', 'user_sessions', 'permissions', 'role_permissions'];
    const existingTables = tables.map(t => t.table_name);
    const missingTables = requiredTables.filter(t => !existingTables.includes(t));
    
    if (missingTables.length > 0) {
      console.log('❌ Missing required tables:', missingTables.join(', '));
      console.log('💡 Run: node scripts/setup-database.js');
      return;
    }
    console.log('✅ All required tables exist:', existingTables.join(', '));
    
    // Test 2: Check demo users
    console.log('\n🔄 Checking demo users...');
    const users = await sql`SELECT email, role, is_active FROM users ORDER BY role`;
    
    if (users.length === 0) {
      console.log('❌ No demo users found');
      console.log('💡 Run: node scripts/setup-database.js');
      return;
    }
    
    console.log('✅ Demo users found:');
    users.forEach(user => {
      console.log(`   - ${user.email} (${user.role}) ${user.is_active ? '✅' : '❌'}`);
    });
    
    // Test 3: Verify password hashes
    console.log('\n🔄 Testing password verification...');
    const adminUser = await sql`
      SELECT email, password_hash FROM users WHERE email = '<EMAIL>'
    `;
    
    if (adminUser.length === 0) {
      console.log('❌ Admin user not found');
      return;
    }
    
    const isValidPassword = await bcrypt.compare('admin123', adminUser[0].password_hash);
    if (isValidPassword) {
      console.log('✅ Password verification working correctly');
    } else {
      console.log('❌ Password verification failed - hash may be incorrect');
    }
    
    // Test 4: Test session token generation
    console.log('\n🔄 Testing session token generation...');
    const crypto = require('crypto');
    const sessionToken = crypto.randomBytes(32).toString('hex');
    console.log('✅ Session token generation working:', sessionToken.substring(0, 16) + '...');
    
    // Test 5: Check user_sessions table structure
    console.log('\n🔄 Checking user_sessions table...');
    const sessionColumns = await sql`
      SELECT column_name, data_type 
      FROM information_schema.columns 
      WHERE table_name = 'user_sessions' AND table_schema = 'public'
      ORDER BY ordinal_position
    `;
    
    const requiredSessionColumns = ['id', 'user_id', 'session_token', 'expires_at', 'created_at'];
    const existingSessionColumns = sessionColumns.map(c => c.column_name);
    const missingSessionColumns = requiredSessionColumns.filter(c => !existingSessionColumns.includes(c));
    
    if (missingSessionColumns.length > 0) {
      console.log('❌ Missing session table columns:', missingSessionColumns.join(', '));
    } else {
      console.log('✅ User sessions table structure correct');
    }
    
    // Test 6: Check permissions setup
    console.log('\n🔄 Checking permissions...');
    const permissionCount = await sql`SELECT COUNT(*) as count FROM permissions`;
    const rolePermissionCount = await sql`SELECT COUNT(*) as count FROM role_permissions`;
    
    console.log(`✅ Permissions: ${permissionCount[0].count} permissions, ${rolePermissionCount[0].count} role assignments`);
    
    // Test 7: Simulate login flow
    console.log('\n🔄 Testing simulated login flow...');
    
    // Get admin user
    const testUser = await sql`
      SELECT id, email, password_hash, role FROM users 
      WHERE email = '<EMAIL>' AND is_active = true
    `;
    
    if (testUser.length === 0) {
      console.log('❌ Test user not found');
      return;
    }
    
    // Verify password
    const passwordValid = await bcrypt.compare('admin123', testUser[0].password_hash);
    if (!passwordValid) {
      console.log('❌ Password verification failed');
      return;
    }
    
    // Create test session
    const testSessionToken = crypto.randomBytes(32).toString('hex');
    const expiresAt = new Date(Date.now() + 7 * 24 * 60 * 60 * 1000); // 7 days
    
    await sql`
      INSERT INTO user_sessions (user_id, session_token, expires_at)
      VALUES (${testUser[0].id}, ${testSessionToken}, ${expiresAt.toISOString()})
    `;
    
    // Verify session retrieval
    const retrievedSession = await sql`
      SELECT * FROM user_sessions 
      WHERE session_token = ${testSessionToken} AND expires_at > NOW()
    `;
    
    if (retrievedSession.length > 0) {
      console.log('✅ Session creation and retrieval working');
      
      // Clean up test session
      await sql`DELETE FROM user_sessions WHERE session_token = ${testSessionToken}`;
      console.log('✅ Session cleanup working');
    } else {
      console.log('❌ Session creation or retrieval failed');
    }
    
    console.log('\n🎉 Authentication system test completed!');
    console.log('\n📋 Summary:');
    console.log('✅ Database connection: Working');
    console.log('✅ Required tables: Present');
    console.log('✅ Demo users: Available');
    console.log('✅ Password hashing: Working');
    console.log('✅ Session management: Working');
    console.log('✅ Permissions: Configured');
    
    console.log('\n🚀 Ready to test login! Demo credentials:');
    users.forEach(user => {
      console.log(`   ${user.email} / admin123`);
    });
    
  } catch (error) {
    console.error('\n❌ Authentication test failed:', error.message);
    
    if (error.message.includes('relation') && error.message.includes('does not exist')) {
      console.log('\n💡 Database tables not created. Run: node scripts/setup-database.js');
    } else if (error.message.includes('password authentication failed')) {
      console.log('\n💡 Check your DATABASE_URL in .env.local');
    }
  }
}

testAuthSystem();
