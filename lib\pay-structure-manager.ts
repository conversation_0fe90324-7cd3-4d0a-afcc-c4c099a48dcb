// Pay Structure Management System
// Phase 2: Core Payroll Engine Development - Multiple Pay Structure Support

import { db } from './neon';
import { nepalConfig } from './nepal-config';
import { PayrollCalculatorFactory, PayStructure } from './payroll-engine';

export interface PayStructureConfig {
  id?: string;
  userId: string;
  type: 'hourly' | 'daily' | 'monthly' | 'project_based';
  baseSalary: number;
  hourlyRate: number;
  dailyRate: number;
  overtimeMultiplier: number;
  providentFundPercentage: number;
  effectiveFrom: string;
  effectiveTo?: string;
  isActive: boolean;
  createdBy: string;
  metadata?: PayStructureMetadata;
}

export interface PayStructureMetadata {
  workingDaysPerWeek?: number;
  workingHoursPerDay?: number;
  minimumHoursForFullDay?: number;
  overtimeEligible?: boolean;
  holidayPayMultiplier?: number;
  probationPeriod?: boolean;
  performanceIncentiveEligible?: boolean;
  notes?: string;
}

export interface PayStructureCalculation {
  type: string;
  baseSalary: number;
  estimatedMonthlyPay: number;
  estimatedAnnualPay: number;
  hourlyEquivalent: number;
  dailyEquivalent: number;
  overtimeRate: number;
  providentFundAmount: number;
  breakdown: PayCalculationBreakdown;
}

export interface PayCalculationBreakdown {
  regularPay: number;
  overtimePay: number;
  allowances: number;
  grossPay: number;
  providentFund: number;
  incomeTax: number;
  otherDeductions: number;
  netPay: number;
}

export class PayStructureManager {
  private nepalConfig = nepalConfig;

  /**
   * Create a new pay structure for an employee
   */
  async createPayStructure(config: Omit<PayStructureConfig, 'id'>): Promise<PayStructureConfig> {
    try {
      // Validate the pay structure
      const validation = this.validatePayStructure(config);
      if (!validation.isValid) {
        throw new Error(`Invalid pay structure: ${validation.errors.join(', ')}`);
      }

      // Calculate derived rates
      const calculatedRates = this.calculateDerivedRates(config);

      // Deactivate existing active pay structure
      await this.deactivateExistingPayStructure(config.userId, config.effectiveFrom);

      // Create new pay structure in database
      const result = await db.sql`
        INSERT INTO employee_pay_structure (
          user_id, pay_structure_type, base_salary, hourly_rate, daily_rate,
          overtime_rate_multiplier, provident_fund_percentage, effective_from,
          effective_to, is_active, created_by
        )
        VALUES (
          ${config.userId}, ${config.type}, ${config.baseSalary}, 
          ${calculatedRates.hourlyRate}, ${calculatedRates.dailyRate},
          ${config.overtimeMultiplier}, ${config.providentFundPercentage},
          ${config.effectiveFrom}, ${config.effectiveTo || null},
          ${config.isActive}, ${config.createdBy}
        )
        RETURNING *
      `;

      return this.mapDatabaseToConfig(result[0]);
    } catch (error) {
      console.error('Error creating pay structure:', error);
      throw new Error(`Failed to create pay structure: ${error.message}`);
    }
  }

  /**
   * Update an existing pay structure
   */
  async updatePayStructure(id: string, updates: Partial<PayStructureConfig>): Promise<PayStructureConfig> {
    try {
      // Get existing pay structure
      const existing = await this.getPayStructureById(id);
      if (!existing) {
        throw new Error('Pay structure not found');
      }

      // Merge updates with existing config
      const updatedConfig = { ...existing, ...updates };

      // Validate updated structure
      const validation = this.validatePayStructure(updatedConfig);
      if (!validation.isValid) {
        throw new Error(`Invalid pay structure: ${validation.errors.join(', ')}`);
      }

      // Calculate derived rates
      const calculatedRates = this.calculateDerivedRates(updatedConfig);

      // Update in database
      const result = await db.sql`
        UPDATE employee_pay_structure
        SET 
          pay_structure_type = ${updatedConfig.type},
          base_salary = ${updatedConfig.baseSalary},
          hourly_rate = ${calculatedRates.hourlyRate},
          daily_rate = ${calculatedRates.dailyRate},
          overtime_rate_multiplier = ${updatedConfig.overtimeMultiplier},
          provident_fund_percentage = ${updatedConfig.providentFundPercentage},
          effective_from = ${updatedConfig.effectiveFrom},
          effective_to = ${updatedConfig.effectiveTo || null},
          is_active = ${updatedConfig.isActive},
          updated_at = NOW()
        WHERE id = ${id}
        RETURNING *
      `;

      return this.mapDatabaseToConfig(result[0]);
    } catch (error) {
      console.error('Error updating pay structure:', error);
      throw new Error(`Failed to update pay structure: ${error.message}`);
    }
  }

  /**
   * Get pay structure by ID
   */
  async getPayStructureById(id: string): Promise<PayStructureConfig | null> {
    try {
      const result = await db.sql`
        SELECT * FROM employee_pay_structure WHERE id = ${id}
      `;

      return result[0] ? this.mapDatabaseToConfig(result[0]) : null;
    } catch (error) {
      console.error('Error fetching pay structure:', error);
      return null;
    }
  }

  /**
   * Get active pay structure for a user
   */
  async getActivePayStructure(userId: string, effectiveDate?: string): Promise<PayStructureConfig | null> {
    try {
      const targetDate = effectiveDate || new Date().toISOString().split('T')[0];

      const result = await db.sql`
        SELECT * FROM employee_pay_structure
        WHERE user_id = ${userId}
          AND effective_from <= ${targetDate}
          AND (effective_to IS NULL OR effective_to >= ${targetDate})
          AND is_active = TRUE
        ORDER BY effective_from DESC
        LIMIT 1
      `;

      return result[0] ? this.mapDatabaseToConfig(result[0]) : null;
    } catch (error) {
      console.error('Error fetching active pay structure:', error);
      return null;
    }
  }

  /**
   * Get pay structure history for a user
   */
  async getPayStructureHistory(userId: string): Promise<PayStructureConfig[]> {
    try {
      const result = await db.sql`
        SELECT * FROM employee_pay_structure
        WHERE user_id = ${userId}
        ORDER BY effective_from DESC
      `;

      return result.map(row => this.mapDatabaseToConfig(row));
    } catch (error) {
      console.error('Error fetching pay structure history:', error);
      return [];
    }
  }

  /**
   * Calculate pay structure estimates
   */
  calculatePayStructureEstimates(config: PayStructureConfig): PayStructureCalculation {
    const laborLaw = this.nepalConfig.getLaborLawConfig();
    const workingDaysPerMonth = 26; // Standard working days
    const workingHoursPerDay = laborLaw.standardWorkingHours;

    let estimatedMonthlyPay = 0;
    let hourlyEquivalent = 0;
    let dailyEquivalent = 0;

    switch (config.type) {
      case 'monthly':
        estimatedMonthlyPay = config.baseSalary;
        hourlyEquivalent = config.baseSalary / (workingDaysPerMonth * workingHoursPerDay);
        dailyEquivalent = config.baseSalary / workingDaysPerMonth;
        break;
      case 'hourly':
        estimatedMonthlyPay = config.hourlyRate * workingDaysPerMonth * workingHoursPerDay;
        hourlyEquivalent = config.hourlyRate;
        dailyEquivalent = config.hourlyRate * workingHoursPerDay;
        break;
      case 'daily':
        estimatedMonthlyPay = config.dailyRate * workingDaysPerMonth;
        hourlyEquivalent = config.dailyRate / workingHoursPerDay;
        dailyEquivalent = config.dailyRate;
        break;
      case 'project_based':
        estimatedMonthlyPay = config.baseSalary; // Assuming monthly project completion
        hourlyEquivalent = config.baseSalary / (workingDaysPerMonth * workingHoursPerDay);
        dailyEquivalent = config.baseSalary / workingDaysPerMonth;
        break;
    }

    const estimatedAnnualPay = estimatedMonthlyPay * 12;
    const overtimeRate = hourlyEquivalent * config.overtimeMultiplier;
    const providentFundAmount = (estimatedMonthlyPay * config.providentFundPercentage) / 100;

    // Calculate breakdown (simplified)
    const breakdown: PayCalculationBreakdown = {
      regularPay: estimatedMonthlyPay,
      overtimePay: 0, // Would depend on actual overtime hours
      allowances: 0, // Would depend on configured allowances
      grossPay: estimatedMonthlyPay,
      providentFund: providentFundAmount,
      incomeTax: this.calculateEstimatedIncomeTax(estimatedMonthlyPay),
      otherDeductions: 0,
      netPay: estimatedMonthlyPay - providentFundAmount - this.calculateEstimatedIncomeTax(estimatedMonthlyPay)
    };

    return {
      type: config.type,
      baseSalary: config.baseSalary,
      estimatedMonthlyPay,
      estimatedAnnualPay,
      hourlyEquivalent,
      dailyEquivalent,
      overtimeRate,
      providentFundAmount,
      breakdown
    };
  }

  /**
   * Validate pay structure configuration
   */
  private validatePayStructure(config: PayStructureConfig): { isValid: boolean; errors: string[] } {
    const errors: string[] = [];

    if (!config.userId) errors.push('User ID is required');
    if (!config.type) errors.push('Pay structure type is required');
    if (!config.effectiveFrom) errors.push('Effective from date is required');
    if (!config.createdBy) errors.push('Created by is required');

    // Type-specific validations
    switch (config.type) {
      case 'monthly':
        if (!config.baseSalary || config.baseSalary <= 0) {
          errors.push('Base salary must be greater than 0 for monthly pay structure');
        }
        break;
      case 'hourly':
        if (!config.hourlyRate || config.hourlyRate <= 0) {
          errors.push('Hourly rate must be greater than 0 for hourly pay structure');
        }
        break;
      case 'daily':
        if (!config.dailyRate || config.dailyRate <= 0) {
          errors.push('Daily rate must be greater than 0 for daily pay structure');
        }
        break;
      case 'project_based':
        if (!config.baseSalary || config.baseSalary <= 0) {
          errors.push('Project amount must be greater than 0 for project-based pay structure');
        }
        break;
    }

    // Check minimum wage compliance
    const laborLaw = this.nepalConfig.getLaborLawConfig();
    const estimates = this.calculatePayStructureEstimates(config);
    if (estimates.estimatedMonthlyPay < laborLaw.minimumWage) {
      errors.push(`Estimated monthly pay (${estimates.estimatedMonthlyPay}) is below minimum wage (${laborLaw.minimumWage})`);
    }

    return {
      isValid: errors.length === 0,
      errors
    };
  }

  /**
   * Calculate derived rates from primary rate
   */
  private calculateDerivedRates(config: PayStructureConfig): { hourlyRate: number; dailyRate: number } {
    const laborLaw = this.nepalConfig.getLaborLawConfig();
    const workingDaysPerMonth = 26;
    const workingHoursPerDay = laborLaw.standardWorkingHours;

    let hourlyRate = config.hourlyRate || 0;
    let dailyRate = config.dailyRate || 0;

    switch (config.type) {
      case 'monthly':
        hourlyRate = config.baseSalary / (workingDaysPerMonth * workingHoursPerDay);
        dailyRate = config.baseSalary / workingDaysPerMonth;
        break;
      case 'hourly':
        hourlyRate = config.hourlyRate;
        dailyRate = config.hourlyRate * workingHoursPerDay;
        break;
      case 'daily':
        hourlyRate = config.dailyRate / workingHoursPerDay;
        dailyRate = config.dailyRate;
        break;
      case 'project_based':
        hourlyRate = config.baseSalary / (workingDaysPerMonth * workingHoursPerDay);
        dailyRate = config.baseSalary / workingDaysPerMonth;
        break;
    }

    return {
      hourlyRate: Math.round(hourlyRate * 100) / 100,
      dailyRate: Math.round(dailyRate * 100) / 100
    };
  }

  /**
   * Deactivate existing active pay structure
   */
  private async deactivateExistingPayStructure(userId: string, effectiveFrom: string): Promise<void> {
    try {
      await db.sql`
        UPDATE employee_pay_structure
        SET is_active = FALSE, effective_to = ${effectiveFrom}
        WHERE user_id = ${userId} AND is_active = TRUE
      `;
    } catch (error) {
      console.error('Error deactivating existing pay structure:', error);
      // Don't throw here as it's not critical for creation
    }
  }

  /**
   * Calculate estimated income tax
   */
  private calculateEstimatedIncomeTax(monthlyPay: number): number {
    const annualPay = monthlyPay * 12;
    const taxThreshold = 500000; // NPR 5 lakhs

    if (annualPay <= taxThreshold) return 0;

    const taxableIncome = annualPay - taxThreshold;
    const annualTax = taxableIncome * 0.1; // Simplified 10% rate

    return Math.round((annualTax / 12) * 100) / 100;
  }

  /**
   * Map database row to config object
   */
  private mapDatabaseToConfig(row: any): PayStructureConfig {
    return {
      id: row.id,
      userId: row.user_id,
      type: row.pay_structure_type,
      baseSalary: Number(row.base_salary),
      hourlyRate: Number(row.hourly_rate),
      dailyRate: Number(row.daily_rate),
      overtimeMultiplier: Number(row.overtime_rate_multiplier),
      providentFundPercentage: Number(row.provident_fund_percentage),
      effectiveFrom: row.effective_from,
      effectiveTo: row.effective_to,
      isActive: Boolean(row.is_active),
      createdBy: row.created_by
    };
  }
}

// Export singleton instance
export const payStructureManager = new PayStructureManager();
