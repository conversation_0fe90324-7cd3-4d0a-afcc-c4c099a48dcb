-- Quick fix for attendance table column data types
-- Run this SQL directly in your Neon database console

-- Check current table structure
SELECT 
    column_name, 
    data_type, 
    is_nullable,
    column_default
FROM information_schema.columns 
WHERE table_name = 'attendance' 
AND column_name IN ('check_in_time', 'check_out_time')
ORDER BY column_name;

-- If the columns are TIME type, run the following migration:

-- Step 1: Add temporary columns with correct data type
ALTER TABLE attendance ADD COLUMN IF NOT EXISTS check_in_time_new TIMESTAMP WITH TIME ZONE;
ALTER TABLE attendance ADD COLUMN IF NOT EXISTS check_out_time_new TIMESTAMP WITH TIME ZONE;

-- Step 2: Migrate existing data (if any exists)
-- For TIME values, combine them with the date column to create proper timestamps
UPDATE attendance 
SET 
    check_in_time_new = CASE 
        WHEN check_in_time IS NOT NULL THEN 
            (date + check_in_time)::TIMESTAMP WITH TIME ZONE
        ELSE NULL 
    END,
    check_out_time_new = CASE 
        WHEN check_out_time IS NOT NULL THEN 
            (date + check_out_time)::TIMESTAMP WITH TIME ZONE
        ELSE NULL 
    END
WHERE check_in_time_new IS NULL OR check_out_time_new IS NULL;

-- Step 3: Drop old columns
ALTER TABLE attendance DROP COLUMN IF EXISTS check_in_time;
ALTER TABLE attendance DROP COLUMN IF EXISTS check_out_time;

-- Step 4: Rename new columns
ALTER TABLE attendance RENAME COLUMN check_in_time_new TO check_in_time;
ALTER TABLE attendance RENAME COLUMN check_out_time_new TO check_out_time;

-- Step 5: Verify the changes
SELECT 
    column_name, 
    data_type, 
    is_nullable,
    column_default
FROM information_schema.columns 
WHERE table_name = 'attendance' 
AND column_name IN ('check_in_time', 'check_out_time')
ORDER BY column_name;

-- Expected result: both columns should now be 'timestamp with time zone'
