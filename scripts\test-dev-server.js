#!/usr/bin/env node

/**
 * Script to test if the development server starts correctly
 * after fixing chunk loading issues
 */

const { spawn } = require('child_process');
const http = require('http');

console.log('🚀 Testing Development Server After Chunk Fix');
console.log('==============================================\n');

async function testDevServer() {
  return new Promise((resolve, reject) => {
    console.log('1. Starting development server...');
    
    // Start the dev server
    const devServer = spawn('npm', ['run', 'dev'], {
      stdio: ['pipe', 'pipe', 'pipe'],
      shell: true,
      cwd: process.cwd()
    });

    let serverStarted = false;
    let serverUrl = '';

    // Listen for server output
    devServer.stdout.on('data', (data) => {
      const output = data.toString();
      console.log('   Server output:', output.trim());
      
      // Check if server started successfully
      if (output.includes('Local:') || output.includes('localhost:3000')) {
        serverStarted = true;
        serverUrl = 'http://localhost:3000';
        console.log('   ✅ Development server started successfully!');
        console.log(`   🌐 Server URL: ${serverUrl}`);
        
        // Test if the server is accessible
        setTimeout(() => {
          testServerAccess(serverUrl, devServer, resolve, reject);
        }, 2000);
      }
      
      // Check for errors
      if (output.includes('Error') || output.includes('Failed')) {
        console.log('   ❌ Server error detected:', output.trim());
      }
    });

    devServer.stderr.on('data', (data) => {
      const error = data.toString();
      console.log('   Server error:', error.trim());
      
      // Check for chunk loading errors
      if (error.includes('ChunkLoadError') || error.includes('chunk')) {
        console.log('   ❌ Chunk loading error still present!');
        devServer.kill();
        reject(new Error('Chunk loading error detected'));
      }
    });

    devServer.on('close', (code) => {
      if (!serverStarted) {
        console.log(`   ❌ Server exited with code ${code}`);
        reject(new Error(`Server failed to start (exit code: ${code})`));
      }
    });

    // Timeout after 60 seconds
    setTimeout(() => {
      if (!serverStarted) {
        console.log('   ⏰ Server startup timeout');
        devServer.kill();
        reject(new Error('Server startup timeout'));
      }
    }, 60000);
  });
}

async function testServerAccess(url, devServer, resolve, reject) {
  console.log('\n2. Testing server accessibility...');
  
  try {
    const req = http.get(url, (res) => {
      console.log(`   Status Code: ${res.statusCode}`);
      console.log(`   Headers: ${JSON.stringify(res.headers, null, 2)}`);
      
      let data = '';
      res.on('data', (chunk) => {
        data += chunk;
      });
      
      res.on('end', () => {
        if (res.statusCode === 200) {
          console.log('   ✅ Server is accessible!');
          console.log('   📄 Response received (length:', data.length, 'bytes)');
          
          // Check if the response contains expected content
          if (data.includes('<html') || data.includes('<!DOCTYPE')) {
            console.log('   ✅ Valid HTML response received');
            console.log('\n🎉 Development server is working correctly!');
            console.log('   ✅ No chunk loading errors detected');
            console.log('   ✅ Server is accessible at http://localhost:3000');
            console.log('   ✅ Application should load properly in the browser');
            
            console.log('\n📋 Next steps:');
            console.log('   1. Open http://localhost:3000 in your browser');
            console.log('   2. Test the dashboard and attendance functionality');
            console.log('   3. Check browser console for any remaining errors');
            
            // Keep server running for testing
            console.log('\n⚠️  Server will continue running for testing...');
            console.log('   Press Ctrl+C to stop the server when done testing');
            
            resolve(true);
          } else {
            console.log('   ⚠️  Unexpected response content');
            console.log('   Response preview:', data.substring(0, 200) + '...');
            devServer.kill();
            reject(new Error('Unexpected response content'));
          }
        } else {
          console.log(`   ❌ Server returned status ${res.statusCode}`);
          devServer.kill();
          reject(new Error(`Server returned status ${res.statusCode}`));
        }
      });
    });
    
    req.on('error', (error) => {
      console.log('   ❌ Connection error:', error.message);
      devServer.kill();
      reject(error);
    });
    
    req.setTimeout(10000, () => {
      console.log('   ⏰ Request timeout');
      req.destroy();
      devServer.kill();
      reject(new Error('Request timeout'));
    });
    
  } catch (error) {
    console.log('   ❌ Test error:', error.message);
    devServer.kill();
    reject(error);
  }
}

// Run the test
if (require.main === module) {
  testDevServer()
    .then(() => {
      console.log('\n✅ All tests passed! The chunk loading issue has been resolved.');
    })
    .catch((error) => {
      console.error('\n❌ Test failed:', error.message);
      console.error('\nTroubleshooting steps:');
      console.error('1. Check if port 3000 is already in use');
      console.error('2. Try running: npm run build && npm run start');
      console.error('3. Check for TypeScript errors: npm run type-check');
      console.error('4. Clear browser cache and try again');
      process.exit(1);
    });
}

module.exports = { testDevServer };
