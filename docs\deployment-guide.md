# Payroll Management System - Deployment Guide

## Overview

This guide covers the complete deployment process for the Nepal Payroll Management System, including infrastructure setup, database configuration, environment setup, and production deployment.

## System Requirements

### Minimum Requirements
- **CPU**: 2 cores, 2.4 GHz
- **RAM**: 4 GB
- **Storage**: 50 GB SSD
- **Network**: 100 Mbps internet connection
- **OS**: Ubuntu 20.04 LTS or later

### Recommended Requirements
- **CPU**: 4 cores, 3.0 GHz
- **RAM**: 8 GB
- **Storage**: 100 GB SSD
- **Network**: 1 Gbps internet connection
- **OS**: Ubuntu 22.04 LTS

### For Production (High Availability)
- **Load Balancer**: 2 instances
- **Application Servers**: 3 instances (minimum)
- **Database**: PostgreSQL cluster with read replicas
- **Cache**: Redis cluster
- **Storage**: Network-attached storage (NAS)

## Technology Stack

### Frontend
- **Framework**: Next.js 14
- **UI Library**: Tailwind CSS + shadcn/ui
- **State Management**: React Context + Zustand
- **Build Tool**: Turbopack

### Backend
- **Runtime**: Node.js 18+
- **Framework**: Next.js API Routes
- **Database**: Neon PostgreSQL
- **ORM**: Drizzle ORM
- **Authentication**: NextAuth.js

### Infrastructure
- **Hosting**: Vercel (recommended) or self-hosted
- **Database**: Neon PostgreSQL
- **CDN**: Vercel Edge Network
- **Monitoring**: Vercel Analytics + Custom monitoring
- **Backup**: Automated database backups

## Environment Setup

### 1. Prerequisites

Install required software:

```bash
# Node.js 18+
curl -fsSL https://deb.nodesource.com/setup_18.x | sudo -E bash -
sudo apt-get install -y nodejs

# pnpm (recommended package manager)
npm install -g pnpm

# Git
sudo apt-get install git

# PostgreSQL client (for database management)
sudo apt-get install postgresql-client
```

### 2. Clone Repository

```bash
git clone https://github.com/your-org/nepal-payroll-system.git
cd nepal-payroll-system
```

### 3. Install Dependencies

```bash
pnpm install
```

### 4. Environment Configuration

Create environment files:

```bash
# Development environment
cp .env.example .env.local

# Production environment
cp .env.example .env.production
```

### Environment Variables

#### Required Variables

```bash
# Database Configuration
DATABASE_URL="postgresql://username:password@host:port/database"
DIRECT_URL="postgresql://username:password@host:port/database"

# Authentication
NEXTAUTH_URL="https://your-domain.com"
NEXTAUTH_SECRET="your-secret-key-here"

# Nepal Configuration
NEPAL_FISCAL_YEAR="2081-82"
NEPAL_TIMEZONE="Asia/Kathmandu"
DEFAULT_CURRENCY="NPR"

# API Keys
NEON_API_KEY="your-neon-api-key"
VERCEL_TOKEN="your-vercel-token"

# Email Configuration (for notifications)
SMTP_HOST="smtp.gmail.com"
SMTP_PORT="587"
SMTP_USER="<EMAIL>"
SMTP_PASS="your-app-password"

# Security
ENCRYPTION_KEY="your-encryption-key"
JWT_SECRET="your-jwt-secret"

# Feature Flags
ENABLE_NEPAL_LOCALIZATION="true"
ENABLE_COMPLIANCE_MONITORING="true"
ENABLE_BULK_PROCESSING="true"
```

#### Optional Variables

```bash
# Analytics
GOOGLE_ANALYTICS_ID="GA-XXXXXXXXX"
VERCEL_ANALYTICS="true"

# Monitoring
SENTRY_DSN="your-sentry-dsn"
LOG_LEVEL="info"

# Performance
REDIS_URL="redis://localhost:6379"
ENABLE_CACHING="true"

# Backup
BACKUP_SCHEDULE="0 2 * * *"
BACKUP_RETENTION_DAYS="30"
```

## Database Setup

### 1. Neon PostgreSQL Setup

#### Create Neon Project

1. Go to [Neon Console](https://console.neon.tech)
2. Create new project
3. Choose region (closest to Nepal: Singapore)
4. Note down connection details

#### Configure Database

```bash
# Set database URL
export DATABASE_URL="your-neon-connection-string"

# Run migrations
pnpm db:migrate

# Seed initial data
pnpm db:seed
```

### 2. Database Schema

The system uses the following main tables:

```sql
-- Core tables
users                    -- Employee and admin users
attendance              -- Daily attendance records
payroll                 -- Payroll calculations
payroll_components      -- Allowances and deductions

-- Nepal-specific tables
nepali_calendar_config  -- BS/AD calendar mapping
nepal_holidays         -- Public holidays
fiscal_years           -- Fiscal year definitions
labor_law_rules        -- Compliance rules

-- System tables
audit_logs             -- System audit trail
settings               -- System configuration
notifications          -- User notifications
```

### 3. Initial Data Setup

```bash
# Load Nepal calendar data
pnpm db:seed:calendar

# Load public holidays
pnpm db:seed:holidays

# Load labor law rules
pnpm db:seed:labor-laws

# Create admin user
pnpm db:seed:admin
```

## Application Deployment

### Option 1: Vercel Deployment (Recommended)

#### 1. Install Vercel CLI

```bash
npm install -g vercel
```

#### 2. Login to Vercel

```bash
vercel login
```

#### 3. Deploy

```bash
# Deploy to preview
vercel

# Deploy to production
vercel --prod
```

#### 4. Configure Environment Variables

```bash
# Set production environment variables
vercel env add DATABASE_URL production
vercel env add NEXTAUTH_SECRET production
# ... add all required variables
```

#### 5. Custom Domain Setup

```bash
# Add custom domain
vercel domains add your-domain.com

# Configure DNS
# Add CNAME record: your-domain.com -> cname.vercel-dns.com
```

### Option 2: Self-Hosted Deployment

#### 1. Build Application

```bash
# Install dependencies
pnpm install

# Build for production
pnpm build

# Start production server
pnpm start
```

#### 2. Process Manager (PM2)

```bash
# Install PM2
npm install -g pm2

# Create ecosystem file
cat > ecosystem.config.js << EOF
module.exports = {
  apps: [{
    name: 'nepal-payroll',
    script: 'npm',
    args: 'start',
    instances: 'max',
    exec_mode: 'cluster',
    env: {
      NODE_ENV: 'production',
      PORT: 3000
    }
  }]
}
EOF

# Start with PM2
pm2 start ecosystem.config.js
pm2 save
pm2 startup
```

#### 3. Nginx Configuration

```nginx
server {
    listen 80;
    server_name your-domain.com;
    return 301 https://$server_name$request_uri;
}

server {
    listen 443 ssl http2;
    server_name your-domain.com;

    ssl_certificate /path/to/certificate.crt;
    ssl_certificate_key /path/to/private.key;

    location / {
        proxy_pass http://localhost:3000;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_cache_bypass $http_upgrade;
    }
}
```

#### 4. SSL Certificate (Let's Encrypt)

```bash
# Install Certbot
sudo apt install certbot python3-certbot-nginx

# Obtain certificate
sudo certbot --nginx -d your-domain.com

# Auto-renewal
sudo crontab -e
# Add: 0 12 * * * /usr/bin/certbot renew --quiet
```

## Production Configuration

### 1. Security Hardening

#### Environment Security

```bash
# Set secure file permissions
chmod 600 .env.production
chown www-data:www-data .env.production

# Disable debug mode
export NODE_ENV=production
export DEBUG=false
```

#### Database Security

```sql
-- Create dedicated database user
CREATE USER payroll_app WITH PASSWORD 'secure_password';

-- Grant minimal required permissions
GRANT CONNECT ON DATABASE payroll TO payroll_app;
GRANT USAGE ON SCHEMA public TO payroll_app;
GRANT SELECT, INSERT, UPDATE, DELETE ON ALL TABLES IN SCHEMA public TO payroll_app;
GRANT USAGE, SELECT ON ALL SEQUENCES IN SCHEMA public TO payroll_app;
```

#### Application Security

```javascript
// next.config.js security headers
const securityHeaders = [
  {
    key: 'X-DNS-Prefetch-Control',
    value: 'on'
  },
  {
    key: 'Strict-Transport-Security',
    value: 'max-age=63072000; includeSubDomains; preload'
  },
  {
    key: 'X-XSS-Protection',
    value: '1; mode=block'
  },
  {
    key: 'X-Frame-Options',
    value: 'DENY'
  },
  {
    key: 'X-Content-Type-Options',
    value: 'nosniff'
  },
  {
    key: 'Referrer-Policy',
    value: 'origin-when-cross-origin'
  }
]
```

### 2. Performance Optimization

#### Caching Strategy

```javascript
// Redis caching configuration
const cacheConfig = {
  // Payroll calculations cache (1 hour)
  payroll: { ttl: 3600 },
  
  // Employee data cache (30 minutes)
  employees: { ttl: 1800 },
  
  // Settings cache (24 hours)
  settings: { ttl: 86400 },
  
  // Calendar data cache (7 days)
  calendar: { ttl: 604800 }
}
```

#### Database Optimization

```sql
-- Create indexes for performance
CREATE INDEX idx_payroll_employee_period ON payroll(employee_id, period_start, period_end);
CREATE INDEX idx_attendance_employee_date ON attendance(employee_id, date);
CREATE INDEX idx_calendar_ad_date ON nepali_calendar_config(ad_date);
CREATE INDEX idx_holidays_date ON nepal_holidays(date);

-- Analyze tables for query optimization
ANALYZE payroll;
ANALYZE attendance;
ANALYZE nepali_calendar_config;
```

### 3. Monitoring and Logging

#### Application Monitoring

```javascript
// monitoring.js
import { createPrometheusMetrics } from './lib/metrics'

export const metrics = createPrometheusMetrics({
  payrollCalculations: 'counter',
  responseTime: 'histogram',
  activeUsers: 'gauge',
  errorRate: 'counter'
})
```

#### Log Configuration

```javascript
// logger.js
import winston from 'winston'

export const logger = winston.createLogger({
  level: process.env.LOG_LEVEL || 'info',
  format: winston.format.combine(
    winston.format.timestamp(),
    winston.format.errors({ stack: true }),
    winston.format.json()
  ),
  transports: [
    new winston.transports.File({ filename: 'logs/error.log', level: 'error' }),
    new winston.transports.File({ filename: 'logs/combined.log' }),
    new winston.transports.Console({
      format: winston.format.simple()
    })
  ]
})
```

### 4. Backup and Recovery

#### Database Backup

```bash
#!/bin/bash
# backup.sh

DATE=$(date +%Y%m%d_%H%M%S)
BACKUP_DIR="/backups/payroll"
DB_NAME="payroll"

# Create backup directory
mkdir -p $BACKUP_DIR

# Perform backup
pg_dump $DATABASE_URL > $BACKUP_DIR/payroll_backup_$DATE.sql

# Compress backup
gzip $BACKUP_DIR/payroll_backup_$DATE.sql

# Remove old backups (keep 30 days)
find $BACKUP_DIR -name "*.sql.gz" -mtime +30 -delete

# Upload to cloud storage (optional)
aws s3 cp $BACKUP_DIR/payroll_backup_$DATE.sql.gz s3://your-backup-bucket/
```

#### Automated Backup Schedule

```bash
# Add to crontab
0 2 * * * /path/to/backup.sh
```

## Health Checks and Monitoring

### 1. Health Check Endpoint

```javascript
// pages/api/health.js
export default async function handler(req, res) {
  try {
    // Check database connection
    await db.sql`SELECT 1`
    
    // Check external services
    const checks = {
      database: 'healthy',
      timestamp: new Date().toISOString(),
      version: process.env.npm_package_version
    }
    
    res.status(200).json(checks)
  } catch (error) {
    res.status(503).json({
      database: 'unhealthy',
      error: error.message,
      timestamp: new Date().toISOString()
    })
  }
}
```

### 2. Monitoring Dashboard

```bash
# Install monitoring tools
npm install @vercel/analytics
npm install @sentry/nextjs

# Configure monitoring
# Add to next.config.js
const { withSentryConfig } = require('@sentry/nextjs')
```

### 3. Alerting

```javascript
// alerts.js
export const alertConfig = {
  // Database connection failures
  database: {
    threshold: 3,
    window: '5m',
    action: 'email'
  },
  
  // High error rate
  errorRate: {
    threshold: 0.05,
    window: '10m',
    action: 'slack'
  },
  
  // Response time degradation
  responseTime: {
    threshold: 2000,
    window: '15m',
    action: 'email'
  }
}
```

## Maintenance and Updates

### 1. Update Process

```bash
# Create maintenance script
cat > maintenance.sh << EOF
#!/bin/bash

echo "Starting maintenance..."

# Put application in maintenance mode
touch maintenance.flag

# Stop application
pm2 stop nepal-payroll

# Backup database
./backup.sh

# Update code
git pull origin main

# Install dependencies
pnpm install

# Run migrations
pnpm db:migrate

# Build application
pnpm build

# Start application
pm2 start nepal-payroll

# Remove maintenance mode
rm maintenance.flag

echo "Maintenance completed!"
EOF

chmod +x maintenance.sh
```

### 2. Zero-Downtime Deployment

```bash
# Blue-green deployment script
./deploy-blue-green.sh
```

### 3. Rollback Procedure

```bash
# Rollback script
cat > rollback.sh << EOF
#!/bin/bash

# Stop current version
pm2 stop nepal-payroll

# Restore previous version
git checkout HEAD~1

# Restore database if needed
# psql $DATABASE_URL < backup_file.sql

# Restart application
pm2 start nepal-payroll

echo "Rollback completed!"
EOF
```

## Troubleshooting

### Common Issues

1. **Database Connection Issues**
   - Check DATABASE_URL format
   - Verify network connectivity
   - Check Neon project status

2. **Build Failures**
   - Clear node_modules and reinstall
   - Check Node.js version compatibility
   - Verify environment variables

3. **Performance Issues**
   - Monitor database query performance
   - Check memory usage
   - Review caching configuration

### Support and Maintenance

- **Documentation**: Keep this guide updated
- **Monitoring**: Regular health checks
- **Backups**: Verify backup integrity
- **Security**: Regular security updates
- **Performance**: Monitor and optimize

---

For additional deployment support, contact the development team or refer to the technical documentation.
