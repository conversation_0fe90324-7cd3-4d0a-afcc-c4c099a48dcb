import { NextRequest, NextResponse } from "next/server"
import { AuthService } from "@/lib/auth-utils"
import { serverDb } from "@/lib/server-db"
import { z } from "zod"

// Validation schema for sub-task updates
const updateSubTaskSchema = z.object({
  title: z.string().min(1, "Title is required").max(255, "Title too long").optional(),
  description: z.string().optional(),
  assigned_to: z.string().uuid().optional(),
  status: z.enum(["todo", "in_progress", "completed", "cancelled"]).optional(),
  due_date: z.string().datetime().optional(),
  position: z.number().int().min(0).optional(),
})

// GET /api/tasks/[id]/subtasks/[subtaskId] - Get specific sub-task
export async function GET(request: NextRequest, { params }: { params: { id: string, subtaskId: string } }) {
  try {
    const sessionToken = request.cookies.get("session-token")?.value

    if (!sessionToken) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 })
    }

    const user = await AuthService.verifySession(sessionToken)

    if (!user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 })
    }

    const { id: taskId, subtaskId } = params

    // Get sub-task with parent task info
    const subTaskResult = await serverDb.sql`
      SELECT 
        st.*,
        assigned_user.full_name as assigned_to_name,
        assigned_user.email as assigned_to_email,
        assigned_user.employee_id as assigned_to_employee_id,
        assigned_by_user.full_name as assigned_by_name,
        t.assigned_to as parent_assigned_to,
        t.assigned_by as parent_assigned_by
      FROM sub_tasks st
      LEFT JOIN users assigned_user ON st.assigned_to = assigned_user.id
      LEFT JOIN users assigned_by_user ON st.assigned_by = assigned_by_user.id
      LEFT JOIN tasks t ON st.parent_task_id = t.id
      WHERE st.id = ${subtaskId} AND st.parent_task_id = ${taskId}
    `

    if (subTaskResult.length === 0) {
      return NextResponse.json({ error: "Sub-task not found" }, { status: 404 })
    }

    const subTask = subTaskResult[0]

    // Check if user is assigned to parent task via task_assignments
    const isAssignedToParent = await serverDb.sql`
      SELECT EXISTS (
        SELECT 1 FROM task_assignments 
        WHERE task_id = ${taskId} AND user_id = ${user.id}
      )
    `

    // Check access permissions
    const hasAccess = 
      ["admin", "hr_manager"].includes(user.role) ||
      subTask.assigned_to === user.id ||
      subTask.assigned_by === user.id ||
      subTask.parent_assigned_to === user.id ||
      subTask.parent_assigned_by === user.id ||
      isAssignedToParent[0].exists

    if (!hasAccess) {
      return NextResponse.json({ error: "Access denied" }, { status: 403 })
    }

    return NextResponse.json({
      success: true,
      data: subTask
    })

  } catch (error) {
    console.error("Get sub-task error:", error)
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    )
  }
}

// PUT /api/tasks/[id]/subtasks/[subtaskId] - Update sub-task
export async function PUT(request: NextRequest, { params }: { params: { id: string, subtaskId: string } }) {
  try {
    const sessionToken = request.cookies.get("session-token")?.value

    if (!sessionToken) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 })
    }

    const user = await AuthService.verifySession(sessionToken)

    if (!user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 })
    }

    const { id: taskId, subtaskId } = params

    // Check if sub-task exists
    const existingSubTaskResult = await serverDb.sql`
      SELECT 
        st.*,
        t.assigned_to as parent_assigned_to,
        t.assigned_by as parent_assigned_by
      FROM sub_tasks st
      LEFT JOIN tasks t ON st.parent_task_id = t.id
      WHERE st.id = ${subtaskId} AND st.parent_task_id = ${taskId}
    `

    if (existingSubTaskResult.length === 0) {
      return NextResponse.json({ error: "Sub-task not found" }, { status: 404 })
    }

    const existingSubTask = existingSubTaskResult[0]

    // Check if user is assigned to parent task via task_assignments
    const isAssignedToParent = await serverDb.sql`
      SELECT EXISTS (
        SELECT 1 FROM task_assignments 
        WHERE task_id = ${taskId} AND user_id = ${user.id}
      )
    `

    // Check permissions
    const canEdit = 
      ["admin", "hr_manager"].includes(user.role) ||
      existingSubTask.assigned_to === user.id ||
      existingSubTask.assigned_by === user.id ||
      existingSubTask.parent_assigned_to === user.id ||
      existingSubTask.parent_assigned_by === user.id ||
      isAssignedToParent[0].exists

    if (!canEdit) {
      return NextResponse.json({ error: "Access denied" }, { status: 403 })
    }

    // Parse and validate request body
    const body = await request.json()
    const validatedData = updateSubTaskSchema.parse(body)

    // Check if user can assign sub-tasks to others
    if (validatedData.assigned_to && validatedData.assigned_to !== user.id) {
      if (!["admin", "hr_manager"].includes(user.role)) {
        return NextResponse.json(
          { error: "You can only assign sub-tasks to yourself" },
          { status: 403 }
        )
      }

      // Validate that the assigned user exists and is active
      const assignedUserResult = await serverDb.sql`
        SELECT id FROM users 
        WHERE id = ${validatedData.assigned_to} AND is_active = true
      `

      if (assignedUserResult.length === 0) {
        return NextResponse.json(
          { error: "Assigned user not found or inactive" },
          { status: 400 }
        )
      }
    }

    // Build update query dynamically
    const updateFields = []
    const updateValues = []
    let paramIndex = 1

    if (validatedData.title !== undefined) {
      updateFields.push(`title = $${paramIndex}`)
      updateValues.push(validatedData.title)
      paramIndex++
    }

    if (validatedData.description !== undefined) {
      updateFields.push(`description = $${paramIndex}`)
      updateValues.push(validatedData.description)
      paramIndex++
    }

    if (validatedData.assigned_to !== undefined) {
      updateFields.push(`assigned_to = $${paramIndex}`)
      updateValues.push(validatedData.assigned_to)
      paramIndex++
    }

    if (validatedData.status !== undefined) {
      updateFields.push(`status = $${paramIndex}`)
      updateValues.push(validatedData.status)
      paramIndex++

      // Set completed_at when status changes to completed
      if (validatedData.status === 'completed') {
        updateFields.push(`completed_at = NOW()`)
      } else if (existingSubTask.status === 'completed' && validatedData.status !== 'completed') {
        updateFields.push(`completed_at = NULL`)
      }
    }

    if (validatedData.due_date !== undefined) {
      updateFields.push(`due_date = $${paramIndex}`)
      updateValues.push(validatedData.due_date)
      paramIndex++
    }

    if (validatedData.position !== undefined) {
      updateFields.push(`position = $${paramIndex}`)
      updateValues.push(validatedData.position)
      paramIndex++
    }

    // Always update the updated_at field
    updateFields.push(`updated_at = NOW()`)

    if (updateFields.length === 1) { // Only updated_at field
      return NextResponse.json({ error: "No fields to update" }, { status: 400 })
    }

    // Add the subtask ID for the WHERE clause
    updateValues.push(subtaskId)
    const whereParamIndex = paramIndex

    const updateQuery = `
      UPDATE sub_tasks 
      SET ${updateFields.join(', ')}
      WHERE id = $${whereParamIndex}
      RETURNING *
    `

    // Execute the update using raw query since we need dynamic parameters
    const updateResult = await serverDb.sql.query(updateQuery, updateValues)

    // Get the complete updated sub-task data
    const completeSubTaskResult = await serverDb.sql`
      SELECT 
        st.*,
        assigned_user.full_name as assigned_to_name,
        assigned_user.email as assigned_to_email,
        assigned_user.employee_id as assigned_to_employee_id,
        assigned_by_user.full_name as assigned_by_name
      FROM sub_tasks st
      LEFT JOIN users assigned_user ON st.assigned_to = assigned_user.id
      LEFT JOIN users assigned_by_user ON st.assigned_by = assigned_by_user.id
      WHERE st.id = ${subtaskId}
    `

    return NextResponse.json({
      success: true,
      data: completeSubTaskResult[0],
      message: "Sub-task updated successfully"
    })

  } catch (error) {
    console.error("Update sub-task error:", error)
    
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: "Invalid sub-task data", details: error.errors },
        { status: 400 }
      )
    }

    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    )
  }
}

// DELETE /api/tasks/[id]/subtasks/[subtaskId] - Delete sub-task
export async function DELETE(request: NextRequest, { params }: { params: { id: string, subtaskId: string } }) {
  try {
    const sessionToken = request.cookies.get("session-token")?.value

    if (!sessionToken) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 })
    }

    const user = await AuthService.verifySession(sessionToken)

    if (!user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 })
    }

    const { id: taskId, subtaskId } = params

    // Check if sub-task exists
    const existingSubTaskResult = await serverDb.sql`
      SELECT 
        st.*,
        t.assigned_to as parent_assigned_to,
        t.assigned_by as parent_assigned_by
      FROM sub_tasks st
      LEFT JOIN tasks t ON st.parent_task_id = t.id
      WHERE st.id = ${subtaskId} AND st.parent_task_id = ${taskId}
    `

    if (existingSubTaskResult.length === 0) {
      return NextResponse.json({ error: "Sub-task not found" }, { status: 404 })
    }

    const existingSubTask = existingSubTaskResult[0]

    // Check if user is assigned to parent task via task_assignments
    const isAssignedToParent = await serverDb.sql`
      SELECT EXISTS (
        SELECT 1 FROM task_assignments 
        WHERE task_id = ${taskId} AND user_id = ${user.id}
      )
    `

    // Check permissions - only admin, hr_manager, sub-task creator, or parent task creator can delete
    const canDelete = 
      ["admin", "hr_manager"].includes(user.role) ||
      existingSubTask.assigned_by === user.id ||
      existingSubTask.parent_assigned_by === user.id

    if (!canDelete) {
      return NextResponse.json({ 
        error: "You don't have permission to delete this sub-task" 
      }, { status: 403 })
    }

    // Delete the sub-task
    await serverDb.sql`
      DELETE FROM sub_tasks WHERE id = ${subtaskId}
    `

    return NextResponse.json({
      success: true,
      message: "Sub-task deleted successfully"
    })

  } catch (error) {
    console.error("Delete sub-task error:", error)
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    )
  }
}
