#!/usr/bin/env node

/**
 * Test script to verify clockIn and clockOut functions work correctly
 * after the database schema migration
 */

require('dotenv').config({ path: '.env.local' });
const { neon } = require('@neondatabase/serverless');

async function testAttendanceFunctions() {
  try {
    const DATABASE_URL = process.env.DATABASE_URL;
    if (!DATABASE_URL) {
      throw new Error('DATABASE_URL not found in environment variables');
    }

    const sql = neon(DATABASE_URL);
    
    console.log('🧪 Testing Attendance Functions After Schema Fix');
    console.log('================================================\n');
    
    // Step 1: Get a test user
    console.log('1. Finding test user...');
    const users = await sql`SELECT id, email, full_name FROM users LIMIT 1`;
    if (users.length === 0) {
      console.log('❌ No users found in database');
      return;
    }
    
    const testUser = users[0];
    console.log(`✅ Using test user: ${testUser.email} (${testUser.id})`);
    
    // Step 2: Clean up any existing attendance for today
    const today = new Date().toISOString().split('T')[0];
    console.log(`\n2. Cleaning up existing attendance for ${today}...`);
    
    const existingRecords = await sql`
      SELECT * FROM attendance 
      WHERE user_id = ${testUser.id} AND date = ${today}
    `;
    
    if (existingRecords.length > 0) {
      console.log(`Found ${existingRecords.length} existing records, cleaning up...`);
      await sql`DELETE FROM attendance WHERE user_id = ${testUser.id} AND date = ${today}`;
      console.log('✅ Cleaned up existing records');
    } else {
      console.log('✅ No existing records to clean up');
    }
    
    // Step 3: Import and test the clockIn function
    console.log('\n3. Testing clockIn function...');
    
    try {
      // Import the database functions
      const { db } = require('../lib/neon');
      
      // Test clock in
      const clockInResult = await db.clockIn(testUser.id, 'Test clock in after schema fix');
      console.log('✅ Clock in successful!');
      console.log(`   ID: ${clockInResult.id}`);
      console.log(`   Check-in time: ${clockInResult.check_in_time}`);
      console.log(`   Status: ${clockInResult.status}`);
      console.log(`   Daily sequence: ${clockInResult.daily_sequence}`);
      console.log(`   Entry type: ${clockInResult.entry_type}`);
      console.log(`   Is active: ${clockInResult.is_active}`);
      
      // Step 4: Test clockOut function
      console.log('\n4. Testing clockOut function...');
      
      // Wait a moment to ensure different timestamps
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      const clockOutResult = await db.clockOut(testUser.id, 'Test clock out after schema fix');
      console.log('✅ Clock out successful!');
      console.log(`   ID: ${clockOutResult.id}`);
      console.log(`   Check-out time: ${clockOutResult.check_out_time}`);
      console.log(`   Hours worked: ${clockOutResult.hours_worked}`);
      console.log(`   Is active: ${clockOutResult.is_active}`);
      
      // Step 5: Test multiple sessions
      console.log('\n5. Testing multiple daily sessions...');
      
      // Second check-in
      const clockIn2 = await db.clockIn(testUser.id, 'Second session test');
      console.log('✅ Second clock in successful!');
      console.log(`   Daily sequence: ${clockIn2.daily_sequence}`);
      console.log(`   Is active: ${clockIn2.is_active}`);
      
      // Second check-out
      await new Promise(resolve => setTimeout(resolve, 1000));
      const clockOut2 = await db.clockOut(testUser.id, 'Second session end');
      console.log('✅ Second clock out successful!');
      console.log(`   Hours worked: ${clockOut2.hours_worked}`);
      console.log(`   Is active: ${clockOut2.is_active}`);
      
      // Step 6: Verify attendance status function
      console.log('\n6. Testing attendance status function...');
      
      const attendanceStatus = await db.getCurrentAttendanceStatus(testUser.id);
      console.log('✅ Attendance status retrieved!');
      console.log(`   Is checked in: ${attendanceStatus.isCheckedIn}`);
      console.log(`   Today entries: ${attendanceStatus.todayEntries.length}`);
      console.log(`   Total hours today: ${attendanceStatus.totalHoursToday}`);
      console.log(`   Remaining check-ins: ${attendanceStatus.remainingCheckIns}`);
      console.log(`   Active session: ${attendanceStatus.activeSession ? 'Yes' : 'No'}`);
      
      // Step 7: Test error scenarios
      console.log('\n7. Testing error scenarios...');
      
      // Try to clock out when not clocked in
      try {
        await db.clockOut(testUser.id, 'Should fail');
        console.log('❌ Clock out should have failed');
      } catch (error) {
        console.log('✅ Clock out correctly failed when not clocked in');
        console.log(`   Error: ${error.message}`);
      }
      
      // Clock in again
      const clockIn3 = await db.clockIn(testUser.id, 'Third session');
      console.log('✅ Third clock in successful');
      
      // Try to clock in when already clocked in
      try {
        await db.clockIn(testUser.id, 'Should fail');
        console.log('❌ Clock in should have failed');
      } catch (error) {
        console.log('✅ Clock in correctly failed when already clocked in');
        console.log(`   Error: ${error.message}`);
      }
      
      // Clean up - clock out the active session
      await db.clockOut(testUser.id, 'Final cleanup');
      console.log('✅ Cleaned up active session');
      
    } catch (error) {
      console.error('❌ Function test failed:', error);
      throw error;
    }
    
    // Step 8: Final verification
    console.log('\n8. Final verification...');
    const finalRecords = await sql`
      SELECT 
        id, 
        check_in_time, 
        check_out_time, 
        daily_sequence, 
        entry_type, 
        is_active, 
        hours_worked,
        status
      FROM attendance 
      WHERE user_id = ${testUser.id} AND date = ${today}
      ORDER BY daily_sequence
    `;
    
    console.log(`✅ Created ${finalRecords.length} attendance records:`);
    finalRecords.forEach((record, index) => {
      console.log(`   ${index + 1}. Sequence ${record.daily_sequence}: ${record.status} (${record.hours_worked}h) - Active: ${record.is_active}`);
    });
    
    console.log('\n🎉 All tests passed successfully!');
    console.log('\n✅ The attendance system is now working correctly:');
    console.log('   ✅ Clock in functionality works');
    console.log('   ✅ Clock out functionality works');
    console.log('   ✅ Multiple daily sessions supported');
    console.log('   ✅ Active session tracking works');
    console.log('   ✅ Error handling works correctly');
    console.log('   ✅ Database schema is properly configured');
    
    console.log('\n🚀 You can now test the Check In/Check Out buttons on the dashboard!');
    
  } catch (error) {
    console.error('❌ Test failed:', error);
    console.error('\nThis indicates there may still be issues with:');
    console.error('  1. Database schema');
    console.error('  2. Function implementation');
    console.error('  3. Database connection');
    process.exit(1);
  }
}

// Run the test
if (require.main === module) {
  testAttendanceFunctions();
}

module.exports = { testAttendanceFunctions };
