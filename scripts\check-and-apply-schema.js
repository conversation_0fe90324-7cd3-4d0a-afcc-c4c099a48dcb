const { neon } = require('@neondatabase/serverless');
const fs = require('fs');

// Load environment variables
require('dotenv').config({ path: '.env.local' });

const sql = neon(process.env.DATABASE_URL);

async function checkAndApplySchema() {
  try {
    console.log('Checking existing tables...');
    
    // Check if user_files table exists
    const userFilesExists = await sql`
      SELECT EXISTS (
        SELECT 1 FROM information_schema.tables 
        WHERE table_schema = 'public' AND table_name = 'user_files'
      )
    `;
    
    console.log('user_files table exists:', userFilesExists[0].exists);
    
    // Check existing task-related tables
    const taskTables = await sql`
      SELECT table_name 
      FROM information_schema.tables 
      WHERE table_schema = 'public' 
      AND table_name LIKE '%task%'
      ORDER BY table_name
    `;
    
    console.log('Existing task tables:', taskTables.map(t => t.table_name));
    
    // If user_files doesn't exist, we need to create it first or modify our schema
    if (!userFilesExists[0].exists) {
      console.log('\n⚠️  user_files table does not exist. Creating modified schema...');
      
      // Create a modified schema without the user_files dependency
      const modifiedSchema = `
        -- Create task_assignments table for many-to-many user assignments
        CREATE TABLE IF NOT EXISTS task_assignments (
            id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
            task_id UUID NOT NULL REFERENCES tasks(id) ON DELETE CASCADE,
            user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
            assigned_by UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
            assigned_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
            is_primary BOOLEAN DEFAULT false,
            created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
            
            CONSTRAINT task_assignments_unique UNIQUE(task_id, user_id)
        );

        -- Create sub_tasks table for hierarchical task management
        CREATE TABLE IF NOT EXISTS sub_tasks (
            id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
            parent_task_id UUID NOT NULL REFERENCES tasks(id) ON DELETE CASCADE,
            title VARCHAR(255) NOT NULL,
            description TEXT,
            status VARCHAR(20) NOT NULL DEFAULT 'todo' CHECK (status IN ('todo', 'in_progress', 'completed', 'cancelled')),
            assigned_to UUID REFERENCES users(id) ON DELETE SET NULL,
            assigned_by UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
            due_date TIMESTAMP WITH TIME ZONE,
            position INTEGER DEFAULT 0,
            completed_at TIMESTAMP WITH TIME ZONE,
            created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
            updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
        );

        -- Create simplified task_attachments table without user_files dependency
        CREATE TABLE IF NOT EXISTS task_attachments (
            id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
            task_id UUID NOT NULL REFERENCES tasks(id) ON DELETE CASCADE,
            filename VARCHAR(255) NOT NULL,
            original_filename VARCHAR(255) NOT NULL,
            file_path TEXT NOT NULL,
            file_size BIGINT NOT NULL,
            mime_type VARCHAR(100) NOT NULL,
            uploaded_by UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
            attachment_type VARCHAR(50) DEFAULT 'document' CHECK (attachment_type IN ('document', 'image', 'video', 'other')),
            description TEXT,
            created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
        );

        -- Add missing columns to existing tasks table
        DO $$ 
        BEGIN
            IF NOT EXISTS (SELECT 1 FROM information_schema.columns 
                           WHERE table_name = 'tasks' AND column_name = 'actual_hours') THEN
                ALTER TABLE tasks ADD COLUMN actual_hours DECIMAL(5,2) DEFAULT 0;
            END IF;
            
            IF NOT EXISTS (SELECT 1 FROM information_schema.columns 
                           WHERE table_name = 'tasks' AND column_name = 'tags') THEN
                ALTER TABLE tasks ADD COLUMN tags TEXT[];
            END IF;
            
            IF NOT EXISTS (SELECT 1 FROM information_schema.columns 
                           WHERE table_name = 'tasks' AND column_name = 'completion_percentage') THEN
                ALTER TABLE tasks ADD COLUMN completion_percentage INTEGER DEFAULT 0 CHECK (completion_percentage >= 0 AND completion_percentage <= 100);
            END IF;
        END $$;

        -- Create indexes
        CREATE INDEX IF NOT EXISTS idx_task_assignments_task_id ON task_assignments(task_id);
        CREATE INDEX IF NOT EXISTS idx_task_assignments_user_id ON task_assignments(user_id);
        CREATE INDEX IF NOT EXISTS idx_task_assignments_primary ON task_assignments(task_id, is_primary) WHERE is_primary = true;

        CREATE INDEX IF NOT EXISTS idx_sub_tasks_parent_task_id ON sub_tasks(parent_task_id);
        CREATE INDEX IF NOT EXISTS idx_sub_tasks_assigned_to ON sub_tasks(assigned_to);
        CREATE INDEX IF NOT EXISTS idx_sub_tasks_status ON sub_tasks(status);
        CREATE INDEX IF NOT EXISTS idx_sub_tasks_position ON sub_tasks(parent_task_id, position);

        CREATE INDEX IF NOT EXISTS idx_task_attachments_task_id ON task_attachments(task_id);
      `;
      
      console.log('Applying modified schema...');
      await sql.query(modifiedSchema);
      console.log('✅ Modified schema applied successfully!');
    } else {
      console.log('\nApplying full schema with user_files integration...');
      const schema = fs.readFileSync('./scripts/enhanced-task-management-schema.sql', 'utf8');
      await sql.query(schema);
      console.log('✅ Full schema applied successfully!');
    }
    
    // Verify the new tables exist
    console.log('\nVerifying new tables...');
    const newTables = await sql`
      SELECT table_name 
      FROM information_schema.tables 
      WHERE table_schema = 'public' 
      AND table_name IN ('task_assignments', 'sub_tasks', 'task_attachments')
      ORDER BY table_name
    `;
    
    console.log('New tables created:', newTables.map(t => t.table_name));
    
    // Check tasks table columns
    const tasksColumns = await sql`
      SELECT column_name, data_type
      FROM information_schema.columns 
      WHERE table_schema = 'public' AND table_name = 'tasks'
      AND column_name IN ('actual_hours', 'tags', 'completion_percentage')
      ORDER BY column_name
    `;
    
    console.log('New task columns:', tasksColumns.map(c => `${c.column_name} (${c.data_type})`));
    
  } catch (error) {
    console.error('❌ Error:', error.message);
    console.error('Full error:', error);
    process.exit(1);
  }
}

checkAndApplySchema();
