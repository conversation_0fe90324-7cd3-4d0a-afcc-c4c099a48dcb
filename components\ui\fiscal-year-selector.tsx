// Fiscal Year Selector Component
// Phase 3: Nepal Localization Implementation - Fiscal Year Management

"use client"

import React, { useState, useEffect } from 'react'
import { Calendar, ChevronDown, Clock, TrendingUp } from 'lucide-react'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Badge } from '@/components/ui/badge'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { Progress } from '@/components/ui/progress'
import { fiscalYearManager, FiscalYear, PayrollPeriod } from '@/lib/fiscal-year-manager'
import { NepaliCalendar } from '@/lib/nepali-calendar'
import { cn } from '@/lib/utils'

interface FiscalYearSelectorProps {
  value?: string
  onChange?: (fiscalYear: string) => void
  showDetails?: boolean
  showPayrollPeriods?: boolean
  className?: string
}

interface PayrollPeriodSelectorProps {
  fiscalYear: string
  value?: PayrollPeriod
  onChange?: (period: PayrollPeriod) => void
  periodType?: 'monthly' | 'quarterly' | 'yearly'
  className?: string
}

export function FiscalYearSelector({
  value,
  onChange,
  showDetails = false,
  showPayrollPeriods = false,
  className
}: FiscalYearSelectorProps) {
  const [selectedFY, setSelectedFY] = useState<string>(value || fiscalYearManager.getCurrentFiscalYear())
  const [fiscalYearData, setFiscalYearData] = useState<FiscalYear | null>(null)
  const [availableFYs, setAvailableFYs] = useState<string[]>([])

  useEffect(() => {
    const fyList = fiscalYearManager.getAvailableFiscalYears(7)
    setAvailableFYs(fyList)
  }, [])

  useEffect(() => {
    if (selectedFY) {
      const fyData = fiscalYearManager.generateFiscalYear(selectedFY)
      setFiscalYearData(fyData)
    }
  }, [selectedFY])

  const handleFYChange = (newFY: string) => {
    setSelectedFY(newFY)
    onChange?.(newFY)
  }

  const calculateProgress = () => {
    if (!fiscalYearData) return 0
    
    const today = new Date()
    const totalDays = Math.ceil((fiscalYearData.adEndDate.getTime() - fiscalYearData.adStartDate.getTime()) / (1000 * 60 * 60 * 24))
    const daysPassed = Math.ceil((today.getTime() - fiscalYearData.adStartDate.getTime()) / (1000 * 60 * 60 * 24))
    
    return Math.max(0, Math.min(100, (daysPassed / totalDays) * 100))
  }

  const getCurrentQuarter = () => {
    if (!fiscalYearData) return null
    
    const today = new Date()
    const bsToday = NepaliCalendar.adToBS(today)
    
    return fiscalYearData.quarters.find(q => q.months.includes(bsToday.month))
  }

  const getCurrentMonth = () => {
    if (!fiscalYearData) return null
    
    return fiscalYearData.months.find(m => m.isCurrentMonth)
  }

  const progress = calculateProgress()
  const currentQuarter = getCurrentQuarter()
  const currentMonth = getCurrentMonth()

  return (
    <Card className={cn("w-full", className)}>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Calendar className="h-5 w-5" />
          Fiscal Year Selection
        </CardTitle>
      </CardHeader>
      
      <CardContent className="space-y-4">
        {/* Fiscal Year Selector */}
        <div className="space-y-2">
          <label className="text-sm font-medium">Select Fiscal Year</label>
          <Select value={selectedFY} onValueChange={handleFYChange}>
            <SelectTrigger>
              <SelectValue placeholder="Choose fiscal year" />
            </SelectTrigger>
            <SelectContent>
              {availableFYs.map((fy) => {
                const isCurrentFY = fy === fiscalYearManager.getCurrentFiscalYear()
                return (
                  <SelectItem key={fy} value={fy}>
                    <div className="flex items-center gap-2">
                      <span>{fiscalYearManager.formatFiscalYear(fy)}</span>
                      {isCurrentFY && (
                        <Badge variant="default" className="text-xs">Current</Badge>
                      )}
                    </div>
                  </SelectItem>
                )
              })}
            </SelectContent>
          </Select>
        </div>

        {/* Fiscal Year Details */}
        {showDetails && fiscalYearData && (
          <div className="space-y-4">
            {/* Basic Info */}
            <div className="grid grid-cols-2 gap-4 text-sm">
              <div>
                <div className="font-medium text-muted-foreground">Start Date</div>
                <div>{fiscalYearData.adStartDate.toLocaleDateString()}</div>
                <div className="text-xs text-muted-foreground">
                  {NepaliCalendar.formatBSDate(fiscalYearData.bsStartDate)} BS
                </div>
              </div>
              <div>
                <div className="font-medium text-muted-foreground">End Date</div>
                <div>{fiscalYearData.adEndDate.toLocaleDateString()}</div>
                <div className="text-xs text-muted-foreground">
                  {NepaliCalendar.formatBSDate(fiscalYearData.bsEndDate)} BS
                </div>
              </div>
            </div>

            {/* Progress */}
            {fiscalYearData.isCurrent && (
              <div className="space-y-2">
                <div className="flex justify-between text-sm">
                  <span className="font-medium">Fiscal Year Progress</span>
                  <span>{progress.toFixed(1)}%</span>
                </div>
                <Progress value={progress} className="h-2" />
              </div>
            )}

            {/* Statistics */}
            <div className="grid grid-cols-3 gap-4">
              <div className="text-center p-3 bg-muted rounded-lg">
                <div className="text-2xl font-bold">{fiscalYearData.totalDays}</div>
                <div className="text-xs text-muted-foreground">Total Days</div>
              </div>
              <div className="text-center p-3 bg-muted rounded-lg">
                <div className="text-2xl font-bold text-green-600">{fiscalYearData.workingDays}</div>
                <div className="text-xs text-muted-foreground">Working Days</div>
              </div>
              <div className="text-center p-3 bg-muted rounded-lg">
                <div className="text-2xl font-bold text-red-600">{fiscalYearData.holidays}</div>
                <div className="text-xs text-muted-foreground">Holidays</div>
              </div>
            </div>

            {/* Current Period Info */}
            {fiscalYearData.isCurrent && (
              <div className="space-y-2">
                <div className="font-medium text-sm">Current Period</div>
                <div className="flex gap-2">
                  {currentQuarter && (
                    <Badge variant="outline">
                      {currentQuarter.name}
                    </Badge>
                  )}
                  {currentMonth && (
                    <Badge variant="default">
                      {currentMonth.monthName} {currentMonth.bsYear}
                    </Badge>
                  )}
                </div>
              </div>
            )}
          </div>
        )}

        {/* Payroll Periods */}
        {showPayrollPeriods && fiscalYearData && (
          <PayrollPeriodSelector
            fiscalYear={selectedFY}
            periodType="monthly"
          />
        )}
      </CardContent>
    </Card>
  )
}

export function PayrollPeriodSelector({
  fiscalYear,
  value,
  onChange,
  periodType = 'monthly',
  className
}: PayrollPeriodSelectorProps) {
  const [periods, setPeriods] = useState<PayrollPeriod[]>([])
  const [selectedPeriod, setSelectedPeriod] = useState<PayrollPeriod | undefined>(value)

  useEffect(() => {
    const loadPeriods = async () => {
      const payrollPeriods = await fiscalYearManager.createPayrollPeriods(fiscalYear)
      const filteredPeriods = payrollPeriods.filter(p => p.type === periodType)
      setPeriods(filteredPeriods)
      
      // Auto-select current period if none selected
      if (!selectedPeriod && periodType === 'monthly') {
        const currentPeriod = fiscalYearManager.getCurrentPayrollPeriod('monthly')
        if (currentPeriod) {
          setSelectedPeriod(currentPeriod)
          onChange?.(currentPeriod)
        }
      }
    }
    
    loadPeriods()
  }, [fiscalYear, periodType])

  const handlePeriodChange = (periodId: string) => {
    const period = periods.find(p => p.id === periodId)
    if (period) {
      setSelectedPeriod(period)
      onChange?.(period)
    }
  }

  const getPeriodStatus = (period: PayrollPeriod) => {
    if (period.payrollProcessed) return { label: 'Processed', variant: 'default' as const }
    if (period.isClosed) return { label: 'Closed', variant: 'secondary' as const }
    return { label: 'Active', variant: 'outline' as const }
  }

  return (
    <div className={cn("space-y-4", className)}>
      <div className="space-y-2">
        <label className="text-sm font-medium">
          Select {periodType.charAt(0).toUpperCase() + periodType.slice(1)} Period
        </label>
        
        <Select 
          value={selectedPeriod?.id || ''} 
          onValueChange={handlePeriodChange}
        >
          <SelectTrigger>
            <SelectValue placeholder={`Choose ${periodType} period`} />
          </SelectTrigger>
          <SelectContent>
            {periods.map((period) => {
              const status = getPeriodStatus(period)
              return (
                <SelectItem key={period.id} value={period.id}>
                  <div className="flex items-center justify-between w-full">
                    <span>{period.name}</span>
                    <Badge variant={status.variant} className="ml-2 text-xs">
                      {status.label}
                    </Badge>
                  </div>
                </SelectItem>
              )
            })}
          </SelectContent>
        </Select>
      </div>

      {/* Selected Period Details */}
      {selectedPeriod && (
        <div className="p-4 bg-muted rounded-lg space-y-3">
          <div className="flex items-center justify-between">
            <h4 className="font-medium">{selectedPeriod.name}</h4>
            <Badge variant={getPeriodStatus(selectedPeriod).variant}>
              {getPeriodStatus(selectedPeriod).label}
            </Badge>
          </div>
          
          <div className="grid grid-cols-2 gap-4 text-sm">
            <div>
              <div className="font-medium text-muted-foreground">Period</div>
              <div>{selectedPeriod.adStartDate.toLocaleDateString()} - {selectedPeriod.adEndDate.toLocaleDateString()}</div>
              <div className="text-xs text-muted-foreground">
                {NepaliCalendar.formatBSDate(selectedPeriod.bsStartDate)} - {NepaliCalendar.formatBSDate(selectedPeriod.bsEndDate)} BS
              </div>
            </div>
            <div>
              <div className="font-medium text-muted-foreground">Working Days</div>
              <div className="flex items-center gap-2">
                <span className="text-lg font-semibold text-green-600">{selectedPeriod.workingDays}</span>
                {selectedPeriod.holidays > 0 && (
                  <span className="text-xs text-muted-foreground">
                    ({selectedPeriod.holidays} holidays)
                  </span>
                )}
              </div>
            </div>
          </div>

          {selectedPeriod.employeeCount !== undefined && (
            <div className="flex items-center gap-4 text-sm">
              <div className="flex items-center gap-1">
                <Clock className="h-4 w-4 text-muted-foreground" />
                <span>{selectedPeriod.employeeCount} employees</span>
              </div>
              {selectedPeriod.totalPayroll !== undefined && (
                <div className="flex items-center gap-1">
                  <TrendingUp className="h-4 w-4 text-muted-foreground" />
                  <span>NPR {selectedPeriod.totalPayroll.toLocaleString()}</span>
                </div>
              )}
            </div>
          )}
        </div>
      )}
    </div>
  )
}

export function FiscalYearOverview({ fiscalYear }: { fiscalYear: string }) {
  const [fyData, setFyData] = useState<FiscalYear | null>(null)

  useEffect(() => {
    const data = fiscalYearManager.generateFiscalYear(fiscalYear)
    setFyData(data)
  }, [fiscalYear])

  if (!fyData) return null

  return (
    <div className="space-y-6">
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card>
          <CardContent className="p-4 text-center">
            <div className="text-2xl font-bold">{fyData.totalDays}</div>
            <div className="text-sm text-muted-foreground">Total Days</div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4 text-center">
            <div className="text-2xl font-bold text-green-600">{fyData.workingDays}</div>
            <div className="text-sm text-muted-foreground">Working Days</div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4 text-center">
            <div className="text-2xl font-bold text-red-600">{fyData.holidays}</div>
            <div className="text-sm text-muted-foreground">Holidays</div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4 text-center">
            <div className="text-2xl font-bold text-blue-600">{fyData.quarters.length}</div>
            <div className="text-sm text-muted-foreground">Quarters</div>
          </CardContent>
        </Card>
      </div>

      <Tabs defaultValue="quarters" className="w-full">
        <TabsList className="grid w-full grid-cols-2">
          <TabsTrigger value="quarters">Quarters</TabsTrigger>
          <TabsTrigger value="months">Months</TabsTrigger>
        </TabsList>
        
        <TabsContent value="quarters" className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {fyData.quarters.map((quarter) => (
              <Card key={quarter.id}>
                <CardHeader className="pb-3">
                  <CardTitle className="text-lg">{quarter.name}</CardTitle>
                </CardHeader>
                <CardContent className="space-y-2">
                  <div className="text-sm">
                    <div className="font-medium">Period:</div>
                    <div className="text-muted-foreground">
                      {quarter.adStartDate.toLocaleDateString()} - {quarter.adEndDate.toLocaleDateString()}
                    </div>
                  </div>
                  <div className="flex justify-between text-sm">
                    <span>Working Days:</span>
                    <span className="font-medium">{quarter.workingDays}</span>
                  </div>
                  <div className="flex justify-between text-sm">
                    <span>Holidays:</span>
                    <span className="font-medium">{quarter.holidays}</span>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </TabsContent>
        
        <TabsContent value="months" className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            {fyData.months.map((month) => (
              <Card key={month.id} className={month.isCurrentMonth ? 'ring-2 ring-primary' : ''}>
                <CardHeader className="pb-3">
                  <CardTitle className="text-base flex items-center justify-between">
                    {month.monthName} {month.bsYear}
                    {month.isCurrentMonth && (
                      <Badge variant="default" className="text-xs">Current</Badge>
                    )}
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-2">
                  <div className="text-xs text-muted-foreground">
                    {month.monthNameNepali}
                  </div>
                  <div className="flex justify-between text-sm">
                    <span>Working Days:</span>
                    <span className="font-medium">{month.workingDays}</span>
                  </div>
                  <div className="flex justify-between text-sm">
                    <span>Holidays:</span>
                    <span className="font-medium">{month.holidays}</span>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </TabsContent>
      </Tabs>
    </div>
  )
}

export default FiscalYearSelector
