#!/usr/bin/env node

// Simple test for login API
const http = require('http');

async function testLoginAPI() {
  console.log('🔐 Testing Login API...\n');
  
  try {
    console.log('🔍 Testing login with admin credentials...');
    
    const response = await makePostRequest('/api/auth/login', {
      email: '<EMAIL>',
      password: 'admin123'
    });
    
    console.log(`📤 Response status: ${response.statusCode}`);
    console.log(`📤 Response data: ${response.data}`);
    
    if (response.statusCode === 200) {
      console.log('✅ Login API: Working correctly!');
      
      const data = JSON.parse(response.data);
      if (data.success && data.user) {
        console.log(`✅ User authenticated: ${data.user.email} (${data.user.role})`);
      }
      
      // Check for session cookie
      const cookies = response.headers['set-cookie'] || [];
      const sessionCookie = cookies.find(cookie => cookie.includes('session-token'));
      
      if (sessionCookie) {
        console.log('✅ Session cookie: Set correctly');
      } else {
        console.log('⚠️  Session cookie: Not found');
      }
      
    } else {
      console.log(`❌ Login API failed with status: ${response.statusCode}`);
      console.log(`❌ Error: ${response.data}`);
    }
    
  } catch (error) {
    console.error('❌ Test failed:', error.message);
  }
}

// Helper function to make POST requests
function makePostRequest(path, body) {
  return new Promise((resolve, reject) => {
    const postData = JSON.stringify(body);
    
    const options = {
      hostname: 'localhost',
      port: 3000,
      path,
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Content-Length': Buffer.byteLength(postData),
      },
      timeout: 10000
    };
    
    const req = http.request(options, (res) => {
      let data = '';
      res.on('data', chunk => data += chunk);
      res.on('end', () => {
        resolve({
          statusCode: res.statusCode,
          statusMessage: res.statusMessage,
          headers: res.headers,
          data
        });
      });
    });
    
    req.on('error', reject);
    req.on('timeout', () => reject(new Error('Request timeout')));
    req.write(postData);
    req.end();
  });
}

testLoginAPI();
