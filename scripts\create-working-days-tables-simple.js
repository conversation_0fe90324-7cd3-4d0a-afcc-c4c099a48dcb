#!/usr/bin/env node

// Simple script to create the missing working_days_configuration and attendance_calculation_settings tables

const { neon } = require('@neondatabase/serverless');
require('dotenv').config({ path: '.env.local' });

async function createTables() {
  console.log('🔧 Creating Working Days Configuration Tables...\n');
  
  if (!process.env.DATABASE_URL) {
    console.error('❌ ERROR: DATABASE_URL environment variable is not set');
    process.exit(1);
  }
  
  const sql = neon(process.env.DATABASE_URL);
  
  try {
    // Test connection
    console.log('🔄 Testing database connection...');
    await sql`SELECT 1`;
    console.log('✅ Database connection successful!\n');
    
    // Create working_days_configuration table
    console.log('📋 Creating working_days_configuration table...');
    await sql`
      CREATE TABLE IF NOT EXISTS working_days_configuration (
        id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
        fiscal_year VARCHAR(10) NOT NULL,
        bs_month INTEGER NOT NULL CHECK (bs_month >= 1 AND bs_month <= 12),
        bs_month_name VARCHAR(20) NOT NULL,
        total_days_in_month INTEGER NOT NULL CHECK (total_days_in_month >= 28 AND total_days_in_month <= 32),
        working_days INTEGER NOT NULL CHECK (working_days >= 0 AND working_days <= total_days_in_month),
        public_holidays INTEGER DEFAULT 0 CHECK (public_holidays >= 0),
        weekend_days INTEGER DEFAULT 0 CHECK (weekend_days >= 0),
        late_penalty_type VARCHAR(20) DEFAULT 'half_day' CHECK (late_penalty_type IN ('none', 'half_day', 'custom')),
        late_penalty_amount DECIMAL(8,2) DEFAULT 0,
        half_day_calculation_method VARCHAR(20) DEFAULT 'fifty_percent' CHECK (half_day_calculation_method IN ('fifty_percent', 'custom')),
        created_by UUID REFERENCES users(id),
        created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
        updated_by UUID REFERENCES users(id),
        updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
        UNIQUE(fiscal_year, bs_month)
      )
    `;
    console.log('✅ working_days_configuration table created');
    
    // Create attendance_calculation_settings table
    console.log('📋 Creating attendance_calculation_settings table...');
    await sql`
      CREATE TABLE IF NOT EXISTS attendance_calculation_settings (
        id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
        setting_name VARCHAR(100) UNIQUE NOT NULL,
        setting_value TEXT NOT NULL,
        setting_type VARCHAR(20) NOT NULL CHECK (setting_type IN ('string', 'number', 'boolean', 'json')),
        description TEXT,
        category VARCHAR(50) DEFAULT 'general',
        is_system_setting BOOLEAN DEFAULT FALSE,
        created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
        updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
      )
    `;
    console.log('✅ attendance_calculation_settings table created');
    
    // Insert default working days for fiscal year 2081-82
    console.log('📊 Inserting default working days data...');
    await sql`
      INSERT INTO working_days_configuration (
        fiscal_year, bs_month, bs_month_name, total_days_in_month, 
        working_days, public_holidays, weekend_days
      ) VALUES 
        ('2081-82', 1, 'Baisakh', 31, 22, 2, 7),
        ('2081-82', 2, 'Jestha', 32, 23, 2, 7),
        ('2081-82', 3, 'Ashadh', 32, 23, 2, 7),
        ('2081-82', 4, 'Shrawan', 32, 23, 2, 7),
        ('2081-82', 5, 'Bhadra', 32, 23, 2, 7),
        ('2081-82', 6, 'Ashwin', 30, 22, 2, 6),
        ('2081-82', 7, 'Kartik', 30, 22, 2, 6),
        ('2081-82', 8, 'Mangsir', 30, 22, 2, 6),
        ('2081-82', 9, 'Poush', 30, 22, 2, 6),
        ('2081-82', 10, 'Magh', 30, 22, 2, 6),
        ('2081-82', 11, 'Falgun', 30, 22, 2, 6),
        ('2081-82', 12, 'Chaitra', 30, 22, 2, 6)
      ON CONFLICT (fiscal_year, bs_month) DO NOTHING
    `;
    console.log('✅ Default working days data inserted');
    
    // Insert default attendance calculation settings
    console.log('📊 Inserting default attendance settings...');
    await sql`
      INSERT INTO attendance_calculation_settings (setting_name, setting_value, setting_type, description, category, is_system_setting) VALUES
      ('enable_attendance_based_calculation', 'true', 'boolean', 'Enable automatic salary calculation based on attendance', 'calculation', TRUE),
      ('default_working_hours_per_day', '8', 'number', 'Standard working hours per day', 'calculation', TRUE),
      ('late_penalty_enabled', 'true', 'boolean', 'Enable late penalty deductions', 'penalties', TRUE),
      ('late_penalty_threshold_minutes', '15', 'number', 'Minutes after which late penalty applies', 'penalties', TRUE),
      ('half_day_threshold_hours', '4', 'number', 'Minimum hours for half day consideration', 'calculation', TRUE),
      ('overtime_calculation_enabled', 'true', 'boolean', 'Enable overtime calculation in attendance-based payroll', 'calculation', TRUE),
      ('leave_salary_calculation', 'full', 'string', 'How to calculate salary for leave days (full, half, none)', 'calculation', TRUE),
      ('weekend_work_multiplier', '1.5', 'number', 'Multiplier for weekend work', 'calculation', TRUE),
      ('holiday_work_multiplier', '2.0', 'number', 'Multiplier for holiday work', 'calculation', TRUE),
      ('attendance_bonus_threshold', '95', 'number', 'Attendance percentage threshold for bonus', 'bonus', TRUE),
      ('attendance_bonus_amount', '2000', 'number', 'Bonus amount for good attendance (NPR)', 'bonus', TRUE)
      ON CONFLICT (setting_name) DO NOTHING
    `;
    console.log('✅ Default attendance settings inserted');
    
    // Create indexes
    console.log('📋 Creating indexes...');
    await sql`CREATE INDEX IF NOT EXISTS idx_working_days_config_fiscal_year ON working_days_configuration(fiscal_year)`;
    await sql`CREATE INDEX IF NOT EXISTS idx_working_days_config_month ON working_days_configuration(bs_month)`;
    await sql`CREATE INDEX IF NOT EXISTS idx_attendance_calc_settings_name ON attendance_calculation_settings(setting_name)`;
    await sql`CREATE INDEX IF NOT EXISTS idx_attendance_calc_settings_category ON attendance_calculation_settings(category)`;
    console.log('✅ Indexes created');
    
    // Verify the results
    console.log('\n🔍 Verifying tables and data...');
    
    const tablesCheck = await sql`
      SELECT table_name 
      FROM information_schema.tables 
      WHERE table_schema = 'public' 
      AND table_name IN ('working_days_configuration', 'attendance_calculation_settings')
    `;
    
    const workingDaysCount = await sql`SELECT COUNT(*) as count FROM working_days_configuration`;
    const settingsCount = await sql`SELECT COUNT(*) as count FROM attendance_calculation_settings`;
    
    console.log(`📋 Tables found: ${tablesCheck.map(t => t.table_name).join(', ')}`);
    console.log(`📊 Working days configurations: ${workingDaysCount[0].count}`);
    console.log(`📊 Attendance settings: ${settingsCount[0].count}`);
    
    if (tablesCheck.length === 2 && workingDaysCount[0].count >= 12 && settingsCount[0].count >= 10) {
      console.log('\n🎉 SUCCESS: All tables and data are properly configured!');
      console.log('🚀 The Payroll Settings page should now work correctly.');
      console.log('🔗 Test it at: http://localhost:3000/admin/payroll/settings');
    } else {
      console.log('\n⚠️  Some issues detected. Check the counts above.');
    }
    
  } catch (error) {
    console.error('💥 ERROR:', error.message);
    process.exit(1);
  }
}

createTables().catch(console.error);
