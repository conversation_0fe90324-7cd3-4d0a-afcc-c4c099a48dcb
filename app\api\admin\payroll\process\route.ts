// Admin Payroll Processing API Endpoint
// Phase 2: Core Payroll Engine Development - API Endpoints

import { NextRequest, NextResponse } from 'next/server';
import { db } from '@/lib/neon';
import { PayrollEngine, PayrollCalculationResult } from '@/lib/payroll-engine';
import { attendancePayrollProcessor } from '@/lib/attendance-payroll-processor';
import { NepaliCalendar } from '@/lib/nepali-calendar';

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { 
      userId, 
      periodStart, 
      periodEnd, 
      payrollCalculation, 
      processedBy,
      notes,
      approvedBy 
    } = body;

    // Validate required fields
    if (!userId || !periodStart || !periodEnd || !payrollCalculation || !processedBy) {
      return NextResponse.json(
        { success: false, error: 'Missing required fields' },
        { status: 400 }
      );
    }

    // Check if payroll already processed for this period
    const existingPayroll = await db.sql`
      SELECT id FROM payroll
      WHERE user_id = ${userId}
        AND pay_period_start = ${periodStart}
        AND pay_period_end = ${periodEnd}
        AND status IN ('processed', 'paid')
    `;

    if (existingPayroll.length > 0) {
      return NextResponse.json(
        { success: false, error: 'Payroll already processed for this period' },
        { status: 409 }
      );
    }

    // Get BS dates for the period
    const bsDateRange = NepaliCalendar.getBSDateRange(periodStart, periodEnd);
    const fiscalYear = NepaliCalendar.getBSFiscalYear(bsDateRange.start);

    // Prepare payroll data for database
    const payrollData = {
      userId,
      payPeriodStart: periodStart,
      payPeriodEnd: periodEnd,
      baseSalary: payrollCalculation.baseSalary,
      overtimeHours: payrollCalculation.overtimeHours,
      overtimeRate: payrollCalculation.overtimePay / (payrollCalculation.overtimeHours || 1),
      bonuses: payrollCalculation.attendanceBonus + payrollCalculation.festivalBonus,
      deductions: payrollCalculation.totalDeductions,
      grossPay: payrollCalculation.grossPay,
      taxDeductions: payrollCalculation.incomeTax,
      netPay: payrollCalculation.netPay,
      status: 'processed',
      processedBy,
      
      // Nepal-specific fields
      nepaliFiscalYear: fiscalYear,
      bsPayPeriodStart: NepaliCalendar.formatBSDate(bsDateRange.start),
      bsPayPeriodEnd: NepaliCalendar.formatBSDate(bsDateRange.end),
      workingDays: payrollCalculation.workingDays,
      publicHolidays: 0, // Would be calculated from holiday data
      totalAttendanceHours: payrollCalculation.totalHoursWorked,
      regularHours: payrollCalculation.regularHours,
      overtimeHoursCalculated: payrollCalculation.overtimeHours,
      payStructureType: 'monthly', // Would come from pay structure
      hourlyRate: 0, // Would be calculated
      dailyRate: 0, // Would be calculated
      providentFund: payrollCalculation.providentFund,
      insuranceDeduction: 0, // Would come from deductions
      otherAllowances: payrollCalculation.allowancesTotal,
      attendanceBonus: payrollCalculation.attendanceBonus,
      latePenalty: payrollCalculation.latePenalty
    };

    // Create payroll record
    const payrollRecord = await db.createPayrollRecord(payrollData);

    // Create payroll component records
    await createPayrollComponentRecords(payrollRecord.id, payrollCalculation);

    // Update payroll status
    await db.sql`
      UPDATE payroll 
      SET 
        status = 'processed',
        processed_at = NOW(),
        notes = ${notes || null},
        approved_by = ${approvedBy || null}
      WHERE id = ${payrollRecord.id}
    `;

    // Log payroll processing activity
    await logPayrollActivity(userId, payrollRecord.id, 'processed', processedBy);

    return NextResponse.json({
      success: true,
      data: {
        payrollId: payrollRecord.id,
        status: 'processed',
        netPay: payrollCalculation.netPay,
        processedAt: new Date().toISOString(),
        message: 'Payroll processed successfully'
      }
    });

  } catch (error) {
    console.error('Error processing payroll:', error);
    return NextResponse.json(
      { 
        success: false, 
        error: 'Failed to process payroll',
        details: error.message 
      },
      { status: 500 }
    );
  }
}

// Bulk payroll processing
export async function PUT(request: NextRequest) {
  try {
    const body = await request.json();
    const { 
      userIds, 
      periodStart, 
      periodEnd, 
      processedBy,
      notes 
    } = body;

    // Validate required fields
    if (!userIds || !Array.isArray(userIds) || userIds.length === 0) {
      return NextResponse.json(
        { success: false, error: 'User IDs array is required' },
        { status: 400 }
      );
    }

    if (!periodStart || !periodEnd || !processedBy) {
      return NextResponse.json(
        { success: false, error: 'Period dates and processed by are required' },
        { status: 400 }
      );
    }

    const results = [];
    const errors = [];

    // Process each employee
    for (const userId of userIds) {
      try {
        // Calculate payroll for this employee
        const calculationResponse = await fetch(`${request.nextUrl.origin}/api/admin/payroll/calculate`, {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({ userId, periodStart, periodEnd })
        });

        if (!calculationResponse.ok) {
          errors.push({ userId, error: 'Failed to calculate payroll' });
          continue;
        }

        const calculationData = await calculationResponse.json();
        
        if (!calculationData.success) {
          errors.push({ userId, error: calculationData.error });
          continue;
        }

        // Process the calculated payroll
        const processResponse = await fetch(`${request.nextUrl.origin}/api/admin/payroll/process`, {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({
            userId,
            periodStart,
            periodEnd,
            payrollCalculation: calculationData.data.payrollCalculation,
            processedBy,
            notes
          })
        });

        if (processResponse.ok) {
          const processData = await processResponse.json();
          results.push({
            userId,
            success: true,
            payrollId: processData.data.payrollId,
            netPay: processData.data.netPay
          });
        } else {
          errors.push({ userId, error: 'Failed to process payroll' });
        }

      } catch (error) {
        errors.push({ userId, error: error.message });
      }
    }

    return NextResponse.json({
      success: true,
      data: {
        processed: results.length,
        failed: errors.length,
        results,
        errors,
        summary: {
          totalEmployees: userIds.length,
          successfullyProcessed: results.length,
          failed: errors.length,
          totalNetPay: results.reduce((sum, r) => sum + r.netPay, 0)
        }
      }
    });

  } catch (error) {
    console.error('Error in bulk payroll processing:', error);
    return NextResponse.json(
      { 
        success: false, 
        error: 'Failed to process bulk payroll',
        details: error.message 
      },
      { status: 500 }
    );
  }
}

// Helper function to create payroll component records
async function createPayrollComponentRecords(payrollId: string, payrollCalculation: PayrollCalculationResult) {
  try {
    // Create allowance records
    for (const allowance of payrollCalculation.allowanceBreakdown) {
      await db.sql`
        INSERT INTO payroll_components (payroll_id, component_type, component_name, amount, is_percentage)
        VALUES (${payrollId}, 'allowance', ${allowance.name}, ${allowance.amount}, FALSE)
      `;
    }

    // Create deduction records
    for (const deduction of payrollCalculation.deductionBreakdown) {
      await db.sql`
        INSERT INTO payroll_components (payroll_id, component_type, component_name, amount, is_percentage)
        VALUES (${payrollId}, 'deduction', ${deduction.name}, ${deduction.amount}, FALSE)
      `;
    }

    // Create statutory deductions
    if (payrollCalculation.providentFund > 0) {
      await db.sql`
        INSERT INTO payroll_components (payroll_id, component_type, component_name, amount, is_percentage)
        VALUES (${payrollId}, 'deduction', 'Provident Fund', ${payrollCalculation.providentFund}, FALSE)
      `;
    }

    if (payrollCalculation.incomeTax > 0) {
      await db.sql`
        INSERT INTO payroll_components (payroll_id, component_type, component_name, amount, is_percentage)
        VALUES (${payrollId}, 'deduction', 'Income Tax', ${payrollCalculation.incomeTax}, FALSE)
      `;
    }

    if (payrollCalculation.socialSecurityFund > 0) {
      await db.sql`
        INSERT INTO payroll_components (payroll_id, component_type, component_name, amount, is_percentage)
        VALUES (${payrollId}, 'deduction', 'Social Security Fund', ${payrollCalculation.socialSecurityFund}, FALSE)
      `;
    }

  } catch (error) {
    console.error('Error creating payroll component records:', error);
    throw error;
  }
}

// Helper function to log payroll activity
async function logPayrollActivity(userId: string, payrollId: string, action: string, performedBy: string) {
  try {
    await db.sql`
      INSERT INTO payroll_activity_log (user_id, payroll_id, action, performed_by, performed_at)
      VALUES (${userId}, ${payrollId}, ${action}, ${performedBy}, NOW())
    `;
  } catch (error) {
    console.error('Error logging payroll activity:', error);
    // Don't throw error as this is not critical
  }
}
