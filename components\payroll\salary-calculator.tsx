"use client"

import React, { useState, useEffect } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Badge } from '@/components/ui/badge'
import { Switch } from '@/components/ui/switch'
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert'
import { Separator } from '@/components/ui/separator'
import { 
  Calculator, 
  DollarSign, 
  TrendingUp, 
  AlertTriangle, 
  CheckCircle,
  Info,
  RefreshCw
} from 'lucide-react'
import { CurrencyDisplay } from '@/components/ui/currency-display'
import { toast } from 'sonner'

interface AttendanceData {
  employee_id: string;
  total_working_days: number;
  days_present: number;
  days_absent: number;
  days_late: number;
  days_half_day: number;
  days_on_leave: number;
  attendance_percentage: number;
}

interface SalaryCalculation {
  base_salary: number;
  daily_rate: number;
  working_days: number;
  days_present: number;
  days_late: number;
  days_half_day: number;
  days_on_leave: number;
  present_salary: number;
  leave_salary: number;
  half_day_salary: number;
  late_penalty: number;
  calculated_salary: number;
}

interface SalaryCalculatorProps {
  baseSalary: number;
  attendanceData: AttendanceData | null;
  onSalaryCalculated?: (calculation: SalaryCalculation) => void;
  enableAttendanceCalculation?: boolean;
  onToggleAttendanceCalculation?: (enabled: boolean) => void;
  className?: string;
}

export function SalaryCalculator({
  baseSalary,
  attendanceData,
  onSalaryCalculated,
  enableAttendanceCalculation = false,
  onToggleAttendanceCalculation,
  className
}: SalaryCalculatorProps) {
  const [calculation, setCalculation] = useState<SalaryCalculation | null>(null)
  const [manualOverride, setManualOverride] = useState(false)
  const [manualSalary, setManualSalary] = useState('')
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)

  // Calculate salary when inputs change
  useEffect(() => {
    if (enableAttendanceCalculation && attendanceData && baseSalary > 0) {
      calculateSalary()
    } else if (!enableAttendanceCalculation) {
      // Use base salary when attendance calculation is disabled
      const simpleCalculation: SalaryCalculation = {
        base_salary: baseSalary,
        daily_rate: 0,
        working_days: 0,
        days_present: 0,
        days_late: 0,
        days_half_day: 0,
        days_on_leave: 0,
        present_salary: baseSalary,
        leave_salary: 0,
        half_day_salary: 0,
        late_penalty: 0,
        calculated_salary: baseSalary
      }
      setCalculation(simpleCalculation)
      onSalaryCalculated?.(simpleCalculation)
    }
  }, [baseSalary, attendanceData, enableAttendanceCalculation])

  const calculateSalary = async () => {
    if (!attendanceData || baseSalary <= 0) return

    try {
      setLoading(true)
      setError(null)

      const response = await fetch('/api/admin/payroll/attendance-summary', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          base_salary: baseSalary,
          working_days: attendanceData.total_working_days,
          days_present: attendanceData.days_present,
          days_late: attendanceData.days_late,
          days_half_day: attendanceData.days_half_day,
          days_on_leave: attendanceData.days_on_leave
        })
      })

      const data = await response.json()

      if (data.success) {
        setCalculation(data.data)
        onSalaryCalculated?.(data.data)
      } else {
        setError(data.error || 'Failed to calculate salary')
        toast.error('Failed to calculate salary')
      }
    } catch (error) {
      console.error('Error calculating salary:', error)
      setError('Error calculating salary')
      toast.error('Error calculating salary')
    } finally {
      setLoading(false)
    }
  }

  const handleManualOverride = () => {
    if (manualOverride && manualSalary && calculation) {
      const overriddenCalculation: SalaryCalculation = {
        ...calculation,
        calculated_salary: parseFloat(manualSalary)
      }
      setCalculation(overriddenCalculation)
      onSalaryCalculated?.(overriddenCalculation)
    }
  }

  const resetToCalculated = () => {
    setManualOverride(false)
    setManualSalary('')
    if (enableAttendanceCalculation && attendanceData) {
      calculateSalary()
    }
  }

  const getFinalSalary = () => {
    if (manualOverride && manualSalary) {
      return parseFloat(manualSalary)
    }
    return calculation?.calculated_salary || baseSalary
  }

  return (
    <Card className={className}>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Calculator className="h-5 w-5" />
          Salary Calculation
        </CardTitle>
        <CardDescription>
          {enableAttendanceCalculation 
            ? "Automatic calculation based on attendance data" 
            : "Using base salary amount"}
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-6">
        {/* Attendance-Based Calculation Toggle */}
        <div className="flex items-center justify-between p-4 border rounded-lg">
          <div className="space-y-1">
            <Label className="text-sm font-medium">Attendance-Based Calculation</Label>
            <p className="text-xs text-muted-foreground">
              Calculate salary based on actual attendance data
            </p>
          </div>
          <Switch
            checked={enableAttendanceCalculation}
            onCheckedChange={onToggleAttendanceCalculation}
          />
        </div>

        {/* Calculation Details */}
        {enableAttendanceCalculation && attendanceData && calculation && (
          <div className="space-y-4">
            {/* Base Information */}
            <div className="grid grid-cols-2 gap-4 p-4 bg-muted/50 rounded-lg">
              <div>
                <Label className="text-xs text-muted-foreground">Base Salary</Label>
                <div className="text-lg font-semibold">
                  <CurrencyDisplay amount={calculation.base_salary} />
                </div>
              </div>
              <div>
                <Label className="text-xs text-muted-foreground">Daily Rate</Label>
                <div className="text-lg font-semibold">
                  <CurrencyDisplay amount={calculation.daily_rate} />
                </div>
              </div>
            </div>

            {/* Attendance Breakdown */}
            <div className="space-y-3">
              <Label className="text-sm font-medium">Attendance Breakdown</Label>
              <div className="grid grid-cols-2 gap-3 text-sm">
                <div className="flex justify-between">
                  <span>Working Days:</span>
                  <Badge variant="outline">{calculation.working_days}</Badge>
                </div>
                <div className="flex justify-between">
                  <span>Days Present:</span>
                  <Badge variant="secondary">{calculation.days_present}</Badge>
                </div>
                <div className="flex justify-between">
                  <span>Days Late:</span>
                  <Badge variant="destructive">{calculation.days_late}</Badge>
                </div>
                <div className="flex justify-between">
                  <span>Half Days:</span>
                  <Badge variant="default">{calculation.days_half_day}</Badge>
                </div>
                <div className="flex justify-between">
                  <span>Leave Days:</span>
                  <Badge variant="secondary">{calculation.days_on_leave}</Badge>
                </div>
              </div>
            </div>

            <Separator />

            {/* Salary Components */}
            <div className="space-y-3">
              <Label className="text-sm font-medium">Salary Components</Label>
              <div className="space-y-2 text-sm">
                <div className="flex justify-between">
                  <span>Present Days Salary:</span>
                  <span className="font-medium text-green-600">
                    +<CurrencyDisplay amount={calculation.present_salary} />
                  </span>
                </div>
                {calculation.leave_salary > 0 && (
                  <div className="flex justify-between">
                    <span>Leave Days Salary:</span>
                    <span className="font-medium text-green-600">
                      +<CurrencyDisplay amount={calculation.leave_salary} />
                    </span>
                  </div>
                )}
                {calculation.half_day_salary > 0 && (
                  <div className="flex justify-between">
                    <span>Half Day Salary:</span>
                    <span className="font-medium text-green-600">
                      +<CurrencyDisplay amount={calculation.half_day_salary} />
                    </span>
                  </div>
                )}
                {calculation.late_penalty > 0 && (
                  <div className="flex justify-between">
                    <span>Late Penalty:</span>
                    <span className="font-medium text-red-600">
                      -<CurrencyDisplay amount={calculation.late_penalty} />
                    </span>
                  </div>
                )}
              </div>
            </div>

            <Separator />
          </div>
        )}

        {/* Manual Override */}
        <div className="space-y-4">
          <div className="flex items-center justify-between">
            <Label className="text-sm font-medium">Manual Override</Label>
            <Switch
              checked={manualOverride}
              onCheckedChange={setManualOverride}
            />
          </div>

          {manualOverride && (
            <div className="space-y-3">
              <div className="space-y-2">
                <Label htmlFor="manual-salary">Override Salary Amount</Label>
                <Input
                  id="manual-salary"
                  type="number"
                  step="0.01"
                  placeholder="Enter salary amount"
                  value={manualSalary}
                  onChange={(e) => setManualSalary(e.target.value)}
                />
              </div>
              <div className="flex gap-2">
                <Button size="sm" onClick={handleManualOverride}>
                  Apply Override
                </Button>
                <Button size="sm" variant="outline" onClick={resetToCalculated}>
                  Reset to Calculated
                </Button>
              </div>
            </div>
          )}
        </div>

        {/* Final Salary Display */}
        <div className="p-4 bg-primary/5 border border-primary/20 rounded-lg">
          <div className="flex items-center justify-between">
            <div className="space-y-1">
              <Label className="text-sm font-medium">Final Payable Salary</Label>
              {manualOverride && (
                <p className="text-xs text-muted-foreground">Manual override applied</p>
              )}
            </div>
            <div className="text-right">
              <div className="text-2xl font-bold text-primary">
                <CurrencyDisplay amount={getFinalSalary()} />
              </div>
              {enableAttendanceCalculation && attendanceData && (
                <div className="text-xs text-muted-foreground">
                  {attendanceData.attendance_percentage}% attendance
                </div>
              )}
            </div>
          </div>
        </div>

        {/* Warnings and Info */}
        {enableAttendanceCalculation && attendanceData && (
          <div className="space-y-2">
            {attendanceData.attendance_percentage < 80 && (
              <Alert variant="destructive">
                <AlertTriangle className="h-4 w-4" />
                <AlertTitle>Low Attendance Warning</AlertTitle>
                <AlertDescription>
                  Attendance is below 80%. Consider reviewing attendance policy.
                </AlertDescription>
              </Alert>
            )}

            {attendanceData.days_late > 5 && (
              <Alert>
                <Info className="h-4 w-4" />
                <AlertTitle>Frequent Late Arrivals</AlertTitle>
                <AlertDescription>
                  Employee has {attendanceData.days_late} late days this period.
                </AlertDescription>
              </Alert>
            )}
          </div>
        )}

        {/* Error Display */}
        {error && (
          <Alert variant="destructive">
            <AlertTriangle className="h-4 w-4" />
            <AlertTitle>Calculation Error</AlertTitle>
            <AlertDescription>
              {error}
              <Button 
                variant="outline" 
                size="sm" 
                onClick={calculateSalary}
                className="mt-2"
              >
                <RefreshCw className="h-4 w-4 mr-2" />
                Retry
              </Button>
            </AlertDescription>
          </Alert>
        )}

        {/* Recalculate Button */}
        {enableAttendanceCalculation && attendanceData && (
          <div className="flex justify-end">
            <Button 
              variant="outline" 
              size="sm" 
              onClick={calculateSalary}
              disabled={loading}
            >
              {loading && <RefreshCw className="h-4 w-4 mr-2 animate-spin" />}
              Recalculate
            </Button>
          </div>
        )}
      </CardContent>
    </Card>
  )
}
