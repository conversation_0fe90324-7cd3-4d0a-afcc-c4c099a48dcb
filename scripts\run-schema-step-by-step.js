require('dotenv').config({ path: '.env.local' });
const { neon } = require('@neondatabase/serverless');

async function runSchemaStepByStep() {
  try {
    const sql = neon(process.env.DATABASE_URL);
    
    console.log('🚀 Running Payroll Schema Enhancement Step by Step...\n');
    
    // Step 1: Create payroll_approvals table
    console.log('📋 Creating payroll_approvals table...');
    await sql`
      CREATE TABLE IF NOT EXISTS payroll_approvals (
        id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
        payroll_id UUID NOT NULL REFERENCES payroll(id) ON DELETE CASCADE,
        approver_id UUID NOT NULL REFERENCES users(id),
        approval_status VARCHAR(20) NOT NULL CHECK (approval_status IN ('pending', 'approved', 'rejected', 'cancelled')),
        approval_date TIMESTAMP WITH TIME ZONE,
        comments TEXT,
        approval_level INTEGER DEFAULT 1,
        is_final_approval BOOLEAN DEFAULT TRUE,
        created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
        updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
        UNIQUE(payroll_id, approver_id, approval_level)
      )
    `;
    console.log('✅ payroll_approvals table created');
    
    // Step 2: Create payroll_disbursements table
    console.log('📋 Creating payroll_disbursements table...');
    await sql`
      CREATE TABLE IF NOT EXISTS payroll_disbursements (
        id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
        payroll_id UUID NOT NULL REFERENCES payroll(id) ON DELETE CASCADE,
        disbursement_method VARCHAR(50) NOT NULL CHECK (disbursement_method IN ('bank_transfer', 'cash', 'cheque', 'digital_wallet')),
        bank_reference VARCHAR(100),
        transaction_id VARCHAR(100),
        disbursement_date DATE,
        disbursement_amount DECIMAL(10,2) NOT NULL,
        status VARCHAR(20) NOT NULL CHECK (status IN ('pending', 'processing', 'completed', 'failed', 'cancelled')),
        failure_reason TEXT,
        processed_by UUID REFERENCES users(id),
        created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
        updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
      )
    `;
    console.log('✅ payroll_disbursements table created');
    
    // Step 3: Create payroll_audit_log table
    console.log('📋 Creating payroll_audit_log table...');
    await sql`
      CREATE TABLE IF NOT EXISTS payroll_audit_log (
        id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
        table_name VARCHAR(50) NOT NULL,
        record_id UUID NOT NULL,
        action VARCHAR(20) NOT NULL CHECK (action IN ('INSERT', 'UPDATE', 'DELETE', 'SELECT')),
        old_values JSONB,
        new_values JSONB,
        changed_by UUID REFERENCES users(id),
        changed_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
        ip_address INET,
        user_agent TEXT,
        session_id VARCHAR(255)
      )
    `;
    console.log('✅ payroll_audit_log table created');
    
    // Step 4: Create payroll_components_master table
    console.log('📋 Creating payroll_components_master table...');
    await sql`
      CREATE TABLE IF NOT EXISTS payroll_components_master (
        id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
        name VARCHAR(100) NOT NULL,
        code VARCHAR(50) NOT NULL UNIQUE,
        type VARCHAR(20) NOT NULL CHECK (type IN ('deduction', 'allowance')),
        category VARCHAR(30) NOT NULL CHECK (category IN ('statutory', 'voluntary', 'company_policy', 'custom')),
        calculation_type VARCHAR(20) NOT NULL CHECK (calculation_type IN ('fixed', 'percentage', 'formula', 'conditional')),
        fixed_amount DECIMAL(10,2),
        percentage DECIMAL(5,2),
        percentage_base VARCHAR(30) CHECK (percentage_base IN ('base_salary', 'gross_pay', 'net_pay', 'total_earnings')),
        formula TEXT,
        conditions JSONB,
        is_taxable BOOLEAN DEFAULT TRUE,
        is_statutory BOOLEAN DEFAULT FALSE,
        affects_provident_fund BOOLEAN DEFAULT TRUE,
        affects_gratuity BOOLEAN DEFAULT TRUE,
        applicable_to_pay_structures JSONB DEFAULT '[]',
        applicable_to_employee_categories JSONB DEFAULT '[]',
        applicable_to_departments JSONB DEFAULT '[]',
        minimum_amount DECIMAL(10,2),
        maximum_amount DECIMAL(10,2),
        minimum_salary_threshold DECIMAL(10,2),
        maximum_salary_threshold DECIMAL(10,2),
        description TEXT,
        is_active BOOLEAN DEFAULT TRUE,
        effective_from DATE NOT NULL,
        effective_to DATE,
        created_by UUID REFERENCES users(id),
        approved_by UUID REFERENCES users(id),
        created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
        updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
      )
    `;
    console.log('✅ payroll_components_master table created');
    
    // Step 5: Create employee_component_assignments table
    console.log('📋 Creating employee_component_assignments table...');
    await sql`
      CREATE TABLE IF NOT EXISTS employee_component_assignments (
        id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
        user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
        component_id UUID NOT NULL REFERENCES payroll_components_master(id) ON DELETE CASCADE,
        is_active BOOLEAN DEFAULT TRUE,
        effective_from DATE NOT NULL,
        effective_to DATE,
        override_amount DECIMAL(10,2),
        override_percentage DECIMAL(5,2),
        override_conditions JSONB,
        assigned_by UUID REFERENCES users(id),
        approved_by UUID REFERENCES users(id),
        approval_date DATE,
        notes TEXT,
        created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
        updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
        UNIQUE(user_id, component_id, effective_from)
      )
    `;
    console.log('✅ employee_component_assignments table created');
    
    // Step 6: Create payroll_periods table
    console.log('📋 Creating payroll_periods table...');
    await sql`
      CREATE TABLE IF NOT EXISTS payroll_periods (
        id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
        period_name VARCHAR(100) NOT NULL,
        period_type VARCHAR(20) NOT NULL CHECK (period_type IN ('monthly', 'quarterly', 'yearly')),
        fiscal_year VARCHAR(10) NOT NULL,
        bs_start_date VARCHAR(10) NOT NULL,
        bs_end_date VARCHAR(10) NOT NULL,
        ad_start_date DATE NOT NULL,
        ad_end_date DATE NOT NULL,
        working_days INTEGER DEFAULT 0,
        public_holidays INTEGER DEFAULT 0,
        is_current_period BOOLEAN DEFAULT FALSE,
        is_closed BOOLEAN DEFAULT FALSE,
        created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
        updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
        UNIQUE(period_name, fiscal_year)
      )
    `;
    console.log('✅ payroll_periods table created');
    
    // Step 7: Create payroll_settings table
    console.log('📋 Creating payroll_settings table...');
    await sql`
      CREATE TABLE IF NOT EXISTS payroll_settings (
        id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
        setting_key VARCHAR(100) NOT NULL UNIQUE,
        setting_value TEXT NOT NULL,
        setting_type VARCHAR(20) NOT NULL CHECK (setting_type IN ('string', 'number', 'boolean', 'json')),
        description TEXT,
        is_system_setting BOOLEAN DEFAULT FALSE,
        created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
        updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
      )
    `;
    console.log('✅ payroll_settings table created');
    
    // Step 8: Create monthly_payroll_summary table
    console.log('📋 Creating monthly_payroll_summary table...');
    await sql`
      CREATE TABLE IF NOT EXISTS monthly_payroll_summary (
        id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
        user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
        fiscal_year VARCHAR(10) NOT NULL,
        bs_month VARCHAR(20) NOT NULL,
        ad_month_start DATE NOT NULL,
        ad_month_end DATE NOT NULL,
        total_working_days INTEGER DEFAULT 0,
        days_present INTEGER DEFAULT 0,
        days_absent INTEGER DEFAULT 0,
        days_late INTEGER DEFAULT 0,
        days_half_day INTEGER DEFAULT 0,
        days_on_leave INTEGER DEFAULT 0,
        total_hours_worked DECIMAL(6,2) DEFAULT 0,
        regular_hours DECIMAL(6,2) DEFAULT 0,
        overtime_hours DECIMAL(6,2) DEFAULT 0,
        base_salary DECIMAL(10,2) NOT NULL,
        overtime_pay DECIMAL(10,2) DEFAULT 0,
        total_allowances DECIMAL(10,2) DEFAULT 0,
        total_deductions DECIMAL(10,2) DEFAULT 0,
        gross_pay DECIMAL(10,2) NOT NULL,
        tax_deductions DECIMAL(10,2) DEFAULT 0,
        provident_fund DECIMAL(10,2) DEFAULT 0,
        net_pay DECIMAL(10,2) NOT NULL,
        attendance_bonus DECIMAL(10,2) DEFAULT 0,
        late_penalty DECIMAL(10,2) DEFAULT 0,
        status VARCHAR(20) NOT NULL DEFAULT 'draft' CHECK (status IN ('draft', 'calculated', 'approved', 'processed', 'paid')),
        calculated_at TIMESTAMP WITH TIME ZONE,
        calculated_by UUID REFERENCES users(id),
        approved_at TIMESTAMP WITH TIME ZONE,
        approved_by UUID REFERENCES users(id),
        created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
        updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
        UNIQUE(user_id, fiscal_year, bs_month)
      )
    `;
    console.log('✅ monthly_payroll_summary table created');
    
    // Step 9: Create indexes
    console.log('📋 Creating indexes...');
    await sql`CREATE INDEX IF NOT EXISTS idx_payroll_approvals_payroll_id ON payroll_approvals(payroll_id)`;
    await sql`CREATE INDEX IF NOT EXISTS idx_payroll_approvals_approver ON payroll_approvals(approver_id)`;
    await sql`CREATE INDEX IF NOT EXISTS idx_payroll_disbursements_payroll_id ON payroll_disbursements(payroll_id)`;
    await sql`CREATE INDEX IF NOT EXISTS idx_payroll_audit_log_table_record ON payroll_audit_log(table_name, record_id)`;
    await sql`CREATE INDEX IF NOT EXISTS idx_payroll_components_master_code ON payroll_components_master(code)`;
    await sql`CREATE INDEX IF NOT EXISTS idx_employee_component_assignments_user ON employee_component_assignments(user_id)`;
    await sql`CREATE INDEX IF NOT EXISTS idx_monthly_payroll_summary_user_fiscal ON monthly_payroll_summary(user_id, fiscal_year)`;
    console.log('✅ Indexes created');
    
    // Step 10: Enable RLS
    console.log('📋 Enabling Row Level Security...');
    await sql`ALTER TABLE payroll ENABLE ROW LEVEL SECURITY`;
    await sql`ALTER TABLE users ENABLE ROW LEVEL SECURITY`;
    await sql`ALTER TABLE attendance ENABLE ROW LEVEL SECURITY`;
    await sql`ALTER TABLE payroll_approvals ENABLE ROW LEVEL SECURITY`;
    await sql`ALTER TABLE payroll_disbursements ENABLE ROW LEVEL SECURITY`;
    await sql`ALTER TABLE payroll_audit_log ENABLE ROW LEVEL SECURITY`;
    await sql`ALTER TABLE payroll_components_master ENABLE ROW LEVEL SECURITY`;
    await sql`ALTER TABLE employee_component_assignments ENABLE ROW LEVEL SECURITY`;
    await sql`ALTER TABLE monthly_payroll_summary ENABLE ROW LEVEL SECURITY`;
    await sql`ALTER TABLE payroll_periods ENABLE ROW LEVEL SECURITY`;
    await sql`ALTER TABLE payroll_settings ENABLE ROW LEVEL SECURITY`;
    console.log('✅ RLS enabled on all tables');
    
    // Final verification
    console.log('\n🔍 Final Verification...');
    const tables = await sql`
      SELECT table_name 
      FROM information_schema.tables 
      WHERE table_name IN (
        'payroll_approvals', 
        'payroll_disbursements', 
        'payroll_audit_log',
        'payroll_components_master',
        'employee_component_assignments',
        'payroll_periods',
        'payroll_settings',
        'monthly_payroll_summary'
      )
      ORDER BY table_name
    `;
    
    console.log('✅ Created tables:', tables.map(t => t.table_name));
    
    console.log('\n🎉 Database Schema Enhancement completed successfully!');
    console.log('\n📋 Summary:');
    console.log('   ✅ 8 new payroll tables created');
    console.log('   ✅ Indexes created for performance');
    console.log('   ✅ Row Level Security enabled');
    console.log('\n🚀 Ready for next step: RLS Policies and Default Data');
    
    return true;
    
  } catch (error) {
    console.error('❌ Schema enhancement failed:', error);
    console.error('Error details:', error.message);
    return false;
  }
}

runSchemaStepByStep()
  .then(success => {
    if (success) {
      console.log('\n✅ Schema enhancement completed successfully');
      process.exit(0);
    } else {
      console.log('\n❌ Schema enhancement failed');
      process.exit(1);
    }
  })
  .catch(error => {
    console.error('❌ Unexpected error:', error);
    process.exit(1);
  });
