#!/usr/bin/env node

// Phase 3 - Create sample tasks with different statuses
const { neon } = require('@neondatabase/serverless');
require('dotenv').config({ path: '.env.local' });

async function createSampleTasks() {
  console.log('🔍 PHASE 3 - CREATING SAMPLE TASKS');
  console.log('==================================\n');

  if (!process.env.DATABASE_URL) {
    console.error('❌ ERROR: DATABASE_URL environment variable is not set');
    process.exit(1);
  }

  try {
    const sql = neon(process.env.DATABASE_URL);

    // Test connection
    console.log('🔄 Testing database connection...');
    await sql`SELECT 1`;
    console.log('✅ Database connection successful!\n');

    // Get the admin user ID (we know from previous tests)
    const adminUser = await sql`
      SELECT id, full_name, email 
      FROM users 
      WHERE email = '<EMAIL>'
      LIMIT 1
    `;

    if (adminUser.length === 0) {
      console.error('❌ Admin user not found');
      process.exit(1);
    }

    const adminUserId = adminUser[0].id;
    console.log(`👤 Using admin user: ${adminUser[0].full_name} (${adminUser[0].email})`);

    // Check current tasks
    console.log('\n📋 CURRENT TASKS:');
    const currentTasks = await sql`
      SELECT id, title, status, priority, created_at
      FROM tasks
      ORDER BY created_at DESC
    `;

    console.log(`Found ${currentTasks.length} existing tasks:`);
    currentTasks.forEach((task, index) => {
      console.log(`  ${index + 1}. ${task.title} - Status: ${task.status}, Priority: ${task.priority}`);
    });

    // Create sample tasks with different statuses
    console.log('\n🆕 CREATING SAMPLE TASKS:');
    console.log('========================');

    const sampleTasks = [
      {
        title: 'Design User Interface Mockups',
        description: 'Create wireframes and mockups for the new dashboard interface',
        status: 'in_progress',
        priority: 'high',
        estimated_hours: 8
      },
      {
        title: 'Implement Authentication System',
        description: 'Set up JWT-based authentication with role-based access control',
        status: 'completed',
        priority: 'high',
        estimated_hours: 12,
        completed_at: new Date()
      },
      {
        title: 'Write API Documentation',
        description: 'Document all REST API endpoints with examples and response formats',
        status: 'todo',
        priority: 'medium',
        estimated_hours: 6
      },
      {
        title: 'Set up Database Migrations',
        description: 'Create migration scripts for production database deployment',
        status: 'in_progress',
        priority: 'medium',
        estimated_hours: 4
      },
      {
        title: 'Implement Email Notifications',
        description: 'Add email notification system for important user actions',
        status: 'todo',
        priority: 'low',
        estimated_hours: 10
      },
      {
        title: 'Performance Optimization',
        description: 'Optimize database queries and implement caching strategies',
        status: 'completed',
        priority: 'high',
        estimated_hours: 16,
        completed_at: new Date(Date.now() - 24 * 60 * 60 * 1000) // 1 day ago
      },
      {
        title: 'Mobile Responsive Design',
        description: 'Ensure all pages work properly on mobile devices',
        status: 'in_progress',
        priority: 'medium',
        estimated_hours: 20
      },
      {
        title: 'Security Audit',
        description: 'Conduct comprehensive security review and fix vulnerabilities',
        status: 'todo',
        priority: 'urgent',
        estimated_hours: 8
      }
    ];

    let createdCount = 0;
    for (const task of sampleTasks) {
      try {
        const result = await sql`
          INSERT INTO tasks (
            title, description, status, priority, 
            assigned_to, assigned_by, estimated_hours,
            completed_at, created_at, updated_at
          ) VALUES (
            ${task.title}, ${task.description}, ${task.status}, ${task.priority},
            ${adminUserId}, ${adminUserId}, ${task.estimated_hours},
            ${task.completed_at || null}, NOW(), NOW()
          )
          RETURNING id, title, status, priority
        `;

        console.log(`✅ Created: ${result[0].title} (${result[0].status}, ${result[0].priority})`);
        createdCount++;
      } catch (error) {
        console.error(`❌ Failed to create task "${task.title}":`, error.message);
      }
    }

    // Verify the final state
    console.log('\n📊 FINAL TASK SUMMARY:');
    console.log('======================');

    const finalTasks = await sql`
      SELECT status, priority, COUNT(*) as count
      FROM tasks
      GROUP BY status, priority
      ORDER BY status, priority
    `;

    console.log('Tasks by status and priority:');
    finalTasks.forEach(row => {
      console.log(`  ${row.status} (${row.priority}): ${row.count} tasks`);
    });

    const statusSummary = await sql`
      SELECT status, COUNT(*) as count
      FROM tasks
      GROUP BY status
      ORDER BY 
        CASE status 
          WHEN 'todo' THEN 1 
          WHEN 'in_progress' THEN 2 
          WHEN 'completed' THEN 3 
          ELSE 4 
        END
    `;

    console.log('\nTasks by status:');
    statusSummary.forEach(row => {
      console.log(`  ${row.status}: ${row.count} tasks`);
    });

    console.log('\n✅ PHASE 3 SAMPLE DATA CREATION COMPLETE');
    console.log('=========================================');
    
    console.log('\n🔍 SUMMARY:');
    console.log(`- Created ${createdCount} new sample tasks`);
    console.log(`- Total tasks in database: ${currentTasks.length + createdCount}`);
    console.log('- Tasks now have varied statuses: todo, in_progress, completed');
    console.log('- Tasks have different priorities: low, medium, high, urgent');
    console.log('- Ready for kanban board testing with multiple columns');
    
    console.log('\n📋 NEXT STEPS:');
    console.log('- Test the kanban board display with the new sample data');
    console.log('- Verify tasks appear in correct columns (Todo, In Progress, Done)');
    console.log('- Test drag-and-drop functionality between columns');

  } catch (error) {
    console.error('❌ Sample data creation failed:', error);
    console.error('Stack trace:', error.stack);
    process.exit(1);
  }
}

createSampleTasks().catch(console.error);
