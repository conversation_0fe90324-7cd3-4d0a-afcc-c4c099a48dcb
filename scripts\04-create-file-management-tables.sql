-- File Management Tables for User Profiles
-- This script creates tables for managing user file uploads including profile pictures, documents, contracts, and signatures

-- Create user_files table for storing file metadata
CREATE TABLE IF NOT EXISTS user_files (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    file_type VARCHAR(50) NOT NULL CHECK (file_type IN ('profile_picture', 'document', 'contract', 'signature')),
    original_filename VARCHAR(255) NOT NULL,
    stored_filename VARCHAR(255) NOT NULL,
    file_path VARCHAR(500) NOT NULL,
    file_size INTEGER NOT NULL,
    mime_type VARCHAR(100) NOT NULL,
    file_extension VARCHAR(10) NOT NULL,
    
    -- File validation and metadata
    is_validated BOOLEAN DEFAULT false,
    validation_status VARCHAR(20) DEFAULT 'pending' CHECK (validation_status IN ('pending', 'approved', 'rejected')),
    validation_notes TEXT,
    validated_by UUID REFERENCES users(id),
    validated_at TIMESTAMP WITH TIME ZONE,
    
    -- File access and security
    is_public BOOLEAN DEFAULT false,
    access_level VARCHAR(20) DEFAULT 'private' CHECK (access_level IN ('private', 'internal', 'public')),
    download_count INTEGER DEFAULT 0,
    last_accessed_at TIMESTAMP WITH TIME ZONE,
    
    -- Upload metadata
    uploaded_by UUID NOT NULL REFERENCES users(id),
    upload_ip_address INET,
    upload_user_agent TEXT,
    
    -- System fields
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    -- Constraints
    UNIQUE(user_id, file_type, stored_filename)
);

-- Create file_storage table for actual file content (using bytea for small files)
CREATE TABLE IF NOT EXISTS file_storage (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    file_id UUID NOT NULL REFERENCES user_files(id) ON DELETE CASCADE,
    file_content BYTEA NOT NULL,
    content_hash VARCHAR(64) NOT NULL, -- SHA-256 hash for integrity
    compression_type VARCHAR(20) DEFAULT 'none' CHECK (compression_type IN ('none', 'gzip', 'lz4')),
    encryption_type VARCHAR(20) DEFAULT 'none' CHECK (encryption_type IN ('none', 'aes256')),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    -- Ensure one storage record per file
    UNIQUE(file_id)
);

-- Create file_access_log table for audit trail
CREATE TABLE IF NOT EXISTS file_access_log (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    file_id UUID NOT NULL REFERENCES user_files(id) ON DELETE CASCADE,
    accessed_by UUID NOT NULL REFERENCES users(id),
    access_type VARCHAR(20) NOT NULL CHECK (access_type IN ('view', 'download', 'upload', 'delete', 'update')),
    access_ip_address INET,
    user_agent TEXT,
    success BOOLEAN DEFAULT true,
    error_message TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_user_files_user_id ON user_files(user_id);
CREATE INDEX IF NOT EXISTS idx_user_files_type ON user_files(file_type);
CREATE INDEX IF NOT EXISTS idx_user_files_validation ON user_files(validation_status);
CREATE INDEX IF NOT EXISTS idx_user_files_active ON user_files(is_active);
CREATE INDEX IF NOT EXISTS idx_user_files_created ON user_files(created_at);

CREATE INDEX IF NOT EXISTS idx_file_storage_file_id ON file_storage(file_id);
CREATE INDEX IF NOT EXISTS idx_file_storage_hash ON file_storage(content_hash);

CREATE INDEX IF NOT EXISTS idx_file_access_log_file_id ON file_access_log(file_id);
CREATE INDEX IF NOT EXISTS idx_file_access_log_user ON file_access_log(accessed_by);
CREATE INDEX IF NOT EXISTS idx_file_access_log_created ON file_access_log(created_at);

-- Create function to update file access count
CREATE OR REPLACE FUNCTION update_file_access_count()
RETURNS TRIGGER AS $$
BEGIN
    IF NEW.access_type IN ('view', 'download') AND NEW.success = true THEN
        UPDATE user_files 
        SET download_count = download_count + 1,
            last_accessed_at = NOW()
        WHERE id = NEW.file_id;
    END IF;
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create trigger to automatically update access count
CREATE TRIGGER trigger_update_file_access_count
    AFTER INSERT ON file_access_log
    FOR EACH ROW
    EXECUTE FUNCTION update_file_access_count();

-- Create function to generate file hash
CREATE OR REPLACE FUNCTION generate_file_hash(content BYTEA)
RETURNS VARCHAR(64) AS $$
BEGIN
    RETURN encode(digest(content, 'sha256'), 'hex');
END;
$$ LANGUAGE plpgsql;

-- Add some sample file type configurations (can be used for validation)
CREATE TABLE IF NOT EXISTS file_type_config (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    file_type VARCHAR(50) NOT NULL,
    allowed_extensions TEXT[] NOT NULL,
    allowed_mime_types TEXT[] NOT NULL,
    max_file_size_mb INTEGER NOT NULL,
    description TEXT,
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Insert default file type configurations
INSERT INTO file_type_config (file_type, allowed_extensions, allowed_mime_types, max_file_size_mb, description) VALUES
('profile_picture', ARRAY['jpg', 'jpeg', 'png', 'gif', 'webp'], ARRAY['image/jpeg', 'image/png', 'image/gif', 'image/webp'], 5, 'User profile pictures'),
('document', ARRAY['pdf', 'doc', 'docx', 'jpg', 'jpeg', 'png'], ARRAY['application/pdf', 'application/msword', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document', 'image/jpeg', 'image/png'], 10, 'General documents'),
('contract', ARRAY['pdf', 'doc', 'docx', 'jpg', 'jpeg', 'png'], ARRAY['application/pdf', 'application/msword', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document', 'image/jpeg', 'image/png'], 10, 'Employment contracts and agreements'),
('signature', ARRAY['jpg', 'jpeg', 'png', 'gif', 'webp'], ARRAY['image/jpeg', 'image/png', 'image/gif', 'image/webp'], 2, 'Digital signatures')
ON CONFLICT DO NOTHING;

-- Create view for file summary by user
CREATE OR REPLACE VIEW user_file_summary AS
SELECT 
    u.id as user_id,
    u.full_name,
    u.email,
    COUNT(uf.id) as total_files,
    COUNT(CASE WHEN uf.file_type = 'profile_picture' THEN 1 END) as profile_pictures,
    COUNT(CASE WHEN uf.file_type = 'document' THEN 1 END) as documents,
    COUNT(CASE WHEN uf.file_type = 'contract' THEN 1 END) as contracts,
    COUNT(CASE WHEN uf.file_type = 'signature' THEN 1 END) as signatures,
    SUM(uf.file_size) as total_file_size_bytes,
    MAX(uf.created_at) as last_upload_date
FROM users u
LEFT JOIN user_files uf ON u.id = uf.user_id AND uf.is_active = true
GROUP BY u.id, u.full_name, u.email;

-- Grant necessary permissions (adjust as needed for your setup)
-- GRANT SELECT, INSERT, UPDATE, DELETE ON user_files TO your_app_user;
-- GRANT SELECT, INSERT, UPDATE, DELETE ON file_storage TO your_app_user;
-- GRANT SELECT, INSERT ON file_access_log TO your_app_user;
-- GRANT SELECT ON file_type_config TO your_app_user;
-- GRANT SELECT ON user_file_summary TO your_app_user;
