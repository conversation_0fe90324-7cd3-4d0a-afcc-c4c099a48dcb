import { NextRequest, NextResponse } from "next/server"
import { AuthService } from "@/lib/auth-utils"
import { serverDb } from "@/lib/server-db"
import { z } from "zod"

// Validation schema for loan creation
const createLoanSchema = z.object({
  customer_id: z.string().uuid("Invalid customer ID"),
  loan_amount: z.number().positive("Loan amount must be positive"),
  amount_paid: z.number().min(0, "Amount paid cannot be negative").default(0),
  due_date: z.string().regex(/^\d{4}-\d{2}-\d{2}$/, "Invalid date format"),
  due_date_bs: z.string().optional(),
})

// Validation schema for loan updates
const updateLoanSchema = z.object({
  loan_amount: z.number().positive("Loan amount must be positive").optional(),
  amount_paid: z.number().min(0, "Amount paid cannot be negative").optional(),
  due_date: z.string().regex(/^\d{4}-\d{2}-\d{2}$/, "Invalid date format").optional(),
  due_date_bs: z.string().optional(),
  current_stage: z.enum(["early", "assertive", "escalation", "legal_recovery", "complete"]).optional(),
})

export async function GET(request: NextRequest) {
  try {
    const sessionToken = request.cookies.get("session-token")?.value

    if (!sessionToken) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 })
    }

    const user = await AuthService.verifySession(sessionToken)

    if (!user || !["admin", "hr_manager"].includes(user.role)) {
      return NextResponse.json({ error: "Forbidden" }, { status: 403 })
    }

    const { searchParams } = new URL(request.url)
    const stage = searchParams.get("stage")
    const customer_id = searchParams.get("customer_id")
    const include_complete = searchParams.get("include_complete") === "true"

    let loans

    // Build query based on filters
    if (stage && stage !== "all") {
      if (customer_id) {
        loans = await serverDb.sql`
          SELECT
            lr.*,
            c.name as customer_name,
            c.phone as customer_phone,
            c.email as customer_email,
            c.address as customer_address,
            u.full_name as created_by_name,
            (lr.loan_amount - lr.amount_paid) as outstanding_amount,
            CASE
              WHEN lr.due_date < CURRENT_DATE THEN
                CURRENT_DATE - lr.due_date
              ELSE 0
            END as days_overdue
          FROM loan_records lr
          LEFT JOIN loan_recovery_customers c ON lr.customer_id = c.id
          LEFT JOIN users u ON lr.created_by = u.id
          WHERE lr.current_stage = ${stage} AND lr.customer_id = ${customer_id}
          ORDER BY
            CASE lr.current_stage
              WHEN 'early' THEN 1
              WHEN 'assertive' THEN 2
              WHEN 'escalation' THEN 3
              WHEN 'legal_recovery' THEN 4
              WHEN 'complete' THEN 5
            END,
            lr.stage_order,
            lr.due_date ASC
        `
      } else {
        loans = await serverDb.sql`
          SELECT
            lr.*,
            c.name as customer_name,
            c.phone as customer_phone,
            c.email as customer_email,
            c.address as customer_address,
            u.full_name as created_by_name,
            (lr.loan_amount - lr.amount_paid) as outstanding_amount,
            CASE
              WHEN lr.due_date < CURRENT_DATE THEN
                CURRENT_DATE - lr.due_date
              ELSE 0
            END as days_overdue
          FROM loan_records lr
          LEFT JOIN loan_recovery_customers c ON lr.customer_id = c.id
          LEFT JOIN users u ON lr.created_by = u.id
          WHERE lr.current_stage = ${stage}
          ORDER BY
            CASE lr.current_stage
              WHEN 'early' THEN 1
              WHEN 'assertive' THEN 2
              WHEN 'escalation' THEN 3
              WHEN 'legal_recovery' THEN 4
              WHEN 'complete' THEN 5
            END,
            lr.stage_order,
            lr.due_date ASC
        `
      }
    } else if (customer_id) {
      if (include_complete) {
        loans = await serverDb.sql`
          SELECT
            lr.*,
            c.name as customer_name,
            c.phone as customer_phone,
            c.email as customer_email,
            c.address as customer_address,
            u.full_name as created_by_name,
            (lr.loan_amount - lr.amount_paid) as outstanding_amount,
            CASE
              WHEN lr.due_date < CURRENT_DATE THEN
                CURRENT_DATE - lr.due_date
              ELSE 0
            END as days_overdue
          FROM loan_records lr
          LEFT JOIN loan_recovery_customers c ON lr.customer_id = c.id
          LEFT JOIN users u ON lr.created_by = u.id
          WHERE lr.customer_id = ${customer_id}
          ORDER BY
            CASE lr.current_stage
              WHEN 'early' THEN 1
              WHEN 'assertive' THEN 2
              WHEN 'escalation' THEN 3
              WHEN 'legal_recovery' THEN 4
              WHEN 'complete' THEN 5
            END,
            lr.stage_order,
            lr.due_date ASC
        `
      } else {
        loans = await serverDb.sql`
          SELECT
            lr.*,
            c.name as customer_name,
            c.phone as customer_phone,
            c.email as customer_email,
            c.address as customer_address,
            u.full_name as created_by_name,
            (lr.loan_amount - lr.amount_paid) as outstanding_amount,
            CASE
              WHEN lr.due_date < CURRENT_DATE THEN
                CURRENT_DATE - lr.due_date
              ELSE 0
            END as days_overdue
          FROM loan_records lr
          LEFT JOIN loan_recovery_customers c ON lr.customer_id = c.id
          LEFT JOIN users u ON lr.created_by = u.id
          WHERE lr.customer_id = ${customer_id} AND lr.current_stage != 'complete'
          ORDER BY
            CASE lr.current_stage
              WHEN 'early' THEN 1
              WHEN 'assertive' THEN 2
              WHEN 'escalation' THEN 3
              WHEN 'legal_recovery' THEN 4
              WHEN 'complete' THEN 5
            END,
            lr.stage_order,
            lr.due_date ASC
        `
      }
    } else {
      if (include_complete) {
        loans = await serverDb.sql`
          SELECT
            lr.*,
            c.name as customer_name,
            c.phone as customer_phone,
            c.email as customer_email,
            c.address as customer_address,
            u.full_name as created_by_name,
            (lr.loan_amount - lr.amount_paid) as outstanding_amount,
            CASE
              WHEN lr.due_date < CURRENT_DATE THEN
                CURRENT_DATE - lr.due_date
              ELSE 0
            END as days_overdue
          FROM loan_records lr
          LEFT JOIN loan_recovery_customers c ON lr.customer_id = c.id
          LEFT JOIN users u ON lr.created_by = u.id
          ORDER BY
            CASE lr.current_stage
              WHEN 'early' THEN 1
              WHEN 'assertive' THEN 2
              WHEN 'escalation' THEN 3
              WHEN 'legal_recovery' THEN 4
              WHEN 'complete' THEN 5
            END,
            lr.stage_order,
            lr.due_date ASC
        `
      } else {
        loans = await serverDb.sql`
          SELECT
            lr.*,
            c.name as customer_name,
            c.phone as customer_phone,
            c.email as customer_email,
            c.address as customer_address,
            u.full_name as created_by_name,
            (lr.loan_amount - lr.amount_paid) as outstanding_amount,
            CASE
              WHEN lr.due_date < CURRENT_DATE THEN
                CURRENT_DATE - lr.due_date
              ELSE 0
            END as days_overdue
          FROM loan_records lr
          LEFT JOIN loan_recovery_customers c ON lr.customer_id = c.id
          LEFT JOIN users u ON lr.created_by = u.id
          WHERE lr.current_stage != 'complete'
          ORDER BY
            CASE lr.current_stage
              WHEN 'early' THEN 1
              WHEN 'assertive' THEN 2
              WHEN 'escalation' THEN 3
              WHEN 'legal_recovery' THEN 4
              WHEN 'complete' THEN 5
            END,
            lr.stage_order,
            lr.due_date ASC
        `
      }
    }

    // Group loans by stage for Kanban board
    const loansByStage = {
      early: [],
      assertive: [],
      escalation: [],
      legal_recovery: [],
      complete: []
    }

    loans.forEach(loan => {
      if (loansByStage[loan.current_stage]) {
        loansByStage[loan.current_stage].push(loan)
      }
    })

    return NextResponse.json({
      success: true,
      loans: loansByStage,
      total: loans.length,
    })
  } catch (error) {
    console.error("Loans API error:", error)
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    )
  }
}

export async function POST(request: NextRequest) {
  try {
    const sessionToken = request.cookies.get("session-token")?.value

    if (!sessionToken) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 })
    }

    const user = await AuthService.verifySession(sessionToken)

    if (!user || !["admin", "hr_manager"].includes(user.role)) {
      return NextResponse.json({ error: "Forbidden" }, { status: 403 })
    }

    const body = await request.json()
    const validatedData = createLoanSchema.parse(body)

    // Verify customer exists
    const customer = await serverDb.sql`
      SELECT id FROM loan_recovery_customers 
      WHERE id = ${validatedData.customer_id}
    `

    if (customer.length === 0) {
      return NextResponse.json(
        { error: "Customer not found" },
        { status: 400 }
      )
    }

    // Validate amount_paid <= loan_amount
    if (validatedData.amount_paid > validatedData.loan_amount) {
      return NextResponse.json(
        { error: "Amount paid cannot exceed loan amount" },
        { status: 400 }
      )
    }

    // Get the next stage_order for the 'early' stage
    const maxOrderResult = await serverDb.sql`
      SELECT COALESCE(MAX(stage_order), 0) as max_order
      FROM loan_records 
      WHERE current_stage = 'early'
    `
    const nextOrder = maxOrderResult[0].max_order + 1

    const loan = await serverDb.sql`
      INSERT INTO loan_records (
        customer_id, loan_amount, amount_paid, due_date, due_date_bs,
        current_stage, stage_order, created_by, updated_by
      )
      VALUES (
        ${validatedData.customer_id},
        ${validatedData.loan_amount},
        ${validatedData.amount_paid},
        ${validatedData.due_date},
        ${validatedData.due_date_bs || null},
        'early',
        ${nextOrder},
        ${user.id},
        ${user.id}
      )
      RETURNING *
    `

    // Get the loan with customer details
    const loanWithDetails = await serverDb.sql`
      SELECT 
        lr.*,
        c.name as customer_name,
        c.phone as customer_phone,
        c.email as customer_email,
        (lr.loan_amount - lr.amount_paid) as outstanding_amount
      FROM loan_records lr
      LEFT JOIN loan_recovery_customers c ON lr.customer_id = c.id
      WHERE lr.id = ${loan[0].id}
    `

    return NextResponse.json({
      success: true,
      loan: loanWithDetails[0],
    })
  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: "Validation error", details: error.errors },
        { status: 400 }
      )
    }

    console.error("Create loan API error:", error)
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    )
  }
}
