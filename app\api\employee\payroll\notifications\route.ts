// Employee Payroll Notifications API
// Manages payroll-related notifications for employees

import { NextRequest, NextResponse } from 'next/server'
import { AuthService } from '@/lib/auth-utils'
import { serverDb as db } from '@/lib/server-db'

export async function GET(request: NextRequest) {
  try {
    const sessionToken = request.cookies.get("session-token")?.value;

    if (!sessionToken) {
      return NextResponse.json({
        success: false,
        error: 'Unauthorized'
      }, { status: 401 });
    }

    const user = await AuthService.verifySession(sessionToken);

    if (!user) {
      return NextResponse.json({
        success: false,
        error: 'Invalid session'
      }, { status: 401 });
    }

    const { searchParams } = new URL(request.url);
    const limit = parseInt(searchParams.get('limit') || '20');
    const unreadOnly = searchParams.get('unreadOnly') === 'true';

    // For now, we'll generate mock notifications based on recent payroll activity
    // In a real implementation, these would come from a notifications table
    const recentPayroll = await db.sql`
      SELECT 
        id,
        pay_period_start,
        pay_period_end,
        status,
        processed_at,
        paid_at,
        net_pay
      FROM payroll
      WHERE user_id = ${user.id}
      ORDER BY pay_period_start DESC
      LIMIT 10
    `;

    const notifications = [];

    // Generate notifications based on payroll status
    for (const payroll of recentPayroll) {
      if (payroll.status === 'processed' || payroll.status === 'paid') {
        notifications.push({
          id: `payroll_processed_${payroll.id}`,
          type: 'payroll_processed',
          title: 'Payroll Processed',
          message: `Your payroll for ${new Date(payroll.pay_period_start).toLocaleDateString()} - ${new Date(payroll.pay_period_end).toLocaleDateString()} has been processed.`,
          timestamp: payroll.processed_at || new Date().toISOString(),
          isRead: Math.random() > 0.3, // Simulate some read/unread status
          actionUrl: `/employee/payroll?payrollId=${payroll.id}`
        });
      }

      if (payroll.status === 'paid' && payroll.paid_at) {
        notifications.push({
          id: `salary_credited_${payroll.id}`,
          type: 'salary_credited',
          title: 'Salary Credited',
          message: `Your salary of NPR ${payroll.net_pay?.toLocaleString()} has been credited to your account.`,
          timestamp: payroll.paid_at,
          isRead: Math.random() > 0.5,
          actionUrl: `/employee/payroll?payrollId=${payroll.id}`
        });
      }

      // Add payslip ready notification
      if (payroll.status === 'approved' || payroll.status === 'processed' || payroll.status === 'paid') {
        notifications.push({
          id: `payslip_ready_${payroll.id}`,
          type: 'payslip_ready',
          title: 'Payslip Ready',
          message: `Your payslip for ${new Date(payroll.pay_period_start).toLocaleDateString()} is ready for download.`,
          timestamp: payroll.processed_at || new Date().toISOString(),
          isRead: Math.random() > 0.4,
          actionUrl: `/employee/payroll/download?payrollId=${payroll.id}`
        });
      }
    }

    // Add some general notifications
    notifications.push({
      id: 'tax_document_2024',
      type: 'tax_document',
      title: 'Annual Tax Document Available',
      message: 'Your annual tax statement for 2024 is now available for download.',
      timestamp: new Date(2024, 11, 31).toISOString(),
      isRead: false,
      actionUrl: '/employee/payroll/documents'
    });

    // Sort by timestamp (newest first)
    notifications.sort((a, b) => new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime());

    // Filter if unread only
    const filteredNotifications = unreadOnly 
      ? notifications.filter(n => !n.isRead)
      : notifications;

    // Limit results
    const limitedNotifications = filteredNotifications.slice(0, limit);

    return NextResponse.json({
      success: true,
      data: {
        notifications: limitedNotifications,
        total: limitedNotifications.length,
        unreadCount: notifications.filter(n => !n.isRead).length
      },
      message: `Retrieved ${limitedNotifications.length} notifications`
    });

  } catch (error) {
    console.error('Error in employee payroll notifications:', error);
    return NextResponse.json({
      success: false,
      error: error.message || 'Internal server error'
    }, { status: 500 });
  }
}

export async function PUT(request: NextRequest) {
  try {
    const sessionToken = request.cookies.get("session-token")?.value;

    if (!sessionToken) {
      return NextResponse.json({
        success: false,
        error: 'Unauthorized'
      }, { status: 401 });
    }

    const user = await AuthService.verifySession(sessionToken);

    if (!user) {
      return NextResponse.json({
        success: false,
        error: 'Invalid session'
      }, { status: 401 });
    }

    const body = await request.json();
    const { notificationId, action } = body;

    if (action === 'mark_read') {
      // In a real implementation, this would update a notifications table
      // For now, we'll just return success
      return NextResponse.json({
        success: true,
        data: {
          notificationId: notificationId,
          action: 'marked_read'
        },
        message: 'Notification marked as read'
      });
    } else if (action === 'mark_all_read') {
      // Mark all notifications as read for the user
      return NextResponse.json({
        success: true,
        data: {
          action: 'marked_all_read'
        },
        message: 'All notifications marked as read'
      });
    } else {
      return NextResponse.json({
        success: false,
        error: 'Invalid action'
      }, { status: 400 });
    }

  } catch (error) {
    console.error('Error in employee payroll notifications PUT:', error);
    return NextResponse.json({
      success: false,
      error: error.message || 'Internal server error'
    }, { status: 500 });
  }
}
