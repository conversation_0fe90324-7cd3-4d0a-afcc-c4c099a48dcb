/**
 * Nepali Calendar (<PERSON><PERSON><PERSON>) Utility Functions
 * Provides date conversion and calendar operations for Bikram Sambat calendar system
 */

export interface NepaliDate {
  year: number // Bikram Sambat year
  month: number // 1-12 (Baisakh to Chaitra)
  day: number // 1-32 depending on month
}

// BSDate is an alias for NepaliDate (<PERSON><PERSON><PERSON> Date)
export type BSDate = NepaliDate

export interface NepaliMonth {
  index: number
  nameEn: string
  nameNp: string
  romanized: string
}

// Nepali month names with Devanagari script
export const NEPALI_MONTHS: NepaliMonth[] = [
  { index: 1, nameEn: "<PERSON>sakh", nameNp: "बैशाख", romanized: "<PERSON>sakh" },
  { index: 2, nameEn: "Jestha", nameNp: "जेष्ठ", romanized: "Jestha" },
  { index: 3, nameEn: "Ashadh", nameNp: "आषाढ", romanized: "Ashadh" },
  { index: 4, nameEn: "<PERSON><PERSON>wan", nameNp: "श्रावण", romanized: "<PERSON><PERSON><PERSON>" },
  { index: 5, nameEn: "<PERSON><PERSON><PERSON>", nameNp: "भाद्र", romanized: "<PERSON><PERSON><PERSON>" },
  { index: 6, nameEn: "<PERSON><PERSON>", nameNp: "आश्विन", romanized: "<PERSON><PERSON>" },
  { index: 7, nameEn: "Kartik", nameNp: "कार्तिक", romanized: "Kartik" },
  { index: 8, nameEn: "Mangsir", nameNp: "मंसिर", romanized: "Mangsir" },
  { index: 9, nameEn: "Poush", nameNp: "पौष", romanized: "Poush" },
  { index: 10, nameEn: "Magh", nameNp: "माघ", romanized: "Magh" },
  { index: 11, nameEn: "Falgun", nameNp: "फाल्गुन", romanized: "Falgun" },
  { index: 12, nameEn: "Chaitra", nameNp: "चैत्र", romanized: "Chaitra" }
]

// Nepali calendar data with month lengths for each year
// This is a simplified lookup table for common business years
export const NEPALI_CALENDAR_DATA: { [year: number]: number[] } = {
  2081: [31, 31, 32, 32, 31, 30, 30, 29, 30, 29, 30, 30], // 2024-2025
  2082: [31, 31, 32, 32, 31, 30, 30, 29, 30, 29, 30, 30], // 2025-2026
  2083: [31, 32, 31, 32, 31, 30, 30, 30, 29, 29, 30, 31], // 2026-2027
  2084: [30, 32, 31, 32, 31, 30, 30, 30, 29, 30, 29, 31], // 2027-2028
  2085: [31, 31, 32, 31, 31, 31, 30, 29, 30, 29, 30, 30], // 2028-2029
  2086: [31, 31, 32, 31, 31, 31, 30, 29, 30, 29, 30, 30], // 2029-2030
  2087: [31, 32, 31, 32, 31, 30, 30, 30, 29, 29, 30, 31], // 2030-2031
  2088: [30, 32, 31, 32, 31, 30, 30, 30, 29, 30, 29, 31], // 2031-2032
  2089: [31, 31, 32, 31, 31, 31, 30, 29, 30, 29, 30, 30], // 2032-2033
  2090: [31, 31, 32, 31, 31, 31, 30, 29, 30, 29, 30, 30], // 2033-2034
}

// Base conversion reference: 1 Baisakh 2081 BS = 14 April 2024 AD
const BASE_NEPALI_DATE: NepaliDate = { year: 2081, month: 1, day: 1 }
const BASE_GREGORIAN_DATE = new Date(2024, 3, 14) // April 14, 2024

/**
 * Get the number of days in a Nepali month
 */
export function getNepaliMonthDays(year: number, month: number): number {
  const yearData = NEPALI_CALENDAR_DATA[year]
  if (!yearData) {
    // Fallback to average days if year data not available
    return month <= 8 ? 31 : 30
  }
  return yearData[month - 1] || 30
}

/**
 * Get Nepali month information
 */
export function getNepaliMonth(monthIndex: number): NepaliMonth {
  return NEPALI_MONTHS[monthIndex - 1] || NEPALI_MONTHS[0]
}

/**
 * Convert Gregorian date to approximate Nepali date
 * This is a simplified conversion for business use
 */
export function gregorianToNepali(gregorianDate: Date): NepaliDate {
  // Calculate days difference from base date
  const timeDiff = gregorianDate.getTime() - BASE_GREGORIAN_DATE.getTime()
  const daysDiff = Math.floor(timeDiff / (1000 * 60 * 60 * 24))

  let nepaliYear = BASE_NEPALI_DATE.year
  let nepaliMonth = BASE_NEPALI_DATE.month
  let nepaliDay = BASE_NEPALI_DATE.day + daysDiff

  // Adjust for month and year overflow/underflow
  while (nepaliDay > getNepaliMonthDays(nepaliYear, nepaliMonth)) {
    nepaliDay -= getNepaliMonthDays(nepaliYear, nepaliMonth)
    nepaliMonth++
    if (nepaliMonth > 12) {
      nepaliMonth = 1
      nepaliYear++
    }
  }

  while (nepaliDay < 1) {
    nepaliMonth--
    if (nepaliMonth < 1) {
      nepaliMonth = 12
      nepaliYear--
    }
    nepaliDay += getNepaliMonthDays(nepaliYear, nepaliMonth)
  }

  return { year: nepaliYear, month: nepaliMonth, day: nepaliDay }
}

/**
 * Convert Nepali date to approximate Gregorian date
 */
export function nepaliToGregorian(nepaliDate: NepaliDate): Date {
  let totalDays = 0

  // Calculate days from base Nepali date
  const yearDiff = nepaliDate.year - BASE_NEPALI_DATE.year
  const monthDiff = nepaliDate.month - BASE_NEPALI_DATE.month
  const dayDiff = nepaliDate.day - BASE_NEPALI_DATE.day

  // Add days for year difference
  for (let year = BASE_NEPALI_DATE.year; year < nepaliDate.year; year++) {
    const yearData = NEPALI_CALENDAR_DATA[year]
    if (yearData) {
      totalDays += yearData.reduce((sum, days) => sum + days, 0)
    } else {
      totalDays += 365 // Approximate
    }
  }

  // Add days for month difference
  for (let month = BASE_NEPALI_DATE.month; month < nepaliDate.month; month++) {
    totalDays += getNepaliMonthDays(nepaliDate.year, month)
  }

  // Add day difference
  totalDays += dayDiff

  // Calculate Gregorian date
  const resultDate = new Date(BASE_GREGORIAN_DATE)
  resultDate.setDate(resultDate.getDate() + totalDays)

  return resultDate
}

/**
 * Get the start and end Gregorian dates for a Nepali month
 */
export function getNepaliMonthDateRange(nepaliYear: number, nepaliMonth: number): {
  startDate: Date
  endDate: Date
} {
  const startNepali: NepaliDate = { year: nepaliYear, month: nepaliMonth, day: 1 }
  const monthDays = getNepaliMonthDays(nepaliYear, nepaliMonth)
  const endNepali: NepaliDate = { year: nepaliYear, month: nepaliMonth, day: monthDays }

  return {
    startDate: nepaliToGregorian(startNepali),
    endDate: nepaliToGregorian(endNepali)
  }
}

/**
 * Navigate Nepali months (add/subtract months)
 */
export function navigateNepaliMonth(nepaliDate: NepaliDate, monthsToAdd: number): NepaliDate {
  let { year, month, day } = nepaliDate

  month += monthsToAdd

  while (month > 12) {
    month -= 12
    year++
  }

  while (month < 1) {
    month += 12
    year--
  }

  // Adjust day if it exceeds the new month's days
  const maxDays = getNepaliMonthDays(year, month)
  if (day > maxDays) {
    day = maxDays
  }

  return { year, month, day }
}

/**
 * Format Nepali date for display
 */
export function formatNepaliDate(nepaliDate: NepaliDate, includeDevanagari = false): string {
  const month = getNepaliMonth(nepaliDate.month)
  const monthName = includeDevanagari ? month.nameNp : month.nameEn
  return `${monthName} ${nepaliDate.year} BS`
}

/**
 * Get current Nepali date
 */
export function getCurrentNepaliDate(): NepaliDate {
  return gregorianToNepali(new Date())
}

/**
 * Check if a year has Nepali calendar data
 */
export function hasNepaliYearData(year: number): boolean {
  return year in NEPALI_CALENDAR_DATA
}

/**
 * Get available Nepali years
 */
export function getAvailableNepaliYears(): number[] {
  return Object.keys(NEPALI_CALENDAR_DATA).map(Number).sort()
}

/**
 * Generate date key for attendance mapping (compatible with Gregorian format)
 */
export function generateNepaliDateKey(nepaliDate: NepaliDate): string {
  const gregorianDate = nepaliToGregorian(nepaliDate)
  return gregorianDate.toISOString().split('T')[0]
}

/**
 * Get days in Nepali month with proper calendar layout
 */
export function getNepaliMonthCalendarDays(nepaliYear: number, nepaliMonth: number): (number | null)[] {
  const monthDays = getNepaliMonthDays(nepaliYear, nepaliMonth)
  const firstDayGregorian = nepaliToGregorian({ year: nepaliYear, month: nepaliMonth, day: 1 })
  const firstDayOfWeek = firstDayGregorian.getDay() // 0 = Sunday

  const days: (number | null)[] = []

  // Add empty cells for days before the first day of the month
  for (let i = 0; i < firstDayOfWeek; i++) {
    days.push(null)
  }

  // Add all days of the month
  for (let day = 1; day <= monthDays; day++) {
    days.push(day)
  }

  return days
}

/**
 * Format BS date for display
 */
export function formatBSDate(bsDate: BSDate, format: 'short' | 'long' = 'short'): string {
  if (format === 'long') {
    const month = getNepaliMonth(bsDate.month)
    return `${bsDate.day} ${month.nameEn} ${bsDate.year} BS`
  }
  return `${bsDate.year}-${bsDate.month.toString().padStart(2, '0')}-${bsDate.day.toString().padStart(2, '0')}`
}

/**
 * Validate BS date
 */
export function isValidBSDate(bsDate: BSDate): boolean {
  if (!bsDate || typeof bsDate.year !== 'number' || typeof bsDate.month !== 'number' || typeof bsDate.day !== 'number') {
    return false
  }

  if (bsDate.month < 1 || bsDate.month > 12) {
    return false
  }

  if (bsDate.day < 1) {
    return false
  }

  const maxDays = getNepaliMonthDays(bsDate.year, bsDate.month)
  return bsDate.day <= maxDays
}

/**
 * Get BS fiscal year from BS date
 */
export function getBSFiscalYear(bsDate: BSDate): string {
  // Fiscal year starts from Shrawan (month 4)
  // If month is 1-3 (Baisakh to Ashadh), it belongs to previous fiscal year
  if (bsDate.month >= 4) {
    // Shrawan to Chaitra - current fiscal year
    const nextYear = bsDate.year + 1
    return `${bsDate.year}-${nextYear.toString().slice(-2)}`
  } else {
    // Baisakh to Ashadh - previous fiscal year
    const prevYear = bsDate.year - 1
    return `${prevYear}-${bsDate.year.toString().slice(-2)}`
  }
}

/**
 * Get fiscal year date ranges
 */
export function getFiscalYearDates(fiscalYear: string): { start: BSDate; end: BSDate } {
  const parts = fiscalYear.split('-')
  const startYear = parseInt(parts[0])
  const endYear = parseInt(`20${parts[1]}`)

  return {
    start: { year: startYear, month: 4, day: 1 }, // 1 Shrawan
    end: { year: endYear, month: 3, day: getNepaliMonthDays(endYear, 3) } // Last day of Ashadh
  }
}

/**
 * Get BS month name
 */
export function getBSMonthName(month: number, language: 'en' | 'ne' = 'en'): string {
  const monthData = getNepaliMonth(month)
  return language === 'ne' ? monthData.nameNp : monthData.nameEn
}

/**
 * NepaliCalendar object providing all calendar operations
 * This object provides a unified interface for all Nepali calendar operations
 */
export const NepaliCalendar = {
  // Date conversion methods
  adToBS: gregorianToNepali,
  bsToAD: nepaliToGregorian,

  // Date formatting and validation
  formatBSDate,
  isValidBSDate,

  // Month and year operations
  getDaysInBSMonth: getNepaliMonthDays,
  getDaysInMonth: getNepaliMonthDays, // Alias for compatibility
  getBSMonthName,

  // Fiscal year operations
  getBSFiscalYear,
  getFiscalYearDates,

  // Utility methods
  getCurrentBSDate: getCurrentNepaliDate,
  navigateBSMonth: navigateNepaliMonth,
  generateDateKey: generateNepaliDateKey,
  getMonthCalendarDays: getNepaliMonthCalendarDays,

  // Calendar data access
  getAvailableYears: getAvailableNepaliYears,
  hasYearData: hasNepaliYearData,
  getMonthData: getNepaliMonth,

  // Constants
  MONTHS: NEPALI_MONTHS,
  CALENDAR_DATA: NEPALI_CALENDAR_DATA
}
