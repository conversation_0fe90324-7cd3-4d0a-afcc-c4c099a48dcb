// Nepal Fiscal Year Management System
// Phase 3: Nepal Localization Implementation - Fiscal Year Management

import { NepaliCalendar, BSDate } from './nepali-calendar'
import { nepalConfig } from './nepal-config'
import { db } from './neon'

export interface FiscalYear {
  id: string
  fiscalYear: string // e.g., "2081-82"
  bsStartDate: BSDate
  bsEndDate: BSDate
  adStartDate: Date
  adEndDate: Date
  isActive: boolean
  isCurrent: boolean
  totalDays: number
  workingDays: number
  holidays: number
  quarters: FiscalQuarter[]
  months: FiscalMonth[]
}

export interface FiscalQuarter {
  id: string
  fiscalYear: string
  quarter: number // 1, 2, 3, 4
  name: string // e.g., "Q1 2081-82"
  bsStartDate: BSDate
  bsEndDate: BSDate
  adStartDate: Date
  adEndDate: Date
  months: number[] // BS month numbers
  workingDays: number
  holidays: number
}

export interface FiscalMonth {
  id: string
  fiscalYear: string
  quarter: number
  bsMonth: number
  bsYear: number
  monthName: string
  monthNameNepali: string
  bsStartDate: BSDate
  bsEndDate: BSDate
  adStartDate: Date
  adEndDate: Date
  workingDays: number
  holidays: number
  isCurrentMonth: boolean
}

export interface PayrollPeriod {
  id: string
  name: string
  type: 'monthly' | 'quarterly' | 'yearly' | 'custom'
  fiscalYear: string
  bsStartDate: BSDate
  bsEndDate: BSDate
  adStartDate: Date
  adEndDate: Date
  workingDays: number
  holidays: number
  isActive: boolean
  isClosed: boolean
  payrollProcessed: boolean
  employeeCount?: number
  totalPayroll?: number
}

export class FiscalYearManager {
  private nepalConfig = nepalConfig

  /**
   * Get current fiscal year
   */
  getCurrentFiscalYear(): string {
    const today = new Date()
    const bsToday = NepaliCalendar.adToBS(today)
    return NepaliCalendar.getBSFiscalYear(bsToday)
  }

  /**
   * Generate fiscal year data
   */
  generateFiscalYear(fiscalYear: string): FiscalYear {
    const fiscalDates = NepaliCalendar.getFiscalYearDates(fiscalYear)
    const adStartDate = NepaliCalendar.bsToAD(fiscalDates.start)
    const adEndDate = NepaliCalendar.bsToAD(fiscalDates.end)
    
    const totalDays = Math.ceil((adEndDate.getTime() - adStartDate.getTime()) / (1000 * 60 * 60 * 24)) + 1
    const workingDays = this.nepalConfig.getWorkingDaysInPeriod(
      adStartDate.toISOString().split('T')[0],
      adEndDate.toISOString().split('T')[0]
    )
    const holidays = this.nepalConfig.getHolidaysInRange(
      adStartDate.toISOString().split('T')[0],
      adEndDate.toISOString().split('T')[0]
    ).length

    const quarters = this.generateQuarters(fiscalYear)
    const months = this.generateMonths(fiscalYear)
    const currentFY = this.getCurrentFiscalYear()

    return {
      id: `fy-${fiscalYear}`,
      fiscalYear,
      bsStartDate: fiscalDates.start,
      bsEndDate: fiscalDates.end,
      adStartDate,
      adEndDate,
      isActive: true,
      isCurrent: fiscalYear === currentFY,
      totalDays,
      workingDays,
      holidays,
      quarters,
      months
    }
  }

  /**
   * Generate quarters for a fiscal year
   */
  private generateQuarters(fiscalYear: string): FiscalQuarter[] {
    const quarters: FiscalQuarter[] = []
    const startYear = parseInt(fiscalYear.split('-')[0])

    // Q1: Shrawan, Bhadra, Ashwin (months 4, 5, 6)
    // Q2: Kartik, Mangsir, Poush (months 7, 8, 9)
    // Q3: Magh, Falgun, Chaitra (months 10, 11, 12)
    // Q4: Baishakh, Jestha, Ashadh (months 1, 2, 3 of next year)

    const quarterDefinitions = [
      { quarter: 1, months: [4, 5, 6], year: startYear },
      { quarter: 2, months: [7, 8, 9], year: startYear },
      { quarter: 3, months: [10, 11, 12], year: startYear },
      { quarter: 4, months: [1, 2, 3], year: startYear + 1 }
    ]

    quarterDefinitions.forEach(({ quarter, months, year }) => {
      const startMonth = months[0]
      const endMonth = months[2]
      
      const bsStartDate: BSDate = { year, month: startMonth, day: 1 }
      const bsEndDate: BSDate = { 
        year, 
        month: endMonth, 
        day: NepaliCalendar.getDaysInBSMonth(year, endMonth) 
      }
      
      const adStartDate = NepaliCalendar.bsToAD(bsStartDate)
      const adEndDate = NepaliCalendar.bsToAD(bsEndDate)
      
      const workingDays = this.nepalConfig.getWorkingDaysInPeriod(
        adStartDate.toISOString().split('T')[0],
        adEndDate.toISOString().split('T')[0]
      )
      
      const holidays = this.nepalConfig.getHolidaysInRange(
        adStartDate.toISOString().split('T')[0],
        adEndDate.toISOString().split('T')[0]
      ).length

      quarters.push({
        id: `q${quarter}-${fiscalYear}`,
        fiscalYear,
        quarter,
        name: `Q${quarter} ${fiscalYear}`,
        bsStartDate,
        bsEndDate,
        adStartDate,
        adEndDate,
        months,
        workingDays,
        holidays
      })
    })

    return quarters
  }

  /**
   * Generate months for a fiscal year
   */
  private generateMonths(fiscalYear: string): FiscalMonth[] {
    const months: FiscalMonth[] = []
    const startYear = parseInt(fiscalYear.split('-')[0])
    const currentBSDate = NepaliCalendar.adToBS(new Date())
    
    // Fiscal year months: Shrawan (4) to Ashadh (3 of next year)
    const monthSequence = [
      { month: 4, year: startYear, quarter: 1 },   // Shrawan
      { month: 5, year: startYear, quarter: 1 },   // Bhadra
      { month: 6, year: startYear, quarter: 1 },   // Ashwin
      { month: 7, year: startYear, quarter: 2 },   // Kartik
      { month: 8, year: startYear, quarter: 2 },   // Mangsir
      { month: 9, year: startYear, quarter: 2 },   // Poush
      { month: 10, year: startYear, quarter: 3 },  // Magh
      { month: 11, year: startYear, quarter: 3 },  // Falgun
      { month: 12, year: startYear, quarter: 3 },  // Chaitra
      { month: 1, year: startYear + 1, quarter: 4 }, // Baishakh
      { month: 2, year: startYear + 1, quarter: 4 }, // Jestha
      { month: 3, year: startYear + 1, quarter: 4 }  // Ashadh
    ]

    monthSequence.forEach(({ month, year, quarter }) => {
      const bsStartDate: BSDate = { year, month, day: 1 }
      const bsEndDate: BSDate = { 
        year, 
        month, 
        day: NepaliCalendar.getDaysInBSMonth(year, month) 
      }
      
      const adStartDate = NepaliCalendar.bsToAD(bsStartDate)
      const adEndDate = NepaliCalendar.bsToAD(bsEndDate)
      
      const workingDays = this.nepalConfig.getWorkingDaysInPeriod(
        adStartDate.toISOString().split('T')[0],
        adEndDate.toISOString().split('T')[0]
      )
      
      const holidays = this.nepalConfig.getHolidaysInRange(
        adStartDate.toISOString().split('T')[0],
        adEndDate.toISOString().split('T')[0]
      ).length

      const isCurrentMonth = currentBSDate.year === year && currentBSDate.month === month

      months.push({
        id: `m${month}-${year}-${fiscalYear}`,
        fiscalYear,
        quarter,
        bsMonth: month,
        bsYear: year,
        monthName: NepaliCalendar.getBSMonthName(month, 'en'),
        monthNameNepali: NepaliCalendar.getBSMonthName(month, 'ne'),
        bsStartDate,
        bsEndDate,
        adStartDate,
        adEndDate,
        workingDays,
        holidays,
        isCurrentMonth
      })
    })

    return months
  }

  /**
   * Get available fiscal years
   */
  getAvailableFiscalYears(count: number = 5): string[] {
    const currentFY = this.getCurrentFiscalYear()
    const currentYear = parseInt(currentFY.split('-')[0])
    
    const fiscalYears: string[] = []
    
    // Generate previous, current, and future fiscal years
    for (let i = -2; i <= count - 3; i++) {
      const year = currentYear + i
      const nextYear = year + 1
      fiscalYears.push(`${year}-${nextYear.toString().slice(-2)}`)
    }
    
    return fiscalYears
  }

  /**
   * Create payroll periods for a fiscal year
   */
  async createPayrollPeriods(fiscalYear: string): Promise<PayrollPeriod[]> {
    const fyData = this.generateFiscalYear(fiscalYear)
    const periods: PayrollPeriod[] = []

    // Create monthly periods
    fyData.months.forEach((month) => {
      periods.push({
        id: `monthly-${month.id}`,
        name: `${month.monthName} ${month.bsYear}`,
        type: 'monthly',
        fiscalYear,
        bsStartDate: month.bsStartDate,
        bsEndDate: month.bsEndDate,
        adStartDate: month.adStartDate,
        adEndDate: month.adEndDate,
        workingDays: month.workingDays,
        holidays: month.holidays,
        isActive: true,
        isClosed: false,
        payrollProcessed: false
      })
    })

    // Create quarterly periods
    fyData.quarters.forEach((quarter) => {
      periods.push({
        id: `quarterly-${quarter.id}`,
        name: quarter.name,
        type: 'quarterly',
        fiscalYear,
        bsStartDate: quarter.bsStartDate,
        bsEndDate: quarter.bsEndDate,
        adStartDate: quarter.adStartDate,
        adEndDate: quarter.adEndDate,
        workingDays: quarter.workingDays,
        holidays: quarter.holidays,
        isActive: true,
        isClosed: false,
        payrollProcessed: false
      })
    })

    // Create yearly period
    periods.push({
      id: `yearly-${fyData.id}`,
      name: `FY ${fiscalYear}`,
      type: 'yearly',
      fiscalYear,
      bsStartDate: fyData.bsStartDate,
      bsEndDate: fyData.bsEndDate,
      adStartDate: fyData.adStartDate,
      adEndDate: fyData.adEndDate,
      workingDays: fyData.workingDays,
      holidays: fyData.holidays,
      isActive: true,
      isClosed: false,
      payrollProcessed: false
    })

    return periods
  }

  /**
   * Get current payroll period
   */
  getCurrentPayrollPeriod(type: 'monthly' | 'quarterly' = 'monthly'): PayrollPeriod | null {
    const currentFY = this.getCurrentFiscalYear()
    const fyData = this.generateFiscalYear(currentFY)
    const today = new Date()
    const bsToday = NepaliCalendar.adToBS(today)

    if (type === 'monthly') {
      const currentMonth = fyData.months.find(m => m.isCurrentMonth)
      if (currentMonth) {
        return {
          id: `current-monthly-${currentMonth.id}`,
          name: `${currentMonth.monthName} ${currentMonth.bsYear}`,
          type: 'monthly',
          fiscalYear: currentFY,
          bsStartDate: currentMonth.bsStartDate,
          bsEndDate: currentMonth.bsEndDate,
          adStartDate: currentMonth.adStartDate,
          adEndDate: currentMonth.adEndDate,
          workingDays: currentMonth.workingDays,
          holidays: currentMonth.holidays,
          isActive: true,
          isClosed: false,
          payrollProcessed: false
        }
      }
    } else {
      // Find current quarter
      const currentQuarter = fyData.quarters.find(q => {
        return q.months.includes(bsToday.month)
      })
      
      if (currentQuarter) {
        return {
          id: `current-quarterly-${currentQuarter.id}`,
          name: currentQuarter.name,
          type: 'quarterly',
          fiscalYear: currentFY,
          bsStartDate: currentQuarter.bsStartDate,
          bsEndDate: currentQuarter.bsEndDate,
          adStartDate: currentQuarter.adStartDate,
          adEndDate: currentQuarter.adEndDate,
          workingDays: currentQuarter.workingDays,
          holidays: currentQuarter.holidays,
          isActive: true,
          isClosed: false,
          payrollProcessed: false
        }
      }
    }

    return null
  }

  /**
   * Check if date falls within fiscal year
   */
  isDateInFiscalYear(date: Date, fiscalYear: string): boolean {
    const fyData = this.generateFiscalYear(fiscalYear)
    return date >= fyData.adStartDate && date <= fyData.adEndDate
  }

  /**
   * Get fiscal year for a specific date
   */
  getFiscalYearForDate(date: Date): string {
    const bsDate = NepaliCalendar.adToBS(date)
    return NepaliCalendar.getBSFiscalYear(bsDate)
  }

  /**
   * Get payroll period summary
   */
  getPayrollPeriodSummary(period: PayrollPeriod): {
    totalDays: number
    workingDays: number
    weekends: number
    holidays: number
    attendanceRate?: number
    payrollStatus: string
  } {
    const totalDays = Math.ceil((period.adEndDate.getTime() - period.adStartDate.getTime()) / (1000 * 60 * 60 * 24)) + 1
    const weekends = totalDays - period.workingDays - period.holidays

    return {
      totalDays,
      workingDays: period.workingDays,
      weekends,
      holidays: period.holidays,
      payrollStatus: period.payrollProcessed ? 'Processed' : period.isClosed ? 'Closed' : 'Active'
    }
  }

  /**
   * Format fiscal year for display
   */
  formatFiscalYear(fiscalYear: string, format: 'short' | 'long' | 'nepali' = 'long'): string {
    const parts = fiscalYear.split('-')
    const startYear = parseInt(parts[0])
    const endYear = parseInt(`20${parts[1]}`)

    switch (format) {
      case 'short':
        return `FY ${parts[1]}`
      case 'nepali':
        return `आर्थिक वर्ष ${startYear}-${endYear}`
      default:
        return `Fiscal Year ${startYear}-${endYear}`
    }
  }

  /**
   * Get next fiscal year
   */
  getNextFiscalYear(fiscalYear?: string): string {
    const currentFY = fiscalYear || this.getCurrentFiscalYear()
    const currentYear = parseInt(currentFY.split('-')[0])
    const nextYear = currentYear + 1
    return `${nextYear}-${(nextYear + 1).toString().slice(-2)}`
  }

  /**
   * Get previous fiscal year
   */
  getPreviousFiscalYear(fiscalYear?: string): string {
    const currentFY = fiscalYear || this.getCurrentFiscalYear()
    const currentYear = parseInt(currentFY.split('-')[0])
    const prevYear = currentYear - 1
    return `${prevYear}-${(prevYear + 1).toString().slice(-2)}`
  }
}

// Export singleton instance
export const fiscalYearManager = new FiscalYearManager()
