const { neon } = require('@neondatabase/serverless');
require('dotenv').config({ path: '.env.local' });

const sql = neon(process.env.DATABASE_URL);

async function checkRecoveryTables() {
  try {
    console.log('🔍 Checking loan recovery tables...\n');

    // Check what tables exist
    console.log('1. Checking existing tables...');
    const tables = await sql`
      SELECT table_name 
      FROM information_schema.tables 
      WHERE table_schema = 'public' 
      AND table_name LIKE '%recovery%' OR table_name LIKE '%loan%'
      ORDER BY table_name
    `;
    
    console.log('Existing loan/recovery tables:');
    tables.forEach(table => {
      console.log(`  - ${table.table_name}`);
    });

    // Check if the required tables exist
    const requiredTables = [
      'loan_recovery_customers',
      'loan_records', 
      'loan_recovery_notes',
      'loan_recovery_reminders'
    ];

    console.log('\n2. Checking required tables...');
    for (const tableName of requiredTables) {
      try {
        const result = await sql`
          SELECT COUNT(*) as count 
          FROM information_schema.tables 
          WHERE table_schema = 'public' 
          AND table_name = ${tableName}
        `;
        
        if (result[0].count > 0) {
          console.log(`  ✅ ${tableName} - EXISTS`);
          
          // Check row count
          const rowCount = await sql.unsafe(`SELECT COUNT(*) as count FROM ${tableName}`);
          console.log(`     Rows: ${rowCount[0].count}`);
        } else {
          console.log(`  ❌ ${tableName} - MISSING`);
        }
      } catch (error) {
        console.log(`  ❌ ${tableName} - ERROR: ${error.message}`);
      }
    }

    console.log('\n3. Creating missing tables...');
    
    // Create loan_recovery_notes table if it doesn't exist
    try {
      await sql`
        CREATE TABLE IF NOT EXISTS loan_recovery_notes (
          id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
          customer_id UUID NOT NULL REFERENCES loan_recovery_customers(id) ON DELETE CASCADE,
          note TEXT NOT NULL,
          created_by UUID REFERENCES users(id),
          created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
          updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
        )
      `;
      console.log('  ✅ loan_recovery_notes table created/verified');
    } catch (error) {
      console.log(`  ❌ Error creating loan_recovery_notes: ${error.message}`);
    }

    // Create loan_recovery_reminders table if it doesn't exist
    try {
      await sql`
        CREATE TABLE IF NOT EXISTS loan_recovery_reminders (
          id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
          customer_id UUID NOT NULL REFERENCES loan_recovery_customers(id) ON DELETE CASCADE,
          reminder_text TEXT NOT NULL,
          reminder_date TIMESTAMP WITH TIME ZONE NOT NULL,
          is_completed BOOLEAN DEFAULT FALSE,
          created_by UUID REFERENCES users(id),
          created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
          updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
        )
      `;
      console.log('  ✅ loan_recovery_reminders table created/verified');
    } catch (error) {
      console.log(`  ❌ Error creating loan_recovery_reminders: ${error.message}`);
    }

    // Add some sample data
    console.log('\n4. Adding sample data...');
    
    // Get a customer ID for sample data
    const customers = await sql`SELECT id FROM loan_recovery_customers LIMIT 1`;
    if (customers.length > 0) {
      const customerId = customers[0].id;
      
      // Add sample notes
      try {
        await sql`
          INSERT INTO loan_recovery_notes (customer_id, note, created_by)
          VALUES 
            (${customerId}, 'Initial contact made. Customer acknowledged the debt.', NULL),
            (${customerId}, 'Follow-up call scheduled for next week.', NULL)
          ON CONFLICT DO NOTHING
        `;
        console.log('  ✅ Sample notes added');
      } catch (error) {
        console.log(`  ❌ Error adding sample notes: ${error.message}`);
      }

      // Add sample reminders
      try {
        await sql`
          INSERT INTO loan_recovery_reminders (customer_id, reminder_text, reminder_date)
          VALUES 
            (${customerId}, 'Call customer for payment follow-up', CURRENT_TIMESTAMP + INTERVAL '1 day'),
            (${customerId}, 'Send payment reminder email', CURRENT_TIMESTAMP + INTERVAL '3 days')
          ON CONFLICT DO NOTHING
        `;
        console.log('  ✅ Sample reminders added');
      } catch (error) {
        console.log(`  ❌ Error adding sample reminders: ${error.message}`);
      }
    }

    console.log('\n✅ Loan recovery tables check completed!');

  } catch (error) {
    console.error('❌ Error:', error);
  }
}

checkRecoveryTables();
