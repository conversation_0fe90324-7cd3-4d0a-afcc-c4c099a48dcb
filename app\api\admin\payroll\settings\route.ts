// Admin Payroll Settings API Endpoint
// Phase 2: Core Payroll Engine Development - API Endpoints

import { NextRequest, NextResponse } from 'next/server';
import { db } from '@/lib/neon';

export async function GET(request: NextRequest) {
  try {
    // Get all payroll settings
    const settings = await db.getPayrollSettings();

    // Group settings by category for better organization
    const categorizedSettings = {
      workingTime: {
        standardWorkingHoursPerDay: settings.standard_working_hours_per_day || 8,
        standardWorkingDaysPerWeek: settings.standard_working_days_per_week || 6,
        weeklyOffDays: settings.weekly_off_days || [6],
        overtimeThresholdDaily: settings.overtime_threshold_daily || 8,
        overtimeRateMultiplier: settings.overtime_rate_multiplier || 1.5,
        maxWorkingHoursPerDay: settings.max_working_hours_per_day || 12,
        maxOvertimeHoursPerDay: settings.max_overtime_hours_per_day || 4
      },
      salary: {
        minimumWageMonthly: settings.minimum_wage_monthly || 17300,
        providentFundRateEmployee: settings.provident_fund_rate_employee || 10,
        providentFundRateEmployer: settings.provident_fund_rate_employer || 10,
        socialSecurityFundRate: settings.social_security_fund_rate || 11,
        incomeTaxThresholdAnnual: settings.income_tax_threshold_annual || 500000
      },
      currency: {
        currencyCode: settings.currency_code || 'NPR',
        currencySymbol: settings.currency_symbol || 'रू',
        useIndianNumbering: settings.use_indian_numbering || true,
        decimalPlaces: settings.decimal_places || 2
      },
      fiscalYear: {
        fiscalYearStartMonthBs: settings.fiscal_year_start_month_bs || 4,
        fiscalYearStartDayBs: settings.fiscal_year_start_day_bs || 1,
        currentFiscalYear: settings.current_fiscal_year || '2081-82'
      },
      attendance: {
        attendanceBonusThreshold: settings.attendance_bonus_threshold || 95,
        attendanceBonusAmount: settings.attendance_bonus_amount || 2000,
        latePenaltyPerMinute: settings.late_penalty_per_minute || 10,
        earlyDeparturePenalty: settings.early_departure_penalty || 500
      },
      leave: {
        annualLeaveDays: settings.annual_leave_days || 18,
        sickLeaveDays: settings.sick_leave_days || 12,
        maternityLeaveDays: settings.maternity_leave_days || 98,
        paternityLeaveDays: settings.paternity_leave_days || 15
      },
      festival: {
        festivalBonusMonths: settings.festival_bonus_months || ['7', '8'],
        festivalBonusPercentage: settings.festival_bonus_percentage || 100
      },
      processing: {
        payrollProcessingDay: settings.payroll_processing_day || 1,
        payrollPaymentDay: settings.payroll_payment_day || 5,
        payrollCutoffDay: settings.payroll_cutoff_day || 25
      },
      system: {
        enableBsCalendar: settings.enable_bs_calendar || true,
        defaultLanguage: settings.default_language || 'en',
        enableDualCalendarDisplay: settings.enable_dual_calendar_display || true
      }
    };

    return NextResponse.json({
      success: true,
      data: categorizedSettings
    });

  } catch (error) {
    console.error('Error fetching payroll settings:', error);
    return NextResponse.json(
      { 
        success: false, 
        error: 'Failed to fetch payroll settings',
        details: error.message 
      },
      { status: 500 }
    );
  }
}

export async function PUT(request: NextRequest) {
  try {
    const body = await request.json();
    const { settings, updatedBy } = body;

    if (!settings || !updatedBy) {
      return NextResponse.json(
        { success: false, error: 'Settings and updatedBy are required' },
        { status: 400 }
      );
    }

    // Flatten the categorized settings back to individual settings
    const flatSettings = flattenSettings(settings);

    // Update each setting
    const updatePromises = Object.entries(flatSettings).map(async ([key, value]) => {
      const settingType = typeof value === 'number' ? 'number' : 
                         typeof value === 'boolean' ? 'boolean' :
                         Array.isArray(value) ? 'json' : 'string';
      
      const settingValue = settingType === 'json' ? JSON.stringify(value) : String(value);

      return db.sql`
        UPDATE payroll_settings
        SET 
          setting_value = ${settingValue},
          updated_at = NOW()
        WHERE setting_key = ${key}
      `;
    });

    await Promise.all(updatePromises);

    // Log the settings update
    await db.sql`
      INSERT INTO payroll_activity_log (action, performed_by, performed_at, details)
      VALUES ('settings_updated', ${updatedBy}, NOW(), ${JSON.stringify({ updatedSettings: Object.keys(flatSettings) })})
    `;

    return NextResponse.json({
      success: true,
      message: 'Payroll settings updated successfully',
      updatedSettings: Object.keys(flatSettings).length
    });

  } catch (error) {
    console.error('Error updating payroll settings:', error);
    return NextResponse.json(
      { 
        success: false, 
        error: 'Failed to update payroll settings',
        details: error.message 
      },
      { status: 500 }
    );
  }
}

// Get specific setting category
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { category } = body;

    if (!category) {
      return NextResponse.json(
        { success: false, error: 'Category is required' },
        { status: 400 }
      );
    }

    // Get all settings first
    const allSettings = await db.getPayrollSettings();

    // Filter settings by category (based on key prefixes)
    let categorySettings = {};

    switch (category) {
      case 'workingTime':
        categorySettings = {
          standardWorkingHoursPerDay: allSettings.standard_working_hours_per_day,
          standardWorkingDaysPerWeek: allSettings.standard_working_days_per_week,
          weeklyOffDays: allSettings.weekly_off_days,
          overtimeThresholdDaily: allSettings.overtime_threshold_daily,
          overtimeRateMultiplier: allSettings.overtime_rate_multiplier,
          maxWorkingHoursPerDay: allSettings.max_working_hours_per_day,
          maxOvertimeHoursPerDay: allSettings.max_overtime_hours_per_day
        };
        break;
      case 'salary':
        categorySettings = {
          minimumWageMonthly: allSettings.minimum_wage_monthly,
          providentFundRateEmployee: allSettings.provident_fund_rate_employee,
          providentFundRateEmployer: allSettings.provident_fund_rate_employer,
          socialSecurityFundRate: allSettings.social_security_fund_rate,
          incomeTaxThresholdAnnual: allSettings.income_tax_threshold_annual
        };
        break;
      case 'currency':
        categorySettings = {
          currencyCode: allSettings.currency_code,
          currencySymbol: allSettings.currency_symbol,
          useIndianNumbering: allSettings.use_indian_numbering,
          decimalPlaces: allSettings.decimal_places
        };
        break;
      default:
        return NextResponse.json(
          { success: false, error: 'Invalid category' },
          { status: 400 }
        );
    }

    return NextResponse.json({
      success: true,
      data: {
        category,
        settings: categorySettings
      }
    });

  } catch (error) {
    console.error('Error fetching category settings:', error);
    return NextResponse.json(
      { 
        success: false, 
        error: 'Failed to fetch category settings',
        details: error.message 
      },
      { status: 500 }
    );
  }
}

// Helper function to flatten categorized settings
function flattenSettings(categorizedSettings: any): { [key: string]: any } {
  const flattened: { [key: string]: any } = {};

  Object.entries(categorizedSettings).forEach(([category, settings]) => {
    if (typeof settings === 'object' && settings !== null) {
      Object.entries(settings as any).forEach(([key, value]) => {
        // Convert camelCase to snake_case
        const snakeKey = key.replace(/[A-Z]/g, letter => `_${letter.toLowerCase()}`);
        flattened[snakeKey] = value;
      });
    }
  });

  return flattened;
}
