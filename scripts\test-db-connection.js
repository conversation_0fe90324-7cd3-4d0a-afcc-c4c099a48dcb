#!/usr/bin/env node

// Simple database connection test
require('dotenv').config({ path: '.env.local' });

async function testConnection() {
  try {
    const { neon } = require('@neondatabase/serverless');
    
    if (!process.env.DATABASE_URL) {
      console.log('❌ DATABASE_URL not found in environment');
      return;
    }
    
    console.log('🔄 Testing database connection...');
    const sql = neon(process.env.DATABASE_URL);
    
    // Test basic connection
    const result = await sql`SELECT NOW() as current_time, version() as db_version`;
    console.log('✅ Database connected successfully!');
    console.log('Current time:', result[0].current_time);
    console.log('Database version:', result[0].db_version.split(' ')[0]);
    
    // Check existing tables
    console.log('\n🔍 Checking existing tables...');
    const tables = await sql`
      SELECT table_name 
      FROM information_schema.tables 
      WHERE table_schema = 'public' 
      ORDER BY table_name
    `;
    
    console.log('📋 Existing tables:');
    tables.forEach(t => console.log(`   - ${t.table_name}`));
    
    // Check if task-related tables exist
    const taskTables = tables.filter(t => t.table_name.includes('task'));
    console.log(`\n📊 Task-related tables: ${taskTables.length}`);
    taskTables.forEach(t => console.log(`   - ${t.table_name}`));
    
    // Check users table
    const users = await sql`SELECT COUNT(*) as count FROM users`;
    console.log(`\n👥 Users in database: ${users[0].count}`);
    
    // Check admin users
    const adminUsers = await sql`SELECT email FROM users WHERE role = 'admin' LIMIT 3`;
    console.log('🔑 Admin users:');
    adminUsers.forEach(u => console.log(`   - ${u.email}`));
    
    console.log('\n✅ Database connection test completed successfully!');
    
  } catch (error) {
    console.error('❌ Database connection failed:', error.message);
    
    if (error.message.includes('password authentication failed')) {
      console.log('\n💡 Troubleshooting tips:');
      console.log('1. Check your DATABASE_URL in .env.local');
      console.log('2. Ensure your Neon database is active');
      console.log('3. Verify the connection string is correct');
    }
  }
}

testConnection();
