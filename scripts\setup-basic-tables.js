#!/usr/bin/env node

/**
 * Basic table setup script
 * Creates essential tables for user management if they don't exist
 */

const { neon } = require('@neondatabase/serverless');
require('dotenv').config({ path: '.env.local' });

async function setupBasicTables() {
  console.log('🔧 Setting up basic tables for user management...\n');
  
  if (!process.env.DATABASE_URL) {
    console.error('❌ ERROR: DATABASE_URL environment variable is not set');
    console.log('📝 Please update your .env.local file with your Neon connection string');
    process.exit(1);
  }
  
  try {
    const sql = neon(process.env.DATABASE_URL);
    
    // Test connection
    console.log('🔄 Testing database connection...');
    await sql`SELECT 1`;
    console.log('✅ Database connection successful!\n');
    
    // Create users table
    console.log('🔄 Creating users table...');
    await sql`
      CREATE TABLE IF NOT EXISTS users (
        id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
        email VARCHAR(255) UNIQUE NOT NULL,
        password_hash VARCHAR(255) NOT NULL,
        full_name VARCHAR(255) NOT NULL,
        role VARCHAR(50) NOT NULL DEFAULT 'staff' CHECK (role IN ('admin', 'hr_manager', 'manager', 'staff')),
        employee_id VARCHAR(20) UNIQUE,
        department VARCHAR(100),
        position VARCHAR(100),
        phone VARCHAR(20),
        hire_date DATE,
        salary DECIMAL(10,2),
        employment_type VARCHAR(20) DEFAULT 'full_time' CHECK (employment_type IN ('full_time', 'part_time', 'contract', 'intern', 'consultant')),
        employment_status VARCHAR(20) DEFAULT 'active' CHECK (employment_status IN ('active', 'inactive', 'terminated', 'resigned', 'retired')),
        is_active BOOLEAN DEFAULT true,
        email_verified BOOLEAN DEFAULT false,
        last_login TIMESTAMP WITH TIME ZONE,
        created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
        updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
      )
    `;
    console.log('✅ Users table created');
    
    // Create user_sessions table
    console.log('🔄 Creating user_sessions table...');
    await sql`
      CREATE TABLE IF NOT EXISTS user_sessions (
        id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
        user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
        session_token VARCHAR(255) UNIQUE NOT NULL,
        expires_at TIMESTAMP WITH TIME ZONE NOT NULL,
        created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
      )
    `;
    console.log('✅ User sessions table created');
    
    // Create departments table
    console.log('🔄 Creating departments table...');
    await sql`
      CREATE TABLE IF NOT EXISTS departments (
        id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
        name VARCHAR(100) UNIQUE NOT NULL,
        description TEXT,
        manager_id UUID REFERENCES users(id),
        budget DECIMAL(12,2),
        location VARCHAR(100),
        is_active BOOLEAN DEFAULT true,
        created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
        updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
      )
    `;
    console.log('✅ Departments table created');
    
    // Create permissions table
    console.log('🔄 Creating permissions table...');
    await sql`
      CREATE TABLE IF NOT EXISTS permissions (
        id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
        name VARCHAR(100) UNIQUE NOT NULL,
        description TEXT,
        resource VARCHAR(100) NOT NULL,
        action VARCHAR(50) NOT NULL,
        created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
      )
    `;
    console.log('✅ Permissions table created');
    
    // Create role_permissions table
    console.log('🔄 Creating role_permissions table...');
    await sql`
      CREATE TABLE IF NOT EXISTS role_permissions (
        id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
        role VARCHAR(50) NOT NULL,
        permission_id UUID NOT NULL REFERENCES permissions(id) ON DELETE CASCADE,
        created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
        UNIQUE(role, permission_id)
      )
    `;
    console.log('✅ Role permissions table created');
    
    // Create indexes
    console.log('🔄 Creating indexes...');
    await sql`CREATE INDEX IF NOT EXISTS idx_users_email ON users(email)`;
    await sql`CREATE INDEX IF NOT EXISTS idx_users_role ON users(role)`;
    await sql`CREATE INDEX IF NOT EXISTS idx_users_is_active ON users(is_active)`;
    await sql`CREATE INDEX IF NOT EXISTS idx_user_sessions_token ON user_sessions(session_token)`;
    await sql`CREATE INDEX IF NOT EXISTS idx_user_sessions_user_id ON user_sessions(user_id)`;
    console.log('✅ Indexes created');
    
    // Insert basic permissions
    console.log('🔄 Inserting basic permissions...');
    const permissions = [
      { name: 'view_dashboard', description: 'View main dashboard', resource: 'dashboard', action: 'read' },
      { name: 'manage_users', description: 'Create, update, delete users', resource: 'users', action: 'write' },
      { name: 'view_users', description: 'View user list and profiles', resource: 'users', action: 'read' },
      { name: 'manage_departments', description: 'Create, update, delete departments', resource: 'departments', action: 'write' },
      { name: 'view_departments', description: 'View department information', resource: 'departments', action: 'read' },
      { name: 'manage_attendance', description: 'Manage attendance records', resource: 'attendance', action: 'write' },
      { name: 'view_attendance', description: 'View attendance records', resource: 'attendance', action: 'read' },
      { name: 'manage_payroll', description: 'Process payroll and manage salary', resource: 'payroll', action: 'write' },
      { name: 'view_payroll', description: 'View payroll information', resource: 'payroll', action: 'read' },
      { name: 'manage_reports', description: 'Generate and manage reports', resource: 'reports', action: 'write' },
      { name: 'view_reports', description: 'View reports', resource: 'reports', action: 'read' },
      { name: 'manage_settings', description: 'Manage system settings', resource: 'settings', action: 'write' }
    ];
    
    for (const perm of permissions) {
      await sql`
        INSERT INTO permissions (name, description, resource, action)
        VALUES (${perm.name}, ${perm.description}, ${perm.resource}, ${perm.action})
        ON CONFLICT (name) DO NOTHING
      `;
    }
    console.log('✅ Basic permissions inserted');
    
    // Insert role permissions
    console.log('🔄 Setting up role permissions...');
    
    // Get permission IDs
    const allPermissions = await sql`SELECT id, name FROM permissions`;
    const permissionMap = {};
    allPermissions.forEach(p => {
      permissionMap[p.name] = p.id;
    });
    
    // Admin gets all permissions
    for (const perm of allPermissions) {
      await sql`
        INSERT INTO role_permissions (role, permission_id)
        VALUES ('admin', ${perm.id})
        ON CONFLICT (role, permission_id) DO NOTHING
      `;
    }
    
    // HR Manager permissions
    const hrPermissions = [
      'view_dashboard', 'manage_users', 'view_users', 'view_departments',
      'manage_attendance', 'view_attendance', 'manage_payroll', 'view_payroll',
      'view_reports', 'manage_reports'
    ];
    for (const permName of hrPermissions) {
      if (permissionMap[permName]) {
        await sql`
          INSERT INTO role_permissions (role, permission_id)
          VALUES ('hr_manager', ${permissionMap[permName]})
          ON CONFLICT (role, permission_id) DO NOTHING
        `;
      }
    }
    
    // Manager permissions
    const managerPermissions = [
      'view_dashboard', 'view_users', 'view_departments',
      'view_attendance', 'view_payroll', 'view_reports'
    ];
    for (const permName of managerPermissions) {
      if (permissionMap[permName]) {
        await sql`
          INSERT INTO role_permissions (role, permission_id)
          VALUES ('manager', ${permissionMap[permName]})
          ON CONFLICT (role, permission_id) DO NOTHING
        `;
      }
    }
    
    // Staff permissions
    const staffPermissions = ['view_dashboard', 'view_attendance'];
    for (const permName of staffPermissions) {
      if (permissionMap[permName]) {
        await sql`
          INSERT INTO role_permissions (role, permission_id)
          VALUES ('staff', ${permissionMap[permName]})
          ON CONFLICT (role, permission_id) DO NOTHING
        `;
      }
    }
    
    console.log('✅ Role permissions configured');
    
    // Insert sample departments
    console.log('🔄 Creating sample departments...');
    const departments = [
      { name: 'Information Technology', description: 'Manages all IT infrastructure and software development', location: 'Floor 3' },
      { name: 'Human Resources', description: 'Handles employee relations, recruitment, and HR policies', location: 'Floor 2' },
      { name: 'Finance & Accounting', description: 'Manages financial operations and accounting', location: 'Floor 2' },
      { name: 'Operations', description: 'Oversees daily business operations and processes', location: 'Floor 1' },
      { name: 'Marketing', description: 'Handles marketing campaigns and brand management', location: 'Floor 4' },
      { name: 'Customer Service', description: 'Provides customer support and service', location: 'Floor 1' }
    ];
    
    for (const dept of departments) {
      await sql`
        INSERT INTO departments (name, description, location)
        VALUES (${dept.name}, ${dept.description}, ${dept.location})
        ON CONFLICT (name) DO NOTHING
      `;
    }
    console.log('✅ Sample departments created');
    
    // Verify setup
    console.log('\n🔍 Verifying setup...');
    const tables = await sql`
      SELECT table_name 
      FROM information_schema.tables 
      WHERE table_schema = 'public' 
      ORDER BY table_name
    `;
    
    console.log(`📊 Created ${tables.length} tables:`);
    tables.forEach(table => {
      console.log(`   - ${table.table_name}`);
    });
    
    const permCount = await sql`SELECT COUNT(*) as count FROM permissions`;
    const deptCount = await sql`SELECT COUNT(*) as count FROM departments`;
    
    console.log(`\n✅ Setup complete!`);
    console.log(`   - ${permCount[0].count} permissions configured`);
    console.log(`   - ${deptCount[0].count} departments created`);
    
    console.log('\n🚀 Next step: Create sample users');
    console.log('Run: node scripts/create-sample-users.js');
    
  } catch (error) {
    console.error('❌ Setup failed:', error.message);
    console.error('Full error:', error);
    process.exit(1);
  }
}

// Run the setup
if (require.main === module) {
  setupBasicTables();
}

module.exports = { setupBasicTables };
