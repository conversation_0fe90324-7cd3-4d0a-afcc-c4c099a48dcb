# Payroll Management System API Documentation

## Overview

The Payroll Management System provides a comprehensive REST API for managing employee payroll calculations, attendance tracking, and Nepal-specific localization features. All endpoints return JSON responses and support standard HTTP status codes.

## Base URL

```
https://your-domain.com/api
```

## Authentication

All API endpoints require authentication. Include the authorization header in your requests:

```
Authorization: Bearer <your-jwt-token>
```

## Response Format

All API responses follow this standard format:

```json
{
  "success": boolean,
  "data": object | array,
  "error": string | null,
  "timestamp": string,
  "requestId": string
}
```

## Payroll Endpoints

### Calculate Payroll

Calculate payroll for a specific employee and period.

**Endpoint:** `POST /api/payroll/calculate`

**Request Body:**
```json
{
  "employeeId": "string",
  "periodStart": "YYYY-MM-DD",
  "periodEnd": "YYYY-MM-DD",
  "attendanceData": {
    "workingDays": number,
    "attendedDays": number,
    "overtimeHours": number,
    "lateHours": number,
    "earlyLeaveHours": number,
    "totalHours": number (for hourly employees),
    "halfDays": number (for daily employees)
  },
  "overrides": {
    "allowances": [
      {
        "id": "string",
        "amount": number,
        "type": "fixed" | "percentage"
      }
    ],
    "deductions": [
      {
        "id": "string",
        "amount": number,
        "type": "fixed" | "percentage"
      }
    ]
  }
}
```

**Response:**
```json
{
  "success": true,
  "data": {
    "payroll": {
      "id": "string",
      "employeeId": "string",
      "payStructure": "monthly" | "hourly" | "daily" | "project",
      "periodStart": "YYYY-MM-DD",
      "periodEnd": "YYYY-MM-DD",
      "basePay": number,
      "overtimePay": number,
      "allowances": number,
      "grossPay": number,
      "deductions": number,
      "taxes": number,
      "netPay": number,
      "workingDays": number,
      "attendedDays": number,
      "overtimeHours": number,
      "currency": "NPR",
      "bsPeriod": {
        "start": "YYYY-MM-DD",
        "end": "YYYY-MM-DD",
        "fiscalYear": "YYYY-YY"
      },
      "complianceStatus": {
        "isCompliant": boolean,
        "violations": ["string"],
        "warnings": ["string"]
      },
      "breakdown": {
        "regularHours": number,
        "overtimeBreakdown": {
          "first4Hours": number,
          "next4Hours": number,
          "beyond8Hours": number
        },
        "allowanceBreakdown": [
          {
            "id": "string",
            "name": "string",
            "amount": number,
            "type": "fixed" | "percentage"
          }
        ],
        "deductionBreakdown": [
          {
            "id": "string",
            "name": "string",
            "amount": number,
            "type": "fixed" | "percentage"
          }
        ]
      }
    }
  }
}
```

**Status Codes:**
- `200` - Success
- `400` - Bad Request (invalid parameters)
- `404` - Employee not found
- `422` - Validation error
- `500` - Internal server error

### Get Payroll History

Retrieve payroll history for an employee.

**Endpoint:** `GET /api/payroll/history`

**Query Parameters:**
- `employeeId` (required): Employee ID
- `page` (optional): Page number (default: 1)
- `limit` (optional): Records per page (default: 10, max: 100)
- `startDate` (optional): Filter from date (YYYY-MM-DD)
- `endDate` (optional): Filter to date (YYYY-MM-DD)
- `fiscalYear` (optional): Filter by fiscal year (YYYY-YY)
- `status` (optional): Filter by status (draft, pending, processed, paid)

**Response:**
```json
{
  "success": true,
  "data": {
    "history": [
      {
        "id": "string",
        "employeeId": "string",
        "payPeriod": "string",
        "bsPayPeriod": "string",
        "periodStart": "YYYY-MM-DD",
        "periodEnd": "YYYY-MM-DD",
        "grossPay": number,
        "netPay": number,
        "status": "draft" | "pending" | "processed" | "paid",
        "processedAt": "ISO-8601",
        "paidAt": "ISO-8601"
      }
    ],
    "pagination": {
      "page": number,
      "limit": number,
      "total": number,
      "totalPages": number
    }
  }
}
```

### Process Bulk Payroll

Process payroll for multiple employees.

**Endpoint:** `POST /api/payroll/bulk-process`

**Request Body:**
```json
{
  "periodStart": "YYYY-MM-DD",
  "periodEnd": "YYYY-MM-DD",
  "employeeIds": ["string"],
  "batchName": "string",
  "autoApprove": boolean
}
```

**Response:**
```json
{
  "success": true,
  "data": {
    "batchId": "string",
    "status": "queued" | "processing" | "completed" | "failed",
    "totalEmployees": number,
    "processedEmployees": number,
    "failedEmployees": number,
    "estimatedCompletion": "ISO-8601"
  }
}
```

### Get Bulk Processing Status

Check the status of bulk payroll processing.

**Endpoint:** `GET /api/payroll/bulk-process/{batchId}`

**Response:**
```json
{
  "success": true,
  "data": {
    "batchId": "string",
    "status": "queued" | "processing" | "completed" | "failed",
    "progress": number,
    "totalEmployees": number,
    "processedEmployees": number,
    "failedEmployees": number,
    "startTime": "ISO-8601",
    "endTime": "ISO-8601",
    "jobs": [
      {
        "employeeId": "string",
        "employeeName": "string",
        "status": "pending" | "processing" | "completed" | "failed",
        "progress": number,
        "error": "string"
      }
    ]
  }
}
```

## Settings Endpoints

### Get Payroll Settings

Retrieve current payroll configuration.

**Endpoint:** `GET /api/payroll/settings`

**Response:**
```json
{
  "success": true,
  "data": {
    "settings": {
      "currency": "NPR",
      "fiscalYear": "YYYY-YY",
      "payrollFrequency": "monthly" | "bi-weekly" | "weekly",
      "overtimeRates": {
        "first4Hours": number,
        "next4Hours": number,
        "beyond8Hours": number
      },
      "minimumWage": number,
      "workingHours": {
        "dailyHours": number,
        "weeklyHours": number,
        "weeklyOff": "saturday" | "sunday"
      },
      "taxRates": {
        "incomeTax": number,
        "socialSecurity": number
      },
      "allowances": [
        {
          "id": "string",
          "name": "string",
          "type": "fixed" | "percentage",
          "defaultAmount": number,
          "isActive": boolean
        }
      ],
      "deductions": [
        {
          "id": "string",
          "name": "string",
          "type": "fixed" | "percentage",
          "defaultAmount": number,
          "isActive": boolean
        }
      ]
    }
  }
}
```

### Update Payroll Settings

Update payroll configuration.

**Endpoint:** `PUT /api/payroll/settings`

**Request Body:** Same as GET response data.settings

**Response:** Same as GET response

## Nepal Localization Endpoints

### Get Fiscal Year Information

Get Nepal fiscal year details.

**Endpoint:** `GET /api/nepal/fiscal-year/{fiscalYear}`

**Response:**
```json
{
  "success": true,
  "data": {
    "fiscalYear": "YYYY-YY",
    "bsStartDate": {
      "year": number,
      "month": number,
      "day": number
    },
    "bsEndDate": {
      "year": number,
      "month": number,
      "day": number
    },
    "adStartDate": "YYYY-MM-DD",
    "adEndDate": "YYYY-MM-DD",
    "totalDays": number,
    "workingDays": number,
    "holidays": number,
    "quarters": [
      {
        "quarter": number,
        "name": "string",
        "months": [number],
        "workingDays": number
      }
    ],
    "months": [
      {
        "bsMonth": number,
        "bsYear": number,
        "monthName": "string",
        "monthNameNepali": "string",
        "workingDays": number,
        "holidays": number
      }
    ]
  }
}
```

### Get Holiday Calendar

Get Nepal holiday information.

**Endpoint:** `GET /api/nepal/holidays`

**Query Parameters:**
- `year` (optional): Calendar year (default: current year)
- `bsYear` (optional): Bikram Sambat year
- `month` (optional): Filter by month (1-12)
- `type` (optional): Filter by type (public, festival, observance)

**Response:**
```json
{
  "success": true,
  "data": {
    "holidays": [
      {
        "id": "string",
        "name": "string",
        "nameNepali": "string",
        "date": "YYYY-MM-DD",
        "bsDate": {
          "year": number,
          "month": number,
          "day": number
        },
        "type": "public" | "festival" | "observance",
        "category": "national" | "religious" | "cultural",
        "isOptional": boolean,
        "affectsPayroll": boolean
      }
    ],
    "statistics": {
      "totalHolidays": number,
      "publicHolidays": number,
      "festivals": number,
      "observances": number
    }
  }
}
```

### Convert Calendar Dates

Convert between AD and BS dates.

**Endpoint:** `POST /api/nepal/calendar/convert`

**Request Body:**
```json
{
  "date": "YYYY-MM-DD" | {
    "year": number,
    "month": number,
    "day": number
  },
  "from": "AD" | "BS",
  "to": "AD" | "BS"
}
```

**Response:**
```json
{
  "success": true,
  "data": {
    "originalDate": "YYYY-MM-DD" | {
      "year": number,
      "month": number,
      "day": number
    },
    "convertedDate": "YYYY-MM-DD" | {
      "year": number,
      "month": number,
      "day": number
    },
    "formatted": {
      "original": "string",
      "converted": "string"
    }
  }
}
```

### Format Currency

Format amounts in NPR with Nepal-specific formatting.

**Endpoint:** `POST /api/nepal/currency/format`

**Request Body:**
```json
{
  "amount": number,
  "options": {
    "showSymbol": boolean,
    "useIndianNumbering": boolean,
    "decimalPlaces": number,
    "compact": boolean
  }
}
```

**Response:**
```json
{
  "success": true,
  "data": {
    "formatted": "string",
    "breakdown": {
      "crores": number,
      "lakhs": number,
      "thousands": number,
      "hundreds": number,
      "remainder": number
    },
    "inWords": {
      "english": "string",
      "nepali": "string"
    },
    "compact": "string"
  }
}
```

## Compliance Endpoints

### Check Labor Law Compliance

Check employee compliance with Nepal labor laws.

**Endpoint:** `POST /api/compliance/check`

**Request Body:**
```json
{
  "employeeId": "string",
  "periodStart": "YYYY-MM-DD",
  "periodEnd": "YYYY-MM-DD"
}
```

**Response:**
```json
{
  "success": true,
  "data": {
    "overallStatus": "compliant" | "non_compliant" | "warning",
    "checks": [
      {
        "ruleId": "string",
        "ruleName": "string",
        "category": "working_hours" | "overtime" | "wages" | "leave",
        "status": "compliant" | "non_compliant" | "warning",
        "message": "string",
        "severity": "low" | "medium" | "high" | "critical",
        "actualValue": number,
        "requiredValue": number,
        "recommendations": ["string"]
      }
    ],
    "summary": {
      "workingHours": {
        "dailyHours": number,
        "weeklyHours": number,
        "status": "string"
      },
      "overtime": {
        "dailyOvertimeHours": number,
        "weeklyOvertimeHours": number,
        "status": "string"
      },
      "wages": {
        "minimumWageCompliance": boolean,
        "overtimePayCompliance": boolean,
        "status": "string"
      }
    }
  }
}
```

## Error Codes

| Code | Description |
|------|-------------|
| 400 | Bad Request - Invalid parameters or request format |
| 401 | Unauthorized - Invalid or missing authentication |
| 403 | Forbidden - Insufficient permissions |
| 404 | Not Found - Resource does not exist |
| 422 | Unprocessable Entity - Validation errors |
| 429 | Too Many Requests - Rate limit exceeded |
| 500 | Internal Server Error - Server-side error |
| 503 | Service Unavailable - Temporary service outage |

## Rate Limiting

API requests are limited to:
- 1000 requests per hour per user
- 100 requests per minute per user
- 10 concurrent requests per user

Rate limit headers are included in responses:
```
X-RateLimit-Limit: 1000
X-RateLimit-Remaining: 999
X-RateLimit-Reset: 1640995200
```

## Webhooks

The system supports webhooks for real-time notifications:

### Payroll Events
- `payroll.calculated` - When payroll is calculated
- `payroll.approved` - When payroll is approved
- `payroll.paid` - When payroll is marked as paid
- `payroll.error` - When payroll processing fails

### Compliance Events
- `compliance.violation` - When compliance violation is detected
- `compliance.warning` - When compliance warning is issued

### Webhook Payload
```json
{
  "event": "string",
  "timestamp": "ISO-8601",
  "data": object,
  "signature": "string"
}
```

## SDK and Libraries

Official SDKs are available for:
- JavaScript/TypeScript
- Python
- PHP
- Java

Example usage (JavaScript):
```javascript
import { PayrollAPI } from '@payroll/sdk'

const api = new PayrollAPI({
  baseUrl: 'https://your-domain.com/api',
  apiKey: 'your-api-key'
})

const payroll = await api.payroll.calculate({
  employeeId: 'EMP001',
  periodStart: '2024-11-01',
  periodEnd: '2024-11-30'
})
```
