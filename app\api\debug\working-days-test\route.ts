// DEBUG API ENDPOINT - Remove this file after fixing the issue
// This endpoint bypasses authentication to test database connectivity
// DO NOT USE IN PRODUCTION

import { NextRequest, NextResponse } from 'next/server';
import { serverDb } from '@/lib/server-db';

export async function GET(request: NextRequest) {
  try {
    console.log('🔍 DEBUG: Testing working days database connection...');

    // Test 1: Check if tables exist
    const tablesCheck = await serverDb.sql`
      SELECT table_name 
      FROM information_schema.tables 
      WHERE table_schema = 'public' 
      AND table_name IN ('working_days_configuration', 'attendance_calculation_settings')
    `;

    console.log('📊 Tables found:', tablesCheck);

    // Test 2: Count records
    let workingDaysCount = 0;
    let attendanceSettingsCount = 0;

    try {
      const workingDaysCountResult = await serverDb.sql`
        SELECT COUNT(*) as count FROM working_days_configuration
      `;
      workingDaysCount = workingDaysCountResult[0]?.count || 0;
    } catch (error) {
      console.log('❌ Error counting working_days_configuration:', error.message);
    }

    try {
      const attendanceSettingsCountResult = await serverDb.sql`
        SELECT COUNT(*) as count FROM attendance_calculation_settings
      `;
      attendanceSettingsCount = attendanceSettingsCountResult[0]?.count || 0;
    } catch (error) {
      console.log('❌ Error counting attendance_calculation_settings:', error.message);
    }

    // Test 3: Try to fetch sample data
    let sampleWorkingDays = [];
    let sampleSettings = [];

    try {
      sampleWorkingDays = await serverDb.sql`
        SELECT 
          fiscal_year,
          bs_month,
          bs_month_name,
          working_days,
          total_days_in_month
        FROM working_days_configuration 
        WHERE fiscal_year = '2081-82'
        ORDER BY bs_month
        LIMIT 5
      `;
    } catch (error) {
      console.log('❌ Error fetching sample working days:', error.message);
    }

    try {
      sampleSettings = await serverDb.sql`
        SELECT 
          setting_name,
          setting_value,
          category
        FROM attendance_calculation_settings
        ORDER BY category, setting_name
        LIMIT 5
      `;
    } catch (error) {
      console.log('❌ Error fetching sample settings:', error.message);
    }

    // Test 4: Test the main query that the API uses
    let mainQueryResult = [];
    try {
      mainQueryResult = await serverDb.sql`
        SELECT 
          id,
          fiscal_year,
          bs_month,
          bs_month_name,
          total_days_in_month,
          working_days,
          public_holidays,
          weekend_days,
          late_penalty_type,
          late_penalty_amount,
          half_day_calculation_method,
          created_at,
          updated_at
        FROM working_days_configuration
        WHERE fiscal_year = '2081-82'
        ORDER BY fiscal_year DESC, bs_month ASC
      `;
    } catch (error) {
      console.log('❌ Error with main query:', error.message);
    }

    const debugInfo = {
      timestamp: new Date().toISOString(),
      database_connection: 'SUCCESS',
      tables_found: tablesCheck.map(t => t.table_name),
      tables_expected: ['working_days_configuration', 'attendance_calculation_settings'],
      data_counts: {
        working_days_configuration: workingDaysCount,
        attendance_calculation_settings: attendanceSettingsCount
      },
      sample_data: {
        working_days: sampleWorkingDays,
        settings: sampleSettings
      },
      main_query_result_count: mainQueryResult.length,
      main_query_sample: mainQueryResult.slice(0, 3),
      diagnosis: {
        tables_exist: tablesCheck.length === 2,
        has_working_days_data: workingDaysCount > 0,
        has_settings_data: attendanceSettingsCount > 0,
        expected_working_days_count: 12,
        expected_settings_count: 11,
        status: workingDaysCount >= 12 && attendanceSettingsCount >= 10 ? 'HEALTHY' : 'NEEDS_FIX'
      }
    };

    console.log('🎯 DEBUG RESULTS:', debugInfo);

    return NextResponse.json({
      success: true,
      debug: true,
      message: 'Database connectivity test completed',
      data: debugInfo
    });

  } catch (error) {
    console.error('💥 DEBUG ERROR:', error);
    
    return NextResponse.json({
      success: false,
      debug: true,
      error: 'Database connection failed',
      details: {
        message: error.message,
        stack: error.stack,
        timestamp: new Date().toISOString()
      }
    }, { status: 500 });
  }
}

// POST endpoint to test data insertion
export async function POST(request: NextRequest) {
  try {
    console.log('🔧 DEBUG: Testing data insertion...');

    // Try to insert a test record
    const testResult = await serverDb.sql`
      INSERT INTO working_days_configuration (
        fiscal_year, bs_month, bs_month_name, total_days_in_month, 
        working_days, public_holidays, weekend_days
      ) VALUES (
        'TEST-DEBUG', 99, 'Test Month', 30, 20, 2, 8
      )
      ON CONFLICT (fiscal_year, bs_month) DO UPDATE SET
        updated_at = NOW()
      RETURNING *
    `;

    // Clean up test record
    await serverDb.sql`
      DELETE FROM working_days_configuration 
      WHERE fiscal_year = 'TEST-DEBUG' AND bs_month = 99
    `;

    return NextResponse.json({
      success: true,
      debug: true,
      message: 'Data insertion test successful',
      test_record: testResult[0]
    });

  } catch (error) {
    console.error('💥 DEBUG INSERT ERROR:', error);
    
    return NextResponse.json({
      success: false,
      debug: true,
      error: 'Data insertion test failed',
      details: {
        message: error.message,
        timestamp: new Date().toISOString()
      }
    }, { status: 500 });
  }
}
