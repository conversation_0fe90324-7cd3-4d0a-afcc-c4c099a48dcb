import { type NextRequest, NextResponse } from "next/server"
import { AuthService } from "@/lib/auth-utils"
import { serverDb } from "@/lib/server-db"

export async function GET(request: NextRequest) {
  try {
    const sessionToken = request.cookies.get("session-token")?.value

    if (!sessionToken) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 })
    }

    const user = await AuthService.verifySession(sessionToken)

    if (!user || !["admin", "hr_manager", "manager"].includes(user.role)) {
      return NextResponse.json({ error: "Forbidden" }, { status: 403 })
    }

    const { searchParams } = new URL(request.url)
    const department = searchParams.get("department") || undefined
    const role = searchParams.get("role") as any || undefined
    const is_active = searchParams.get("is_active") ? searchParams.get("is_active") === "true" : undefined
    const search = searchParams.get("search") || ""

    let users
    if (search) {
      // Use direct SQL for search functionality
      users = await serverDb.sql`
        SELECT id, email, full_name, role, employee_id, department, position,
               employment_type, employment_status, hire_date, salary, phone, is_active, created_at
        FROM users
        WHERE (full_name ILIKE ${`%${search}%`} OR email ILIKE ${`%${search}%`} OR employee_id ILIKE ${`%${search}%`})
        ${department ? serverDb.sql`AND department = ${department}` : serverDb.sql``}
        ${role ? serverDb.sql`AND role = ${role}` : serverDb.sql``}
        ${is_active !== undefined ? serverDb.sql`AND is_active = ${is_active}` : serverDb.sql``}
        ORDER BY full_name
        LIMIT 100
      `
    } else {
      users = await serverDb.getAllUsers({ department, role, is_active })
    }

    // Remove sensitive information
    const sanitizedUsers = users.map(({ password_hash, ...user }: any) => user)

    return NextResponse.json({
      success: true,
      users: sanitizedUsers,
    })
  } catch (error) {
    console.error("Admin users API error:", error)
    return NextResponse.json({ error: "Internal server error" }, { status: 500 })
  }
}

export async function POST(request: NextRequest) {
  try {
    const sessionToken = request.cookies.get("session-token")?.value

    if (!sessionToken) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 })
    }

    const currentUser = await AuthService.verifySession(sessionToken)

    if (!currentUser || !["admin", "hr_manager"].includes(currentUser.role)) {
      return NextResponse.json({ error: "Forbidden" }, { status: 403 })
    }

    const userData = await request.json()

    // Validate required fields
    if (!userData.email || !userData.full_name || !userData.role) {
      return NextResponse.json({ error: "Email, full name, and role are required" }, { status: 400 })
    }

    // Generate a temporary password if not provided
    const password = userData.password || "TempPass123!"
    const result = await AuthService.register({
      ...userData,
      password,
      email_verified: true, // Admin-created users are pre-verified
    })

    if (result.success) {
      return NextResponse.json({
        success: true,
        user: result.user,
        message: "User created successfully"
      })
    } else {
      return NextResponse.json({ error: result.error }, { status: 400 })
    }
  } catch (error) {
    console.error("Create user API error:", error)
    return NextResponse.json({ error: "Internal server error" }, { status: 500 })
  }
}
