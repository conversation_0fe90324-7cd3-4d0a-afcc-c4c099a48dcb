# Database Schema Execution Guide
## Fixing Missing Working Days Configuration Tables

### 🚨 **Problem**
The attendance-based payroll system is failing with PostgreSQL error 42P01: "relation 'working_days_configuration' does not exist" because the required database tables were not created.

### ✅ **Solution**
Execute the complete database schema script to create all required tables, functions, and data.

---

## 📋 **Step-by-Step Execution Instructions**

### **Step 1: Access Your Neon Database**

1. **Via Neon Console (Recommended):**
   - Go to [Neon Console](https://console.neon.tech)
   - Navigate to your project
   - Click on "SQL Editor" or "Query" tab
   - Copy and paste the entire content of `scripts/execute-working-days-schema.sql`
   - Click "Run" to execute

2. **Via psql Command Line:**
   ```bash
   psql "your-neon-connection-string"
   \i scripts/execute-working-days-schema.sql
   ```

3. **Via Database Client (DBeaver, pgAdmin, etc.):**
   - Connect to your Neon database
   - Open a new SQL script
   - Copy and paste the content of `scripts/execute-working-days-schema.sql`
   - Execute the script

### **Step 2: Verify Schema Creation**

After executing the script, run these verification queries:

```sql
-- 1. Check if tables were created
SELECT table_name FROM information_schema.tables 
WHERE table_schema = 'public' 
AND table_name IN ('working_days_configuration', 'attendance_calculation_settings');

-- Expected result: 2 rows showing both table names

-- 2. Check if default data was inserted
SELECT COUNT(*) as working_days_configs FROM working_days_configuration;
SELECT COUNT(*) as attendance_settings FROM attendance_calculation_settings;

-- Expected results: 
-- working_days_configs: 12 (one for each Nepali month)
-- attendance_settings: 11 (default settings)

-- 3. Test database functions
SELECT get_working_days_for_period('2081-82', 1) as baisakh_working_days;
SELECT get_attendance_setting('enable_attendance_based_calculation') as attendance_calc_enabled;
SELECT calculate_attendance_based_salary(50000, 22, 20, 2, 0, 0) as calculated_salary;

-- Expected results:
-- baisakh_working_days: 22
-- attendance_calc_enabled: 'true'
-- calculated_salary: ~45454.55 (calculated based on attendance)
```

### **Step 3: Test API Endpoints**

1. **Test Working Days API:**
   ```bash
   # GET request to fetch working days configuration
   curl -X GET "http://localhost:3000/api/admin/payroll/working-days?fiscal_year=2081-82" \
        -H "Cookie: session-token=your-session-token"
   ```

2. **Expected Response:**
   ```json
   {
     "success": true,
     "data": {
       "working_days_config": [
         {
           "id": "uuid",
           "fiscal_year": "2081-82",
           "bs_month": 1,
           "bs_month_name": "Baisakh",
           "total_days_in_month": 31,
           "working_days": 22,
           "public_holidays": 2,
           "weekend_days": 7,
           "late_penalty_type": "half_day",
           "late_penalty_amount": 0,
           "half_day_calculation_method": "fifty_percent"
         }
         // ... more months
       ],
       "attendance_settings": [
         // ... settings array
       ]
     }
   }
   ```

### **Step 4: Test UI Functionality**

1. **Access Payroll Settings:**
   - Navigate to `/admin/payroll/settings`
   - Should load without errors

2. **Test Working Days Tab:**
   - Click on "Working Days" tab
   - Should display a table with 12 Nepali months for fiscal year 2081-82
   - Each row should show month name, total days, working days, holidays, and late penalty settings

3. **Test CRUD Operations:**
   - **Create:** Click "Add Month" to add a new configuration
   - **Read:** View existing configurations in the table
   - **Update:** Click edit button on any row to modify settings
   - **Delete:** Click delete button to remove a configuration (admin only)

---

## 🔍 **Database Objects Created**

### **Tables:**
- ✅ `working_days_configuration` - Stores working days config for each Nepali month
- ✅ `attendance_calculation_settings` - Stores system-wide attendance calculation settings

### **Functions:**
- ✅ `get_working_days_for_period(fiscal_year, bs_month)` - Returns working days for a period
- ✅ `get_attendance_setting(setting_name)` - Returns a specific setting value
- ✅ `calculate_attendance_based_salary(...)` - Calculates salary based on attendance

### **Indexes:**
- ✅ `idx_working_days_config_fiscal_year` - Performance index on fiscal_year
- ✅ `idx_working_days_config_month` - Performance index on bs_month
- ✅ `idx_attendance_calc_settings_name` - Performance index on setting_name
- ✅ `idx_attendance_calc_settings_category` - Performance index on category

### **Security:**
- ✅ Row Level Security (RLS) enabled on both tables
- ✅ Admin/HR manager access policies
- ✅ Proper permissions granted

### **Default Data:**
- ✅ 12 working days configurations for fiscal year 2081-82 (all Nepali months)
- ✅ 11 default attendance calculation settings

---

## 🧪 **Testing Checklist**

### **Database Level:**
- [ ] Tables created successfully
- [ ] Functions created and working
- [ ] Indexes created
- [ ] RLS policies active
- [ ] Default data inserted (12 + 11 records)

### **API Level:**
- [ ] GET `/api/admin/payroll/working-days` returns data without errors
- [ ] POST `/api/admin/payroll/working-days` creates new configurations
- [ ] PUT `/api/admin/payroll/working-days` updates existing configurations
- [ ] DELETE `/api/admin/payroll/working-days` removes configurations

### **UI Level:**
- [ ] `/admin/payroll/settings` page loads without errors
- [ ] Working Days tab displays configuration table
- [ ] Add/Edit/Delete operations work correctly
- [ ] Data persists after operations
- [ ] Validation works (e.g., working days ≤ total days)

### **Integration Level:**
- [ ] Enhanced Payroll Form shows attendance summary
- [ ] Salary calculator uses working days configuration
- [ ] Attendance-based calculation works end-to-end

---

## 🚨 **Troubleshooting**

### **If script execution fails:**
1. Check database connection
2. Verify user has CREATE TABLE permissions
3. Check for conflicting table names
4. Review error messages for specific issues

### **If API still returns errors:**
1. Restart your Next.js development server
2. Clear any cached database connections
3. Verify environment variables are correct
4. Check server logs for detailed error messages

### **If UI doesn't load:**
1. Check browser console for JavaScript errors
2. Verify API endpoints are responding
3. Check authentication/authorization
4. Review network tab for failed requests

---

## ✅ **Success Confirmation**

After successful execution, you should see:
1. ✅ No PostgreSQL "relation does not exist" errors
2. ✅ Payroll Settings page loads completely
3. ✅ Working days configuration interface is functional
4. ✅ CRUD operations work for working days
5. ✅ Default data for fiscal year 2081-82 is accessible
6. ✅ Attendance-based salary calculation system is operational

The attendance-based payroll system should now be fully functional! 🎉
