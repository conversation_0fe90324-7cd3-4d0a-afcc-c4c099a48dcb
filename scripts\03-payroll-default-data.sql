-- ============================================================================
-- PAYROLL SYSTEM DEFAULT DATA INSERTION
-- Task 1.1: Insert default payroll settings and components
-- ============================================================================

-- ============================================================================
-- Step 1: Insert default payroll settings
-- ============================================================================

INSERT INTO payroll_settings (setting_key, setting_value, setting_type, description, is_system_setting) VALUES
-- Working hours and overtime
('default_working_hours_per_day', '8', 'number', 'Standard working hours per day', TRUE),
('overtime_threshold_hours', '8', 'number', 'Hours after which overtime applies', TRUE),
('default_overtime_multiplier', '1.5', 'number', 'Default overtime rate multiplier', TRUE),
('max_overtime_hours_per_day', '4', 'number', 'Maximum overtime hours allowed per day', TRUE),

-- Provident fund and benefits
('provident_fund_rate', '10', 'number', 'Default provident fund percentage', TRUE),
('employer_pf_contribution', '10', 'number', 'Employer provident fund contribution percentage', TRUE),
('gratuity_rate', '8.33', 'number', 'Gratuity calculation rate percentage', TRUE),

-- Tax settings (Nepal FY 2080/81)
('tax_threshold_annual', '500000', 'number', 'Annual income tax threshold in NPR', TRUE),
('tax_year_start_month', '4', 'number', 'Tax year start month (Shrawan = 4)', TRUE),
('social_security_fund_rate', '11', 'number', 'Social Security Fund contribution rate', TRUE),

-- Currency and display
('currency_symbol', 'NPR', 'string', 'Currency symbol for display', TRUE),
('currency_decimal_places', '2', 'number', 'Number of decimal places for currency', TRUE),

-- Working days and calendar
('working_days_per_week', '6', 'number', 'Standard working days per week', TRUE),
('fiscal_year_start_month', '4', 'number', 'Fiscal year start month (Shrawan = 4)', TRUE),

-- Penalties and bonuses
('late_penalty_per_minute', '10', 'number', 'Penalty amount per minute of lateness in NPR', TRUE),
('attendance_bonus_threshold', '95', 'number', 'Attendance percentage for bonus eligibility', TRUE),
('attendance_bonus_amount', '2000', 'number', 'Monthly attendance bonus amount in NPR', TRUE),
('max_late_minutes_per_day', '30', 'number', 'Maximum late minutes before half-day deduction', TRUE),

-- Leave and holidays
('annual_leave_days', '18', 'number', 'Annual leave entitlement in days', TRUE),
('sick_leave_days', '12', 'number', 'Annual sick leave entitlement in days', TRUE),
('festival_leave_days', '13', 'number', 'Annual festival leave days', TRUE),

-- Payroll processing
('payroll_processing_day', '25', 'number', 'Day of month for payroll processing', TRUE),
('salary_payment_day', '30', 'number', 'Day of month for salary payment', TRUE),
('payroll_approval_required', 'true', 'boolean', 'Whether payroll requires approval', TRUE),
('auto_calculate_overtime', 'true', 'boolean', 'Automatically calculate overtime from attendance', TRUE),
('auto_calculate_late_penalty', 'true', 'boolean', 'Automatically calculate late penalties', TRUE)

ON CONFLICT (setting_key) DO UPDATE SET
    setting_value = EXCLUDED.setting_value,
    description = EXCLUDED.description,
    updated_at = NOW();

-- ============================================================================
-- Step 2: Insert default payroll components (allowances and deductions)
-- ============================================================================

-- Travel Allowance
INSERT INTO payroll_components_master (
    name, code, type, category, calculation_type,
    fixed_amount, is_taxable, is_statutory, description,
    effective_from, created_by
) VALUES (
    'Travel Allowance', 'TRAVEL_ALLOW', 'allowance', 'company_policy', 'fixed',
    5000.00, FALSE, FALSE, 'Monthly travel allowance for employees',
    CURRENT_DATE, (SELECT id FROM users WHERE role = 'admin' LIMIT 1)
) ON CONFLICT (code) DO UPDATE SET
    name = EXCLUDED.name,
    description = EXCLUDED.description,
    updated_at = NOW();

-- Phone Allowance
INSERT INTO payroll_components_master (
    name, code, type, category, calculation_type,
    fixed_amount, is_taxable, is_statutory, description,
    effective_from, created_by
) VALUES (
    'Phone Allowance', 'PHONE_ALLOW', 'allowance', 'company_policy', 'fixed',
    2000.00, FALSE, FALSE, 'Monthly phone/communication allowance',
    CURRENT_DATE, (SELECT id FROM users WHERE role = 'admin' LIMIT 1)
) ON CONFLICT (code) DO UPDATE SET
    name = EXCLUDED.name,
    description = EXCLUDED.description,
    updated_at = NOW();

-- Meal Allowance
INSERT INTO payroll_components_master (
    name, code, type, category, calculation_type,
    fixed_amount, is_taxable, is_statutory, description,
    effective_from, created_by
) VALUES (
    'Meal Allowance', 'MEAL_ALLOW', 'allowance', 'company_policy', 'fixed',
    3000.00, FALSE, FALSE, 'Monthly meal allowance',
    CURRENT_DATE, (SELECT id FROM users WHERE role = 'admin' LIMIT 1)
) ON CONFLICT (code) DO UPDATE SET
    name = EXCLUDED.name,
    description = EXCLUDED.description,
    updated_at = NOW();

-- Performance Bonus
INSERT INTO payroll_components_master (
    name, code, type, category, calculation_type,
    percentage, percentage_base, is_taxable, is_statutory, description,
    effective_from, created_by
) VALUES (
    'Performance Bonus', 'PERF_BONUS', 'allowance', 'company_policy', 'percentage',
    10.00, 'base_salary', TRUE, FALSE, 'Monthly performance-based bonus',
    CURRENT_DATE, (SELECT id FROM users WHERE role = 'admin' LIMIT 1)
) ON CONFLICT (code) DO UPDATE SET
    name = EXCLUDED.name,
    description = EXCLUDED.description,
    updated_at = NOW();

-- Income Tax Deduction
INSERT INTO payroll_components_master (
    name, code, type, category, calculation_type,
    is_taxable, is_statutory, description,
    effective_from, created_by
) VALUES (
    'Income Tax', 'INCOME_TAX', 'deduction', 'statutory', 'formula',
    FALSE, TRUE, 'Nepal income tax deduction as per tax slabs',
    CURRENT_DATE, (SELECT id FROM users WHERE role = 'admin' LIMIT 1)
) ON CONFLICT (code) DO UPDATE SET
    name = EXCLUDED.name,
    description = EXCLUDED.description,
    updated_at = NOW();

-- Provident Fund Deduction
INSERT INTO payroll_components_master (
    name, code, type, category, calculation_type,
    percentage, percentage_base, is_taxable, is_statutory, description,
    effective_from, created_by
) VALUES (
    'Provident Fund', 'PF_DEDUCTION', 'deduction', 'statutory', 'percentage',
    10.00, 'base_salary', FALSE, TRUE, 'Employee provident fund contribution (10%)',
    CURRENT_DATE, (SELECT id FROM users WHERE role = 'admin' LIMIT 1)
) ON CONFLICT (code) DO UPDATE SET
    name = EXCLUDED.name,
    description = EXCLUDED.description,
    updated_at = NOW();

-- Social Security Fund
INSERT INTO payroll_components_master (
    name, code, type, category, calculation_type,
    percentage, percentage_base, is_taxable, is_statutory, description,
    maximum_amount, effective_from, created_by
) VALUES (
    'Social Security Fund', 'SSF_DEDUCTION', 'deduction', 'statutory', 'percentage',
    11.00, 'base_salary', FALSE, TRUE, 'Social Security Fund contribution (11%)',
    4400.00, CURRENT_DATE, (SELECT id FROM users WHERE role = 'admin' LIMIT 1)
) ON CONFLICT (code) DO UPDATE SET
    name = EXCLUDED.name,
    description = EXCLUDED.description,
    updated_at = NOW();

-- Late Penalty
INSERT INTO payroll_components_master (
    name, code, type, category, calculation_type,
    is_taxable, is_statutory, description,
    effective_from, created_by
) VALUES (
    'Late Penalty', 'LATE_PENALTY', 'deduction', 'company_policy', 'formula',
    FALSE, FALSE, 'Penalty for late attendance',
    CURRENT_DATE, (SELECT id FROM users WHERE role = 'admin' LIMIT 1)
) ON CONFLICT (code) DO UPDATE SET
    name = EXCLUDED.name,
    description = EXCLUDED.description,
    updated_at = NOW();

-- Loan Deduction
INSERT INTO payroll_components_master (
    name, code, type, category, calculation_type,
    is_taxable, is_statutory, description,
    effective_from, created_by
) VALUES (
    'Loan Deduction', 'LOAN_DEDUCTION', 'deduction', 'voluntary', 'fixed',
    FALSE, FALSE, 'Employee loan repayment deduction',
    CURRENT_DATE, (SELECT id FROM users WHERE role = 'admin' LIMIT 1)
) ON CONFLICT (code) DO UPDATE SET
    name = EXCLUDED.name,
    description = EXCLUDED.description,
    updated_at = NOW();

-- Advance Salary Deduction
INSERT INTO payroll_components_master (
    name, code, type, category, calculation_type,
    is_taxable, is_statutory, description,
    effective_from, created_by
) VALUES (
    'Advance Salary', 'ADVANCE_DEDUCTION', 'deduction', 'voluntary', 'fixed',
    FALSE, FALSE, 'Advance salary deduction',
    CURRENT_DATE, (SELECT id FROM users WHERE role = 'admin' LIMIT 1)
) ON CONFLICT (code) DO UPDATE SET
    name = EXCLUDED.name,
    description = EXCLUDED.description,
    updated_at = NOW();

-- ============================================================================
-- Step 3: Create current fiscal year period
-- ============================================================================

-- Insert current fiscal year period (2081-82)
INSERT INTO payroll_periods (
    period_name, period_type, fiscal_year,
    bs_start_date, bs_end_date, ad_start_date, ad_end_date,
    working_days, is_current_period
) VALUES (
    'FY 2081-82', 'yearly', '2081-82',
    '2081-04-01', '2082-03-32', '2024-07-16', '2025-07-15',
    300, TRUE
) ON CONFLICT (period_name, fiscal_year) DO UPDATE SET
    is_current_period = EXCLUDED.is_current_period,
    updated_at = NOW();

-- Insert current month period (example: Shrawan 2081)
INSERT INTO payroll_periods (
    period_name, period_type, fiscal_year,
    bs_start_date, bs_end_date, ad_start_date, ad_end_date,
    working_days, is_current_period
) VALUES (
    'Shrawan 2081', 'monthly', '2081-82',
    '2081-04-01', '2081-04-32', '2024-07-16', '2024-08-16',
    26, TRUE
) ON CONFLICT (period_name, fiscal_year) DO UPDATE SET
    is_current_period = EXCLUDED.is_current_period,
    updated_at = NOW();

-- ============================================================================
-- Step 4: Create updated_at triggers for new tables
-- ============================================================================

-- Create trigger function if it doesn't exist
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Add triggers for updated_at columns
DROP TRIGGER IF EXISTS update_payroll_approvals_updated_at ON payroll_approvals;
CREATE TRIGGER update_payroll_approvals_updated_at 
    BEFORE UPDATE ON payroll_approvals 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

DROP TRIGGER IF EXISTS update_payroll_disbursements_updated_at ON payroll_disbursements;
CREATE TRIGGER update_payroll_disbursements_updated_at 
    BEFORE UPDATE ON payroll_disbursements 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

DROP TRIGGER IF EXISTS update_payroll_components_master_updated_at ON payroll_components_master;
CREATE TRIGGER update_payroll_components_master_updated_at
    BEFORE UPDATE ON payroll_components_master
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

DROP TRIGGER IF EXISTS update_employee_component_assignments_updated_at ON employee_component_assignments;
CREATE TRIGGER update_employee_component_assignments_updated_at
    BEFORE UPDATE ON employee_component_assignments
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

DROP TRIGGER IF EXISTS update_payroll_periods_updated_at ON payroll_periods;
CREATE TRIGGER update_payroll_periods_updated_at 
    BEFORE UPDATE ON payroll_periods 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

DROP TRIGGER IF EXISTS update_payroll_settings_updated_at ON payroll_settings;
CREATE TRIGGER update_payroll_settings_updated_at 
    BEFORE UPDATE ON payroll_settings 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

DROP TRIGGER IF EXISTS update_monthly_payroll_summary_updated_at ON monthly_payroll_summary;
CREATE TRIGGER update_monthly_payroll_summary_updated_at 
    BEFORE UPDATE ON monthly_payroll_summary 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- ============================================================================
-- SUCCESS VERIFICATION
-- ============================================================================

-- Verify settings were inserted
SELECT COUNT(*) as settings_count FROM payroll_settings;

-- Verify components were inserted
SELECT name, code, type, category FROM payroll_components_master ORDER BY type, name;

-- Verify periods were inserted
SELECT period_name, period_type, fiscal_year, is_current_period FROM payroll_periods ORDER BY period_type;
