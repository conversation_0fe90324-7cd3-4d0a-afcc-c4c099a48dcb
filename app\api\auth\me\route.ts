import { type NextRequest, NextResponse } from "next/server"
import { AuthService } from "@/lib/auth-utils"

export async function GET(request: NextRequest) {
  try {
    const sessionToken = request.cookies.get("session-token")?.value

    if (!sessionToken) {
      return NextResponse.json(null, { status: 401 })
    }

    const user = await AuthService.verifySession(sessionToken)

    if (user) {
      return NextResponse.json(user)
    } else {
      return NextResponse.json(null, { status: 401 })
    }
  } catch (error) {
    console.error("Me API error:", error)
    return NextResponse.json(null, { status: 500 })
  }
}
