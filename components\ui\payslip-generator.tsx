// Payslip Generator Component
// Phase 4: User Interface Development - Payslip Generation & Viewer

"use client"

import React, { useState } from 'react'
import { 
  FileText, 
  Download, 
  Mail, 
  Settings, 
  Eye,
  Calendar,
  Users,
  Building
} from 'lucide-react'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Checkbox } from '@/components/ui/checkbox'
import { Textarea } from '@/components/ui/textarea'
import { <PERSON><PERSON>, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { Badge } from '@/components/ui/badge'
import { Progress } from '@/components/ui/progress'
import { PayslipViewer } from './payslip-viewer'
import { NepaliDatePicker } from './nepali-date-picker'
import { CurrencyInput } from './currency-input'

interface PayslipTemplate {
  id: string
  name: string
  description: string
  isDefault: boolean
  settings: {
    showLogo: boolean
    showBankDetails: boolean
    showAttendance: boolean
    showRemarks: boolean
    language: 'en' | 'ne' | 'both'
    format: 'standard' | 'detailed' | 'compact'
    colorScheme: 'default' | 'blue' | 'green' | 'corporate'
  }
}

interface BulkGenerationOptions {
  employees: string[]
  payPeriod: string
  template: string
  format: 'pdf' | 'html' | 'email'
  emailSettings?: {
    subject: string
    message: string
    sendImmediately: boolean
  }
}

export function PayslipGenerator() {
  const [activeTab, setActiveTab] = useState('single')
  const [selectedEmployee, setSelectedEmployee] = useState('')
  const [selectedPeriod, setSelectedPeriod] = useState('')
  const [selectedTemplate, setSelectedTemplate] = useState('default')
  const [generationProgress, setGenerationProgress] = useState(0)
  const [isGenerating, setIsGenerating] = useState(false)
  const [previewPayslip, setPreviewPayslip] = useState(null)
  const [bulkOptions, setBulkOptions] = useState<BulkGenerationOptions>({
    employees: [],
    payPeriod: '',
    template: 'default',
    format: 'pdf'
  })

  // Mock data
  const employees = [
    { id: 'EMP001', name: 'Ram Sharma', department: 'Engineering' },
    { id: 'EMP002', name: 'Sita Poudel', department: 'Marketing' },
    { id: 'EMP003', name: 'Hari Thapa', department: 'Design' }
  ]

  const payPeriods = [
    { id: '2024-11', name: 'November 2024', bsName: 'Kartik 2081' },
    { id: '2024-10', name: 'October 2024', bsName: 'Ashwin 2081' },
    { id: '2024-09', name: 'September 2024', bsName: 'Bhadra 2081' }
  ]

  const templates: PayslipTemplate[] = [
    {
      id: 'default',
      name: 'Standard Template',
      description: 'Standard payslip format with all basic information',
      isDefault: true,
      settings: {
        showLogo: true,
        showBankDetails: true,
        showAttendance: true,
        showRemarks: true,
        language: 'en',
        format: 'standard',
        colorScheme: 'default'
      }
    },
    {
      id: 'detailed',
      name: 'Detailed Template',
      description: 'Comprehensive payslip with detailed breakdown',
      isDefault: false,
      settings: {
        showLogo: true,
        showBankDetails: true,
        showAttendance: true,
        showRemarks: true,
        language: 'both',
        format: 'detailed',
        colorScheme: 'corporate'
      }
    },
    {
      id: 'compact',
      name: 'Compact Template',
      description: 'Minimal payslip format for quick generation',
      isDefault: false,
      settings: {
        showLogo: false,
        showBankDetails: false,
        showAttendance: false,
        showRemarks: false,
        language: 'en',
        format: 'compact',
        colorScheme: 'blue'
      }
    }
  ]

  const handleSingleGeneration = async () => {
    if (!selectedEmployee || !selectedPeriod) {
      return
    }

    setIsGenerating(true)
    setGenerationProgress(0)

    // Simulate generation process
    for (let i = 0; i <= 100; i += 10) {
      setGenerationProgress(i)
      await new Promise(resolve => setTimeout(resolve, 100))
    }

    // Mock payslip data
    const mockPayslip = {
      id: `PAY-${Date.now()}`,
      employeeId: selectedEmployee,
      employeeName: employees.find(e => e.id === selectedEmployee)?.name || '',
      designation: 'Senior Developer',
      department: employees.find(e => e.id === selectedEmployee)?.department || '',
      payPeriod: payPeriods.find(p => p.id === selectedPeriod)?.name || '',
      bsPayPeriod: payPeriods.find(p => p.id === selectedPeriod)?.bsName || '',
      periodStart: '2024-11-01',
      periodEnd: '2024-11-30',
      payDate: '2024-12-01',
      bsPayDate: 'Mangsir 16, 2081',
      baseSalary: 65000,
      overtimePay: 8000,
      allowances: [
        { id: 'transport', name: 'Transport Allowance', amount: 5000, type: 'fixed' as const },
        { id: 'meal', name: 'Meal Allowance', amount: 3000, type: 'fixed' as const }
      ],
      bonuses: 2000,
      grossPay: 83000,
      deductions: [
        { id: 'pf', name: 'Provident Fund', amount: 6500, type: 'percentage' as const },
        { id: 'insurance', name: 'Health Insurance', amount: 2000, type: 'fixed' as const }
      ],
      taxes: 3500,
      totalDeductions: 12000,
      netPay: 71000,
      workingDays: 26,
      attendedDays: 25,
      absentDays: 1,
      overtimeHours: 12,
      lateHours: 2,
      company: {
        name: 'Tech Solutions Nepal Pvt. Ltd.',
        nameNepali: 'टेक सोल्युसन्स नेपाल प्रा.लि.',
        address: 'Kathmandu, Nepal',
        addressNepali: 'काठमाडौं, नेपाल',
        phone: '+977-1-4444444',
        email: '<EMAIL>',
        panNumber: '*********',
        registrationNumber: 'REG-12345'
      },
      bankAccount: {
        accountNumber: '*********0',
        bankName: 'Nepal Bank Limited',
        branchName: 'Kathmandu Branch'
      },
      status: 'processed' as const
    }

    setPreviewPayslip(mockPayslip)
    setIsGenerating(false)
  }

  const handleBulkGeneration = async () => {
    if (bulkOptions.employees.length === 0 || !bulkOptions.payPeriod) {
      return
    }

    setIsGenerating(true)
    setGenerationProgress(0)

    const totalEmployees = bulkOptions.employees.length
    
    for (let i = 0; i < totalEmployees; i++) {
      // Simulate processing each employee
      await new Promise(resolve => setTimeout(resolve, 500))
      setGenerationProgress(((i + 1) / totalEmployees) * 100)
    }

    setIsGenerating(false)
    
    // Show success message or download link
    alert(`Successfully generated ${totalEmployees} payslips!`)
  }

  const handleTemplateCustomization = (templateId: string, settings: any) => {
    // Update template settings
    console.log('Updating template:', templateId, settings)
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold">Payslip Generator</h2>
          <p className="text-muted-foreground">
            Generate and customize payslips for employees
          </p>
        </div>
        
        <div className="flex items-center gap-2">
          <Button variant="outline">
            <Settings className="h-4 w-4 mr-2" />
            Templates
          </Button>
          <Button variant="outline">
            <Mail className="h-4 w-4 mr-2" />
            Email Settings
          </Button>
        </div>
      </div>

      <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-4">
        <TabsList className="grid w-full grid-cols-3">
          <TabsTrigger value="single">Single Payslip</TabsTrigger>
          <TabsTrigger value="bulk">Bulk Generation</TabsTrigger>
          <TabsTrigger value="templates">Templates</TabsTrigger>
        </TabsList>

        <TabsContent value="single" className="space-y-6">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* Generation Form */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <FileText className="h-5 w-5" />
                  Generate Single Payslip
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-2">
                  <Label htmlFor="employee">Select Employee</Label>
                  <Select value={selectedEmployee} onValueChange={setSelectedEmployee}>
                    <SelectTrigger>
                      <SelectValue placeholder="Choose employee" />
                    </SelectTrigger>
                    <SelectContent>
                      {employees.map((employee) => (
                        <SelectItem key={employee.id} value={employee.id}>
                          {employee.name} ({employee.id}) - {employee.department}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="period">Pay Period</Label>
                  <Select value={selectedPeriod} onValueChange={setSelectedPeriod}>
                    <SelectTrigger>
                      <SelectValue placeholder="Choose pay period" />
                    </SelectTrigger>
                    <SelectContent>
                      {payPeriods.map((period) => (
                        <SelectItem key={period.id} value={period.id}>
                          {period.name} ({period.bsName})
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="template">Template</Label>
                  <Select value={selectedTemplate} onValueChange={setSelectedTemplate}>
                    <SelectTrigger>
                      <SelectValue placeholder="Choose template" />
                    </SelectTrigger>
                    <SelectContent>
                      {templates.map((template) => (
                        <SelectItem key={template.id} value={template.id}>
                          {template.name}
                          {template.isDefault && <Badge variant="secondary" className="ml-2">Default</Badge>}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>

                {isGenerating && (
                  <div className="space-y-2">
                    <div className="flex justify-between text-sm">
                      <span>Generating payslip...</span>
                      <span>{generationProgress}%</span>
                    </div>
                    <Progress value={generationProgress} />
                  </div>
                )}

                <div className="flex gap-2">
                  <Button 
                    onClick={handleSingleGeneration}
                    disabled={!selectedEmployee || !selectedPeriod || isGenerating}
                    className="flex-1"
                  >
                    <FileText className="h-4 w-4 mr-2" />
                    Generate Payslip
                  </Button>
                  
                  {previewPayslip && (
                    <Button variant="outline">
                      <Eye className="h-4 w-4 mr-2" />
                      Preview
                    </Button>
                  )}
                </div>
              </CardContent>
            </Card>

            {/* Preview */}
            <Card>
              <CardHeader>
                <CardTitle>Preview</CardTitle>
              </CardHeader>
              <CardContent>
                {previewPayslip ? (
                  <div className="space-y-4">
                    <div className="text-sm text-muted-foreground">
                      Payslip generated successfully for {previewPayslip.employeeName}
                    </div>
                    <div className="flex gap-2">
                      <Button size="sm">
                        <Download className="h-4 w-4 mr-2" />
                        Download PDF
                      </Button>
                      <Button size="sm" variant="outline">
                        <Mail className="h-4 w-4 mr-2" />
                        Email
                      </Button>
                    </div>
                  </div>
                ) : (
                  <div className="text-center py-8 text-muted-foreground">
                    <FileText className="h-12 w-12 mx-auto mb-4 opacity-50" />
                    <p>Select employee and period to generate payslip</p>
                  </div>
                )}
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="bulk" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Users className="h-5 w-5" />
                Bulk Payslip Generation
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="space-y-4">
                  <div className="space-y-2">
                    <Label>Select Employees</Label>
                    <div className="border rounded-lg p-4 max-h-48 overflow-y-auto">
                      <div className="space-y-2">
                        <div className="flex items-center space-x-2">
                          <Checkbox 
                            id="select-all"
                            checked={bulkOptions.employees.length === employees.length}
                            onCheckedChange={(checked) => {
                              setBulkOptions(prev => ({
                                ...prev,
                                employees: checked ? employees.map(e => e.id) : []
                              }))
                            }}
                          />
                          <Label htmlFor="select-all" className="font-medium">
                            Select All ({employees.length})
                          </Label>
                        </div>
                        {employees.map((employee) => (
                          <div key={employee.id} className="flex items-center space-x-2">
                            <Checkbox 
                              id={employee.id}
                              checked={bulkOptions.employees.includes(employee.id)}
                              onCheckedChange={(checked) => {
                                setBulkOptions(prev => ({
                                  ...prev,
                                  employees: checked 
                                    ? [...prev.employees, employee.id]
                                    : prev.employees.filter(id => id !== employee.id)
                                }))
                              }}
                            />
                            <Label htmlFor={employee.id} className="text-sm">
                              {employee.name} ({employee.department})
                            </Label>
                          </div>
                        ))}
                      </div>
                    </div>
                  </div>

                  <div className="space-y-2">
                    <Label>Pay Period</Label>
                    <Select 
                      value={bulkOptions.payPeriod} 
                      onValueChange={(value) => setBulkOptions(prev => ({ ...prev, payPeriod: value }))}
                    >
                      <SelectTrigger>
                        <SelectValue placeholder="Choose pay period" />
                      </SelectTrigger>
                      <SelectContent>
                        {payPeriods.map((period) => (
                          <SelectItem key={period.id} value={period.id}>
                            {period.name} ({period.bsName})
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>
                </div>

                <div className="space-y-4">
                  <div className="space-y-2">
                    <Label>Template</Label>
                    <Select 
                      value={bulkOptions.template} 
                      onValueChange={(value) => setBulkOptions(prev => ({ ...prev, template: value }))}
                    >
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        {templates.map((template) => (
                          <SelectItem key={template.id} value={template.id}>
                            {template.name}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>

                  <div className="space-y-2">
                    <Label>Output Format</Label>
                    <Select 
                      value={bulkOptions.format} 
                      onValueChange={(value: 'pdf' | 'html' | 'email') => 
                        setBulkOptions(prev => ({ ...prev, format: value }))
                      }
                    >
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="pdf">PDF Files</SelectItem>
                        <SelectItem value="html">HTML Files</SelectItem>
                        <SelectItem value="email">Email to Employees</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>

                  {bulkOptions.format === 'email' && (
                    <div className="space-y-2">
                      <Label>Email Subject</Label>
                      <Input 
                        placeholder="Your payslip for {period}"
                        value={bulkOptions.emailSettings?.subject || ''}
                        onChange={(e) => setBulkOptions(prev => ({
                          ...prev,
                          emailSettings: {
                            ...prev.emailSettings,
                            subject: e.target.value,
                            message: prev.emailSettings?.message || '',
                            sendImmediately: prev.emailSettings?.sendImmediately || false
                          }
                        }))}
                      />
                    </div>
                  )}
                </div>
              </div>

              {isGenerating && (
                <div className="space-y-2">
                  <div className="flex justify-between text-sm">
                    <span>Processing {bulkOptions.employees.length} payslips...</span>
                    <span>{Math.round(generationProgress)}%</span>
                  </div>
                  <Progress value={generationProgress} />
                </div>
              )}

              <div className="flex justify-between items-center">
                <div className="text-sm text-muted-foreground">
                  {bulkOptions.employees.length} employee(s) selected
                </div>
                
                <Button 
                  onClick={handleBulkGeneration}
                  disabled={bulkOptions.employees.length === 0 || !bulkOptions.payPeriod || isGenerating}
                >
                  <FileText className="h-4 w-4 mr-2" />
                  Generate {bulkOptions.employees.length} Payslips
                </Button>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="templates" className="space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {templates.map((template) => (
              <Card key={template.id}>
                <CardHeader>
                  <div className="flex items-center justify-between">
                    <CardTitle className="text-lg">{template.name}</CardTitle>
                    {template.isDefault && (
                      <Badge variant="secondary">Default</Badge>
                    )}
                  </div>
                  <p className="text-sm text-muted-foreground">
                    {template.description}
                  </p>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="space-y-2 text-sm">
                    <div className="flex justify-between">
                      <span>Language:</span>
                      <span className="capitalize">{template.settings.language}</span>
                    </div>
                    <div className="flex justify-between">
                      <span>Format:</span>
                      <span className="capitalize">{template.settings.format}</span>
                    </div>
                    <div className="flex justify-between">
                      <span>Color Scheme:</span>
                      <span className="capitalize">{template.settings.colorScheme}</span>
                    </div>
                  </div>
                  
                  <div className="flex gap-2">
                    <Button size="sm" variant="outline" className="flex-1">
                      <Eye className="h-4 w-4 mr-2" />
                      Preview
                    </Button>
                    <Button size="sm" variant="outline">
                      <Settings className="h-4 w-4" />
                    </Button>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </TabsContent>
      </Tabs>
    </div>
  )
}
