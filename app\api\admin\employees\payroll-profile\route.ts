// Employee Payroll Profile API Endpoints
// Admin endpoints for managing employee payroll profiles

import { NextRequest, NextResponse } from 'next/server';
import { employeePayrollManager } from '@/lib/employee-payroll-manager';
import { AuthService } from '@/lib/auth-utils';

// GET - Get employee payroll profile or all employees summary
export async function GET(request: NextRequest) {
  try {
    const sessionToken = request.cookies.get("session-token")?.value;

    if (!sessionToken) {
      return NextResponse.json({
        success: false,
        error: 'Unauthorized'
      }, { status: 401 });
    }

    const user = await AuthService.verifySession(sessionToken);

    if (!user || (user.role !== 'admin' && user.role !== 'hr_manager')) {
      return NextResponse.json({
        success: false,
        error: 'Insufficient permissions'
      }, { status: 403 });
    }

    const { searchParams } = new URL(request.url);
    const employeeId = searchParams.get('employeeId');

    if (employeeId) {
      // Get specific employee payroll profile
      const profile = await employeePayrollManager.getEmployeePayrollProfile(employeeId);
      
      if (!profile) {
        return NextResponse.json({
          success: false,
          error: 'Employee not found'
        }, { status: 404 });
      }

      return NextResponse.json({
        success: true,
        data: profile
      });
    } else {
      // Get all employees payroll summary
      const employees = await employeePayrollManager.getAllEmployeesPayrollSummary();

      return NextResponse.json({
        success: true,
        data: employees
      });
    }

  } catch (error) {
    console.error('Error in payroll profile GET:', error);
    return NextResponse.json({
      success: false,
      error: 'Internal server error'
    }, { status: 500 });
  }
}

// PUT - Update employee payroll information
export async function PUT(request: NextRequest) {
  try {
    const sessionToken = request.cookies.get("session-token")?.value;

    if (!sessionToken) {
      return NextResponse.json({
        success: false,
        error: 'Unauthorized'
      }, { status: 401 });
    }

    const user = await AuthService.verifySession(sessionToken);

    if (!user || (user.role !== 'admin' && user.role !== 'hr_manager')) {
      return NextResponse.json({
        success: false,
        error: 'Insufficient permissions'
      }, { status: 403 });
    }

    const body = await request.json();
    const { employeeId, updates } = body;

    if (!employeeId || !updates) {
      return NextResponse.json({
        success: false,
        error: 'Employee ID and updates are required'
      }, { status: 400 });
    }

    // Validate employee_type if provided
    if (updates.employee_type && !['full_time', 'part_time', 'contract', 'intern', 'consultant'].includes(updates.employee_type)) {
      return NextResponse.json({
        success: false,
        error: 'Invalid employee type'
      }, { status: 400 });
    }

    // Validate employee_category if provided
    if (updates.employee_category && !['regular', 'probation', 'temporary', 'seasonal', 'project_based'].includes(updates.employee_category)) {
      return NextResponse.json({
        success: false,
        error: 'Invalid employee category'
      }, { status: 400 });
    }

    // Validate employment_status if provided
    if (updates.employment_status && !['active', 'inactive', 'terminated', 'resigned', 'retired'].includes(updates.employment_status)) {
      return NextResponse.json({
        success: false,
        error: 'Invalid employment status'
      }, { status: 400 });
    }

    const success = await employeePayrollManager.updateEmployeePayrollInfo(employeeId, updates);

    if (success) {
      return NextResponse.json({
        success: true,
        message: 'Employee payroll information updated successfully'
      });
    } else {
      return NextResponse.json({
        success: false,
        error: 'Failed to update employee payroll information'
      }, { status: 500 });
    }

  } catch (error) {
    console.error('Error in payroll profile PUT:', error);
    return NextResponse.json({
      success: false,
      error: 'Internal server error'
    }, { status: 500 });
  }
}

// POST - Add bank account for employee
export async function POST(request: NextRequest) {
  try {
    const sessionToken = request.cookies.get("session-token")?.value;

    if (!sessionToken) {
      return NextResponse.json({
        success: false,
        error: 'Unauthorized'
      }, { status: 401 });
    }

    const user = await AuthService.verifySession(sessionToken);

    if (!user || (user.role !== 'admin' && user.role !== 'hr_manager')) {
      return NextResponse.json({
        success: false,
        error: 'Insufficient permissions'
      }, { status: 403 });
    }

    const body = await request.json();
    const { employeeId, bankAccount } = body;

    if (!employeeId || !bankAccount) {
      return NextResponse.json({
        success: false,
        error: 'Employee ID and bank account details are required'
      }, { status: 400 });
    }

    // Validate required bank account fields
    if (!bankAccount.bank_name || !bankAccount.account_number || !bankAccount.account_holder_name) {
      return NextResponse.json({
        success: false,
        error: 'Bank name, account number, and account holder name are required'
      }, { status: 400 });
    }

    // Validate account_type
    if (bankAccount.account_type && !['savings', 'current', 'salary'].includes(bankAccount.account_type)) {
      return NextResponse.json({
        success: false,
        error: 'Invalid account type'
      }, { status: 400 });
    }

    const accountId = await employeePayrollManager.addBankAccount(employeeId, {
      bank_name: bankAccount.bank_name,
      bank_branch: bankAccount.bank_branch || null,
      account_number: bankAccount.account_number,
      account_holder_name: bankAccount.account_holder_name,
      account_type: bankAccount.account_type || 'savings',
      is_primary: bankAccount.is_primary || false,
      is_active: bankAccount.is_active !== false
    });

    return NextResponse.json({
      success: true,
      data: { accountId },
      message: 'Bank account added successfully'
    });

  } catch (error) {
    console.error('Error in payroll profile POST:', error);
    return NextResponse.json({
      success: false,
      error: 'Internal server error'
    }, { status: 500 });
  }
}
