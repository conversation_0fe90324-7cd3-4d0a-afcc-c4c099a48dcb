// Nepal-specific configuration and utilities for payroll system
// Phase 1: Nepal Configuration Setup

export interface NepalConfig {
  calendar: NepalCalendarConfig;
  currency: NepalCurrencyConfig;
  fiscalYear: NepalFiscalYearConfig;
  laborLaw: NepalLaborLawConfig;
  holidays: NepalHolidayConfig;
}

export interface NepalCalendarConfig {
  currentBSYear: number;
  currentBSMonth: number;
  currentBSDay: number;
  monthNames: string[];
  monthNamesNepali: string[];
  dayNames: string[];
  dayNamesNepali: string[];
}

export interface NepalCurrencyConfig {
  symbol: string;
  code: string;
  name: string;
  subunit: string;
  decimalPlaces: number;
  thousandSeparator: string;
  decimalSeparator: string;
  useIndianNumbering: boolean; // For lakhs/crores
}

export interface NepalFiscalYearConfig {
  startMonth: number; // Shrawan (4th month in BS)
  startDay: number;   // 1st day
  endMonth: number;   // Ashadh (12th month in BS)
  endDay: number;     // Last day of Ashadh
  currentFiscalYear: string; // e.g., "2081-82"
}

export interface NepalLaborLawConfig {
  standardWorkingHours: number;
  standardWorkingDays: number;
  overtimeThreshold: number;
  overtimeMultiplier: number;
  weeklyOffDays: number[];
  maxWorkingHoursPerDay: number;
  maxOvertimeHoursPerDay: number;
  providentFundRate: number;
  minimumWage: number;
}

export interface NepalHolidayConfig {
  publicHolidays: NepalHoliday[];
  festivals: NepalHoliday[];
  observances: NepalHoliday[];
}

export interface NepalHoliday {
  id: string;
  name: string;
  nameNepali?: string;
  type: 'public' | 'festival' | 'observance';
  bsDate: string; // BS format: YYYY-MM-DD
  adDate: string; // AD format: YYYY-MM-DD
  isRecurring: boolean;
  description?: string;
}

// Default Nepal configuration
export const DEFAULT_NEPAL_CONFIG: NepalConfig = {
  calendar: {
    currentBSYear: 2081,
    currentBSMonth: 4, // Shrawan
    currentBSDay: 1,
    monthNames: [
      'Baishakh', 'Jestha', 'Ashadh', 'Shrawan', 'Bhadra', 'Ashwin',
      'Kartik', 'Mangsir', 'Poush', 'Magh', 'Falgun', 'Chaitra'
    ],
    monthNamesNepali: [
      'बैशाख', 'जेठ', 'आषाढ', 'श्रावण', 'भाद्र', 'आश्विन',
      'कार्तिक', 'मंसिर', 'पौष', 'माघ', 'फाल्गुन', 'चैत्र'
    ],
    dayNames: ['Sunday', 'Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday'],
    dayNamesNepali: ['आइतबार', 'सोमबार', 'मंगलबार', 'बुधबार', 'बिहिबार', 'शुक्रबार', 'शनिबार']
  },
  
  currency: {
    symbol: 'NPR',
    code: 'NPR',
    name: 'Nepali Rupee',
    subunit: 'Paisa',
    decimalPlaces: 2,
    thousandSeparator: ',',
    decimalSeparator: '.',
    useIndianNumbering: true
  },
  
  fiscalYear: {
    startMonth: 4, // Shrawan
    startDay: 1,
    endMonth: 12, // Ashadh
    endDay: 32, // Last day varies by year
    currentFiscalYear: '2081-82'
  },
  
  laborLaw: {
    standardWorkingHours: 8,
    standardWorkingDays: 6, // Sunday to Friday
    overtimeThreshold: 8,
    overtimeMultiplier: 1.5,
    weeklyOffDays: [6], // Saturday (0=Sunday, 6=Saturday)
    maxWorkingHoursPerDay: 12,
    maxOvertimeHoursPerDay: 4,
    providentFundRate: 10, // 10%
    minimumWage: 17300 // NPR per month (as of 2024)
  },
  
  holidays: {
    publicHolidays: [
      {
        id: 'new-year',
        name: 'New Year',
        type: 'public',
        bsDate: '2081-01-01',
        adDate: '2024-04-14',
        isRecurring: true,
        description: 'Nepali New Year'
      },
      {
        id: 'democracy-day',
        name: 'Democracy Day',
        type: 'public',
        bsDate: '2081-01-07',
        adDate: '2024-04-20',
        isRecurring: true,
        description: 'Democracy Day'
      },
      {
        id: 'constitution-day',
        name: 'Constitution Day',
        type: 'public',
        bsDate: '2081-06-03',
        adDate: '2024-09-19',
        isRecurring: true,
        description: 'Constitution Day'
      }
    ],
    festivals: [
      {
        id: 'dashain',
        name: 'Dashain',
        nameNepali: 'दशैं',
        type: 'festival',
        bsDate: '2081-07-10',
        adDate: '2024-10-26',
        isRecurring: true,
        description: 'Major Hindu festival'
      },
      {
        id: 'tihar',
        name: 'Tihar',
        nameNepali: 'तिहार',
        type: 'festival',
        bsDate: '2081-08-15',
        adDate: '2024-11-30',
        isRecurring: true,
        description: 'Festival of Lights'
      }
    ],
    observances: []
  }
};

// Utility functions for Nepal configuration
export class NepalConfigManager {
  private config: NepalConfig;

  constructor(config: NepalConfig = DEFAULT_NEPAL_CONFIG) {
    this.config = config;
  }

  // Get current configuration
  getConfig(): NepalConfig {
    return this.config;
  }

  // Update configuration
  updateConfig(updates: Partial<NepalConfig>): void {
    this.config = { ...this.config, ...updates };
  }

  // Currency formatting
  formatCurrency(amount: number, options?: {
    showSymbol?: boolean;
    useIndianNumbering?: boolean;
    decimalPlaces?: number;
  }): string {
    const opts = {
      showSymbol: true,
      useIndianNumbering: this.config.currency.useIndianNumbering,
      decimalPlaces: this.config.currency.decimalPlaces,
      ...options
    };

    let formattedAmount: string;

    if (opts.useIndianNumbering) {
      // Indian numbering system (lakhs, crores)
      formattedAmount = this.formatIndianNumbering(amount, opts.decimalPlaces);
    } else {
      // Standard international numbering
      formattedAmount = amount.toLocaleString('en-US', {
        minimumFractionDigits: opts.decimalPlaces,
        maximumFractionDigits: opts.decimalPlaces
      });
    }

    return opts.showSymbol ? `${this.config.currency.symbol} ${formattedAmount}` : formattedAmount;
  }

  // Format numbers in Indian numbering system (lakhs, crores)
  private formatIndianNumbering(amount: number, decimalPlaces: number): string {
    const parts = amount.toFixed(decimalPlaces).split('.');
    const integerPart = parts[0];
    const decimalPart = parts[1];

    // Add commas in Indian style (last 3 digits, then every 2 digits)
    let formatted = '';
    const reversed = integerPart.split('').reverse();
    
    for (let i = 0; i < reversed.length; i++) {
      if (i === 3 || (i > 3 && (i - 3) % 2 === 0)) {
        formatted = ',' + formatted;
      }
      formatted = reversed[i] + formatted;
    }

    return decimalPart ? `${formatted}.${decimalPart}` : formatted;
  }

  // Get current fiscal year
  getCurrentFiscalYear(): string {
    return this.config.fiscalYear.currentFiscalYear;
  }

  // Check if date is a holiday
  isHoliday(date: string, type?: 'public' | 'festival' | 'observance'): boolean {
    const allHolidays = [
      ...this.config.holidays.publicHolidays,
      ...this.config.holidays.festivals,
      ...this.config.holidays.observances
    ];

    return allHolidays.some(holiday => {
      const matchesDate = holiday.adDate === date || holiday.bsDate === date;
      const matchesType = !type || holiday.type === type;
      return matchesDate && matchesType;
    });
  }

  // Get holidays for a date range
  getHolidaysInRange(startDate: string, endDate: string): NepalHoliday[] {
    const allHolidays = [
      ...this.config.holidays.publicHolidays,
      ...this.config.holidays.festivals,
      ...this.config.holidays.observances
    ];

    return allHolidays.filter(holiday => {
      const holidayDate = new Date(holiday.adDate);
      const start = new Date(startDate);
      const end = new Date(endDate);
      return holidayDate >= start && holidayDate <= end;
    });
  }

  // Check if day is working day
  isWorkingDay(date: Date): boolean {
    const dayOfWeek = date.getDay();
    const isWeeklyOff = this.config.laborLaw.weeklyOffDays.includes(dayOfWeek);
    const isHoliday = this.isHoliday(date.toISOString().split('T')[0]);
    
    return !isWeeklyOff && !isHoliday;
  }

  // Calculate working days in a period
  getWorkingDaysInPeriod(startDate: string, endDate: string): number {
    const start = new Date(startDate);
    const end = new Date(endDate);
    let workingDays = 0;
    
    const currentDate = new Date(start);
    while (currentDate <= end) {
      if (this.isWorkingDay(currentDate)) {
        workingDays++;
      }
      currentDate.setDate(currentDate.getDate() + 1);
    }
    
    return workingDays;
  }

  // Get labor law settings
  getLaborLawConfig(): NepalLaborLawConfig {
    return this.config.laborLaw;
  }

  // Calculate overtime hours
  calculateOvertimeHours(totalHours: number): {
    regularHours: number;
    overtimeHours: number;
  } {
    const threshold = this.config.laborLaw.overtimeThreshold;
    const regularHours = Math.min(totalHours, threshold);
    const overtimeHours = Math.max(0, totalHours - threshold);
    
    return { regularHours, overtimeHours };
  }

  // Calculate overtime pay
  calculateOvertimePay(overtimeHours: number, hourlyRate: number): number {
    return overtimeHours * hourlyRate * this.config.laborLaw.overtimeMultiplier;
  }

  // Get month name in English
  getMonthName(monthNumber: number, language: 'en' | 'ne' = 'en'): string {
    const index = monthNumber - 1;
    if (language === 'ne') {
      return this.config.calendar.monthNamesNepali[index] || '';
    }
    return this.config.calendar.monthNames[index] || '';
  }

  // Get day name
  getDayName(dayNumber: number, language: 'en' | 'ne' = 'en'): string {
    if (language === 'ne') {
      return this.config.calendar.dayNamesNepali[dayNumber] || '';
    }
    return this.config.calendar.dayNames[dayNumber] || '';
  }
}

// Global instance
export const nepalConfig = new NepalConfigManager();

// Export types and utilities
export type { NepalConfig, NepalHoliday, NepalLaborLawConfig };
