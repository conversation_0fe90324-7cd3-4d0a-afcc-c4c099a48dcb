const { neon } = require('@neondatabase/serverless');
require('dotenv').config({ path: '.env.local' });

const sql = neon(process.env.DATABASE_URL);

async function checkUserRole() {
  try {
    const user = await sql`
      SELECT id, full_name, role 
      FROM users 
      WHERE full_name = '<PERSON><PERSON>'
    `;
    
    console.log('User details:', user[0]);
    
    if (user[0]) {
      const hasAccess = ['admin', 'hr_manager'].includes(user[0].role);
      console.log('Has access to Recovery Flow:', hasAccess);
    }
  } catch (error) {
    console.error('Error:', error);
  }
}

checkUserRole();
