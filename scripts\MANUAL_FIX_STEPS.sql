-- MANUAL FIX FOR ATTENDANCE TABLE DATA TYPE MISMATCH
-- Copy and paste these commands one by one into your Neon SQL console

-- ============================================================================
-- STEP 1: VERIFY CURRENT PROBLEM
-- ============================================================================
-- This should show 'time without time zone' for both columns (the problem)
SELECT 
    table_name,
    column_name, 
    data_type, 
    is_nullable
FROM information_schema.columns 
WHERE table_name = 'attendance' 
AND column_name IN ('check_in_time', 'check_out_time')
ORDER BY column_name;

-- ============================================================================
-- STEP 2: CHECK FOR EXISTING DATA
-- ============================================================================
-- See if there's any existing attendance data that needs to be migrated
SELECT COUNT(*) as total_records FROM attendance;
SELECT * FROM attendance LIMIT 5;

-- ============================================================================
-- STEP 3: CREATE NEW COLUMNS WITH CORRECT DATA TYPE
-- ============================================================================
-- Add temporary columns with the correct TIMESTAMP WITH TIME ZONE type
ALTER TABLE attendance ADD COLUMN check_in_time_temp TIMESTAMP WITH TIME ZONE;
ALTER TABLE attendance ADD COLUMN check_out_time_temp TIMESTAMP WITH TIME ZONE;

-- ============================================================================
-- STEP 4: MIGRATE EXISTING DATA (IF ANY)
-- ============================================================================
-- Convert existing TIME data to TIMESTAMP WITH TIME ZONE by combining with date
UPDATE attendance 
SET 
    check_in_time_temp = CASE 
        WHEN check_in_time IS NOT NULL THEN 
            (date + check_in_time)::TIMESTAMP WITH TIME ZONE
        ELSE NULL 
    END,
    check_out_time_temp = CASE 
        WHEN check_out_time IS NOT NULL THEN 
            (date + check_out_time)::TIMESTAMP WITH TIME ZONE
        ELSE NULL 
    END;

-- ============================================================================
-- STEP 5: VERIFY DATA MIGRATION
-- ============================================================================
-- Check that the data was migrated correctly
SELECT 
    date,
    check_in_time as old_check_in,
    check_in_time_temp as new_check_in,
    check_out_time as old_check_out,
    check_out_time_temp as new_check_out
FROM attendance 
WHERE check_in_time IS NOT NULL OR check_out_time IS NOT NULL
LIMIT 5;

-- ============================================================================
-- STEP 6: DROP OLD COLUMNS
-- ============================================================================
-- Remove the old TIME columns
ALTER TABLE attendance DROP COLUMN check_in_time;
ALTER TABLE attendance DROP COLUMN check_out_time;

-- ============================================================================
-- STEP 7: RENAME NEW COLUMNS
-- ============================================================================
-- Rename the temporary columns to the original names
ALTER TABLE attendance RENAME COLUMN check_in_time_temp TO check_in_time;
ALTER TABLE attendance RENAME COLUMN check_out_time_temp TO check_out_time;

-- ============================================================================
-- STEP 8: VERIFY THE FIX
-- ============================================================================
-- This should now show 'timestamp with time zone' for both columns (fixed!)
SELECT 
    table_name,
    column_name, 
    data_type, 
    is_nullable
FROM information_schema.columns 
WHERE table_name = 'attendance' 
AND column_name IN ('check_in_time', 'check_out_time')
ORDER BY column_name;

-- ============================================================================
-- STEP 9: TEST THE FIX
-- ============================================================================
-- Test inserting a full timestamp (this should work now)
-- Note: Replace 'your-user-id-here' with an actual user ID from your users table

-- First, get a valid user ID:
SELECT id, email FROM users LIMIT 1;

-- Then test the insert (replace the user_id with actual ID from above):
-- INSERT INTO attendance (user_id, date, check_in_time, status) 
-- VALUES ('your-user-id-here', CURRENT_DATE, NOW(), 'present');

-- Verify the test record was inserted:
-- SELECT * FROM attendance WHERE date = CURRENT_DATE ORDER BY created_at DESC LIMIT 1;

-- Clean up the test record:
-- DELETE FROM attendance WHERE date = CURRENT_DATE AND status = 'present';

-- ============================================================================
-- EXPECTED RESULTS
-- ============================================================================
-- After running all steps:
-- 1. Both check_in_time and check_out_time should be 'timestamp with time zone'
-- 2. Any existing data should be preserved and converted
-- 3. New timestamp inserts should work without errors
-- 4. The attendance system should work correctly

-- ============================================================================
-- TROUBLESHOOTING
-- ============================================================================
-- If something goes wrong, you can check:

-- View table structure:
-- \d attendance;

-- Check for any remaining temp columns:
-- SELECT column_name FROM information_schema.columns WHERE table_name = 'attendance';

-- If you need to start over:
-- DROP TABLE attendance;
-- -- Then re-run the table creation from scripts/01-create-neon-tables.sql
