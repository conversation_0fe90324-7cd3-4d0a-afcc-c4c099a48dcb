// Working Days Configuration API Endpoint
// Manages working days configuration for attendance-based payroll calculations

import { NextRequest, NextResponse } from 'next/server';
import { AuthService } from '@/lib/auth-utils';
import { serverDb } from '@/lib/server-db';

interface WorkingDaysConfig {
  id?: string;
  fiscal_year: string;
  bs_month: number;
  bs_month_name: string;
  total_days_in_month: number;
  working_days: number;
  public_holidays: number;
  weekend_days: number;
  late_penalty_type: 'none' | 'half_day' | 'custom';
  late_penalty_amount: number;
  half_day_calculation_method: 'fifty_percent' | 'custom';
}

// GET - Fetch working days configuration
export async function GET(request: NextRequest) {
  try {
    const sessionToken = request.cookies.get("session-token")?.value;

    if (!sessionToken) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const user = await AuthService.verifySession(sessionToken);

    if (!user || !["admin", "hr_manager"].includes(user.role)) {
      return NextResponse.json({ error: "Forbidden" }, { status: 403 });
    }

    const { searchParams } = new URL(request.url);
    const fiscalYear = searchParams.get("fiscal_year");

    // Fetch working days configuration
    let workingDaysConfig;
    if (fiscalYear) {
      workingDaysConfig = await serverDb.sql`
        SELECT
          id,
          fiscal_year,
          bs_month,
          bs_month_name,
          total_days_in_month,
          working_days,
          public_holidays,
          weekend_days,
          late_penalty_type,
          late_penalty_amount,
          half_day_calculation_method,
          created_at,
          updated_at
        FROM working_days_configuration
        WHERE fiscal_year = ${fiscalYear}
        ORDER BY fiscal_year DESC, bs_month ASC
      `;
    } else {
      workingDaysConfig = await serverDb.sql`
        SELECT
          id,
          fiscal_year,
          bs_month,
          bs_month_name,
          total_days_in_month,
          working_days,
          public_holidays,
          weekend_days,
          late_penalty_type,
          late_penalty_amount,
          half_day_calculation_method,
          created_at,
          updated_at
        FROM working_days_configuration
        ORDER BY fiscal_year DESC, bs_month ASC
      `;
    }

    // Also fetch attendance calculation settings
    const attendanceSettings = await serverDb.sql`
      SELECT setting_name, setting_value, setting_type, description, category
      FROM attendance_calculation_settings
      ORDER BY category, setting_name
    `;

    return NextResponse.json({
      success: true,
      data: {
        working_days_config: workingDaysConfig,
        attendance_settings: attendanceSettings
      }
    });

  } catch (error) {
    console.error('Error fetching working days configuration:', error);
    return NextResponse.json(
      { 
        success: false, 
        error: 'Failed to fetch working days configuration',
        details: error.message 
      },
      { status: 500 }
    );
  }
}

// POST - Create new working days configuration
export async function POST(request: NextRequest) {
  try {
    const sessionToken = request.cookies.get("session-token")?.value;

    if (!sessionToken) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const user = await AuthService.verifySession(sessionToken);

    if (!user || !["admin", "hr_manager"].includes(user.role)) {
      return NextResponse.json({ error: "Forbidden" }, { status: 403 });
    }

    const body = await request.json();
    const config: WorkingDaysConfig = body;

    // Validation
    if (!config.fiscal_year || !config.bs_month || !config.bs_month_name) {
      return NextResponse.json(
        { success: false, error: 'Fiscal year, month number, and month name are required' },
        { status: 400 }
      );
    }

    if (config.bs_month < 1 || config.bs_month > 12) {
      return NextResponse.json(
        { success: false, error: 'Month number must be between 1 and 12' },
        { status: 400 }
      );
    }

    if (config.working_days > config.total_days_in_month) {
      return NextResponse.json(
        { success: false, error: 'Working days cannot exceed total days in month' },
        { status: 400 }
      );
    }

    // Insert new configuration
    const result = await serverDb.sql`
      INSERT INTO working_days_configuration (
        fiscal_year, bs_month, bs_month_name, total_days_in_month,
        working_days, public_holidays, weekend_days,
        late_penalty_type, late_penalty_amount, half_day_calculation_method,
        created_by
      ) VALUES (
        ${config.fiscal_year}, ${config.bs_month}, ${config.bs_month_name},
        ${config.total_days_in_month}, ${config.working_days},
        ${config.public_holidays || 0}, ${config.weekend_days || 0},
        ${config.late_penalty_type || 'half_day'}, ${config.late_penalty_amount || 0},
        ${config.half_day_calculation_method || 'fifty_percent'}, ${user.id}
      )
      RETURNING *
    `;

    return NextResponse.json({
      success: true,
      message: 'Working days configuration created successfully',
      data: result[0]
    });

  } catch (error) {
    console.error('Error creating working days configuration:', error);
    
    if (error.message?.includes('duplicate key')) {
      return NextResponse.json(
        { 
          success: false, 
          error: 'Configuration already exists for this fiscal year and month' 
        },
        { status: 409 }
      );
    }

    return NextResponse.json(
      { 
        success: false, 
        error: 'Failed to create working days configuration',
        details: error.message 
      },
      { status: 500 }
    );
  }
}

// PUT - Update working days configuration
export async function PUT(request: NextRequest) {
  try {
    const sessionToken = request.cookies.get("session-token")?.value;

    if (!sessionToken) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const user = await AuthService.verifySession(sessionToken);

    if (!user || !["admin", "hr_manager"].includes(user.role)) {
      return NextResponse.json({ error: "Forbidden" }, { status: 403 });
    }

    const body = await request.json();
    const { id, ...config }: WorkingDaysConfig & { id: string } = body;

    if (!id) {
      return NextResponse.json(
        { success: false, error: 'Configuration ID is required' },
        { status: 400 }
      );
    }

    // Validation
    if (config.working_days > config.total_days_in_month) {
      return NextResponse.json(
        { success: false, error: 'Working days cannot exceed total days in month' },
        { status: 400 }
      );
    }

    // Update configuration
    const result = await serverDb.sql`
      UPDATE working_days_configuration SET
        total_days_in_month = ${config.total_days_in_month},
        working_days = ${config.working_days},
        public_holidays = ${config.public_holidays || 0},
        weekend_days = ${config.weekend_days || 0},
        late_penalty_type = ${config.late_penalty_type || 'half_day'},
        late_penalty_amount = ${config.late_penalty_amount || 0},
        half_day_calculation_method = ${config.half_day_calculation_method || 'fifty_percent'},
        updated_by = ${user.id},
        updated_at = NOW()
      WHERE id = ${id}
      RETURNING *
    `;

    if (result.length === 0) {
      return NextResponse.json(
        { success: false, error: 'Configuration not found' },
        { status: 404 }
      );
    }

    return NextResponse.json({
      success: true,
      message: 'Working days configuration updated successfully',
      data: result[0]
    });

  } catch (error) {
    console.error('Error updating working days configuration:', error);
    return NextResponse.json(
      { 
        success: false, 
        error: 'Failed to update working days configuration',
        details: error.message 
      },
      { status: 500 }
    );
  }
}

// DELETE - Delete working days configuration
export async function DELETE(request: NextRequest) {
  try {
    const sessionToken = request.cookies.get("session-token")?.value;

    if (!sessionToken) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const user = await AuthService.verifySession(sessionToken);

    if (!user || !["admin"].includes(user.role)) {
      return NextResponse.json({ error: "Forbidden - Admin only" }, { status: 403 });
    }

    const { searchParams } = new URL(request.url);
    const id = searchParams.get("id");

    if (!id) {
      return NextResponse.json(
        { success: false, error: 'Configuration ID is required' },
        { status: 400 }
      );
    }

    const result = await serverDb.sql`
      DELETE FROM working_days_configuration
      WHERE id = ${id}
      RETURNING *
    `;

    if (result.length === 0) {
      return NextResponse.json(
        { success: false, error: 'Configuration not found' },
        { status: 404 }
      );
    }

    return NextResponse.json({
      success: true,
      message: 'Working days configuration deleted successfully'
    });

  } catch (error) {
    console.error('Error deleting working days configuration:', error);
    return NextResponse.json(
      { 
        success: false, 
        error: 'Failed to delete working days configuration',
        details: error.message 
      },
      { status: 500 }
    );
  }
}
