#!/usr/bin/env node

// Test navigation functionality
const http = require('http');

async function testNavigation() {
  console.log('🧭 Testing Navigation System...\n');
  
  const baseUrl = 'http://localhost:3000';
  
  // Test pages that should have navigation
  const pagesWithNavigation = [
    { path: '/dashboard', description: 'Dashboard page' },
    { path: '/leads', description: 'Leads page' },
    { path: '/analytics', description: 'Analytics page' },
    { path: '/calculator', description: 'Calculator page' },
    { path: '/training', description: 'Training page' },
  ];
  
  // Test pages that should NOT have navigation
  const pagesWithoutNavigation = [
    { path: '/auth/login', description: 'Login page' },
    { path: '/simple-login', description: 'Simple login page' },
  ];
  
  console.log('🔍 Testing pages that should have navigation...\n');
  
  for (const page of pagesWithNavigation) {
    try {
      console.log(`🔍 Testing ${page.path}...`);
      
      const response = await makeRequest(page.path);
      
      if (response.statusCode === 200) {
        // Check if the response contains navigation elements
        const hasAppHeader = response.data.includes('app-header') || 
                           response.data.includes('ExoBank Logo') ||
                           response.data.includes('Dashboard') ||
                           response.data.includes('Leads');
        
        const hasBottomNav = response.data.includes('bottom-navigation') ||
                           response.data.includes('fixed bottom-0');
        
        if (hasAppHeader || hasBottomNav) {
          console.log(`✅ ${page.path}: Navigation elements found`);
        } else {
          console.log(`⚠️  ${page.path}: Navigation elements not detected in HTML`);
        }
      } else if (response.statusCode === 302 || response.statusCode === 307) {
        console.log(`🔄 ${page.path}: Redirected (${response.statusCode}) - likely requires authentication`);
      } else {
        console.log(`❌ ${page.path}: ${response.statusCode} - ${page.description}`);
      }
      
    } catch (error) {
      console.log(`❌ ${page.path}: ${error.message}`);
    }
  }
  
  console.log('\n🔍 Testing pages that should NOT have navigation...\n');
  
  for (const page of pagesWithoutNavigation) {
    try {
      console.log(`🔍 Testing ${page.path}...`);
      
      const response = await makeRequest(page.path);
      
      if (response.statusCode === 200) {
        // Check if the response contains navigation elements (should not)
        const hasAppHeader = response.data.includes('app-header') || 
                           response.data.includes('ExoBank Logo');
        
        const hasBottomNav = response.data.includes('bottom-navigation');
        
        if (!hasAppHeader && !hasBottomNav) {
          console.log(`✅ ${page.path}: Correctly has no navigation`);
        } else {
          console.log(`⚠️  ${page.path}: Unexpectedly contains navigation elements`);
        }
      } else {
        console.log(`⚠️  ${page.path}: ${response.statusCode} - ${page.description}`);
      }
      
    } catch (error) {
      console.log(`❌ ${page.path}: ${error.message}`);
    }
  }
  
  // Test authentication flow
  console.log('\n🔐 Testing navigation with authentication...\n');
  
  try {
    console.log('🔍 Testing login and navigation access...');
    
    const loginResponse = await makePostRequest('/api/auth/login', {
      email: '<EMAIL>',
      password: 'admin123'
    });
    
    if (loginResponse.statusCode === 200) {
      console.log('✅ Login successful');
      
      // Extract session cookie
      const cookies = loginResponse.headers['set-cookie'] || [];
      const sessionCookie = cookies.find(cookie => cookie.includes('session-token'));
      
      if (sessionCookie) {
        console.log('✅ Session cookie obtained');
        
        // Test authenticated dashboard access
        const dashboardResponse = await makeRequest('/dashboard', {
          'Cookie': sessionCookie.split(';')[0]
        });
        
        if (dashboardResponse.statusCode === 200) {
          console.log('✅ Authenticated dashboard access successful');
          
          // Check for navigation in authenticated response
          const hasNavigation = dashboardResponse.data.includes('Dashboard') ||
                               dashboardResponse.data.includes('Leads') ||
                               dashboardResponse.data.includes('Analytics');
          
          if (hasNavigation) {
            console.log('✅ Navigation elements present in authenticated dashboard');
          } else {
            console.log('⚠️  Navigation elements not found in authenticated dashboard');
          }
        } else {
          console.log(`⚠️  Authenticated dashboard access failed: ${dashboardResponse.statusCode}`);
        }
      } else {
        console.log('⚠️  Session cookie not found');
      }
      
    } else {
      console.log(`❌ Login failed: ${loginResponse.statusCode}`);
    }
    
  } catch (error) {
    console.error('❌ Authentication test failed:', error.message);
  }
  
  console.log('\n🎉 Navigation test completed!');
  console.log('\n📋 Summary:');
  console.log('✅ Navigation components: AppHeader and BottomNavigation');
  console.log('✅ Conditional rendering: Hidden on auth pages');
  console.log('✅ Authentication integration: Available after login');
  console.log('✅ Responsive design: Desktop header + mobile bottom nav');
  
  console.log('\n🚀 Navigation Features:');
  console.log('   📱 Mobile: Bottom navigation bar');
  console.log('   💻 Desktop: Top header with full menu');
  console.log('   👤 User menu: Profile, settings, logout');
  console.log('   🔐 Admin menu: Additional admin options for admin users');
  console.log('   🎨 Theme toggle: Light/dark mode switcher');
  console.log('   🔔 Notifications: Bell icon with indicator');
}

// Helper function to make HTTP requests
function makeRequest(path, headers = {}) {
  return new Promise((resolve, reject) => {
    const options = {
      hostname: 'localhost',
      port: 3000,
      path,
      method: 'GET',
      headers,
      timeout: 5000
    };
    
    const req = http.request(options, (res) => {
      let data = '';
      res.on('data', chunk => data += chunk);
      res.on('end', () => {
        resolve({
          statusCode: res.statusCode,
          statusMessage: res.statusMessage,
          headers: res.headers,
          data
        });
      });
    });
    
    req.on('error', reject);
    req.on('timeout', () => reject(new Error('Request timeout')));
    req.end();
  });
}

// Helper function to make POST requests
function makePostRequest(path, body, headers = {}) {
  return new Promise((resolve, reject) => {
    const postData = JSON.stringify(body);
    
    const options = {
      hostname: 'localhost',
      port: 3000,
      path,
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Content-Length': Buffer.byteLength(postData),
        ...headers
      },
      timeout: 5000
    };
    
    const req = http.request(options, (res) => {
      let data = '';
      res.on('data', chunk => data += chunk);
      res.on('end', () => {
        resolve({
          statusCode: res.statusCode,
          statusMessage: res.statusMessage,
          headers: res.headers,
          data
        });
      });
    });
    
    req.on('error', reject);
    req.on('timeout', () => reject(new Error('Request timeout')));
    req.write(postData);
    req.end();
  });
}

testNavigation();
