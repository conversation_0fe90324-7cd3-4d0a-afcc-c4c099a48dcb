const { neon } = require('@neondatabase/serverless');

// Load environment variables
require('dotenv').config({ path: '.env.local' });

const sql = neon(process.env.DATABASE_URL);

async function checkTableStructure() {
  try {
    console.log('📋 Checking task_attachments table structure...\n');

    const columns = await sql`
      SELECT column_name, data_type, is_nullable, column_default
      FROM information_schema.columns 
      WHERE table_name = 'task_attachments' 
      ORDER BY ordinal_position
    `;

    if (columns.length === 0) {
      console.log('❌ task_attachments table not found');
      return;
    }

    console.log('✅ task_attachments table structure:');
    columns.forEach(col => {
      console.log(`   - ${col.column_name}: ${col.data_type} ${col.is_nullable === 'NO' ? '(NOT NULL)' : '(NULLABLE)'} ${col.column_default ? `DEFAULT ${col.column_default}` : ''}`);
    });

    // Also check if the table has any data
    const count = await sql`SELECT COUNT(*) as count FROM task_attachments`;
    console.log(`\n📊 Current records: ${count[0].count}`);

  } catch (error) {
    console.error('❌ Error:', error.message);
  }
}

checkTableStructure();
