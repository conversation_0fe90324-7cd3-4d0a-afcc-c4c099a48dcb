// NPR Currency Input Component
// Phase 3: Nepal Localization Implementation - Currency Formatting

"use client"

import React, { useState, useEffect, useRef } from 'react'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover'
import { Calculator, Info, DollarSign } from 'lucide-react'
import { nprFormatter, CurrencyFormatOptions } from '@/lib/currency-formatter'
import { CurrencyBreakdownDisplay } from './currency-display'
import { cn } from '@/lib/utils'

interface CurrencyInputProps {
  value?: number
  onChange?: (value: number | undefined) => void
  placeholder?: string
  label?: string
  showBreakdown?: boolean
  allowNegative?: boolean
  maxValue?: number
  minValue?: number
  formatOptions?: CurrencyFormatOptions
  required?: boolean
  disabled?: boolean
  className?: string
  error?: string
  showCalculator?: boolean
  autoFormat?: boolean
}

export function CurrencyInput({
  value,
  onChange,
  placeholder = "Enter amount",
  label,
  showBreakdown = false,
  allowNegative = false,
  maxValue,
  minValue = 0,
  formatOptions = {},
  required = false,
  disabled = false,
  className,
  error,
  showCalculator = false,
  autoFormat = true
}: CurrencyInputProps) {
  const [inputValue, setInputValue] = useState('')
  const [isFocused, setIsFocused] = useState(false)
  const [validationError, setValidationError] = useState('')
  const [showBreakdownPopover, setShowBreakdownPopover] = useState(false)
  const inputRef = useRef<HTMLInputElement>(null)

  const defaultFormatOptions: CurrencyFormatOptions = {
    showSymbol: false,
    useIndianNumbering: true,
    decimalPlaces: 2,
    ...formatOptions
  }

  // Update input value when value prop changes
  useEffect(() => {
    if (value !== undefined) {
      if (isFocused) {
        // Show raw number when focused for easier editing
        setInputValue(value.toString())
      } else {
        // Show formatted currency when not focused
        setInputValue(autoFormat ? 
          nprFormatter.formatCurrency(value, defaultFormatOptions) : 
          value.toString()
        )
      }
    } else {
      setInputValue('')
    }
  }, [value, isFocused, autoFormat])

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const newValue = e.target.value
    setInputValue(newValue)
    setValidationError('')

    // Parse the input value
    try {
      if (!newValue.trim()) {
        onChange?.(undefined)
        return
      }

      let parsedValue: number
      
      if (nprFormatter.isValidCurrency(newValue)) {
        parsedValue = nprFormatter.parseCurrency(newValue)
      } else {
        // Try parsing as plain number
        parsedValue = parseFloat(newValue.replace(/,/g, ''))
        if (isNaN(parsedValue)) {
          throw new Error('Invalid number format')
        }
      }

      // Validate constraints
      if (!allowNegative && parsedValue < 0) {
        throw new Error('Negative values are not allowed')
      }

      if (minValue !== undefined && parsedValue < minValue) {
        throw new Error(`Value must be at least ${nprFormatter.formatCurrency(minValue)}`)
      }

      if (maxValue !== undefined && parsedValue > maxValue) {
        throw new Error(`Value must not exceed ${nprFormatter.formatCurrency(maxValue)}`)
      }

      onChange?.(parsedValue)
    } catch (error) {
      // Don't show error immediately while typing
    }
  }

  const handleInputFocus = () => {
    setIsFocused(true)
    if (value !== undefined) {
      setInputValue(value.toString())
    }
  }

  const handleInputBlur = () => {
    setIsFocused(false)
    
    if (!inputValue.trim()) {
      onChange?.(undefined)
      return
    }

    try {
      let parsedValue: number
      
      if (nprFormatter.isValidCurrency(inputValue)) {
        parsedValue = nprFormatter.parseCurrency(inputValue)
      } else {
        parsedValue = parseFloat(inputValue.replace(/,/g, ''))
        if (isNaN(parsedValue)) {
          throw new Error('Invalid number format')
        }
      }

      // Validate constraints
      if (!allowNegative && parsedValue < 0) {
        throw new Error('Negative values are not allowed')
      }

      if (minValue !== undefined && parsedValue < minValue) {
        throw new Error(`Value must be at least ${nprFormatter.formatCurrency(minValue)}`)
      }

      if (maxValue !== undefined && parsedValue > maxValue) {
        throw new Error(`Value must not exceed ${nprFormatter.formatCurrency(maxValue)}`)
      }

      onChange?.(parsedValue)
      
      // Format the input for display
      if (autoFormat) {
        setInputValue(nprFormatter.formatCurrency(parsedValue, defaultFormatOptions))
      }
    } catch (error) {
      setValidationError(error.message)
      if (value !== undefined) {
        setInputValue(autoFormat ? 
          nprFormatter.formatCurrency(value, defaultFormatOptions) : 
          value.toString()
        )
      } else {
        setInputValue('')
        onChange?.(undefined)
      }
    }
  }

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      handleInputBlur()
      inputRef.current?.blur()
    } else if (e.key === 'Escape') {
      if (value !== undefined) {
        setInputValue(autoFormat ? 
          nprFormatter.formatCurrency(value, defaultFormatOptions) : 
          value.toString()
        )
      } else {
        setInputValue('')
      }
      setValidationError('')
      inputRef.current?.blur()
    }
  }

  const openCalculator = () => {
    // This would open a calculator popup
    // For now, just focus the input
    inputRef.current?.focus()
  }

  const formatPresets = [
    { label: '1 Thousand', value: 1000 },
    { label: '10 Thousand', value: 10000 },
    { label: '1 Lakh', value: 100000 },
    { label: '10 Lakh', value: 1000000 },
    { label: '1 Crore', value: 10000000 }
  ]

  return (
    <div className={cn("space-y-2", className)}>
      {label && (
        <Label htmlFor="currency-input" className="text-sm font-medium">
          {label}
          {required && <span className="text-red-500 ml-1">*</span>}
        </Label>
      )}
      
      <div className="relative">
        <div className="relative">
          <DollarSign className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
          <Input
            ref={inputRef}
            id="currency-input"
            value={inputValue}
            onChange={handleInputChange}
            onFocus={handleInputFocus}
            onBlur={handleInputBlur}
            onKeyDown={handleKeyDown}
            placeholder={placeholder}
            className={cn(
              "pl-10 pr-20 font-mono",
              (error || validationError) && "border-red-500"
            )}
            disabled={disabled}
          />
          
          <div className="absolute right-2 top-1/2 transform -translate-y-1/2 flex items-center gap-1">
            {showCalculator && (
              <Button
                type="button"
                variant="ghost"
                size="sm"
                onClick={openCalculator}
                className="h-6 w-6 p-0"
              >
                <Calculator className="h-3 w-3" />
              </Button>
            )}
            
            {showBreakdown && value !== undefined && value > 0 && (
              <Popover open={showBreakdownPopover} onOpenChange={setShowBreakdownPopover}>
                <PopoverTrigger asChild>
                  <Button
                    type="button"
                    variant="ghost"
                    size="sm"
                    className="h-6 w-6 p-0"
                  >
                    <Info className="h-3 w-3" />
                  </Button>
                </PopoverTrigger>
                <PopoverContent className="w-80">
                  <CurrencyBreakdownDisplay amount={value} showInWords={true} />
                </PopoverContent>
              </Popover>
            )}
          </div>
        </div>

        {/* Quick amount presets */}
        {!disabled && (
          <div className="flex flex-wrap gap-1 mt-2">
            {formatPresets.map((preset) => (
              <Button
                key={preset.value}
                type="button"
                variant="outline"
                size="sm"
                onClick={() => {
                  onChange?.(preset.value)
                  setInputValue(autoFormat ? 
                    nprFormatter.formatCurrency(preset.value, defaultFormatOptions) : 
                    preset.value.toString()
                  )
                }}
                className="text-xs h-6"
              >
                {preset.label}
              </Button>
            ))}
          </div>
        )}
      </div>

      {/* Value display */}
      {value !== undefined && value > 0 && (
        <div className="flex items-center gap-2 text-sm">
          <Badge variant="outline" className="font-mono">
            {nprFormatter.formatCurrency(value, { 
              showSymbol: true, 
              useIndianNumbering: true, 
              decimalPlaces: 2 
            })}
          </Badge>
          
          {value >= 100000 && (
            <Badge variant="secondary" className="text-xs">
              {nprFormatter.formatCompact(value)}
            </Badge>
          )}
        </div>
      )}

      {/* Validation constraints display */}
      {(minValue !== undefined || maxValue !== undefined) && (
        <div className="text-xs text-muted-foreground">
          {minValue !== undefined && maxValue !== undefined ? (
            <>Range: {nprFormatter.formatCurrency(minValue)} - {nprFormatter.formatCurrency(maxValue)}</>
          ) : minValue !== undefined ? (
            <>Minimum: {nprFormatter.formatCurrency(minValue)}</>
          ) : (
            <>Maximum: {nprFormatter.formatCurrency(maxValue!)}</>
          )}
        </div>
      )}

      {(error || validationError) && (
        <p className="text-sm text-red-500">{error || validationError}</p>
      )}
    </div>
  )
}

export default CurrencyInput
