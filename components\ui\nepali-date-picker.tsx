// Nepali Date Picker Component
// Phase 3: Nepal Localization Implementation - Calendar Integration

"use client"

import React, { useState, useRef, useEffect } from 'react'
import { Calendar, CalendarDays, X } from 'lucide-react'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover'
import { Badge } from '@/components/ui/badge'
import { NepaliCalendarComponent } from './nepali-calendar'
import { NepaliCalendar, BSDate } from '@/lib/nepali-calendar'
import { nepalConfig } from '@/lib/nepal-config'
import { cn } from '@/lib/utils'

interface NepaliDatePickerProps {
  value?: Date
  onChange?: (date: Date | undefined) => void
  placeholder?: string
  label?: string
  showBothCalendars?: boolean
  allowManualInput?: boolean
  minDate?: Date
  maxDate?: Date
  highlightHolidays?: boolean
  highlightWorkingDays?: boolean
  required?: boolean
  disabled?: boolean
  className?: string
  error?: string
}

export function NepaliDatePicker({
  value,
  onChange,
  placeholder = "Select date",
  label,
  showBothCalendars = true,
  allowManualInput = true,
  minDate,
  maxDate,
  highlightHolidays = true,
  highlightWorkingDays = true,
  required = false,
  disabled = false,
  className,
  error
}: NepaliDatePickerProps) {
  const [isOpen, setIsOpen] = useState(false)
  const [inputValue, setInputValue] = useState('')
  const [inputMode, setInputMode] = useState<'ad' | 'bs'>('ad')
  const [validationError, setValidationError] = useState<string>('')
  const inputRef = useRef<HTMLInputElement>(null)

  // Update input value when value changes
  useEffect(() => {
    if (value) {
      updateInputValue(value)
    } else {
      setInputValue('')
    }
  }, [value, inputMode])

  const updateInputValue = (date: Date) => {
    if (inputMode === 'ad') {
      setInputValue(date.toISOString().split('T')[0])
    } else {
      const bsDate = NepaliCalendar.adToBS(date)
      setInputValue(NepaliCalendar.formatBSDate(bsDate))
    }
  }

  const handleDateSelect = (date: Date, bsDate: BSDate) => {
    onChange?.(date)
    updateInputValue(date)
    setIsOpen(false)
    setValidationError('')
  }

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const newValue = e.target.value
    setInputValue(newValue)
    setValidationError('')

    if (!allowManualInput) return

    // Try to parse the input
    try {
      let parsedDate: Date

      if (inputMode === 'ad') {
        // Parse AD date (YYYY-MM-DD format)
        parsedDate = new Date(newValue)
        if (isNaN(parsedDate.getTime())) {
          throw new Error('Invalid AD date format')
        }
      } else {
        // Parse BS date (YYYY-MM-DD format)
        const bsDate = NepaliCalendar.parseBSDate(newValue)
        if (!NepaliCalendar.isValidBSDate(bsDate)) {
          throw new Error('Invalid BS date')
        }
        parsedDate = NepaliCalendar.bsToAD(bsDate)
      }

      // Validate date range
      if (minDate && parsedDate < minDate) {
        throw new Error('Date is before minimum allowed date')
      }
      if (maxDate && parsedDate > maxDate) {
        throw new Error('Date is after maximum allowed date')
      }

      onChange?.(parsedDate)
    } catch (error) {
      // Don't show error immediately, wait for blur or enter
    }
  }

  const handleInputBlur = () => {
    if (!inputValue) {
      onChange?.(undefined)
      return
    }

    try {
      let parsedDate: Date

      if (inputMode === 'ad') {
        parsedDate = new Date(inputValue)
        if (isNaN(parsedDate.getTime())) {
          throw new Error('Invalid date format. Use YYYY-MM-DD')
        }
      } else {
        const bsDate = NepaliCalendar.parseBSDate(inputValue)
        if (!NepaliCalendar.isValidBSDate(bsDate)) {
          throw new Error('Invalid BS date format. Use YYYY-MM-DD')
        }
        parsedDate = NepaliCalendar.bsToAD(bsDate)
      }

      // Validate date range
      if (minDate && parsedDate < minDate) {
        throw new Error('Date is before minimum allowed date')
      }
      if (maxDate && parsedDate > maxDate) {
        throw new Error('Date is after maximum allowed date')
      }

      onChange?.(parsedDate)
      updateInputValue(parsedDate)
    } catch (error) {
      setValidationError(error.message)
      if (value) {
        updateInputValue(value) // Reset to previous valid value
      } else {
        setInputValue('')
        onChange?.(undefined)
      }
    }
  }

  const handleInputKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      handleInputBlur()
    } else if (e.key === 'Escape') {
      if (value) {
        updateInputValue(value)
      } else {
        setInputValue('')
      }
      setValidationError('')
      inputRef.current?.blur()
    }
  }

  const clearDate = () => {
    onChange?.(undefined)
    setInputValue('')
    setValidationError('')
  }

  const toggleInputMode = () => {
    setInputMode(prev => prev === 'ad' ? 'bs' : 'ad')
    if (value) {
      updateInputValue(value)
    }
  }

  const formatDisplayValue = () => {
    if (!value) return placeholder

    if (showBothCalendars) {
      const bsDate = NepaliCalendar.adToBS(value)
      const adFormatted = value.toLocaleDateString('en-US', { 
        year: 'numeric', 
        month: 'short', 
        day: 'numeric' 
      })
      const bsFormatted = `${NepaliCalendar.getBSMonthName(bsDate.month)} ${bsDate.day}, ${bsDate.year}`
      return `${adFormatted} (${bsFormatted})`
    }

    return value.toLocaleDateString('en-US', { 
      year: 'numeric', 
      month: 'long', 
      day: 'numeric' 
    })
  }

  const getHolidayInfo = () => {
    if (!value) return null
    
    const holidayInfo = nepalConfig.getHolidaysInRange(
      value.toISOString().split('T')[0],
      value.toISOString().split('T')[0]
    )[0]
    
    if (holidayInfo) {
      return { type: 'holiday', name: holidayInfo.name }
    }
    
    if (!nepalConfig.isWorkingDay(value)) {
      return { type: 'weekly-off', name: 'Weekly Off' }
    }
    
    return { type: 'working-day', name: 'Working Day' }
  }

  const holidayInfo = getHolidayInfo()

  return (
    <div className={cn("space-y-2", className)}>
      {label && (
        <Label htmlFor="date-picker" className="text-sm font-medium">
          {label}
          {required && <span className="text-red-500 ml-1">*</span>}
        </Label>
      )}
      
      <div className="relative">
        <Popover open={isOpen} onOpenChange={setIsOpen}>
          <PopoverTrigger asChild>
            <Button
              id="date-picker"
              variant="outline"
              className={cn(
                "w-full justify-start text-left font-normal",
                !value && "text-muted-foreground",
                error || validationError ? "border-red-500" : ""
              )}
              disabled={disabled}
            >
              <Calendar className="mr-2 h-4 w-4" />
              {formatDisplayValue()}
            </Button>
          </PopoverTrigger>
          
          <PopoverContent className="w-auto p-0" align="start">
            <div className="p-4 space-y-4">
              {/* Manual input section */}
              {allowManualInput && (
                <div className="space-y-2">
                  <div className="flex items-center justify-between">
                    <Label className="text-sm font-medium">Manual Input</Label>
                    <div className="flex gap-1">
                      <Button
                        variant={inputMode === 'ad' ? 'default' : 'outline'}
                        size="sm"
                        onClick={toggleInputMode}
                      >
                        AD
                      </Button>
                      <Button
                        variant={inputMode === 'bs' ? 'default' : 'outline'}
                        size="sm"
                        onClick={toggleInputMode}
                      >
                        BS
                      </Button>
                    </div>
                  </div>
                  
                  <div className="flex gap-2">
                    <Input
                      ref={inputRef}
                      value={inputValue}
                      onChange={handleInputChange}
                      onBlur={handleInputBlur}
                      onKeyDown={handleInputKeyDown}
                      placeholder={inputMode === 'ad' ? 'YYYY-MM-DD' : 'YYYY-MM-DD (BS)'}
                      className="flex-1"
                    />
                    {value && (
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={clearDate}
                      >
                        <X className="h-4 w-4" />
                      </Button>
                    )}
                  </div>
                  
                  {validationError && (
                    <p className="text-sm text-red-500">{validationError}</p>
                  )}
                </div>
              )}
              
              {/* Calendar component */}
              <NepaliCalendarComponent
                selectedDate={value}
                onDateSelect={handleDateSelect}
                showDualCalendar={showBothCalendars}
                highlightHolidays={highlightHolidays}
                highlightWorkingDays={highlightWorkingDays}
                minDate={minDate}
                maxDate={maxDate}
              />
            </div>
          </PopoverContent>
        </Popover>
        
        {/* Holiday/Working day indicator */}
        {value && holidayInfo && (
          <div className="mt-2">
            <Badge 
              variant={
                holidayInfo.type === 'holiday' ? 'destructive' :
                holidayInfo.type === 'weekly-off' ? 'secondary' : 'default'
              }
              className="text-xs"
            >
              <CalendarDays className="mr-1 h-3 w-3" />
              {holidayInfo.name}
            </Badge>
          </div>
        )}
      </div>
      
      {(error || validationError) && (
        <p className="text-sm text-red-500">{error || validationError}</p>
      )}
    </div>
  )
}

export default NepaliDatePicker
