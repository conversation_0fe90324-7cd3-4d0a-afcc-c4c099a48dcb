// Test the form submission logic without the full Next.js server
require('dotenv').config({ path: '.env.local' })

// Mock the NepaliCalendar library
const NepaliCalendar = {
  adToBS: (date) => {
    // Simple mock conversion - in real app this would be proper conversion
    const year = date.getFullYear() + 57 // Rough BS year calculation
    const month = date.getMonth() + 1
    const day = date.getDate()
    return { year, month, day }
  },
  formatBSDate: (bsDate) => {
    return `${bsDate.year}-${bsDate.month.toString().padStart(2, '0')}-${bsDate.day.toString().padStart(2, '0')}`
  }
}

// Mock form data that would come from the AddCustomerModal
const mockFormData = {
  name: 'Test Customer',
  phone: '9800123456',
  email: '<EMAIL>',
  address: 'Test Address',
  loan_amount: 50000,
  amount_paid: 0,
  due_date: '2024-12-31'
}

console.log('🧪 Testing form submission logic...')
console.log('📋 Mock form data:', mockFormData)

// Test form validation
function validateFormData(data) {
  const errors = []
  
  if (!data.name || data.name.trim().length === 0) {
    errors.push('Name is required')
  }
  
  if (!data.phone || data.phone.trim().length === 0) {
    errors.push('Phone is required')
  }
  
  if (!data.loan_amount || data.loan_amount <= 0) {
    errors.push('Loan amount must be positive')
  }
  
  if (!data.due_date) {
    errors.push('Due date is required')
  }
  
  // Test date format
  if (data.due_date && !/^\d{4}-\d{2}-\d{2}$/.test(data.due_date)) {
    errors.push('Due date must be in YYYY-MM-DD format')
  }
  
  return errors
}

// Test the validation
const validationErrors = validateFormData(mockFormData)
if (validationErrors.length > 0) {
  console.error('❌ Validation failed:', validationErrors)
  process.exit(1)
}

console.log('✅ Form validation passed')

// Test date conversion logic
try {
  console.log('📅 Testing date conversion...')
  
  if (mockFormData.due_date) {
    const date = new Date(mockFormData.due_date)
    console.log('📅 Parsed date:', date)
    
    const bsDate = NepaliCalendar.adToBS(date)
    console.log('📅 BS date object:', bsDate)
    
    const due_date_bs = NepaliCalendar.formatBSDate(bsDate)
    console.log('📅 Formatted BS date:', due_date_bs)
  }
  
  console.log('✅ Date conversion successful')
} catch (error) {
  console.error('❌ Date conversion failed:', error)
  process.exit(1)
}

// Test API request payload construction
function buildCustomerPayload(data) {
  return {
    name: data.name,
    phone: data.phone,
    email: data.email || null,
    address: data.address || null,
  }
}

function buildLoanPayload(data, customerId) {
  let due_date_bs = ""
  if (data.due_date) {
    try {
      const date = new Date(data.due_date)
      const bsDate = NepaliCalendar.adToBS(date)
      due_date_bs = NepaliCalendar.formatBSDate(bsDate)
    } catch (error) {
      console.warn("Failed to convert date to BS:", error)
    }
  }

  return {
    customer_id: customerId,
    loan_amount: data.loan_amount,
    amount_paid: data.amount_paid,
    due_date: data.due_date,
    due_date_bs: due_date_bs,
  }
}

console.log('🔧 Testing payload construction...')

const customerPayload = buildCustomerPayload(mockFormData)
console.log('📞 Customer payload:', customerPayload)

const mockCustomerId = 'test-customer-id-123'
const loanPayload = buildLoanPayload(mockFormData, mockCustomerId)
console.log('💰 Loan payload:', loanPayload)

console.log('✅ Payload construction successful')

// Test JSON serialization
try {
  const customerJson = JSON.stringify(customerPayload)
  const loanJson = JSON.stringify(loanPayload)
  
  console.log('📦 Customer JSON:', customerJson)
  console.log('📦 Loan JSON:', loanJson)
  
  console.log('✅ JSON serialization successful')
} catch (error) {
  console.error('❌ JSON serialization failed:', error)
  process.exit(1)
}

console.log('\n🎉 All form logic tests passed!')
console.log('📝 The form submission logic appears to be working correctly.')
console.log('🔍 The issue is likely in the API endpoints or server configuration.')
