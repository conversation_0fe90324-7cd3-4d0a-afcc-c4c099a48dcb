"use client"

import { useState } from "react"
import { use<PERSON><PERSON><PERSON> } from "next/navigation"
import { AppHead<PERSON> } from "@/components/app-header"
import { AdminSidebar } from "@/components/admin-sidebar"
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { Switch } from "@/components/ui/switch"
import { Label } from "@/components/ui/label"
import { <PERSON><PERSON>, <PERSON><PERSON>Content, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { useToast } from "@/components/ui/use-toast"
import { ArrowLeft, Save, Info } from "lucide-react"

export default function UserPermissionsPage({ params }: { params: { id: string } }) {
  const router = useRouter()
  const { toast } = useToast()
  const [isLoading, setIsLoading] = useState(false)

  // Mock user data - in a real app, you would fetch this based on the ID
  const user = {
    id: params.id,
    name: "<PERSON>",
    email: "<EMAIL>",
    role: "Manager",
    department: "Marketing",
  }

  // Permission states
  const [permissions, setPermissions] = useState({
    // Dashboard permissions
    viewDashboard: true,
    exportReports: true,

    // User management
    viewUsers: true,
    createUsers: false,
    editUsers: false,
    deleteUsers: false,

    // Task management
    viewTasks: true,
    createTasks: true,
    editTasks: true,
    deleteTasks: true,
    assignTasks: true,

    // Lead management
    viewLeads: true,
    createLeads: true,
    editLeads: true,
    deleteLeads: false,

    // Finance
    viewFinance: true,
    managePayroll: false,
    approveExpenses: false,

    // Attendance
    viewAttendance: true,
    manageAttendance: true,
    approveLeave: true,

    // System settings
    viewSettings: false,
    editSettings: false,
  })

  const handleTogglePermission = (permission: string) => {
    setPermissions((prev) => ({
      ...prev,
      [permission]: !prev[permission as keyof typeof permissions],
    }))
  }

  const handleSavePermissions = async () => {
    setIsLoading(true)

    try {
      // Simulate API call
      await new Promise((resolve) => setTimeout(resolve, 1000))

      toast({
        title: "Permissions updated successfully",
        description: `${user.name}'s permissions have been updated.`,
      })

      router.push("/admin/users")
    } catch (error) {
      toast({
        variant: "destructive",
        title: "Error updating permissions",
        description: "There was a problem updating the user's permissions. Please try again.",
      })
    } finally {
      setIsLoading(false)
    }
  }

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900 flex flex-col">
      <AppHeader />

      <div className="flex flex-1">
        <AdminSidebar />

        <div className="flex-1 p-4 md:p-6">
          <div className="flex items-center justify-between mb-6">
            <div className="flex items-center">
              <Button variant="ghost" size="icon" onClick={() => router.push("/admin/users")} className="mr-2">
                <ArrowLeft className="h-5 w-5" />
              </Button>
              <div>
                <h1 className="text-xl font-semibold text-teal-800 dark:text-teal-300">User Permissions</h1>
                <p className="text-sm text-gray-500 dark:text-gray-400">Configure access permissions for {user.name}</p>
              </div>
            </div>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
            <div className="md:col-span-1">
              <Card className="dark:bg-gray-800 border-gray-200 dark:border-gray-700">
                <CardHeader>
                  <CardTitle className="text-teal-800 dark:text-teal-300">User Information</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    <div>
                      <p className="text-sm font-medium text-gray-500 dark:text-gray-400">Name</p>
                      <p className="text-base dark:text-gray-200">{user.name}</p>
                    </div>
                    <div>
                      <p className="text-sm font-medium text-gray-500 dark:text-gray-400">Email</p>
                      <p className="text-base dark:text-gray-200">{user.email}</p>
                    </div>
                    <div>
                      <p className="text-sm font-medium text-gray-500 dark:text-gray-400">Role</p>
                      <p className="text-base dark:text-gray-200">{user.role}</p>
                    </div>
                    <div>
                      <p className="text-sm font-medium text-gray-500 dark:text-gray-400">Department</p>
                      <p className="text-base dark:text-gray-200">{user.department}</p>
                    </div>
                  </div>
                </CardContent>
              </Card>

              <div className="mt-4 p-4 bg-blue-50 dark:bg-blue-900/20 rounded-lg border border-blue-100 dark:border-blue-800">
                <div className="flex">
                  <Info className="h-5 w-5 text-blue-600 dark:text-blue-400 mr-2 flex-shrink-0" />
                  <div className="text-sm text-blue-600 dark:text-blue-400">
                    <p className="font-medium">Permission Tips</p>
                    <p className="mt-1">
                      Permissions determine what actions a user can perform in the system. Be careful when granting
                      administrative permissions.
                    </p>
                  </div>
                </div>
              </div>
            </div>

            <div className="md:col-span-3">
              <Card className="dark:bg-gray-800 border-gray-200 dark:border-gray-700">
                <CardHeader>
                  <CardTitle className="text-teal-800 dark:text-teal-300">Permission Settings</CardTitle>
                  <CardDescription>Configure what this user can view and modify in the system</CardDescription>
                </CardHeader>
                <CardContent>
                  <Tabs defaultValue="dashboard">
                    <TabsList className="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-7 mb-4">
                      <TabsTrigger value="dashboard">Dashboard</TabsTrigger>
                      <TabsTrigger value="users">Users</TabsTrigger>
                      <TabsTrigger value="tasks">Tasks</TabsTrigger>
                      <TabsTrigger value="leads">Leads</TabsTrigger>
                      <TabsTrigger value="finance">Finance</TabsTrigger>
                      <TabsTrigger value="attendance">Attendance</TabsTrigger>
                      <TabsTrigger value="settings">Settings</TabsTrigger>
                    </TabsList>

                    <TabsContent value="dashboard" className="space-y-4">
                      <div className="space-y-4">
                        <div className="flex items-center justify-between py-2">
                          <Label htmlFor="viewDashboard" className="flex-1">
                            <span className="font-medium dark:text-gray-200">View Dashboard</span>
                            <p className="text-sm text-gray-500 dark:text-gray-400">
                              Can view the main dashboard and analytics
                            </p>
                          </Label>
                          <Switch
                            id="viewDashboard"
                            checked={permissions.viewDashboard}
                            onCheckedChange={() => handleTogglePermission("viewDashboard")}
                          />
                        </div>

                        <div className="flex items-center justify-between py-2">
                          <Label htmlFor="exportReports" className="flex-1">
                            <span className="font-medium dark:text-gray-200">Export Reports</span>
                            <p className="text-sm text-gray-500 dark:text-gray-400">
                              Can export dashboard data and reports
                            </p>
                          </Label>
                          <Switch
                            id="exportReports"
                            checked={permissions.exportReports}
                            onCheckedChange={() => handleTogglePermission("exportReports")}
                          />
                        </div>
                      </div>
                    </TabsContent>

                    <TabsContent value="users" className="space-y-4">
                      <div className="space-y-4">
                        <div className="flex items-center justify-between py-2">
                          <Label htmlFor="viewUsers" className="flex-1">
                            <span className="font-medium dark:text-gray-200">View Users</span>
                            <p className="text-sm text-gray-500 dark:text-gray-400">Can view user list and profiles</p>
                          </Label>
                          <Switch
                            id="viewUsers"
                            checked={permissions.viewUsers}
                            onCheckedChange={() => handleTogglePermission("viewUsers")}
                          />
                        </div>

                        <div className="flex items-center justify-between py-2">
                          <Label htmlFor="createUsers" className="flex-1">
                            <span className="font-medium dark:text-gray-200">Create Users</span>
                            <p className="text-sm text-gray-500 dark:text-gray-400">Can add new users to the system</p>
                          </Label>
                          <Switch
                            id="createUsers"
                            checked={permissions.createUsers}
                            onCheckedChange={() => handleTogglePermission("createUsers")}
                          />
                        </div>

                        <div className="flex items-center justify-between py-2">
                          <Label htmlFor="editUsers" className="flex-1">
                            <span className="font-medium dark:text-gray-200">Edit Users</span>
                            <p className="text-sm text-gray-500 dark:text-gray-400">Can modify user information</p>
                          </Label>
                          <Switch
                            id="editUsers"
                            checked={permissions.editUsers}
                            onCheckedChange={() => handleTogglePermission("editUsers")}
                          />
                        </div>

                        <div className="flex items-center justify-between py-2">
                          <Label htmlFor="deleteUsers" className="flex-1">
                            <span className="font-medium dark:text-gray-200">Delete Users</span>
                            <p className="text-sm text-gray-500 dark:text-gray-400">Can remove users from the system</p>
                          </Label>
                          <Switch
                            id="deleteUsers"
                            checked={permissions.deleteUsers}
                            onCheckedChange={() => handleTogglePermission("deleteUsers")}
                          />
                        </div>
                      </div>
                    </TabsContent>

                    <TabsContent value="tasks" className="space-y-4">
                      {/* Task permissions */}
                      <div className="space-y-4">
                        <div className="flex items-center justify-between py-2">
                          <Label htmlFor="viewTasks" className="flex-1">
                            <span className="font-medium dark:text-gray-200">View Tasks</span>
                            <p className="text-sm text-gray-500 dark:text-gray-400">Can view task board and details</p>
                          </Label>
                          <Switch
                            id="viewTasks"
                            checked={permissions.viewTasks}
                            onCheckedChange={() => handleTogglePermission("viewTasks")}
                          />
                        </div>

                        <div className="flex items-center justify-between py-2">
                          <Label htmlFor="createTasks" className="flex-1">
                            <span className="font-medium dark:text-gray-200">Create Tasks</span>
                            <p className="text-sm text-gray-500 dark:text-gray-400">Can add new tasks</p>
                          </Label>
                          <Switch
                            id="createTasks"
                            checked={permissions.createTasks}
                            onCheckedChange={() => handleTogglePermission("createTasks")}
                          />
                        </div>

                        <div className="flex items-center justify-between py-2">
                          <Label htmlFor="editTasks" className="flex-1">
                            <span className="font-medium dark:text-gray-200">Edit Tasks</span>
                            <p className="text-sm text-gray-500 dark:text-gray-400">Can modify task details</p>
                          </Label>
                          <Switch
                            id="editTasks"
                            checked={permissions.editTasks}
                            onCheckedChange={() => handleTogglePermission("editTasks")}
                          />
                        </div>

                        <div className="flex items-center justify-between py-2">
                          <Label htmlFor="deleteTasks" className="flex-1">
                            <span className="font-medium dark:text-gray-200">Delete Tasks</span>
                            <p className="text-sm text-gray-500 dark:text-gray-400">Can remove tasks</p>
                          </Label>
                          <Switch
                            id="deleteTasks"
                            checked={permissions.deleteTasks}
                            onCheckedChange={() => handleTogglePermission("deleteTasks")}
                          />
                        </div>

                        <div className="flex items-center justify-between py-2">
                          <Label htmlFor="assignTasks" className="flex-1">
                            <span className="font-medium dark:text-gray-200">Assign Tasks</span>
                            <p className="text-sm text-gray-500 dark:text-gray-400">Can assign tasks to other users</p>
                          </Label>
                          <Switch
                            id="assignTasks"
                            checked={permissions.assignTasks}
                            onCheckedChange={() => handleTogglePermission("assignTasks")}
                          />
                        </div>
                      </div>
                    </TabsContent>

                    {/* Other tabs content would go here */}
                    <TabsContent value="leads" className="space-y-4">
                      {/* Lead management permissions */}
                    </TabsContent>

                    <TabsContent value="finance" className="space-y-4">
                      {/* Finance permissions */}
                    </TabsContent>

                    <TabsContent value="attendance" className="space-y-4">
                      {/* Attendance permissions */}
                    </TabsContent>

                    <TabsContent value="settings" className="space-y-4">
                      {/* Settings permissions */}
                    </TabsContent>
                  </Tabs>
                </CardContent>
                <CardFooter className="flex justify-between">
                  <Button variant="outline" onClick={() => router.push("/admin/users")}>
                    Cancel
                  </Button>
                  <Button
                    onClick={handleSavePermissions}
                    className="bg-exobank-green hover:bg-exobank-green/90"
                    disabled={isLoading}
                  >
                    {isLoading ? (
                      "Saving..."
                    ) : (
                      <>
                        <Save className="mr-2 h-4 w-4" />
                        Save Permissions
                      </>
                    )}
                  </Button>
                </CardFooter>
              </Card>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
