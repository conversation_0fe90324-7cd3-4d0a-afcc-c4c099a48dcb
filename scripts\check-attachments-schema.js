const { neon } = require('@neondatabase/serverless');

// Load environment variables
require('dotenv').config({ path: '.env.local' });

const sql = neon(process.env.DATABASE_URL);

async function checkAttachmentsSchema() {
  try {
    console.log('🔍 Checking task_attachments table schema...\n');

    // Check if table exists
    const tableExists = await sql`
      SELECT EXISTS (
        SELECT 1 FROM information_schema.tables 
        WHERE table_schema = 'public' AND table_name = 'task_attachments'
      )
    `;

    if (!tableExists[0].exists) {
      console.log('❌ task_attachments table does not exist');
      return;
    }

    console.log('✅ task_attachments table exists');

    // Get all columns in the table
    const columns = await sql`
      SELECT 
        column_name,
        data_type,
        is_nullable,
        column_default
      FROM information_schema.columns 
      WHERE table_schema = 'public' AND table_name = 'task_attachments'
      ORDER BY ordinal_position
    `;

    console.log('\n📋 Current table structure:');
    columns.forEach((col, index) => {
      console.log(`   ${index + 1}. ${col.column_name} (${col.data_type}) ${col.is_nullable === 'NO' ? 'NOT NULL' : 'NULL'}`);
      if (col.column_default) {
        console.log(`      Default: ${col.column_default}`);
      }
    });

    // Check for specific columns we need
    const hasUploadedBy = columns.some(col => col.column_name === 'uploaded_by');
    const hasUserId = columns.some(col => col.column_name === 'user_id');
    const hasFileName = columns.some(col => col.column_name === 'file_name');
    const hasOriginalFilename = columns.some(col => col.column_name === 'original_filename');
    const hasFileType = columns.some(col => col.column_name === 'file_type');
    const hasMimeType = columns.some(col => col.column_name === 'mime_type');

    console.log('\n🔍 Column analysis:');
    console.log(`   uploaded_by: ${hasUploadedBy ? '✅' : '❌'}`);
    console.log(`   user_id: ${hasUserId ? '✅' : '❌'}`);
    console.log(`   file_name: ${hasFileName ? '✅' : '❌'}`);
    console.log(`   original_filename: ${hasOriginalFilename ? '✅' : '❌'}`);
    console.log(`   file_type: ${hasFileType ? '✅' : '❌'}`);
    console.log(`   mime_type: ${hasMimeType ? '✅' : '❌'}`);

    // Get sample data
    const sampleData = await sql`
      SELECT * FROM task_attachments LIMIT 3
    `;

    console.log(`\n📊 Sample data (${sampleData.length} records):`);
    sampleData.forEach((record, index) => {
      console.log(`   ${index + 1}. Record ID: ${record.id}`);
      console.log(`      Task ID: ${record.task_id}`);
      if (record.uploaded_by) console.log(`      Uploaded by: ${record.uploaded_by}`);
      if (record.user_id) console.log(`      User ID: ${record.user_id}`);
      if (record.file_name) console.log(`      File name: ${record.file_name}`);
      if (record.original_filename) console.log(`      Original filename: ${record.original_filename}`);
      if (record.file_type) console.log(`      File type: ${record.file_type}`);
      if (record.mime_type) console.log(`      MIME type: ${record.mime_type}`);
      console.log(`      Created at: ${record.created_at}`);
    });

  } catch (error) {
    console.error('❌ Error checking attachments schema:', error);
  }
}

checkAttachmentsSchema();
