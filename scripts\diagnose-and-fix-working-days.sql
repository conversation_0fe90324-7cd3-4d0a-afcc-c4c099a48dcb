-- ============================================================================
-- COMPREHENSIVE DIAGNOSIS AND FIX FOR WORKING DAYS CONFIGURATION
-- ============================================================================
-- This script will diagnose the issue and fix the working days configuration system
-- Run this in your Neon SQL Editor to resolve the problem

-- ============================================================================
-- STEP 1: DIAGNOSTIC - Check if tables exist
-- ============================================================================

DO $$
DECLARE
    working_days_exists boolean := false;
    attendance_settings_exists boolean := false;
    working_days_count integer := 0;
    attendance_settings_count integer := 0;
BEGIN
    -- Check if working_days_configuration table exists
    SELECT EXISTS (
        SELECT FROM information_schema.tables 
        WHERE table_schema = 'public' 
        AND table_name = 'working_days_configuration'
    ) INTO working_days_exists;
    
    -- Check if attendance_calculation_settings table exists
    SELECT EXISTS (
        SELECT FROM information_schema.tables 
        WHERE table_schema = 'public' 
        AND table_name = 'attendance_calculation_settings'
    ) INTO attendance_settings_exists;
    
    RAISE NOTICE '=== DIAGNOSTIC RESULTS ===';
    RAISE NOTICE 'working_days_configuration table exists: %', working_days_exists;
    RAISE NOTICE 'attendance_calculation_settings table exists: %', attendance_settings_exists;
    
    -- If tables exist, check data count
    IF working_days_exists THEN
        SELECT COUNT(*) INTO working_days_count FROM working_days_configuration;
        RAISE NOTICE 'working_days_configuration records: %', working_days_count;
    END IF;
    
    IF attendance_settings_exists THEN
        SELECT COUNT(*) INTO attendance_settings_count FROM attendance_calculation_settings;
        RAISE NOTICE 'attendance_calculation_settings records: %', attendance_settings_count;
    END IF;
    
    RAISE NOTICE '=== END DIAGNOSTIC ===';
END $$;

-- ============================================================================
-- STEP 2: CREATE TABLES IF THEY DON'T EXIST
-- ============================================================================

-- Create working_days_configuration table
CREATE TABLE IF NOT EXISTS working_days_configuration (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    fiscal_year VARCHAR(10) NOT NULL,
    bs_month INTEGER NOT NULL CHECK (bs_month >= 1 AND bs_month <= 12),
    bs_month_name VARCHAR(20) NOT NULL,
    total_days_in_month INTEGER NOT NULL CHECK (total_days_in_month >= 28 AND total_days_in_month <= 32),
    working_days INTEGER NOT NULL CHECK (working_days >= 0 AND working_days <= total_days_in_month),
    public_holidays INTEGER DEFAULT 0 CHECK (public_holidays >= 0),
    weekend_days INTEGER DEFAULT 0 CHECK (weekend_days >= 0),
    late_penalty_type VARCHAR(20) DEFAULT 'half_day' CHECK (late_penalty_type IN ('none', 'half_day', 'custom')),
    late_penalty_amount DECIMAL(8,2) DEFAULT 0,
    half_day_calculation_method VARCHAR(20) DEFAULT 'fifty_percent' CHECK (half_day_calculation_method IN ('fifty_percent', 'custom')),
    created_by UUID REFERENCES users(id),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_by UUID REFERENCES users(id),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(fiscal_year, bs_month)
);

-- Create attendance_calculation_settings table
CREATE TABLE IF NOT EXISTS attendance_calculation_settings (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    setting_name VARCHAR(100) NOT NULL UNIQUE,
    setting_value TEXT NOT NULL,
    setting_type VARCHAR(20) NOT NULL CHECK (setting_type IN ('boolean', 'number', 'string', 'json')),
    description TEXT,
    category VARCHAR(50) DEFAULT 'general',
    is_system_setting BOOLEAN DEFAULT FALSE,
    created_by UUID REFERENCES users(id),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_by UUID REFERENCES users(id),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- ============================================================================
-- STEP 3: INSERT DEFAULT DATA (with conflict handling)
-- ============================================================================

-- Insert default working days for fiscal year 2081-82
INSERT INTO working_days_configuration (
    fiscal_year, bs_month, bs_month_name, total_days_in_month, 
    working_days, public_holidays, weekend_days
) VALUES 
    ('2081-82', 1, 'Baisakh', 31, 22, 2, 7),
    ('2081-82', 2, 'Jestha', 32, 23, 2, 7),
    ('2081-82', 3, 'Ashadh', 32, 23, 2, 7),
    ('2081-82', 4, 'Shrawan', 32, 23, 2, 7),
    ('2081-82', 5, 'Bhadra', 32, 23, 2, 7),
    ('2081-82', 6, 'Ashwin', 30, 22, 2, 6),
    ('2081-82', 7, 'Kartik', 30, 22, 2, 6),
    ('2081-82', 8, 'Mangsir', 30, 22, 2, 6),
    ('2081-82', 9, 'Poush', 30, 22, 2, 6),
    ('2081-82', 10, 'Magh', 30, 22, 2, 6),
    ('2081-82', 11, 'Falgun', 30, 22, 2, 6),
    ('2081-82', 12, 'Chaitra', 30, 22, 2, 6)
ON CONFLICT (fiscal_year, bs_month) DO NOTHING;

-- Insert default attendance calculation settings
INSERT INTO attendance_calculation_settings (setting_name, setting_value, setting_type, description, category, is_system_setting) VALUES
('enable_attendance_based_calculation', 'true', 'boolean', 'Enable automatic salary calculation based on attendance', 'calculation', TRUE),
('default_working_hours_per_day', '8', 'number', 'Standard working hours per day', 'calculation', TRUE),
('late_penalty_enabled', 'true', 'boolean', 'Enable late penalty deductions', 'penalties', TRUE),
('late_penalty_threshold_minutes', '15', 'number', 'Minutes after which late penalty applies', 'penalties', TRUE),
('half_day_threshold_hours', '4', 'number', 'Minimum hours for half day consideration', 'calculation', TRUE),
('overtime_calculation_enabled', 'true', 'boolean', 'Enable overtime calculation in attendance-based payroll', 'calculation', TRUE),
('leave_salary_calculation', 'full', 'string', 'How to calculate salary for leave days (full, half, none)', 'calculation', TRUE),
('weekend_work_multiplier', '1.5', 'number', 'Multiplier for weekend work', 'calculation', TRUE),
('holiday_work_multiplier', '2.0', 'number', 'Multiplier for holiday work', 'calculation', TRUE),
('attendance_bonus_threshold', '95', 'number', 'Attendance percentage threshold for bonus', 'bonus', TRUE),
('attendance_bonus_amount', '2000', 'number', 'Bonus amount for good attendance (NPR)', 'bonus', TRUE)
ON CONFLICT (setting_name) DO NOTHING;

-- ============================================================================
-- STEP 4: CREATE HELPER FUNCTIONS
-- ============================================================================

-- Function to get working days for a specific month and fiscal year
CREATE OR REPLACE FUNCTION get_working_days_for_period(
    p_fiscal_year VARCHAR(10),
    p_bs_month INTEGER
) RETURNS INTEGER AS $$
DECLARE
    working_days INTEGER;
BEGIN
    SELECT wdc.working_days INTO working_days
    FROM working_days_configuration wdc
    WHERE wdc.fiscal_year = p_fiscal_year 
    AND wdc.bs_month = p_bs_month;
    
    RETURN COALESCE(working_days, 22);
END;
$$ LANGUAGE plpgsql;

-- Function to get attendance calculation setting
CREATE OR REPLACE FUNCTION get_attendance_setting(
    p_setting_name VARCHAR(100)
) RETURNS TEXT AS $$
DECLARE
    setting_value TEXT;
BEGIN
    SELECT acs.setting_value INTO setting_value
    FROM attendance_calculation_settings acs
    WHERE acs.setting_name = p_setting_name;
    
    RETURN setting_value;
END;
$$ LANGUAGE plpgsql;

-- Function to calculate attendance-based salary
CREATE OR REPLACE FUNCTION calculate_attendance_based_salary(
    p_base_salary DECIMAL(10,2),
    p_working_days INTEGER,
    p_days_present INTEGER,
    p_days_late INTEGER DEFAULT 0,
    p_days_half_day INTEGER DEFAULT 0,
    p_days_on_leave INTEGER DEFAULT 0
) RETURNS DECIMAL(10,2) AS $$
DECLARE
    daily_rate DECIMAL(10,2);
    payable_salary DECIMAL(10,2);
    late_deduction DECIMAL(10,2) := 0;
BEGIN
    daily_rate := p_base_salary / p_working_days;
    payable_salary := daily_rate * p_days_present;
    payable_salary := payable_salary + (daily_rate * p_days_on_leave);
    payable_salary := payable_salary + (daily_rate * 0.5 * p_days_half_day);
    late_deduction := daily_rate * 0.5 * p_days_late;
    payable_salary := payable_salary - late_deduction;
    
    RETURN GREATEST(payable_salary, 0);
END;
$$ LANGUAGE plpgsql;

-- ============================================================================
-- STEP 5: CREATE INDEXES
-- ============================================================================

CREATE INDEX IF NOT EXISTS idx_working_days_config_fiscal_year ON working_days_configuration(fiscal_year);
CREATE INDEX IF NOT EXISTS idx_working_days_config_month ON working_days_configuration(bs_month);
CREATE INDEX IF NOT EXISTS idx_attendance_calc_settings_name ON attendance_calculation_settings(setting_name);
CREATE INDEX IF NOT EXISTS idx_attendance_calc_settings_category ON attendance_calculation_settings(category);

-- ============================================================================
-- STEP 6: SETUP RLS POLICIES
-- ============================================================================

-- Enable RLS
ALTER TABLE working_days_configuration ENABLE ROW LEVEL SECURITY;
ALTER TABLE attendance_calculation_settings ENABLE ROW LEVEL SECURITY;

-- Drop existing policies if they exist (to avoid conflicts)
DROP POLICY IF EXISTS "Admin and HR can manage working days config" ON working_days_configuration;
DROP POLICY IF EXISTS "Admin and HR can manage attendance calc settings" ON attendance_calculation_settings;

-- Create policies
CREATE POLICY "Admin and HR can manage working days config" ON working_days_configuration
    FOR ALL USING (
        EXISTS (
            SELECT 1 FROM users 
            WHERE users.id = auth.uid() 
            AND users.role IN ('admin', 'hr_manager')
        )
    );

CREATE POLICY "Admin and HR can manage attendance calc settings" ON attendance_calculation_settings
    FOR ALL USING (
        EXISTS (
            SELECT 1 FROM users 
            WHERE users.id = auth.uid() 
            AND users.role IN ('admin', 'hr_manager')
        )
    );

-- Grant permissions
GRANT SELECT, INSERT, UPDATE, DELETE ON working_days_configuration TO authenticated;
GRANT SELECT, INSERT, UPDATE, DELETE ON attendance_calculation_settings TO authenticated;

-- ============================================================================
-- STEP 7: FINAL VERIFICATION
-- ============================================================================

DO $$
DECLARE
    working_days_count integer := 0;
    attendance_settings_count integer := 0;
BEGIN
    SELECT COUNT(*) INTO working_days_count FROM working_days_configuration WHERE fiscal_year = '2081-82';
    SELECT COUNT(*) INTO attendance_settings_count FROM attendance_calculation_settings;
    
    RAISE NOTICE '=== FINAL VERIFICATION ===';
    RAISE NOTICE 'Working days configurations for 2081-82: %', working_days_count;
    RAISE NOTICE 'Attendance calculation settings: %', attendance_settings_count;
    
    IF working_days_count >= 12 AND attendance_settings_count >= 10 THEN
        RAISE NOTICE '✅ SUCCESS: All data is properly configured!';
        RAISE NOTICE 'The Payroll Settings page should now work correctly.';
    ELSE
        RAISE NOTICE '⚠️ WARNING: Some data may be missing. Check the tables manually.';
    END IF;
    
    RAISE NOTICE '=== END VERIFICATION ===';
END $$;

-- Show sample data to confirm
SELECT 'Working Days Sample' as data_type, fiscal_year, bs_month_name, working_days, total_days_in_month 
FROM working_days_configuration 
WHERE fiscal_year = '2081-82' 
ORDER BY bs_month 
LIMIT 5;

SELECT 'Settings Sample' as data_type, setting_name, setting_value, category 
FROM attendance_calculation_settings 
ORDER BY category, setting_name 
LIMIT 5;
