// NPR Currency Formatting with Lakhs/Crores Notation
// Phase 3: Nepal Localization Implementation - Currency Formatting

import { nepalConfig } from './nepal-config'

export interface CurrencyFormatOptions {
  showSymbol?: boolean
  showCode?: boolean
  useIndianNumbering?: boolean
  decimalPlaces?: number
  showFullForm?: boolean
  useShortForm?: boolean
  locale?: 'en' | 'ne'
  symbolPosition?: 'before' | 'after'
  spaceAfterSymbol?: boolean
}

export interface CurrencyBreakdown {
  crores: number
  lakhs: number
  thousands: number
  hundreds: number
  remainder: number
  formatted: string
  inWords: string
}

export class NPRCurrencyFormatter {
  private config = nepalConfig.getConfig().currency

  /**
   * Format amount in NPR with lakhs/crores notation
   */
  formatCurrency(amount: number, options: CurrencyFormatOptions = {}): string {
    const opts = {
      showSymbol: true,
      showCode: false,
      useIndianNumbering: this.config.useIndianNumbering,
      decimalPlaces: this.config.decimalPlaces,
      showFullForm: false,
      useShortForm: false,
      locale: 'en' as const,
      symbolPosition: 'before' as const,
      spaceAfterSymbol: true,
      ...options
    }

    // Handle negative numbers
    const isNegative = amount < 0
    const absoluteAmount = Math.abs(amount)

    // Format the number
    let formattedNumber: string
    if (opts.useIndianNumbering) {
      formattedNumber = this.formatIndianNumbering(absoluteAmount, opts.decimalPlaces)
    } else {
      formattedNumber = this.formatInternationalNumbering(absoluteAmount, opts.decimalPlaces)
    }

    // Add currency symbol/code
    let result = formattedNumber
    if (opts.showSymbol || opts.showCode) {
      const currencyIndicator = opts.showCode ? this.config.code : this.config.symbol
      const space = opts.spaceAfterSymbol ? ' ' : ''
      
      if (opts.symbolPosition === 'before') {
        result = `${currencyIndicator}${space}${formattedNumber}`
      } else {
        result = `${formattedNumber}${space}${currencyIndicator}`
      }
    }

    // Add negative sign
    if (isNegative) {
      result = `-${result}`
    }

    // Add full form if requested
    if (opts.showFullForm) {
      const inWords = this.convertToWords(absoluteAmount, opts.locale)
      result += ` (${inWords})`
    }

    return result
  }

  /**
   * Format number using Indian numbering system (lakhs, crores)
   */
  private formatIndianNumbering(amount: number, decimalPlaces: number): string {
    const parts = amount.toFixed(decimalPlaces).split('.')
    const integerPart = parts[0]
    const decimalPart = parts[1]

    // Add commas in Indian style (last 3 digits, then every 2 digits)
    let formatted = ''
    const reversed = integerPart.split('').reverse()
    
    for (let i = 0; i < reversed.length; i++) {
      if (i === 3 || (i > 3 && (i - 3) % 2 === 0)) {
        formatted = ',' + formatted
      }
      formatted = reversed[i] + formatted
    }

    return decimalPart && parseInt(decimalPart) > 0 ? `${formatted}.${decimalPart}` : formatted
  }

  /**
   * Format number using international numbering system
   */
  private formatInternationalNumbering(amount: number, decimalPlaces: number): string {
    return amount.toLocaleString('en-US', {
      minimumFractionDigits: decimalPlaces,
      maximumFractionDigits: decimalPlaces
    })
  }

  /**
   * Break down amount into crores, lakhs, thousands, etc.
   */
  getCurrencyBreakdown(amount: number): CurrencyBreakdown {
    const absoluteAmount = Math.abs(amount)
    
    const crores = Math.floor(absoluteAmount / 10000000) // 1 crore = 10,000,000
    const remainderAfterCrores = absoluteAmount % 10000000
    
    const lakhs = Math.floor(remainderAfterCrores / 100000) // 1 lakh = 100,000
    const remainderAfterLakhs = remainderAfterCrores % 100000
    
    const thousands = Math.floor(remainderAfterLakhs / 1000)
    const remainderAfterThousands = remainderAfterLakhs % 1000
    
    const hundreds = Math.floor(remainderAfterThousands / 100)
    const remainder = remainderAfterThousands % 100

    // Create formatted breakdown
    const parts: string[] = []
    if (crores > 0) parts.push(`${crores} crore${crores > 1 ? 's' : ''}`)
    if (lakhs > 0) parts.push(`${lakhs} lakh${lakhs > 1 ? 's' : ''}`)
    if (thousands > 0) parts.push(`${thousands} thousand`)
    if (hundreds > 0) parts.push(`${hundreds} hundred`)
    if (remainder > 0) parts.push(`${remainder}`)

    const formatted = parts.join(', ')
    const inWords = this.convertToWords(absoluteAmount, 'en')

    return {
      crores,
      lakhs,
      thousands,
      hundreds,
      remainder,
      formatted,
      inWords
    }
  }

  /**
   * Convert number to words in English or Nepali
   */
  convertToWords(amount: number, locale: 'en' | 'ne' = 'en'): string {
    if (locale === 'ne') {
      return this.convertToNepaliWords(amount)
    }
    return this.convertToEnglishWords(amount)
  }

  /**
   * Convert number to English words
   */
  private convertToEnglishWords(amount: number): string {
    if (amount === 0) return 'zero'

    const ones = ['', 'one', 'two', 'three', 'four', 'five', 'six', 'seven', 'eight', 'nine']
    const teens = ['ten', 'eleven', 'twelve', 'thirteen', 'fourteen', 'fifteen', 'sixteen', 'seventeen', 'eighteen', 'nineteen']
    const tens = ['', '', 'twenty', 'thirty', 'forty', 'fifty', 'sixty', 'seventy', 'eighty', 'ninety']

    const convertHundreds = (num: number): string => {
      let result = ''
      
      if (num >= 100) {
        result += ones[Math.floor(num / 100)] + ' hundred'
        num %= 100
        if (num > 0) result += ' '
      }
      
      if (num >= 20) {
        result += tens[Math.floor(num / 10)]
        num %= 10
        if (num > 0) result += ' ' + ones[num]
      } else if (num >= 10) {
        result += teens[num - 10]
      } else if (num > 0) {
        result += ones[num]
      }
      
      return result
    }

    const absoluteAmount = Math.abs(amount)
    const parts: string[] = []

    // Crores
    if (absoluteAmount >= 10000000) {
      const crores = Math.floor(absoluteAmount / 10000000)
      parts.push(convertHundreds(crores) + ' crore' + (crores > 1 ? 's' : ''))
    }

    // Lakhs
    const remainder1 = absoluteAmount % 10000000
    if (remainder1 >= 100000) {
      const lakhs = Math.floor(remainder1 / 100000)
      parts.push(convertHundreds(lakhs) + ' lakh' + (lakhs > 1 ? 's' : ''))
    }

    // Thousands
    const remainder2 = remainder1 % 100000
    if (remainder2 >= 1000) {
      const thousands = Math.floor(remainder2 / 1000)
      parts.push(convertHundreds(thousands) + ' thousand')
    }

    // Hundreds and below
    const remainder3 = remainder2 % 1000
    if (remainder3 > 0) {
      parts.push(convertHundreds(remainder3))
    }

    let result = parts.join(' ')
    
    // Handle decimal part
    const decimalPart = amount % 1
    if (decimalPart > 0) {
      const paisaAmount = Math.round(decimalPart * 100)
      if (paisaAmount > 0) {
        result += ' and ' + convertHundreds(paisaAmount) + ' paisa'
      }
    }

    return result + ' rupees'
  }

  /**
   * Convert number to Nepali words (simplified version)
   */
  private convertToNepaliWords(amount: number): string {
    // This is a simplified version - in production, implement full Nepali number conversion
    const nepaliNumbers = ['शून्य', 'एक', 'दुई', 'तीन', 'चार', 'पाँच', 'छ', 'सात', 'आठ', 'नौ']
    
    // For now, return a basic conversion
    if (amount < 10) {
      return nepaliNumbers[Math.floor(amount)] + ' रुपैयाँ'
    }
    
    // For larger numbers, fall back to English with Nepali currency
    return this.convertToEnglishWords(amount).replace('rupees', 'रुपैयाँ')
  }

  /**
   * Format currency for display in tables/lists
   */
  formatForDisplay(amount: number, compact: boolean = false): string {
    if (compact) {
      return this.formatCompact(amount)
    }
    
    return this.formatCurrency(amount, {
      showSymbol: true,
      useIndianNumbering: true,
      decimalPlaces: 2
    })
  }

  /**
   * Format currency in compact form (e.g., 1.5L, 2.3Cr)
   */
  formatCompact(amount: number): string {
    const absoluteAmount = Math.abs(amount)
    const isNegative = amount < 0
    
    let result: string
    
    if (absoluteAmount >= 10000000) {
      // Crores
      const crores = absoluteAmount / 10000000
      result = `${crores.toFixed(1)}Cr`
    } else if (absoluteAmount >= 100000) {
      // Lakhs
      const lakhs = absoluteAmount / 100000
      result = `${lakhs.toFixed(1)}L`
    } else if (absoluteAmount >= 1000) {
      // Thousands
      const thousands = absoluteAmount / 1000
      result = `${thousands.toFixed(1)}K`
    } else {
      result = absoluteAmount.toFixed(0)
    }
    
    return `${isNegative ? '-' : ''}${this.config.symbol} ${result}`
  }

  /**
   * Parse currency string back to number
   */
  parseCurrency(currencyString: string): number {
    // Remove currency symbols and spaces
    let cleanString = currencyString
      .replace(new RegExp(this.config.symbol, 'g'), '')
      .replace(new RegExp(this.config.code, 'g'), '')
      .replace(/\s/g, '')
      .replace(/,/g, '')

    // Handle negative numbers
    const isNegative = cleanString.startsWith('-')
    if (isNegative) {
      cleanString = cleanString.substring(1)
    }

    // Handle compact notation
    if (cleanString.endsWith('Cr')) {
      const value = parseFloat(cleanString.replace('Cr', '')) * 10000000
      return isNegative ? -value : value
    } else if (cleanString.endsWith('L')) {
      const value = parseFloat(cleanString.replace('L', '')) * 100000
      return isNegative ? -value : value
    } else if (cleanString.endsWith('K')) {
      const value = parseFloat(cleanString.replace('K', '')) * 1000
      return isNegative ? -value : value
    }

    const value = parseFloat(cleanString)
    return isNegative ? -value : value
  }

  /**
   * Validate currency input
   */
  isValidCurrency(currencyString: string): boolean {
    try {
      const parsed = this.parseCurrency(currencyString)
      return !isNaN(parsed) && isFinite(parsed)
    } catch {
      return false
    }
  }

  /**
   * Get currency formatting options for different contexts
   */
  getFormattingPresets() {
    return {
      payroll: {
        showSymbol: true,
        useIndianNumbering: true,
        decimalPlaces: 2,
        showFullForm: false
      },
      report: {
        showSymbol: true,
        useIndianNumbering: true,
        decimalPlaces: 2,
        showCode: true
      },
      compact: {
        showSymbol: true,
        useIndianNumbering: false,
        decimalPlaces: 1,
        useShortForm: true
      },
      detailed: {
        showSymbol: true,
        useIndianNumbering: true,
        decimalPlaces: 2,
        showFullForm: true
      }
    }
  }
}

// Export singleton instance
export const nprFormatter = new NPRCurrencyFormatter()

// Export utility functions
export const formatNPR = (amount: number, options?: CurrencyFormatOptions) => 
  nprFormatter.formatCurrency(amount, options)

export const formatNPRCompact = (amount: number) => 
  nprFormatter.formatCompact(amount)

export const formatNPRForDisplay = (amount: number, compact?: boolean) => 
  nprFormatter.formatForDisplay(amount, compact)

export const parseNPR = (currencyString: string) => 
  nprFormatter.parseCurrency(currencyString)

export const getNPRBreakdown = (amount: number) => 
  nprFormatter.getCurrencyBreakdown(amount)
