// Payslip API Endpoints
// Phase 4: User Interface Development - Payslip Generation & Viewer

import { NextRequest, NextResponse } from 'next/server'
import { db } from '@/lib/neon'
import { PayrollEngine, PayrollInput } from '@/lib/payroll-engine'
import { NepaliCalendar } from '@/lib/nepali-calendar'
import { nprFormatter } from '@/lib/currency-formatter'

interface PayslipGenerationRequest {
  employeeId: string
  payrollId?: string
  periodStart: string
  periodEnd: string
  template?: string
  format?: 'json' | 'html' | 'pdf'
  language?: 'en' | 'ne'
}

interface BulkPayslipRequest {
  employeeIds: string[]
  payrollIds?: string[]
  periodStart: string
  periodEnd: string
  template?: string
  format?: 'json' | 'html' | 'pdf' | 'email'
  emailSettings?: {
    subject: string
    message: string
    sendImmediately: boolean
  }
}

// Generate single payslip
export async function POST(request: NextRequest) {
  try {
    const body: PayslipGenerationRequest = await request.json()
    
    const { 
      employeeId, 
      payrollId, 
      periodStart, 
      periodEnd, 
      template = 'default',
      format = 'json',
      language = 'en'
    } = body

    // Validate required fields
    if (!employeeId || !periodStart || !periodEnd) {
      return NextResponse.json({
        success: false,
        error: 'Employee ID, period start, and period end are required'
      }, { status: 400 })
    }

    // Get employee data
    const employee = await db.sql`
      SELECT 
        e.*,
        d.name as department_name,
        p.title as position_title
      FROM employees e
      LEFT JOIN departments d ON e.department_id = d.id
      LEFT JOIN positions p ON e.position_id = p.id
      WHERE e.id = ${employeeId}
    `

    if (employee.length === 0) {
      return NextResponse.json({
        success: false,
        error: 'Employee not found'
      }, { status: 404 })
    }

    const employeeData = employee[0]

    // Get or generate payroll data
    let payrollData
    if (payrollId) {
      // Get existing payroll record
      const payroll = await db.sql`
        SELECT * FROM payroll 
        WHERE id = ${payrollId} AND employee_id = ${employeeId}
      `
      
      if (payroll.length === 0) {
        return NextResponse.json({
          success: false,
          error: 'Payroll record not found'
        }, { status: 404 })
      }
      
      payrollData = payroll[0]
    } else {
      // Generate payroll calculation

      // Get detailed attendance data
      const attendanceRecords = await db.sql`
        SELECT
          date,
          total_hours,
          regular_hours,
          overtime_hours,
          status,
          late_minutes,
          early_departure
        FROM attendance
        WHERE employee_id = ${employeeId}
        AND date BETWEEN ${periodStart} AND ${periodEnd}
        ORDER BY date
      `

      // Transform attendance data to match PayrollInput interface
      const attendanceData = attendanceRecords.map(record => ({
        date: record.date,
        totalHours: parseFloat(record.total_hours) || 0,
        regularHours: parseFloat(record.regular_hours) || 0,
        overtimeHours: parseFloat(record.overtime_hours) || 0,
        status: record.status as 'present' | 'absent' | 'late' | 'half_day' | 'on_leave',
        lateMinutes: parseInt(record.late_minutes) || 0,
        earlyDeparture: record.early_departure || false,
        sessionCount: 1
      }))

      // Prepare PayrollInput
      const payrollInput: PayrollInput = {
        userId: employeeData.id,
        periodStart,
        periodEnd,
        attendanceData,
        employeeData: {
          id: employeeData.id,
          fullName: employeeData.full_name,
          email: employeeData.email,
          department: employeeData.department,
          position: employeeData.position,
          hireDate: employeeData.hire_date,
          isActive: employeeData.is_active
        },
        payStructure: {
          type: (employeeData.pay_structure || 'monthly') as 'hourly' | 'daily' | 'monthly' | 'project_based',
          baseSalary: employeeData.base_salary || 0,
          hourlyRate: employeeData.hourly_rate || (employeeData.base_salary || 0) / 208, // 26 days * 8 hours
          dailyRate: employeeData.daily_rate || (employeeData.base_salary || 0) / 26, // 26 working days
          overtimeMultiplier: 1.5,
          providentFundPercentage: 10,
          effectiveFrom: employeeData.hire_date || periodStart
        },
        deductions: JSON.parse(employeeData.deductions || '[]'),
        allowances: JSON.parse(employeeData.allowances || '[]')
      }

      // Calculate payroll using PayrollEngine
      const payrollResult = await PayrollEngine.calculatePayroll(payrollInput)

      // Transform result to match expected payrollData format
      payrollData = {
        id: `temp-${employeeData.id}-${Date.now()}`,
        user_id: employeeData.id,
        pay_period_start: periodStart,
        pay_period_end: periodEnd,
        base_salary: payrollResult.baseSalary,
        overtime_pay: payrollResult.overtimePay,
        allowances: payrollResult.totalAllowances,
        deductions: payrollResult.totalDeductions,
        gross_pay: payrollResult.grossPay,
        net_pay: payrollResult.netPay,
        working_days: payrollResult.workingDays,
        overtime_hours: payrollResult.overtimeHours,
        status: 'calculated',
        created_at: new Date().toISOString()
      }
    }

    // Get company information
    const company = await db.sql`
      SELECT * FROM company_settings LIMIT 1
    `

    const companyData = company[0] || {
      name: 'Your Company Name',
      address: 'Company Address',
      phone: '+977-1-XXXXXXX',
      email: '<EMAIL>'
    }

    // Convert dates to Nepali calendar
    const bsStartDate = NepaliCalendar.adToBS(new Date(periodStart))
    const bsEndDate = NepaliCalendar.adToBS(new Date(periodEnd))
    const bsPayDate = NepaliCalendar.adToBS(new Date())

    // Generate payslip data
    const payslipData = {
      id: payrollData.id || `PAYSLIP-${Date.now()}`,
      employeeId: employeeData.id,
      employeeName: employeeData.full_name,
      employeeNameNepali: employeeData.full_name_nepali,
      designation: employeeData.position_title || employeeData.designation,
      department: employeeData.department_name || employeeData.department,
      payPeriod: `${new Date(periodStart).toLocaleDateString('en-US', { month: 'long', year: 'numeric' })}`,
      bsPayPeriod: `${NepaliCalendar.getBSMonthName(bsStartDate.month, 'en')} ${bsStartDate.year}`,
      periodStart,
      periodEnd,
      payDate: new Date().toISOString().split('T')[0],
      bsPayDate: NepaliCalendar.formatBSDate(bsPayDate),
      
      // Salary details
      baseSalary: payrollData.basePay || 0,
      overtimePay: payrollData.overtimePay || 0,
      allowances: payrollData.allowanceBreakdown || [],
      bonuses: payrollData.bonuses || 0,
      grossPay: payrollData.grossPay || 0,
      
      // Deductions
      deductions: payrollData.deductionBreakdown || [],
      taxes: payrollData.taxes || 0,
      totalDeductions: payrollData.deductions || 0,
      netPay: payrollData.netPay || 0,
      
      // Attendance
      workingDays: payrollData.workingDays || 0,
      attendedDays: payrollData.attendedDays || 0,
      absentDays: (payrollData.workingDays || 0) - (payrollData.attendedDays || 0),
      overtimeHours: payrollData.overtimeHours || 0,
      lateHours: payrollData.lateHours || 0,
      
      // Company details
      company: {
        name: companyData.name,
        nameNepali: companyData.name_nepali,
        address: companyData.address,
        addressNepali: companyData.address_nepali,
        phone: companyData.phone,
        email: companyData.email,
        logo: companyData.logo_url,
        panNumber: companyData.pan_number,
        registrationNumber: companyData.registration_number
      },
      
      // Bank details
      bankAccount: employeeData.bank_account ? {
        accountNumber: employeeData.bank_account_number,
        bankName: employeeData.bank_name,
        branchName: employeeData.bank_branch
      } : undefined,
      
      remarks: payrollData.remarks,
      status: payrollData.status || 'generated'
    }

    // Handle different output formats
    switch (format) {
      case 'html':
        const htmlContent = generateHTMLPayslip(payslipData, template, language)
        return new NextResponse(htmlContent, {
          headers: {
            'Content-Type': 'text/html',
            'Content-Disposition': `inline; filename="payslip-${employeeId}-${periodStart}.html"`
          }
        })

      case 'pdf':
        // In production, you would use a PDF generation library like puppeteer or jsPDF
        return NextResponse.json({
          success: false,
          error: 'PDF generation not implemented yet'
        }, { status: 501 })

      default:
        return NextResponse.json({
          success: true,
          data: {
            payslip: payslipData,
            template,
            language,
            generatedAt: new Date().toISOString()
          }
        })
    }

  } catch (error) {
    console.error('Error generating payslip:', error)
    return NextResponse.json({
      success: false,
      error: 'Internal server error'
    }, { status: 500 })
  }
}

// Generate bulk payslips
export async function PUT(request: NextRequest) {
  try {
    const body: BulkPayslipRequest = await request.json()
    
    const { 
      employeeIds, 
      payrollIds, 
      periodStart, 
      periodEnd, 
      template = 'default',
      format = 'json',
      emailSettings
    } = body

    // Validate required fields
    if (!employeeIds || employeeIds.length === 0 || !periodStart || !periodEnd) {
      return NextResponse.json({
        success: false,
        error: 'Employee IDs, period start, and period end are required'
      }, { status: 400 })
    }

    const results = []
    const errors = []

    // Process each employee
    for (let i = 0; i < employeeIds.length; i++) {
      const employeeId = employeeIds[i]
      const payrollId = payrollIds?.[i]

      try {
        // Generate individual payslip
        const payslipRequest = new NextRequest(request.url, {
          method: 'POST',
          body: JSON.stringify({
            employeeId,
            payrollId,
            periodStart,
            periodEnd,
            template,
            format: 'json' // Always get JSON for bulk processing
          })
        })

        const response = await POST(payslipRequest)
        const data = await response.json()

        if (data.success) {
          results.push({
            employeeId,
            payslip: data.data.payslip,
            status: 'success'
          })

          // Send email if requested
          if (format === 'email' && emailSettings) {
            await sendPayslipEmail(data.data.payslip, emailSettings)
          }
        } else {
          errors.push({
            employeeId,
            error: data.error,
            status: 'failed'
          })
        }
      } catch (error) {
        errors.push({
          employeeId,
          error: error instanceof Error ? error.message : 'Unknown error',
          status: 'failed'
        })
      }
    }

    return NextResponse.json({
      success: true,
      data: {
        processed: results.length,
        failed: errors.length,
        total: employeeIds.length,
        results,
        errors,
        generatedAt: new Date().toISOString()
      }
    })

  } catch (error) {
    console.error('Error generating bulk payslips:', error)
    return NextResponse.json({
      success: false,
      error: 'Internal server error'
    }, { status: 500 })
  }
}

// Get payslip by ID
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const payslipId = searchParams.get('id')
    const employeeId = searchParams.get('employeeId')
    const format = searchParams.get('format') || 'json'

    if (!payslipId && !employeeId) {
      return NextResponse.json({
        success: false,
        error: 'Payslip ID or Employee ID is required'
      }, { status: 400 })
    }

    // Get payslip data from database
    let query
    if (payslipId) {
      query = db.sql`SELECT * FROM payroll WHERE id = ${payslipId}`
    } else {
      query = db.sql`
        SELECT * FROM payroll 
        WHERE employee_id = ${employeeId} 
        ORDER BY created_at DESC 
        LIMIT 1
      `
    }

    const payroll = await query
    
    if (payroll.length === 0) {
      return NextResponse.json({
        success: false,
        error: 'Payslip not found'
      }, { status: 404 })
    }

    const payrollData = payroll[0]

    // Get employee and company data
    const employee = await db.sql`
      SELECT e.*, d.name as department_name, p.title as position_title
      FROM employees e
      LEFT JOIN departments d ON e.department_id = d.id
      LEFT JOIN positions p ON e.position_id = p.id
      WHERE e.id = ${payrollData.employee_id}
    `

    if (employee.length === 0) {
      return NextResponse.json({
        success: false,
        error: 'Employee not found'
      }, { status: 404 })
    }

    // Return the payslip data (similar to POST method)
    // ... (implementation similar to POST method)

    return NextResponse.json({
      success: true,
      data: {
        payslip: payrollData,
        employee: employee[0]
      }
    })

  } catch (error) {
    console.error('Error retrieving payslip:', error)
    return NextResponse.json({
      success: false,
      error: 'Internal server error'
    }, { status: 500 })
  }
}

// Helper function to generate HTML payslip
function generateHTMLPayslip(payslip: any, template: string, language: string): string {
  // This would generate HTML based on the template
  // For now, return a basic HTML structure
  return `
    <!DOCTYPE html>
    <html>
      <head>
        <title>Payslip - ${payslip.employeeName}</title>
        <style>
          body { font-family: Arial, sans-serif; margin: 20px; }
          .header { text-align: center; margin-bottom: 30px; }
          .section { margin-bottom: 20px; }
          .table { width: 100%; border-collapse: collapse; }
          .table th, .table td { border: 1px solid #ddd; padding: 8px; }
        </style>
      </head>
      <body>
        <div class="header">
          <h1>${payslip.company.name}</h1>
          <h2>PAYSLIP</h2>
          <p>${payslip.payPeriod}</p>
        </div>
        
        <div class="section">
          <h3>Employee Details</h3>
          <p>Name: ${payslip.employeeName}</p>
          <p>ID: ${payslip.employeeId}</p>
          <p>Department: ${payslip.department}</p>
        </div>
        
        <div class="section">
          <h3>Earnings</h3>
          <table class="table">
            <tr><td>Base Salary</td><td>${nprFormatter.formatCurrency(payslip.baseSalary)}</td></tr>
            <tr><td>Overtime</td><td>${nprFormatter.formatCurrency(payslip.overtimePay)}</td></tr>
            <tr><td>Gross Pay</td><td>${nprFormatter.formatCurrency(payslip.grossPay)}</td></tr>
          </table>
        </div>
        
        <div class="section">
          <h3>Net Pay</h3>
          <p><strong>${nprFormatter.formatCurrency(payslip.netPay)}</strong></p>
        </div>
      </body>
    </html>
  `
}

// Helper function to send payslip email
async function sendPayslipEmail(payslip: any, emailSettings: any): Promise<void> {
  // This would integrate with an email service
  // For now, just log the action
  console.log(`Sending payslip email to ${payslip.employeeName}`)
  console.log(`Subject: ${emailSettings.subject}`)
  console.log(`Message: ${emailSettings.message}`)
}
