"use client"

import React, { useState, useEffect } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Badge } from '@/components/ui/badge'
import { <PERSON><PERSON>, <PERSON>bs<PERSON>ontent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Switch } from '@/components/ui/switch'
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert'
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table'
import { 
  Settings, 
  Calendar, 
  Calculator, 
  Clock, 
  DollarSign, 
  Save, 
  RefreshCw,
  AlertTriangle,
  CheckCircle,
  Plus,
  Edit,
  Trash2
} from 'lucide-react'
import { toast } from 'sonner'

interface WorkingDaysConfig {
  id?: string;
  fiscal_year: string;
  bs_month: number;
  bs_month_name: string;
  total_days_in_month: number;
  working_days: number;
  public_holidays: number;
  weekend_days: number;
  late_penalty_type: 'none' | 'half_day' | 'custom';
  late_penalty_amount: number;
  half_day_calculation_method: 'fifty_percent' | 'custom';
}

interface AttendanceSetting {
  setting_name: string;
  setting_value: string;
  setting_type: string;
  description: string;
  category: string;
}

const NEPALI_MONTHS = [
  { value: 1, name: 'Baisakh' },
  { value: 2, name: 'Jestha' },
  { value: 3, name: 'Ashadh' },
  { value: 4, name: 'Shrawan' },
  { value: 5, name: 'Bhadra' },
  { value: 6, name: 'Ashwin' },
  { value: 7, name: 'Kartik' },
  { value: 8, name: 'Mangsir' },
  { value: 9, name: 'Poush' },
  { value: 10, name: 'Magh' },
  { value: 11, name: 'Falgun' },
  { value: 12, name: 'Chaitra' }
];

export default function PayrollSettingsPage() {
  const [workingDaysConfig, setWorkingDaysConfig] = useState<WorkingDaysConfig[]>([])
  const [attendanceSettings, setAttendanceSettings] = useState<AttendanceSetting[]>([])
  const [selectedFiscalYear, setSelectedFiscalYear] = useState('2081-82')
  const [loading, setLoading] = useState(true)
  const [saving, setSaving] = useState(false)
  const [editingConfig, setEditingConfig] = useState<WorkingDaysConfig | null>(null)
  const [showAddForm, setShowAddForm] = useState(false)

  // Fetch data on component mount
  useEffect(() => {
    fetchData()
  }, [selectedFiscalYear])

  const fetchData = async () => {
    try {
      setLoading(true)
      const response = await fetch(`/api/admin/payroll/working-days?fiscal_year=${selectedFiscalYear}`)
      const data = await response.json()

      if (data.success) {
        setWorkingDaysConfig(data.data.working_days_config || [])
        setAttendanceSettings(data.data.attendance_settings || [])
      } else {
        toast.error('Failed to fetch payroll settings')
      }
    } catch (error) {
      console.error('Error fetching data:', error)
      toast.error('Error loading payroll settings')
    } finally {
      setLoading(false)
    }
  }

  const handleSaveWorkingDays = async (config: WorkingDaysConfig) => {
    try {
      setSaving(true)
      const method = config.id ? 'PUT' : 'POST'
      const response = await fetch('/api/admin/payroll/working-days', {
        method,
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(config)
      })

      const data = await response.json()

      if (data.success) {
        toast.success(`Working days configuration ${config.id ? 'updated' : 'created'} successfully`)
        fetchData()
        setEditingConfig(null)
        setShowAddForm(false)
      } else {
        toast.error(data.error || 'Failed to save configuration')
      }
    } catch (error) {
      console.error('Error saving configuration:', error)
      toast.error('Error saving configuration')
    } finally {
      setSaving(false)
    }
  }

  const handleDeleteConfig = async (id: string) => {
    if (!confirm('Are you sure you want to delete this configuration?')) return

    try {
      const response = await fetch(`/api/admin/payroll/working-days?id=${id}`, {
        method: 'DELETE'
      })

      const data = await response.json()

      if (data.success) {
        toast.success('Configuration deleted successfully')
        fetchData()
      } else {
        toast.error(data.error || 'Failed to delete configuration')
      }
    } catch (error) {
      console.error('Error deleting configuration:', error)
      toast.error('Error deleting configuration')
    }
  }

  const WorkingDaysForm = ({ config, onSave, onCancel }: {
    config?: WorkingDaysConfig;
    onSave: (config: WorkingDaysConfig) => void;
    onCancel: () => void;
  }) => {
    const [formData, setFormData] = useState<WorkingDaysConfig>(
      config || {
        fiscal_year: selectedFiscalYear,
        bs_month: 1,
        bs_month_name: 'Baisakh',
        total_days_in_month: 31,
        working_days: 22,
        public_holidays: 2,
        weekend_days: 7,
        late_penalty_type: 'half_day',
        late_penalty_amount: 0,
        half_day_calculation_method: 'fifty_percent'
      }
    )

    const handleSubmit = (e: React.FormEvent) => {
      e.preventDefault()
      
      // Validation
      if (formData.working_days > formData.total_days_in_month) {
        toast.error('Working days cannot exceed total days in month')
        return
      }

      onSave(formData)
    }

    return (
      <Card>
        <CardHeader>
          <CardTitle>{config ? 'Edit' : 'Add'} Working Days Configuration</CardTitle>
          <CardDescription>
            Configure working days for {formData.bs_month_name} {formData.fiscal_year}
          </CardDescription>
        </CardHeader>
        <CardContent>
          <form onSubmit={handleSubmit} className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="bs_month">Nepali Month</Label>
                <Select
                  value={formData.bs_month.toString()}
                  onValueChange={(value) => {
                    const monthNum = parseInt(value)
                    const monthData = NEPALI_MONTHS.find(m => m.value === monthNum)
                    setFormData({
                      ...formData,
                      bs_month: monthNum,
                      bs_month_name: monthData?.name || ''
                    })
                  }}
                  disabled={!!config}
                >
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    {NEPALI_MONTHS.map(month => (
                      <SelectItem key={month.value} value={month.value.toString()}>
                        {month.name}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-2">
                <Label htmlFor="total_days">Total Days in Month</Label>
                <Input
                  id="total_days"
                  type="number"
                  min="28"
                  max="32"
                  value={formData.total_days_in_month}
                  onChange={(e) => setFormData({
                    ...formData,
                    total_days_in_month: parseInt(e.target.value) || 0
                  })}
                  required
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="working_days">Working Days</Label>
                <Input
                  id="working_days"
                  type="number"
                  min="0"
                  max={formData.total_days_in_month}
                  value={formData.working_days}
                  onChange={(e) => setFormData({
                    ...formData,
                    working_days: parseInt(e.target.value) || 0
                  })}
                  required
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="public_holidays">Public Holidays</Label>
                <Input
                  id="public_holidays"
                  type="number"
                  min="0"
                  value={formData.public_holidays}
                  onChange={(e) => setFormData({
                    ...formData,
                    public_holidays: parseInt(e.target.value) || 0
                  })}
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="late_penalty_type">Late Penalty Type</Label>
                <Select
                  value={formData.late_penalty_type}
                  onValueChange={(value: 'none' | 'half_day' | 'custom') => 
                    setFormData({ ...formData, late_penalty_type: value })
                  }
                >
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="none">No Penalty</SelectItem>
                    <SelectItem value="half_day">Half Day Deduction</SelectItem>
                    <SelectItem value="custom">Custom Amount</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              {formData.late_penalty_type === 'custom' && (
                <div className="space-y-2">
                  <Label htmlFor="late_penalty_amount">Late Penalty Amount (NPR)</Label>
                  <Input
                    id="late_penalty_amount"
                    type="number"
                    min="0"
                    step="0.01"
                    value={formData.late_penalty_amount}
                    onChange={(e) => setFormData({
                      ...formData,
                      late_penalty_amount: parseFloat(e.target.value) || 0
                    })}
                  />
                </div>
              )}
            </div>

            <div className="flex justify-end space-x-2">
              <Button type="button" variant="outline" onClick={onCancel}>
                Cancel
              </Button>
              <Button type="submit" disabled={saving}>
                {saving && <RefreshCw className="h-4 w-4 mr-2 animate-spin" />}
                {config ? 'Update' : 'Create'}
              </Button>
            </div>
          </form>
        </CardContent>
      </Card>
    )
  }

  if (loading) {
    return (
      <div className="p-6 space-y-6">
        <div className="flex items-center justify-center py-12">
          <RefreshCw className="h-8 w-8 animate-spin text-muted-foreground" />
        </div>
      </div>
    )
  }

  return (
    <div className="p-6 space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold">Payroll Settings</h1>
          <p className="text-muted-foreground">
            Configure working days and attendance-based payroll calculations
          </p>
        </div>
        
        <div className="flex items-center gap-4">
          <Select value={selectedFiscalYear} onValueChange={setSelectedFiscalYear}>
            <SelectTrigger className="w-40">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="2081-82">2081-82</SelectItem>
              <SelectItem value="2080-81">2080-81</SelectItem>
              <SelectItem value="2082-83">2082-83</SelectItem>
            </SelectContent>
          </Select>
          
          <Button onClick={fetchData} variant="outline">
            <RefreshCw className="h-4 w-4 mr-2" />
            Refresh
          </Button>
        </div>
      </div>

      <Tabs defaultValue="working-days" className="space-y-6">
        <TabsList>
          <TabsTrigger value="working-days" className="flex items-center space-x-2">
            <Calendar className="h-4 w-4" />
            <span>Working Days</span>
          </TabsTrigger>
          <TabsTrigger value="calculation" className="flex items-center space-x-2">
            <Calculator className="h-4 w-4" />
            <span>Calculation Settings</span>
          </TabsTrigger>
        </TabsList>

        <TabsContent value="working-days" className="space-y-6">
          {/* Add/Edit Form */}
          {(showAddForm || editingConfig) && (
            <WorkingDaysForm
              config={editingConfig || undefined}
              onSave={handleSaveWorkingDays}
              onCancel={() => {
                setShowAddForm(false)
                setEditingConfig(null)
              }}
            />
          )}

          {/* Working Days Configuration Table */}
          <Card>
            <CardHeader>
              <div className="flex items-center justify-between">
                <div>
                  <CardTitle>Working Days Configuration - {selectedFiscalYear}</CardTitle>
                  <CardDescription>
                    Configure working days for each Nepali calendar month
                  </CardDescription>
                </div>
                <Button onClick={() => setShowAddForm(true)} disabled={showAddForm || editingConfig}>
                  <Plus className="h-4 w-4 mr-2" />
                  Add Month
                </Button>
              </div>
            </CardHeader>
            <CardContent>
              {workingDaysConfig.length === 0 ? (
                <div className="text-center py-8 text-muted-foreground">
                  <Calendar className="h-12 w-12 mx-auto mb-4 opacity-50" />
                  <p>No working days configuration found for {selectedFiscalYear}</p>
                  <p className="text-sm">Click "Add Month" to create configuration</p>
                </div>
              ) : (
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Month</TableHead>
                      <TableHead>Total Days</TableHead>
                      <TableHead>Working Days</TableHead>
                      <TableHead>Holidays</TableHead>
                      <TableHead>Late Penalty</TableHead>
                      <TableHead>Actions</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {workingDaysConfig.map((config) => (
                      <TableRow key={config.id}>
                        <TableCell className="font-medium">
                          {config.bs_month_name}
                        </TableCell>
                        <TableCell>{config.total_days_in_month}</TableCell>
                        <TableCell>
                          <Badge variant="secondary">
                            {config.working_days} days
                          </Badge>
                        </TableCell>
                        <TableCell>{config.public_holidays}</TableCell>
                        <TableCell>
                          <Badge variant={config.late_penalty_type === 'none' ? 'outline' : 'default'}>
                            {config.late_penalty_type === 'none' ? 'None' : 
                             config.late_penalty_type === 'half_day' ? 'Half Day' : 
                             `NPR ${config.late_penalty_amount}`}
                          </Badge>
                        </TableCell>
                        <TableCell>
                          <div className="flex items-center space-x-2">
                            <Button
                              size="sm"
                              variant="outline"
                              onClick={() => setEditingConfig(config)}
                              disabled={editingConfig !== null || showAddForm}
                            >
                              <Edit className="h-4 w-4" />
                            </Button>
                            <Button
                              size="sm"
                              variant="outline"
                              onClick={() => handleDeleteConfig(config.id!)}
                              disabled={editingConfig !== null || showAddForm}
                            >
                              <Trash2 className="h-4 w-4" />
                            </Button>
                          </div>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="calculation" className="space-y-6">
          <Alert>
            <Settings className="h-4 w-4" />
            <AlertTitle>Attendance Calculation Settings</AlertTitle>
            <AlertDescription>
              These settings control how attendance data is used for payroll calculations.
              Changes will affect future payroll calculations.
            </AlertDescription>
          </Alert>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {attendanceSettings.map((setting) => (
              <Card key={setting.setting_name}>
                <CardHeader>
                  <CardTitle className="text-sm">{setting.setting_name.replace(/_/g, ' ').toUpperCase()}</CardTitle>
                  <CardDescription className="text-xs">
                    {setting.description}
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="flex items-center justify-between">
                    <span className="text-sm font-medium">
                      Current Value: <Badge variant="outline">{setting.setting_value}</Badge>
                    </span>
                    <Badge variant="secondary">{setting.category}</Badge>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </TabsContent>
      </Tabs>
    </div>
  )
}
