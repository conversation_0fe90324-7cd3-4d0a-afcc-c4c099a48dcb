const { neon } = require('@neondatabase/serverless');
require('dotenv').config({ path: '.env.local' });

if (!process.env.DATABASE_URL) {
  console.error('❌ DATABASE_URL environment variable is required');
  process.exit(1);
}

const sql = neon(process.env.DATABASE_URL);

async function testLoanRecoveryAPI() {
  try {
    console.log('🔍 Testing loan recovery database setup...\n');

    // Test 1: Check if tables exist
    console.log('1. Checking if tables exist...');
    const tables = await sql`
      SELECT table_name 
      FROM information_schema.tables 
      WHERE table_schema = 'public' 
      AND table_name LIKE 'loan_%'
      ORDER BY table_name
    `;
    
    console.log('Found tables:', tables.map(t => t.table_name));

    // Test 2: Check loan_records table structure
    console.log('\n2. Checking loan_records table structure...');
    const columns = await sql`
      SELECT column_name, data_type, is_nullable
      FROM information_schema.columns 
      WHERE table_name = 'loan_records'
      ORDER BY ordinal_position
    `;
    
    console.log('loan_records columns:', columns);

    // Test 3: Check if sample data exists
    console.log('\n3. Checking sample data...');
    const customerCount = await sql`SELECT COUNT(*) as count FROM loan_recovery_customers`;
    const loanCount = await sql`SELECT COUNT(*) as count FROM loan_records`;
    
    console.log(`Customers: ${customerCount[0].count}`);
    console.log(`Loans: ${loanCount[0].count}`);

    // Test 4: Test the actual query from the API
    console.log('\n4. Testing API query...');
    const loans = await sql`
      SELECT 
        lr.*,
        c.name as customer_name,
        c.phone as customer_phone,
        c.email as customer_email,
        c.address as customer_address,
        u.full_name as created_by_name,
        (lr.loan_amount - lr.amount_paid) as outstanding_amount,
        CASE 
          WHEN lr.due_date < CURRENT_DATE THEN 
            CURRENT_DATE - lr.due_date
          ELSE 0
        END as days_overdue
      FROM loan_records lr
      LEFT JOIN loan_recovery_customers c ON lr.customer_id = c.id
      LEFT JOIN users u ON lr.created_by = u.id
      WHERE lr.current_stage != 'complete'
      ORDER BY 
        CASE lr.current_stage
          WHEN 'early' THEN 1
          WHEN 'assertive' THEN 2
          WHEN 'escalation' THEN 3
          WHEN 'legal_recovery' THEN 4
          WHEN 'complete' THEN 5
        END,
        lr.stage_order,
        lr.due_date ASC
    `;

    console.log(`Query returned ${loans.length} loans`);
    
    if (loans.length > 0) {
      console.log('Sample loan:', {
        id: loans[0].id,
        customer_name: loans[0].customer_name,
        loan_amount: loans[0].loan_amount,
        current_stage: loans[0].current_stage,
        outstanding_amount: loans[0].outstanding_amount
      });
    }

    // Test 5: Group by stage
    console.log('\n5. Testing stage grouping...');
    const loansByStage = {
      early: [],
      assertive: [],
      escalation: [],
      legal_recovery: [],
      complete: []
    };

    loans.forEach(loan => {
      if (loansByStage[loan.current_stage]) {
        loansByStage[loan.current_stage].push(loan);
      }
    });

    console.log('Loans by stage:');
    Object.keys(loansByStage).forEach(stage => {
      console.log(`  ${stage}: ${loansByStage[stage].length} loans`);
    });

    console.log('\n✅ All tests passed! Database setup is working correctly.');

  } catch (error) {
    console.error('❌ Test failed:', error);
    console.error('Error details:', error.message);
    if (error.stack) {
      console.error('Stack trace:', error.stack);
    }
  }
}

testLoanRecoveryAPI();
