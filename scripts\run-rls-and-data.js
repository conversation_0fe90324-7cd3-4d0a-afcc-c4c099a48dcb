require('dotenv').config({ path: '.env.local' });
const { neon } = require('@neondatabase/serverless');

async function runRLSAndData() {
  try {
    const sql = neon(process.env.DATABASE_URL);
    
    console.log('🔒 Setting up RLS Policies and Default Data...\n');
    
    // Step 1: Create helper functions for RLS
    console.log('📋 Creating RLS helper functions...');
    
    await sql`
      CREATE OR REPLACE FUNCTION get_current_user_role()
      RETURNS TEXT AS $$
      DECLARE
          user_role TEXT;
      BEGIN
          SELECT role INTO user_role 
          FROM users 
          WHERE id = auth.uid();
          
          RETURN COALESCE(user_role, 'guest');
      END;
      $$ LANGUAGE plpgsql SECURITY DEFINER
    `;
    
    await sql`
      CREATE OR REPLACE FUNCTION is_admin_or_hr()
      RETURNS BOOLEAN AS $$
      BEGIN
          RETURN get_current_user_role() IN ('admin', 'hr_manager');
      END;
      $$ LANGUAGE plpgsql SECURITY DEFINER
    `;
    
    console.log('✅ RLS helper functions created');
    
    // Step 2: Create basic RLS policies for payroll table
    console.log('📋 Creating payroll RLS policies...');
    
    await sql`
      CREATE POLICY payroll_select_policy ON payroll
      FOR SELECT USING (
          user_id = auth.uid() OR 
          EXISTS (SELECT 1 FROM users WHERE id = auth.uid() AND role IN ('admin', 'hr_manager'))
      )
    `;
    
    await sql`
      CREATE POLICY payroll_insert_policy ON payroll
      FOR INSERT WITH CHECK (
          EXISTS (SELECT 1 FROM users WHERE id = auth.uid() AND role IN ('admin', 'hr_manager'))
      )
    `;
    
    await sql`
      CREATE POLICY payroll_update_policy ON payroll
      FOR UPDATE USING (
          EXISTS (SELECT 1 FROM users WHERE id = auth.uid() AND role IN ('admin', 'hr_manager'))
      )
    `;
    
    console.log('✅ Payroll RLS policies created');
    
    // Step 3: Create RLS policies for new tables
    console.log('📋 Creating RLS policies for new tables...');
    
    // Payroll approvals policies
    await sql`
      CREATE POLICY payroll_approvals_select_policy ON payroll_approvals
      FOR SELECT USING (
          approver_id = auth.uid() OR 
          EXISTS (SELECT 1 FROM users WHERE id = auth.uid() AND role IN ('admin', 'hr_manager')) OR
          EXISTS (SELECT 1 FROM payroll p WHERE p.id = payroll_id AND p.user_id = auth.uid())
      )
    `;
    
    await sql`
      CREATE POLICY payroll_approvals_insert_policy ON payroll_approvals
      FOR INSERT WITH CHECK (
          EXISTS (SELECT 1 FROM users WHERE id = auth.uid() AND role IN ('admin', 'hr_manager'))
      )
    `;
    
    // Payroll components policies
    await sql`
      CREATE POLICY payroll_components_master_select_policy ON payroll_components_master
      FOR SELECT USING (
          auth.uid() IS NOT NULL AND (
              is_active = true OR 
              EXISTS (SELECT 1 FROM users WHERE id = auth.uid() AND role IN ('admin', 'hr_manager'))
          )
      )
    `;
    
    await sql`
      CREATE POLICY payroll_components_master_insert_policy ON payroll_components_master
      FOR INSERT WITH CHECK (
          EXISTS (SELECT 1 FROM users WHERE id = auth.uid() AND role IN ('admin', 'hr_manager'))
      )
    `;
    
    // Employee component assignments policies
    await sql`
      CREATE POLICY employee_component_assignments_select_policy ON employee_component_assignments
      FOR SELECT USING (
          user_id = auth.uid() OR 
          EXISTS (SELECT 1 FROM users WHERE id = auth.uid() AND role IN ('admin', 'hr_manager'))
      )
    `;
    
    await sql`
      CREATE POLICY employee_component_assignments_insert_policy ON employee_component_assignments
      FOR INSERT WITH CHECK (
          EXISTS (SELECT 1 FROM users WHERE id = auth.uid() AND role IN ('admin', 'hr_manager'))
      )
    `;
    
    // Monthly payroll summary policies
    await sql`
      CREATE POLICY monthly_payroll_summary_select_policy ON monthly_payroll_summary
      FOR SELECT USING (
          user_id = auth.uid() OR 
          EXISTS (SELECT 1 FROM users WHERE id = auth.uid() AND role IN ('admin', 'hr_manager'))
      )
    `;
    
    await sql`
      CREATE POLICY monthly_payroll_summary_insert_policy ON monthly_payroll_summary
      FOR INSERT WITH CHECK (
          EXISTS (SELECT 1 FROM users WHERE id = auth.uid() AND role IN ('admin', 'hr_manager'))
      )
    `;
    
    // Payroll settings policies
    await sql`
      CREATE POLICY payroll_settings_select_policy ON payroll_settings
      FOR SELECT USING (
          auth.uid() IS NOT NULL AND (
              is_system_setting = false OR 
              EXISTS (SELECT 1 FROM users WHERE id = auth.uid() AND role IN ('admin', 'hr_manager'))
          )
      )
    `;
    
    await sql`
      CREATE POLICY payroll_settings_insert_policy ON payroll_settings
      FOR INSERT WITH CHECK (
          EXISTS (SELECT 1 FROM users WHERE id = auth.uid() AND role = 'admin')
      )
    `;
    
    // Payroll periods policies (readable by all authenticated users)
    await sql`
      CREATE POLICY payroll_periods_select_policy ON payroll_periods
      FOR SELECT USING (auth.uid() IS NOT NULL)
    `;
    
    await sql`
      CREATE POLICY payroll_periods_insert_policy ON payroll_periods
      FOR INSERT WITH CHECK (
          EXISTS (SELECT 1 FROM users WHERE id = auth.uid() AND role IN ('admin', 'hr_manager'))
      )
    `;
    
    console.log('✅ RLS policies for new tables created');
    
    // Step 4: Insert default payroll settings
    console.log('📊 Inserting default payroll settings...');
    
    const defaultSettings = [
      ['default_working_hours_per_day', '8', 'number', 'Standard working hours per day', true],
      ['overtime_threshold_hours', '8', 'number', 'Hours after which overtime applies', true],
      ['default_overtime_multiplier', '1.5', 'number', 'Default overtime rate multiplier', true],
      ['provident_fund_rate', '10', 'number', 'Default provident fund percentage', true],
      ['tax_threshold_annual', '500000', 'number', 'Annual income tax threshold in NPR', true],
      ['currency_symbol', 'NPR', 'string', 'Currency symbol for display', true],
      ['working_days_per_week', '6', 'number', 'Standard working days per week', true],
      ['late_penalty_per_minute', '10', 'number', 'Penalty amount per minute of lateness', true],
      ['attendance_bonus_threshold', '95', 'number', 'Attendance percentage for bonus eligibility', true],
      ['attendance_bonus_amount', '2000', 'number', 'Monthly attendance bonus amount in NPR', true],
      ['payroll_approval_required', 'true', 'boolean', 'Whether payroll requires approval', true],
      ['auto_calculate_overtime', 'true', 'boolean', 'Automatically calculate overtime from attendance', true],
      ['auto_calculate_late_penalty', 'true', 'boolean', 'Automatically calculate late penalties', true]
    ];
    
    for (const [key, value, type, description, isSystem] of defaultSettings) {
      await sql`
        INSERT INTO payroll_settings (setting_key, setting_value, setting_type, description, is_system_setting)
        VALUES (${key}, ${value}, ${type}, ${description}, ${isSystem})
        ON CONFLICT (setting_key) DO UPDATE SET
          setting_value = EXCLUDED.setting_value,
          description = EXCLUDED.description,
          updated_at = NOW()
      `;
    }
    
    console.log('✅ Default payroll settings inserted');
    
    // Step 5: Insert default payroll components
    console.log('📊 Inserting default payroll components...');
    
    // Get admin user ID for created_by field
    const adminUser = await sql`SELECT id FROM users WHERE role = 'admin' LIMIT 1`;
    const adminId = adminUser.length > 0 ? adminUser[0].id : null;
    
    const defaultComponents = [
      ['Travel Allowance', 'TRAVEL_ALLOW', 'allowance', 'company_policy', 'fixed', 5000.00, null, false, false, 'Monthly travel allowance for employees'],
      ['Phone Allowance', 'PHONE_ALLOW', 'allowance', 'company_policy', 'fixed', 2000.00, null, false, false, 'Monthly phone/communication allowance'],
      ['Meal Allowance', 'MEAL_ALLOW', 'allowance', 'company_policy', 'fixed', 3000.00, null, false, false, 'Monthly meal allowance'],
      ['Performance Bonus', 'PERF_BONUS', 'allowance', 'company_policy', 'percentage', null, 10.00, true, false, 'Monthly performance-based bonus'],
      ['Income Tax', 'INCOME_TAX', 'deduction', 'statutory', 'formula', null, null, false, true, 'Nepal income tax deduction as per tax slabs'],
      ['Provident Fund', 'PF_DEDUCTION', 'deduction', 'statutory', 'percentage', null, 10.00, false, true, 'Employee provident fund contribution (10%)'],
      ['Late Penalty', 'LATE_PENALTY', 'deduction', 'company_policy', 'formula', null, null, false, false, 'Penalty for late attendance'],
      ['Loan Deduction', 'LOAN_DEDUCTION', 'deduction', 'voluntary', 'fixed', null, null, false, false, 'Employee loan repayment deduction']
    ];
    
    for (const [name, code, type, category, calcType, fixedAmount, percentage, isTaxable, isStatutory, description] of defaultComponents) {
      await sql`
        INSERT INTO payroll_components_master (
          name, code, type, category, calculation_type, fixed_amount, percentage, 
          percentage_base, is_taxable, is_statutory, description, effective_from, created_by
        )
        VALUES (
          ${name}, ${code}, ${type}, ${category}, ${calcType}, ${fixedAmount}, ${percentage},
          ${percentage ? 'base_salary' : null}, ${isTaxable}, ${isStatutory}, ${description}, 
          CURRENT_DATE, ${adminId}
        )
        ON CONFLICT (code) DO UPDATE SET
          name = EXCLUDED.name,
          description = EXCLUDED.description,
          updated_at = NOW()
      `;
    }
    
    console.log('✅ Default payroll components inserted');
    
    // Step 6: Insert current fiscal year period
    console.log('📊 Inserting current fiscal year period...');
    
    await sql`
      INSERT INTO payroll_periods (
        period_name, period_type, fiscal_year,
        bs_start_date, bs_end_date, ad_start_date, ad_end_date,
        working_days, is_current_period
      ) VALUES (
        'FY 2081-82', 'yearly', '2081-82',
        '2081-04-01', '2082-03-32', '2024-07-16', '2025-07-15',
        300, TRUE
      ) ON CONFLICT (period_name, fiscal_year) DO UPDATE SET
        is_current_period = EXCLUDED.is_current_period,
        updated_at = NOW()
    `;
    
    console.log('✅ Fiscal year period inserted');
    
    // Step 7: Create updated_at triggers
    console.log('📋 Creating updated_at triggers...');
    
    await sql`
      CREATE OR REPLACE FUNCTION update_updated_at_column()
      RETURNS TRIGGER AS $$
      BEGIN
          NEW.updated_at = NOW();
          RETURN NEW;
      END;
      $$ language 'plpgsql'
    `;
    
    const triggerTables = [
      'payroll_approvals', 'payroll_disbursements', 'payroll_components_master',
      'employee_component_assignments', 'payroll_periods', 'payroll_settings',
      'monthly_payroll_summary'
    ];
    
    for (const table of triggerTables) {
      await sql`DROP TRIGGER IF EXISTS ${sql(table + '_updated_at')} ON ${sql(table)}`;
      await sql`
        CREATE TRIGGER ${sql(table + '_updated_at')}
        BEFORE UPDATE ON ${sql(table)}
        FOR EACH ROW EXECUTE FUNCTION update_updated_at_column()
      `;
    }
    
    console.log('✅ Updated_at triggers created');
    
    // Final verification
    console.log('\n🔍 Final Verification...');
    
    const settingsCount = await sql`SELECT COUNT(*) as count FROM payroll_settings`;
    console.log(`✅ Payroll settings: ${settingsCount[0].count} records`);
    
    const componentsCount = await sql`SELECT COUNT(*) as count FROM payroll_components_master`;
    console.log(`✅ Payroll components: ${componentsCount[0].count} records`);
    
    const periodsCount = await sql`SELECT COUNT(*) as count FROM payroll_periods`;
    console.log(`✅ Payroll periods: ${periodsCount[0].count} records`);
    
    console.log('\n🎉 RLS Policies and Default Data setup completed successfully!');
    console.log('\n📋 Summary:');
    console.log('   ✅ RLS helper functions created');
    console.log('   ✅ RLS policies applied to all payroll tables');
    console.log('   ✅ Default payroll settings inserted');
    console.log('   ✅ Default payroll components inserted');
    console.log('   ✅ Current fiscal year period created');
    console.log('   ✅ Updated_at triggers configured');
    console.log('\n🚀 Task 1.1 (Database Schema Completion) COMPLETED!');
    
    return true;
    
  } catch (error) {
    console.error('❌ RLS and data setup failed:', error);
    console.error('Error details:', error.message);
    return false;
  }
}

runRLSAndData()
  .then(success => {
    if (success) {
      console.log('\n✅ RLS and data setup completed successfully');
      process.exit(0);
    } else {
      console.log('\n❌ RLS and data setup failed');
      process.exit(1);
    }
  })
  .catch(error => {
    console.error('❌ Unexpected error:', error);
    process.exit(1);
  });
