// Nepal Labor Law Compliance System
// Phase 3: Nepal Localization Implementation - Labor Law Compliance

import { nepalConfig } from './nepal-config'
import { NepaliCalendar } from './nepali-calendar'
import { db } from './neon'

export interface LaborLawRule {
  id: string
  category: 'working_hours' | 'overtime' | 'leave' | 'wages' | 'safety' | 'termination'
  title: string
  description: string
  ruleText: string
  applicableToCategories: string[] // employee categories
  minimumValue?: number
  maximumValue?: number
  unit?: string
  isActive: boolean
  effectiveFrom: string
  source: string // e.g., "Labor Act 2074"
  section?: string
  penalties?: string
}

export interface ComplianceCheck {
  ruleId: string
  ruleName: string
  category: string
  status: 'compliant' | 'non_compliant' | 'warning' | 'not_applicable'
  message: string
  actualValue?: number
  requiredValue?: number
  unit?: string
  severity: 'low' | 'medium' | 'high' | 'critical'
  recommendations?: string[]
  affectedEmployees?: string[]
}

export interface ComplianceReport {
  employeeId?: string
  periodStart: string
  periodEnd: string
  overallStatus: 'compliant' | 'non_compliant' | 'warning'
  totalChecks: number
  compliantChecks: number
  nonCompliantChecks: number
  warningChecks: number
  checks: ComplianceCheck[]
  summary: ComplianceSummary
  generatedAt: string
}

export interface ComplianceSummary {
  workingHours: {
    dailyHours: number
    weeklyHours: number
    monthlyHours: number
    status: string
  }
  overtime: {
    dailyOvertimeHours: number
    weeklyOvertimeHours: number
    monthlyOvertimeHours: number
    status: string
  }
  wages: {
    minimumWageCompliance: boolean
    overtimePayCompliance: boolean
    status: string
  }
  leave: {
    annualLeaveEntitlement: number
    sickLeaveEntitlement: number
    maternityLeaveEntitlement: number
    status: string
  }
}

export class LaborLawComplianceChecker {
  private nepalConfig = nepalConfig
  private laborLaw = this.nepalConfig.getLaborLawConfig()

  /**
   * Get all labor law rules
   */
  async getLaborLawRules(): Promise<LaborLawRule[]> {
    try {
      const result = await db.sql`
        SELECT * FROM labor_law_rules
        WHERE is_active = TRUE
        ORDER BY category, title
      `

      return result.map(row => this.mapDatabaseToRule(row))
    } catch (error) {
      console.error('Error fetching labor law rules:', error)
      return this.getDefaultRules()
    }
  }

  /**
   * Check compliance for a specific employee and period
   */
  async checkEmployeeCompliance(
    employeeId: string,
    periodStart: string,
    periodEnd: string
  ): Promise<ComplianceReport> {
    try {
      // Get employee data
      const employee = await db.getUserById(employeeId)
      if (!employee) {
        throw new Error('Employee not found')
      }

      // Get attendance data for the period
      const attendanceData = await db.getAttendanceForPayrollPeriod(employeeId, periodStart, periodEnd)
      
      // Get payroll data if available
      const payrollData = await this.getPayrollDataForPeriod(employeeId, periodStart, periodEnd)

      // Get applicable rules
      const rules = await this.getApplicableRules(employee.employee_category || 'regular')

      // Perform compliance checks
      const checks: ComplianceCheck[] = []

      // Working hours compliance
      checks.push(...await this.checkWorkingHoursCompliance(attendanceData, rules))

      // Overtime compliance
      checks.push(...await this.checkOvertimeCompliance(attendanceData, rules))

      // Wage compliance
      if (payrollData) {
        checks.push(...await this.checkWageCompliance(payrollData, employee, rules))
      }

      // Leave compliance
      checks.push(...await this.checkLeaveCompliance(employeeId, periodStart, periodEnd, rules))

      // Calculate overall status
      const nonCompliantChecks = checks.filter(c => c.status === 'non_compliant').length
      const warningChecks = checks.filter(c => c.status === 'warning').length
      const compliantChecks = checks.filter(c => c.status === 'compliant').length

      let overallStatus: 'compliant' | 'non_compliant' | 'warning'
      if (nonCompliantChecks > 0) {
        overallStatus = 'non_compliant'
      } else if (warningChecks > 0) {
        overallStatus = 'warning'
      } else {
        overallStatus = 'compliant'
      }

      // Generate summary
      const summary = this.generateComplianceSummary(attendanceData, payrollData)

      return {
        employeeId,
        periodStart,
        periodEnd,
        overallStatus,
        totalChecks: checks.length,
        compliantChecks,
        nonCompliantChecks,
        warningChecks,
        checks,
        summary,
        generatedAt: new Date().toISOString()
      }
    } catch (error) {
      console.error('Error checking employee compliance:', error)
      throw new Error(`Failed to check compliance: ${error.message}`)
    }
  }

  /**
   * Check working hours compliance
   */
  private async checkWorkingHoursCompliance(
    attendanceData: any[],
    rules: LaborLawRule[]
  ): Promise<ComplianceCheck[]> {
    const checks: ComplianceCheck[] = []
    const workingHoursRule = rules.find(r => r.category === 'working_hours' && r.title.includes('Daily'))

    if (workingHoursRule) {
      // Check daily working hours
      const maxDailyHours = workingHoursRule.maximumValue || this.laborLaw.maxWorkingHoursPerDay
      
      attendanceData.forEach(day => {
        if (day.totalHours > maxDailyHours) {
          checks.push({
            ruleId: workingHoursRule.id,
            ruleName: workingHoursRule.title,
            category: 'working_hours',
            status: 'non_compliant',
            message: `Daily working hours (${day.totalHours}) exceed maximum allowed (${maxDailyHours})`,
            actualValue: day.totalHours,
            requiredValue: maxDailyHours,
            unit: 'hours',
            severity: 'high',
            recommendations: [
              'Reduce daily working hours to comply with labor law',
              'Consider redistributing workload across multiple days',
              'Ensure proper overtime approval and compensation'
            ]
          })
        }
      })

      // Check weekly working hours
      const weeklyHours = attendanceData.reduce((sum, day) => sum + day.totalHours, 0)
      const maxWeeklyHours = this.laborLaw.maxWorkingHoursPerWeek
      
      if (weeklyHours > maxWeeklyHours) {
        checks.push({
          ruleId: workingHoursRule.id,
          ruleName: 'Weekly Working Hours',
          category: 'working_hours',
          status: 'non_compliant',
          message: `Weekly working hours (${weeklyHours}) exceed maximum allowed (${maxWeeklyHours})`,
          actualValue: weeklyHours,
          requiredValue: maxWeeklyHours,
          unit: 'hours',
          severity: 'high',
          recommendations: [
            'Ensure weekly working hours do not exceed legal limits',
            'Provide mandatory rest days',
            'Monitor and control overtime hours'
          ]
        })
      }
    }

    return checks
  }

  /**
   * Check overtime compliance
   */
  private async checkOvertimeCompliance(
    attendanceData: any[],
    rules: LaborLawRule[]
  ): Promise<ComplianceCheck[]> {
    const checks: ComplianceCheck[] = []
    const overtimeRule = rules.find(r => r.category === 'overtime')

    if (overtimeRule) {
      const maxDailyOvertime = this.laborLaw.maxOvertimeHoursPerDay
      
      attendanceData.forEach(day => {
        if (day.overtimeHours > maxDailyOvertime) {
          checks.push({
            ruleId: overtimeRule.id,
            ruleName: overtimeRule.title,
            category: 'overtime',
            status: 'non_compliant',
            message: `Daily overtime hours (${day.overtimeHours}) exceed maximum allowed (${maxDailyOvertime})`,
            actualValue: day.overtimeHours,
            requiredValue: maxDailyOvertime,
            unit: 'hours',
            severity: 'medium',
            recommendations: [
              'Limit daily overtime to legal maximum',
              'Ensure overtime is pre-approved',
              'Provide proper overtime compensation at 1.5x rate'
            ]
          })
        }
      })
    }

    return checks
  }

  /**
   * Check wage compliance
   */
  private async checkWageCompliance(
    payrollData: any,
    employee: any,
    rules: LaborLawRule[]
  ): Promise<ComplianceCheck[]> {
    const checks: ComplianceCheck[] = []
    const wageRule = rules.find(r => r.category === 'wages' && r.title.includes('Minimum'))

    if (wageRule && payrollData) {
      const minimumWage = this.laborLaw.minimumWage
      const actualWage = payrollData.baseSalary || payrollData.grossPay

      if (actualWage < minimumWage) {
        checks.push({
          ruleId: wageRule.id,
          ruleName: wageRule.title,
          category: 'wages',
          status: 'non_compliant',
          message: `Employee wage (${actualWage}) is below minimum wage (${minimumWage})`,
          actualValue: actualWage,
          requiredValue: minimumWage,
          unit: 'NPR',
          severity: 'critical',
          recommendations: [
            'Increase employee wage to meet minimum wage requirements',
            'Review pay structure for compliance',
            'Ensure all employees receive at least minimum wage'
          ]
        })
      }
    }

    return checks
  }

  /**
   * Check leave compliance
   */
  private async checkLeaveCompliance(
    employeeId: string,
    periodStart: string,
    periodEnd: string,
    rules: LaborLawRule[]
  ): Promise<ComplianceCheck[]> {
    const checks: ComplianceCheck[] = []
    
    // This would check leave entitlements and usage
    // For now, return empty array as leave system is not fully implemented
    
    return checks
  }

  /**
   * Get applicable rules for employee category
   */
  private async getApplicableRules(employeeCategory: string): Promise<LaborLawRule[]> {
    const allRules = await this.getLaborLawRules()
    return allRules.filter(rule => 
      rule.applicableToCategories.length === 0 || 
      rule.applicableToCategories.includes(employeeCategory)
    )
  }

  /**
   * Get payroll data for period
   */
  private async getPayrollDataForPeriod(employeeId: string, periodStart: string, periodEnd: string): Promise<any> {
    try {
      const result = await db.sql`
        SELECT * FROM payroll
        WHERE user_id = ${employeeId}
          AND pay_period_start = ${periodStart}
          AND pay_period_end = ${periodEnd}
        ORDER BY created_at DESC
        LIMIT 1
      `

      return result[0] || null
    } catch (error) {
      console.error('Error fetching payroll data:', error)
      return null
    }
  }

  /**
   * Generate compliance summary
   */
  private generateComplianceSummary(attendanceData: any[], payrollData?: any): ComplianceSummary {
    const totalHours = attendanceData.reduce((sum, day) => sum + day.totalHours, 0)
    const totalOvertimeHours = attendanceData.reduce((sum, day) => sum + day.overtimeHours, 0)
    
    return {
      workingHours: {
        dailyHours: attendanceData.length > 0 ? totalHours / attendanceData.length : 0,
        weeklyHours: totalHours,
        monthlyHours: totalHours * 4, // Approximate
        status: totalHours <= this.laborLaw.maxWorkingHoursPerWeek ? 'compliant' : 'non_compliant'
      },
      overtime: {
        dailyOvertimeHours: attendanceData.length > 0 ? totalOvertimeHours / attendanceData.length : 0,
        weeklyOvertimeHours: totalOvertimeHours,
        monthlyOvertimeHours: totalOvertimeHours * 4, // Approximate
        status: totalOvertimeHours <= this.laborLaw.maxOvertimeHoursPerDay * 7 ? 'compliant' : 'non_compliant'
      },
      wages: {
        minimumWageCompliance: payrollData ? payrollData.baseSalary >= this.laborLaw.minimumWage : true,
        overtimePayCompliance: true, // Would need to check overtime rate
        status: 'compliant'
      },
      leave: {
        annualLeaveEntitlement: 18, // Nepal standard
        sickLeaveEntitlement: 12,
        maternityLeaveEntitlement: 98,
        status: 'compliant'
      }
    }
  }

  /**
   * Get default labor law rules
   */
  private getDefaultRules(): LaborLawRule[] {
    return [
      {
        id: 'rule-working-hours-daily',
        category: 'working_hours',
        title: 'Daily Working Hours Limit',
        description: 'Maximum working hours per day including overtime',
        ruleText: 'No employee shall be required to work for more than 12 hours in a day',
        applicableToCategories: [],
        maximumValue: 12,
        unit: 'hours',
        isActive: true,
        effectiveFrom: '2024-01-01',
        source: 'Labor Act 2074',
        section: 'Section 25',
        penalties: 'Fine up to NPR 50,000'
      },
      {
        id: 'rule-overtime-daily',
        category: 'overtime',
        title: 'Daily Overtime Limit',
        description: 'Maximum overtime hours per day',
        ruleText: 'Overtime work shall not exceed 4 hours per day',
        applicableToCategories: [],
        maximumValue: 4,
        unit: 'hours',
        isActive: true,
        effectiveFrom: '2024-01-01',
        source: 'Labor Act 2074',
        section: 'Section 26'
      },
      {
        id: 'rule-minimum-wage',
        category: 'wages',
        title: 'Minimum Wage',
        description: 'Minimum monthly wage for employees',
        ruleText: 'Minimum wage shall be NPR 17,300 per month',
        applicableToCategories: [],
        minimumValue: 17300,
        unit: 'NPR',
        isActive: true,
        effectiveFrom: '2024-01-01',
        source: 'Minimum Wage Fixation Committee'
      }
    ]
  }

  /**
   * Map database row to rule object
   */
  private mapDatabaseToRule(row: any): LaborLawRule {
    return {
      id: row.id,
      category: row.category,
      title: row.title,
      description: row.description,
      ruleText: row.rule_text,
      applicableToCategories: JSON.parse(row.applicable_to_categories || '[]'),
      minimumValue: row.minimum_value,
      maximumValue: row.maximum_value,
      unit: row.unit,
      isActive: row.is_active,
      effectiveFrom: row.effective_from,
      source: row.source,
      section: row.section,
      penalties: row.penalties
    }
  }

  /**
   * Bulk compliance check for multiple employees
   */
  async checkBulkCompliance(
    employeeIds: string[],
    periodStart: string,
    periodEnd: string
  ): Promise<{ [employeeId: string]: ComplianceReport }> {
    const results: { [employeeId: string]: ComplianceReport } = {}

    for (const employeeId of employeeIds) {
      try {
        results[employeeId] = await this.checkEmployeeCompliance(employeeId, periodStart, periodEnd)
      } catch (error) {
        console.error(`Error checking compliance for employee ${employeeId}:`, error)
      }
    }

    return results
  }

  /**
   * Get compliance dashboard data
   */
  async getComplianceDashboard(periodStart: string, periodEnd: string): Promise<{
    overallCompliance: number
    totalEmployees: number
    compliantEmployees: number
    nonCompliantEmployees: number
    warningEmployees: number
    topViolations: { rule: string; count: number }[]
    complianceByCategory: { [category: string]: number }
  }> {
    // This would aggregate compliance data across all employees
    // For now, return mock data
    return {
      overallCompliance: 85,
      totalEmployees: 100,
      compliantEmployees: 85,
      nonCompliantEmployees: 10,
      warningEmployees: 5,
      topViolations: [
        { rule: 'Daily Working Hours Limit', count: 8 },
        { rule: 'Daily Overtime Limit', count: 5 },
        { rule: 'Weekly Rest Day', count: 3 }
      ],
      complianceByCategory: {
        working_hours: 90,
        overtime: 85,
        wages: 95,
        leave: 88
      }
    }
  }
}

// Export singleton instance
export const laborLawComplianceChecker = new LaborLawComplianceChecker()
