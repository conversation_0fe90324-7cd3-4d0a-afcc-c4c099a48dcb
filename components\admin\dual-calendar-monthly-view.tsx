"use client"

import React, { useState, useEffect } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { 
  ChevronLeft, 
  ChevronRight, 
  Calendar,
  CalendarDays
} from "lucide-react"
import { cn } from "@/lib/utils"
import { NepaliCalendar, BSDate } from "@/lib/nepali-calendar"

interface DualCalendarMonthlyViewProps {
  employees: any[]
  currentDate: Date
  onDateChange: (date: Date) => void
  calendarType: "english" | "nepali"
  onCalendarTypeChange: (type: "english" | "nepali") => void
}

export function DualCalendarMonthlyView({
  employees,
  currentDate,
  onDateChange,
  calendarType,
  onCalendarTypeChange
}: DualCalendarMonthlyViewProps) {
  const [currentBSDate, setCurrentBSDate] = useState<BSDate>(NepaliCalendar.adToBS(currentDate))

  // Update BS date when current date changes
  useEffect(() => {
    setCurrentBSDate(NepaliCalendar.adToBS(currentDate))
  }, [currentDate])

  const navigateMonth = (direction: "prev" | "next") => {
    if (calendarType === "english") {
      const newDate = new Date(currentDate)
      if (direction === "prev") {
        newDate.setMonth(newDate.getMonth() - 1)
      } else {
        newDate.setMonth(newDate.getMonth() + 1)
      }
      onDateChange(newDate)
    } else {
      // Navigate Nepali calendar
      const newBSDate = { ...currentBSDate }
      if (direction === "prev") {
        if (newBSDate.month === 1) {
          newBSDate.month = 12
          newBSDate.year -= 1
        } else {
          newBSDate.month -= 1
        }
      } else {
        if (newBSDate.month === 12) {
          newBSDate.month = 1
          newBSDate.year += 1
        } else {
          newBSDate.month += 1
        }
      }
      newBSDate.day = 1 // Reset to first day of month
      const newADDate = NepaliCalendar.bsToAD(newBSDate)
      onDateChange(newADDate)
    }
  }

  const getEnglishMonthName = (date: Date) => {
    return date.toLocaleString('default', { month: 'long', year: 'numeric' })
  }

  const getNepaliMonthName = (bsDate: BSDate) => {
    const nepaliMonths = [
      'बैशाख', 'जेठ', 'आषाढ', 'श्रावण', 'भाद्र', 'आश्विन',
      'कार्तिक', 'मंसिर', 'पौष', 'माघ', 'फाल्गुन', 'चैत्र'
    ]
    return `${nepaliMonths[bsDate.month - 1]} ${bsDate.year}`
  }

  const getDaysInMonth = () => {
    if (calendarType === "english") {
      const year = currentDate.getFullYear()
      const month = currentDate.getMonth()
      const daysInMonth = new Date(year, month + 1, 0).getDate()
      const firstDayOfWeek = new Date(year, month, 1).getDay()
      
      const days = []
      
      // Add empty cells for days before the first day of the month
      for (let i = 0; i < firstDayOfWeek; i++) {
        days.push(null)
      }
      
      // Add all days of the month
      for (let day = 1; day <= daysInMonth; day++) {
        days.push({
          day,
          date: new Date(year, month, day),
          bsDate: NepaliCalendar.adToBS(new Date(year, month, day))
        })
      }
      
      return days
    } else {
      // Nepali calendar logic
      const daysInBSMonth = NepaliCalendar.getDaysInMonth(currentBSDate.year, currentBSDate.month)
      const firstDayBS = { ...currentBSDate, day: 1 }
      const firstDayAD = NepaliCalendar.bsToAD(firstDayBS)
      const firstDayOfWeek = firstDayAD.getDay()
      
      const days = []
      
      // Add empty cells for days before the first day of the month
      for (let i = 0; i < firstDayOfWeek; i++) {
        days.push(null)
      }
      
      // Add all days of the month
      for (let day = 1; day <= daysInBSMonth; day++) {
        const bsDate = { ...currentBSDate, day }
        const adDate = NepaliCalendar.bsToAD(bsDate)
        days.push({
          day,
          date: adDate,
          bsDate: bsDate
        })
      }
      
      return days
    }
  }

  const formatDateKey = (date: Date) => {
    return date.toISOString().split('T')[0]
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case "present":
        return "bg-green-100 text-green-800 border-green-200"
      case "late":
        return "bg-yellow-100 text-yellow-800 border-yellow-200"
      case "half_day":
        return "bg-blue-100 text-blue-800 border-blue-200"
      case "on_leave":
        return "bg-purple-100 text-purple-800 border-purple-200"
      case "absent":
      default:
        return "bg-red-100 text-red-800 border-red-200"
    }
  }

  const getStatusIcon = (status: string) => {
    switch (status) {
      case "present":
        return "✓"
      case "late":
        return "⏰"
      case "half_day":
        return "½"
      case "on_leave":
        return "🏖️"
      case "absent":
      default:
        return "✗"
    }
  }

  const days = getDaysInMonth()

  return (
    <Card>
      <CardHeader>
        <div className="flex flex-col space-y-4 lg:flex-row lg:items-center lg:justify-between lg:space-y-0">
          <div className="flex items-center space-x-4">
            <Button
              onClick={() => navigateMonth("prev")}
              variant="outline"
              size="icon"
            >
              <ChevronLeft className="h-4 w-4" />
            </Button>
            
            <div className="text-center">
              <CardTitle className="text-xl">
                {calendarType === "english" 
                  ? getEnglishMonthName(currentDate)
                  : getNepaliMonthName(currentBSDate)
                }
              </CardTitle>
              <CardDescription>
                {calendarType === "english" ? "Gregorian Calendar" : "Bikram Sambat Calendar"}
              </CardDescription>
            </div>
            
            <Button
              onClick={() => navigateMonth("next")}
              variant="outline"
              size="icon"
            >
              <ChevronRight className="h-4 w-4" />
            </Button>
          </div>

          {/* Calendar Type Toggle */}
          <div className="flex items-center space-x-2">
            <Button
              onClick={() => onCalendarTypeChange("english")}
              variant={calendarType === "english" ? "default" : "outline"}
              size="sm"
            >
              <Calendar className="mr-2 h-4 w-4" />
              English
            </Button>
            <Button
              onClick={() => onCalendarTypeChange("nepali")}
              variant={calendarType === "nepali" ? "default" : "outline"}
              size="sm"
            >
              <CalendarDays className="mr-2 h-4 w-4" />
              Nepali
            </Button>
          </div>
        </div>
      </CardHeader>
      <CardContent>
        {employees.length === 0 ? (
          <div className="text-center py-8">
            <p className="text-muted-foreground">No employees found matching the current filters.</p>
          </div>
        ) : (
          <div className="space-y-6">
            {/* Calendar Header */}
            <div className="grid grid-cols-8 gap-2 text-sm font-medium text-muted-foreground">
              <div className="p-2">Employee</div>
              <div className="p-2 text-center">Sun</div>
              <div className="p-2 text-center">Mon</div>
              <div className="p-2 text-center">Tue</div>
              <div className="p-2 text-center">Wed</div>
              <div className="p-2 text-center">Thu</div>
              <div className="p-2 text-center">Fri</div>
              <div className="p-2 text-center">Sat</div>
            </div>

            {/* Employee Rows */}
            {employees.map((employee) => (
              <div key={employee.employee_id} className="border rounded-lg p-2">
                <div className="grid grid-cols-8 gap-2 items-center">
                  {/* Employee Info */}
                  <div className="p-2 flex items-center space-x-3">
                    <Avatar className="h-8 w-8">
                      <AvatarImage src="/placeholder.svg" />
                      <AvatarFallback>
                        {employee.employee_name.split(' ').map((n: string) => n[0]).join('').toUpperCase()}
                      </AvatarFallback>
                    </Avatar>
                    <div className="min-w-0 flex-1">
                      <p className="text-sm font-medium truncate">
                        {employee.employee_name}
                      </p>
                      <p className="text-xs text-muted-foreground truncate">
                        {employee.department || 'No Department'}
                      </p>
                      <div className="flex items-center space-x-2 mt-1">
                        <Badge variant="outline" className="text-xs">
                          {employee.monthly_stats.attendance_percentage.toFixed(1)}%
                        </Badge>
                        <span className="text-xs text-muted-foreground">
                          {employee.monthly_stats.total_hours_worked.toFixed(1)}h
                        </span>
                      </div>
                    </div>
                  </div>

                  {/* Calendar Days */}
                  {days.map((dayData, index) => {
                    if (dayData === null) {
                      return <div key={index} className="p-1"></div>
                    }

                    const dateKey = formatDateKey(dayData.date)
                    const attendanceData = employee.daily_attendance[dateKey]
                    const status = attendanceData?.status || "absent"
                    
                    return (
                      <div key={dayData.day} className="p-1">
                        <div
                          className={cn(
                            "w-8 h-8 rounded-md flex flex-col items-center justify-center text-xs font-medium border cursor-pointer transition-all hover:scale-110",
                            getStatusColor(status)
                          )}
                          title={`${employee.employee_name} - ${calendarType === "english" ? `${dayData.day}/${currentDate.getMonth() + 1}` : `${dayData.bsDate.day}/${dayData.bsDate.month}`}: ${status.replace('_', ' ').toUpperCase()}${attendanceData?.total_hours ? ` (${attendanceData.total_hours}h)` : ''}`}
                        >
                          <span className="text-xs leading-none">
                            {getStatusIcon(status)}
                          </span>
                          <span className="text-xs leading-none mt-0.5">
                            {calendarType === "english" ? dayData.day : dayData.bsDate.day}
                          </span>
                        </div>
                      </div>
                    )
                  })}
                </div>
              </div>
            ))}
          </div>
        )}
      </CardContent>
    </Card>
  )
}
