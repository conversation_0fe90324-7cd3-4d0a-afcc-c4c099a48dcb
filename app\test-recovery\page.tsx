"use client"

import { useState } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card"

export default function TestRecoveryPage() {
  const [result, setResult] = useState<string>("")
  const [loading, setLoading] = useState(false)

  const testAPI = async () => {
    setLoading(true)
    setResult("")
    
    try {
      console.log("Testing API endpoint...")
      
      const response = await fetch("/api/loan-recovery/loans?include_complete=false", {
        method: "GET",
        credentials: "include",
      })

      console.log("Response status:", response.status)
      console.log("Response headers:", response.headers)

      const data = await response.json()
      console.log("Response data:", data)

      if (response.ok) {
        setResult(`✅ Success! Got ${Object.values(data.loans).flat().length} loans`)
      } else {
        setResult(`❌ Error: ${data.error || 'Unknown error'}`)
      }
    } catch (error) {
      console.error("Test error:", error)
      setResult(`❌ Network Error: ${error.message}`)
    } finally {
      setLoading(false)
    }
  }

  return (
    <div className="container mx-auto px-4 py-8">
      <Card>
        <CardHeader>
          <CardTitle>Loan Recovery API Test</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <Button 
            onClick={testAPI} 
            disabled={loading}
            className="bg-exobank-green hover:bg-exobank-green/90"
          >
            {loading ? "Testing..." : "Test API"}
          </Button>
          
          {result && (
            <div className="p-4 border rounded-lg">
              <pre className="text-sm">{result}</pre>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  )
}
