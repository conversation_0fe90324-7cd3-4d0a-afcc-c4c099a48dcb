#!/usr/bin/env node

/**
 * Demo Data Population Script for Task Management System
 * 
 * This script populates the database with realistic sample data for testing
 * and demonstration purposes. It creates:
 * - Sample projects/categories
 * - Diverse tasks across different statuses and priorities
 * - Task comments and activity logs
 * - User assignments and collaborations
 * - Time tracking entries
 * 
 * Run with: node scripts/populate-demo-task-data.js
 */

const { neon } = require('@neondatabase/serverless')
require('dotenv').config()

if (!process.env.DATABASE_URL) {
  console.error('❌ DATABASE_URL environment variable is required')
  process.exit(1)
}

const sql = neon(process.env.DATABASE_URL)

// Sample data configurations
const SAMPLE_PROJECTS = [
  {
    name: 'Website Redesign',
    description: 'Complete overhaul of company website with modern design and improved UX',
    color: '#3B82F6'
  },
  {
    name: 'Mobile App Development',
    description: 'Native mobile application for iOS and Android platforms',
    color: '#10B981'
  },
  {
    name: 'Marketing Campaign Q1',
    description: 'Digital marketing campaign for Q1 product launch',
    color: '#F59E0B'
  },
  {
    name: 'Database Migration',
    description: 'Migration from legacy database to modern cloud infrastructure',
    color: '#EF4444'
  },
  {
    name: 'Customer Support Portal',
    description: 'Self-service portal for customer support and documentation',
    color: '#8B5CF6'
  }
]

const SAMPLE_TASKS = [
  // Website Redesign Project
  {
    title: 'Design new homepage layout',
    description: 'Create wireframes and mockups for the new homepage design focusing on user engagement and conversion optimization',
    status: 'completed',
    priority: 'high',
    project: 'Website Redesign',
    estimated_hours: 16,
    actual_hours: 14.5
  },
  {
    title: 'Implement responsive navigation',
    description: 'Develop mobile-first responsive navigation component with accessibility features',
    status: 'in_progress',
    priority: 'high',
    project: 'Website Redesign',
    estimated_hours: 12,
    actual_hours: 8
  },
  {
    title: 'Optimize page loading speed',
    description: 'Implement lazy loading, image optimization, and code splitting to improve Core Web Vitals',
    status: 'todo',
    priority: 'medium',
    project: 'Website Redesign',
    estimated_hours: 20
  },
  {
    title: 'Set up analytics tracking',
    description: 'Configure Google Analytics 4 and custom event tracking for user behavior analysis',
    status: 'todo',
    priority: 'medium',
    project: 'Website Redesign',
    estimated_hours: 8
  },
  
  // Mobile App Development
  {
    title: 'Create user authentication flow',
    description: 'Implement secure login/signup with biometric authentication and social login options',
    status: 'in_progress',
    priority: 'high',
    project: 'Mobile App Development',
    estimated_hours: 24,
    actual_hours: 16
  },
  {
    title: 'Design app icon and splash screen',
    description: 'Create app store ready icons and animated splash screen for both iOS and Android',
    status: 'completed',
    priority: 'medium',
    project: 'Mobile App Development',
    estimated_hours: 6,
    actual_hours: 7
  },
  {
    title: 'Implement push notifications',
    description: 'Set up Firebase Cloud Messaging for cross-platform push notifications',
    status: 'todo',
    priority: 'medium',
    project: 'Mobile App Development',
    estimated_hours: 16
  },
  {
    title: 'App Store submission preparation',
    description: 'Prepare app store listings, screenshots, and submission materials for both platforms',
    status: 'todo',
    priority: 'low',
    project: 'Mobile App Development',
    estimated_hours: 12
  },
  
  // Marketing Campaign
  {
    title: 'Create social media content calendar',
    description: 'Plan and schedule 3 months of social media content across all platforms',
    status: 'completed',
    priority: 'high',
    project: 'Marketing Campaign Q1',
    estimated_hours: 10,
    actual_hours: 12
  },
  {
    title: 'Design email newsletter templates',
    description: 'Create responsive email templates for weekly newsletters and promotional campaigns',
    status: 'in_progress',
    priority: 'medium',
    project: 'Marketing Campaign Q1',
    estimated_hours: 8,
    actual_hours: 5
  },
  {
    title: 'Set up Google Ads campaigns',
    description: 'Create and configure targeted Google Ads campaigns for product launch',
    status: 'todo',
    priority: 'high',
    project: 'Marketing Campaign Q1',
    estimated_hours: 14
  },
  
  // Database Migration
  {
    title: 'Analyze current database schema',
    description: 'Document existing database structure and identify migration requirements',
    status: 'completed',
    priority: 'urgent',
    project: 'Database Migration',
    estimated_hours: 20,
    actual_hours: 22
  },
  {
    title: 'Set up new cloud database',
    description: 'Configure new PostgreSQL instance on AWS RDS with proper security and backup settings',
    status: 'completed',
    priority: 'urgent',
    project: 'Database Migration',
    estimated_hours: 8,
    actual_hours: 6
  },
  {
    title: 'Create data migration scripts',
    description: 'Develop and test scripts for migrating data from legacy system to new database',
    status: 'in_progress',
    priority: 'urgent',
    project: 'Database Migration',
    estimated_hours: 32,
    actual_hours: 24
  },
  {
    title: 'Perform migration testing',
    description: 'Execute comprehensive testing of migrated data and application functionality',
    status: 'todo',
    priority: 'urgent',
    project: 'Database Migration',
    estimated_hours: 16
  },
  
  // Customer Support Portal
  {
    title: 'Design knowledge base structure',
    description: 'Create information architecture and categorization for self-service knowledge base',
    status: 'completed',
    priority: 'medium',
    project: 'Customer Support Portal',
    estimated_hours: 12,
    actual_hours: 10
  },
  {
    title: 'Implement search functionality',
    description: 'Build intelligent search with auto-suggestions and content ranking',
    status: 'in_progress',
    priority: 'high',
    project: 'Customer Support Portal',
    estimated_hours: 20,
    actual_hours: 12
  },
  {
    title: 'Create ticket submission system',
    description: 'Develop user-friendly ticket submission form with file attachments and priority selection',
    status: 'todo',
    priority: 'high',
    project: 'Customer Support Portal',
    estimated_hours: 18
  },
  
  // Standalone tasks (no project)
  {
    title: 'Update company security policies',
    description: 'Review and update information security policies to comply with latest regulations',
    status: 'todo',
    priority: 'medium',
    project: null,
    estimated_hours: 6
  },
  {
    title: 'Organize team building event',
    description: 'Plan and coordinate quarterly team building activities for remote and in-office staff',
    status: 'in_progress',
    priority: 'low',
    project: null,
    estimated_hours: 8,
    actual_hours: 4
  },
  {
    title: 'Conduct code review training',
    description: 'Organize training session on best practices for code reviews and collaborative development',
    status: 'completed',
    priority: 'medium',
    project: null,
    estimated_hours: 4,
    actual_hours: 4.5
  }
]

const SAMPLE_COMMENTS = [
  'Great progress on this task! The design looks fantastic.',
  'I have some feedback on the implementation approach. Let\'s discuss in our next standup.',
  'This is ready for testing. I\'ve deployed it to the staging environment.',
  'Found a small bug in the edge case handling. I\'ll fix it today.',
  'The client has requested some changes to the color scheme. I\'ll update the mockups.',
  'Performance looks good after the optimization. Load time improved by 40%.',
  'Documentation has been updated to reflect the new changes.',
  'This task is blocked by the API endpoint development. Moving to next sprint.',
  'Excellent work! This exceeded our expectations.',
  'I\'ve added some additional test cases to ensure robustness.'
]

async function populateDemoData() {
  console.log('🚀 Starting demo data population...\n')

  try {
    // Get existing users for task assignment
    console.log('📋 Fetching existing users...')
    const users = await sql`SELECT id, full_name, email FROM users WHERE is_active = true LIMIT 10`
    
    if (users.length === 0) {
      console.error('❌ No active users found. Please create some users first.')
      process.exit(1)
    }
    
    console.log(`✅ Found ${users.length} active users`)
    
    // Create sample projects
    console.log('\n📁 Creating sample projects...')
    const createdProjects = []
    
    for (const project of SAMPLE_PROJECTS) {
      const randomUser = users[Math.floor(Math.random() * users.length)]
      
      const result = await sql`
        INSERT INTO task_projects (name, description, color, created_by)
        VALUES (${project.name}, ${project.description}, ${project.color}, ${randomUser.id})
        ON CONFLICT (name, created_by) DO UPDATE SET
          description = EXCLUDED.description,
          color = EXCLUDED.color
        RETURNING *
      `
      
      createdProjects.push(result[0])
      console.log(`  ✅ Created project: ${project.name}`)
    }
    
    // Create sample tasks
    console.log('\n📝 Creating sample tasks...')
    const createdTasks = []
    
    for (const task of SAMPLE_TASKS) {
      const assignedUser = users[Math.floor(Math.random() * users.length)]
      const createdByUser = users[Math.floor(Math.random() * users.length)]
      
      // Find project ID if specified
      let projectId = null
      if (task.project) {
        const project = createdProjects.find(p => p.name === task.project)
        projectId = project?.id || null
      }
      
      // Calculate due date (random between 1-30 days from now)
      const dueDate = new Date()
      dueDate.setDate(dueDate.getDate() + Math.floor(Math.random() * 30) + 1)
      
      const result = await sql`
        INSERT INTO tasks (
          title, description, assigned_to, assigned_by, status, priority,
          due_date, project_id, estimated_hours, actual_hours
        )
        VALUES (
          ${task.title}, ${task.description}, ${assignedUser.id}, ${createdByUser.id},
          ${task.status}, ${task.priority}, ${dueDate.toISOString()}, ${projectId},
          ${task.estimated_hours}, ${task.actual_hours || 0}
        )
        RETURNING *
      `
      
      createdTasks.push(result[0])
      console.log(`  ✅ Created task: ${task.title}`)
    }
    
    // Create sample comments
    console.log('\n💬 Creating sample comments...')
    let commentCount = 0
    
    for (const task of createdTasks) {
      // Randomly add 0-3 comments per task
      const numComments = Math.floor(Math.random() * 4)
      
      for (let i = 0; i < numComments; i++) {
        const randomUser = users[Math.floor(Math.random() * users.length)]
        const randomComment = SAMPLE_COMMENTS[Math.floor(Math.random() * SAMPLE_COMMENTS.length)]
        
        await sql`
          INSERT INTO task_comments (task_id, user_id, content)
          VALUES (${task.id}, ${randomUser.id}, ${randomComment})
        `
        
        commentCount++
      }
    }
    
    console.log(`  ✅ Created ${commentCount} comments`)
    
    // Create sample time logs for completed and in-progress tasks
    console.log('\n⏱️  Creating sample time logs...')
    let timeLogCount = 0
    
    const tasksWithTime = createdTasks.filter(t => t.status === 'completed' || t.status === 'in_progress')
    
    for (const task of tasksWithTime) {
      const assignedUser = users.find(u => u.id === task.assigned_to)
      if (!assignedUser) continue
      
      // Create 1-3 time log entries per task
      const numLogs = Math.floor(Math.random() * 3) + 1
      
      for (let i = 0; i < numLogs; i++) {
        const startTime = new Date()
        startTime.setDate(startTime.getDate() - Math.floor(Math.random() * 7)) // Within last week
        startTime.setHours(9 + Math.floor(Math.random() * 8)) // Between 9 AM and 5 PM
        
        const endTime = new Date(startTime)
        endTime.setHours(startTime.getHours() + Math.floor(Math.random() * 4) + 1) // 1-4 hours later
        
        await sql`
          INSERT INTO task_time_logs (task_id, user_id, start_time, end_time, description)
          VALUES (
            ${task.id}, ${assignedUser.id}, ${startTime.toISOString()}, ${endTime.toISOString()},
            'Working on task implementation and testing'
          )
        `
        
        timeLogCount++
      }
    }
    
    console.log(`  ✅ Created ${timeLogCount} time log entries`)
    
    // Generate summary statistics
    console.log('\n📊 Demo data summary:')
    console.log(`  • Projects: ${createdProjects.length}`)
    console.log(`  • Tasks: ${createdTasks.length}`)
    console.log(`  • Comments: ${commentCount}`)
    console.log(`  • Time logs: ${timeLogCount}`)
    
    const statusCounts = createdTasks.reduce((acc, task) => {
      acc[task.status] = (acc[task.status] || 0) + 1
      return acc
    }, {})
    
    console.log('\n📈 Task distribution by status:')
    Object.entries(statusCounts).forEach(([status, count]) => {
      console.log(`  • ${status}: ${count} tasks`)
    })
    
    console.log('\n🎉 Demo data population completed successfully!')
    console.log('\n💡 You can now test the kanban board with realistic data.')
    console.log('   Navigate to /dashboard and click on the Tasks tab to see the populated board.')
    
  } catch (error) {
    console.error('❌ Error populating demo data:', error)
    process.exit(1)
  }
}

// Run the script
if (require.main === module) {
  populateDemoData()
}
