require('dotenv').config({ path: '.env.local' });
const { neon } = require('@neondatabase/serverless');

async function createEssentialTables() {
  try {
    const sql = neon(process.env.DATABASE_URL);
    
    console.log('🔄 Creating essential payroll tables...');
    
    // 1. Enhance users table with payroll fields
    console.log('1. Enhancing users table...');
    await sql`
      ALTER TABLE users ADD COLUMN IF NOT EXISTS employee_type VARCHAR(20) DEFAULT 'full_time' 
        CHECK (employee_type IN ('full_time', 'part_time', 'contract', 'intern', 'consultant'))
    `;
    await sql`
      ALTER TABLE users ADD COLUMN IF NOT EXISTS employee_category VARCHAR(30) DEFAULT 'regular' 
        CHECK (employee_category IN ('regular', 'probation', 'temporary', 'seasonal', 'project_based'))
    `;
    await sql`ALTER TABLE users ADD COLUMN IF NOT EXISTS tax_identification_number VARCHAR(50)`;
    await sql`ALTER TABLE users ADD COLUMN IF NOT EXISTS citizenship_number VARCHAR(20)`;
    await sql`ALTER TABLE users ADD COLUMN IF NOT EXISTS pan_number VARCHAR(20)`;
    await sql`ALTER TABLE users ADD COLUMN IF NOT EXISTS employment_status VARCHAR(20) DEFAULT 'active' 
        CHECK (employment_status IN ('active', 'inactive', 'terminated', 'resigned', 'retired'))`;
    await sql`ALTER TABLE users ADD COLUMN IF NOT EXISTS pay_grade VARCHAR(10)`;
    await sql`ALTER TABLE users ADD COLUMN IF NOT EXISTS joining_bonus DECIMAL(10,2) DEFAULT 0`;
    
    console.log('✅ Users table enhanced');
    
    // 2. Create employee_bank_accounts table
    console.log('2. Creating employee_bank_accounts table...');
    await sql`
      CREATE TABLE IF NOT EXISTS employee_bank_accounts (
        id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
        user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
        bank_name VARCHAR(100) NOT NULL,
        bank_branch VARCHAR(100),
        account_number VARCHAR(50) NOT NULL,
        account_holder_name VARCHAR(200) NOT NULL,
        account_type VARCHAR(20) DEFAULT 'savings' 
          CHECK (account_type IN ('savings', 'current', 'salary')),
        is_primary BOOLEAN DEFAULT TRUE,
        is_active BOOLEAN DEFAULT TRUE,
        created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
        updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
        UNIQUE(user_id, account_number, bank_name)
      )
    `;
    
    console.log('✅ employee_bank_accounts table created');
    
    // 3. Create payroll_components_master table
    console.log('3. Creating payroll_components_master table...');
    await sql`
      CREATE TABLE IF NOT EXISTS payroll_components_master (
        id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
        name VARCHAR(100) NOT NULL,
        code VARCHAR(50) NOT NULL UNIQUE,
        type VARCHAR(20) NOT NULL CHECK (type IN ('deduction', 'allowance')),
        category VARCHAR(30) NOT NULL CHECK (category IN ('statutory', 'voluntary', 'company_policy', 'custom')),
        calculation_type VARCHAR(20) NOT NULL CHECK (calculation_type IN ('fixed', 'percentage', 'formula')),
        fixed_amount DECIMAL(10,2),
        percentage DECIMAL(5,2),
        percentage_base VARCHAR(30) CHECK (percentage_base IN ('base_salary', 'gross_pay', 'net_pay')),
        is_taxable BOOLEAN DEFAULT TRUE,
        is_statutory BOOLEAN DEFAULT FALSE,
        description TEXT,
        is_active BOOLEAN DEFAULT TRUE,
        effective_from DATE NOT NULL,
        effective_to DATE,
        created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
        updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
      )
    `;
    
    console.log('✅ payroll_components_master table created');
    
    // 4. Create allowance_assignments table
    console.log('4. Creating allowance_assignments table...');
    await sql`
      CREATE TABLE IF NOT EXISTS allowance_assignments (
        id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
        user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
        allowance_type VARCHAR(50) NOT NULL 
          CHECK (allowance_type IN ('travelling', 'phone', 'meal', 'transport', 'medical', 'education', 'custom')),
        allowance_name VARCHAR(100) NOT NULL,
        calculation_type VARCHAR(20) NOT NULL CHECK (calculation_type IN ('fixed', 'percentage')),
        amount DECIMAL(10,2) DEFAULT 0,
        percentage DECIMAL(5,2) DEFAULT 0,
        percentage_base VARCHAR(30) CHECK (percentage_base IN ('base_salary', 'gross_pay')),
        is_taxable BOOLEAN DEFAULT TRUE,
        effective_from DATE NOT NULL,
        effective_to DATE,
        is_active BOOLEAN DEFAULT TRUE,
        assigned_by UUID REFERENCES users(id),
        notes TEXT,
        created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
        updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
      )
    `;
    
    console.log('✅ allowance_assignments table created');
    
    // 5. Create deduction_approvals table
    console.log('5. Creating deduction_approvals table...');
    await sql`
      CREATE TABLE IF NOT EXISTS deduction_approvals (
        id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
        user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
        component_id UUID NOT NULL REFERENCES payroll_components_master(id),
        deduction_amount DECIMAL(10,2) NOT NULL,
        deduction_reason TEXT NOT NULL,
        requested_by UUID NOT NULL REFERENCES users(id),
        approved_by UUID REFERENCES users(id),
        status VARCHAR(20) DEFAULT 'pending' 
          CHECK (status IN ('pending', 'approved', 'rejected', 'cancelled')),
        request_date DATE NOT NULL DEFAULT CURRENT_DATE,
        approval_date DATE,
        effective_from DATE NOT NULL,
        effective_to DATE,
        notes TEXT,
        created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
        updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
      )
    `;
    
    console.log('✅ deduction_approvals table created');
    
    // 6. Insert default allowance and deduction types
    console.log('6. Inserting default payroll components...');
    await sql`
      INSERT INTO payroll_components_master (name, code, type, category, calculation_type, description, is_active, effective_from) VALUES
      ('Travelling Allowance', 'TRAVEL_ALLOW', 'allowance', 'company_policy', 'fixed', 'Monthly travelling allowance for employees', TRUE, CURRENT_DATE),
      ('Phone Allowance', 'PHONE_ALLOW', 'allowance', 'company_policy', 'fixed', 'Monthly phone/communication allowance', TRUE, CURRENT_DATE),
      ('Meal Allowance', 'MEAL_ALLOW', 'allowance', 'company_policy', 'fixed', 'Daily meal allowance for employees', TRUE, CURRENT_DATE),
      ('Transport Allowance', 'TRANSPORT_ALLOW', 'allowance', 'company_policy', 'fixed', 'Monthly transport allowance', TRUE, CURRENT_DATE),
      ('Income Tax', 'INCOME_TAX', 'deduction', 'statutory', 'percentage', 'Nepal Income Tax deduction', TRUE, CURRENT_DATE),
      ('Provident Fund', 'PF_DEDUCTION', 'deduction', 'statutory', 'percentage', 'Employee Provident Fund contribution', TRUE, CURRENT_DATE),
      ('Insurance Premium', 'INSURANCE', 'deduction', 'voluntary', 'fixed', 'Health/Life insurance premium deduction', TRUE, CURRENT_DATE),
      ('Loan EMI', 'LOAN_EMI', 'deduction', 'voluntary', 'fixed', 'Employee loan EMI deduction', TRUE, CURRENT_DATE)
      ON CONFLICT (code) DO NOTHING
    `;
    
    console.log('✅ Default payroll components inserted');
    
    // 7. Create indexes
    console.log('7. Creating indexes...');
    await sql`CREATE INDEX IF NOT EXISTS idx_employee_bank_accounts_user ON employee_bank_accounts(user_id)`;
    await sql`CREATE INDEX IF NOT EXISTS idx_allowance_assignments_user ON allowance_assignments(user_id)`;
    await sql`CREATE INDEX IF NOT EXISTS idx_deduction_approvals_user ON deduction_approvals(user_id)`;
    await sql`CREATE INDEX IF NOT EXISTS idx_payroll_components_master_type ON payroll_components_master(type)`;
    
    console.log('✅ Indexes created');
    
    // Verify creation
    console.log('\n🔍 Verifying table creation...');
    const tables = await sql`
      SELECT table_name 
      FROM information_schema.tables 
      WHERE table_name IN ('employee_bank_accounts', 'payroll_components_master', 'allowance_assignments', 'deduction_approvals')
      ORDER BY table_name
    `;
    
    console.log('✅ Created tables:', tables.map(t => t.table_name));
    
    // Check enhanced user columns
    const userColumns = await sql`
      SELECT column_name 
      FROM information_schema.columns 
      WHERE table_name = 'users' AND column_name IN ('employee_type', 'employee_category', 'tax_identification_number')
      ORDER BY column_name
    `;
    
    console.log('✅ Enhanced user columns:', userColumns.map(c => c.column_name));
    
    console.log('\n🎉 Essential payroll tables created successfully!');
    
  } catch (error) {
    console.error('❌ Table creation failed:', error.message);
    throw error;
  }
}

createEssentialTables();
