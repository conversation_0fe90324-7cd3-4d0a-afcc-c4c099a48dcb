// Employee Bank Accounts API Endpoints
// Admin endpoints for managing employee bank accounts

import { NextRequest, NextResponse } from 'next/server';
import { employeePayrollManager } from '@/lib/employee-payroll-manager';
import { AuthService } from '@/lib/auth-utils';

// PUT - Update bank account
export async function PUT(request: NextRequest) {
  try {
    const sessionToken = request.cookies.get("session-token")?.value;

    if (!sessionToken) {
      return NextResponse.json({
        success: false,
        error: 'Unauthorized'
      }, { status: 401 });
    }

    const user = await AuthService.verifySession(sessionToken);

    if (!user || (user.role !== 'admin' && user.role !== 'hr_manager')) {
      return NextResponse.json({
        success: false,
        error: 'Insufficient permissions'
      }, { status: 403 });
    }

    const body = await request.json();
    const { accountId, updates } = body;

    if (!accountId || !updates) {
      return NextResponse.json({
        success: false,
        error: 'Account ID and updates are required'
      }, { status: 400 });
    }

    // Validate account_type if provided
    if (updates.account_type && !['savings', 'current', 'salary'].includes(updates.account_type)) {
      return NextResponse.json({
        success: false,
        error: 'Invalid account type'
      }, { status: 400 });
    }

    const success = await employeePayrollManager.updateBankAccount(accountId, updates);

    if (success) {
      return NextResponse.json({
        success: true,
        message: 'Bank account updated successfully'
      });
    } else {
      return NextResponse.json({
        success: false,
        error: 'Failed to update bank account'
      }, { status: 500 });
    }

  } catch (error) {
    console.error('Error in bank account PUT:', error);
    return NextResponse.json({
      success: false,
      error: 'Internal server error'
    }, { status: 500 });
  }
}

// DELETE - Deactivate bank account
export async function DELETE(request: NextRequest) {
  try {
    const sessionToken = request.cookies.get("session-token")?.value;

    if (!sessionToken) {
      return NextResponse.json({
        success: false,
        error: 'Unauthorized'
      }, { status: 401 });
    }

    const user = await AuthService.verifySession(sessionToken);

    if (!user || (user.role !== 'admin' && user.role !== 'hr_manager')) {
      return NextResponse.json({
        success: false,
        error: 'Insufficient permissions'
      }, { status: 403 });
    }

    const { searchParams } = new URL(request.url);
    const accountId = searchParams.get('accountId');

    if (!accountId) {
      return NextResponse.json({
        success: false,
        error: 'Account ID is required'
      }, { status: 400 });
    }

    const success = await employeePayrollManager.updateBankAccount(accountId, { is_active: false });

    if (success) {
      return NextResponse.json({
        success: true,
        message: 'Bank account deactivated successfully'
      });
    } else {
      return NextResponse.json({
        success: false,
        error: 'Failed to deactivate bank account'
      }, { status: 500 });
    }

  } catch (error) {
    console.error('Error in bank account DELETE:', error);
    return NextResponse.json({
      success: false,
      error: 'Internal server error'
    }, { status: 500 });
  }
}
