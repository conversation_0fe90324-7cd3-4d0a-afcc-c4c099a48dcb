import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query"
import { toast } from "@/hooks/use-toast"

// Types
interface Comment {
  id: string
  task_id: string
  user_id: string
  content: string
  is_edited: boolean
  created_at: string
  updated_at: string
  user_name: string
  user_email: string
}

interface CreateCommentData {
  content: string
}

interface UpdateCommentData {
  content: string
}

// API functions
const commentApi = {
  async getTaskComments(taskId: string) {
    const response = await fetch(`/api/tasks/${taskId}/comments`, {
      credentials: "include",
    })

    if (!response.ok) {
      throw new Error("Failed to fetch comments")
    }

    return response.json()
  },

  async createComment(taskId: string, data: CreateCommentData) {
    const response = await fetch(`/api/tasks/${taskId}/comments`, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      credentials: "include",
      body: JSON.stringify(data),
    })

    if (!response.ok) {
      const error = await response.json()
      throw new Error(error.error || "Failed to create comment")
    }

    return response.json()
  },

  async updateComment(commentId: string, data: UpdateCommentData) {
    const response = await fetch(`/api/comments/${commentId}`, {
      method: "PUT",
      headers: {
        "Content-Type": "application/json",
      },
      credentials: "include",
      body: JSON.stringify(data),
    })

    if (!response.ok) {
      const error = await response.json()
      throw new Error(error.error || "Failed to update comment")
    }

    return response.json()
  },

  async deleteComment(commentId: string) {
    const response = await fetch(`/api/comments/${commentId}`, {
      method: "DELETE",
      credentials: "include",
    })

    if (!response.ok) {
      const error = await response.json()
      throw new Error(error.error || "Failed to delete comment")
    }

    return response.json()
  },
}

// React Query hooks
export function useTaskComments(taskId: string) {
  return useQuery({
    queryKey: ["comments", taskId],
    queryFn: () => commentApi.getTaskComments(taskId),
    enabled: !!taskId,
    staleTime: 30 * 1000, // 30 seconds
  })
}

export function useCreateComment(taskId: string) {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: (data: CreateCommentData) => commentApi.createComment(taskId, data),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["comments", taskId] })
      queryClient.invalidateQueries({ queryKey: ["tasks"] }) // Update comment count
      toast({
        title: "Success",
        description: "Comment added successfully",
      })
    },
    onError: (error: Error) => {
      toast({
        title: "Error",
        description: error.message,
        variant: "destructive",
      })
    },
  })
}

export function useUpdateComment(taskId: string) {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: ({ commentId, data }: { commentId: string; data: UpdateCommentData }) =>
      commentApi.updateComment(commentId, data),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["comments", taskId] })
      toast({
        title: "Success",
        description: "Comment updated successfully",
      })
    },
    onError: (error: Error) => {
      toast({
        title: "Error",
        description: error.message,
        variant: "destructive",
      })
    },
  })
}

export function useDeleteComment(taskId: string) {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: commentApi.deleteComment,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["comments", taskId] })
      queryClient.invalidateQueries({ queryKey: ["tasks"] }) // Update comment count
      toast({
        title: "Success",
        description: "Comment deleted successfully",
      })
    },
    onError: (error: Error) => {
      toast({
        title: "Error",
        description: error.message,
        variant: "destructive",
      })
    },
  })
}
