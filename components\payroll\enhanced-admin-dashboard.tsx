// Enhanced Admin Payroll Dashboard
// Integrates with Phase 1 APIs for comprehensive payroll management

"use client"

import React, { useState, useEffect } from 'react'
import {
  Calculator,
  Users,
  DollarSign,
  TrendingUp,
  Calendar,
  FileText,
  AlertTriangle,
  CheckCircle,
  Clock,
  Download,
  Settings,
  Plus,
  Edit,
  Trash2,
  Search,
  Filter,
  PlayCircle,
  PauseCircle,
  RefreshCw,
  Eye,
  UserCheck,
  AlertCircle,
  BarChart3
} from 'lucide-react'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { Progress } from '@/components/ui/progress'
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar'
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog'
import { CurrencyCard, CurrencyDisplay } from '@/components/ui/currency-display'
import { toast } from '@/hooks/use-toast'

interface EnhancedPayrollDashboardData {
  overview: {
    totalEmployees: number
    eligibleEmployees: number
    processedEmployees: number
    pendingApprovals: number
    totalPayrollAmount: number
    lastProcessedDate: string
    currentFiscalYear: string
    currentPeriod: string
  }
  processingStatus: {
    not_processed: number
    calculated: number
    approved: number
    processed: number
    paid: number
  }
  recentActivity: {
    id: string
    type: 'bulk_processed' | 'approved' | 'component_assigned' | 'error'
    description: string
    employee?: string
    amount?: number
    timestamp: string
    status: string
  }[]
  componentsSummary: {
    total_allowances: number
    total_deductions: number
    active_assignments: number
    pending_approvals: number
  }
  complianceAlerts: {
    id: string
    type: 'warning' | 'error' | 'info'
    message: string
    count: number
    action: string
  }[]
}

interface BulkProcessingOptions {
  year: number
  month: number
  departments: string[]
  userIds: string[]
  dryRun: boolean
}

export function EnhancedAdminDashboard() {
  const [dashboardData, setDashboardData] = useState<EnhancedPayrollDashboardData | null>(null)
  const [loading, setLoading] = useState(true)
  const [processing, setProcessing] = useState(false)
  const [activeTab, setActiveTab] = useState('overview')
  const [bulkProcessingDialog, setBulkProcessingDialog] = useState(false)
  const [bulkOptions, setBulkOptions] = useState<BulkProcessingOptions>({
    year: new Date().getFullYear(),
    month: new Date().getMonth() + 1,
    departments: [],
    userIds: [],
    dryRun: true
  })

  useEffect(() => {
    loadDashboardData()
    // Set up real-time updates
    const interval = setInterval(loadDashboardData, 30000) // Refresh every 30 seconds
    return () => clearInterval(interval)
  }, [])

  const loadDashboardData = async () => {
    try {
      setLoading(true)
      
      // Load processing status
      const statusResponse = await fetch(`/api/admin/payroll/bulk-process?action=processing_status&year=${bulkOptions.year}&month=${bulkOptions.month}`)
      const statusData = await statusResponse.json()

      // Load recent activity
      const activityResponse = await fetch('/api/admin/payroll/bulk-process?action=processing_history&limit=10')
      const activityData = await activityResponse.json()

      // Load components summary
      const allowancesResponse = await fetch('/api/admin/payroll/allowances?action=all_assignments&limit=1')
      const allowancesData = await allowancesResponse.json()

      const deductionsResponse = await fetch('/api/admin/payroll/deductions?action=statutory_deductions')
      const deductionsData = await deductionsResponse.json()

      // Construct dashboard data
      const dashboardData: EnhancedPayrollDashboardData = {
        overview: {
          totalEmployees: statusData.data?.summary?.total_employees || 0,
          eligibleEmployees: statusData.data?.summary?.total_employees || 0,
          processedEmployees: statusData.data?.summary?.processed || 0,
          pendingApprovals: statusData.data?.summary?.calculated || 0,
          totalPayrollAmount: statusData.data?.employees?.reduce((sum: number, emp: any) => sum + (emp.net_pay || 0), 0) || 0,
          lastProcessedDate: new Date().toISOString(),
          currentFiscalYear: statusData.data?.fiscal_year?.fiscal_year || '2081-82',
          currentPeriod: statusData.data?.period?.bs_month_name || 'Current Period'
        },
        processingStatus: {
          not_processed: statusData.data?.summary?.not_processed || 0,
          calculated: statusData.data?.summary?.calculated || 0,
          approved: statusData.data?.summary?.approved || 0,
          processed: statusData.data?.summary?.processed || 0,
          paid: statusData.data?.summary?.paid || 0
        },
        recentActivity: activityData.data?.map((item: any) => ({
          id: item.id || Math.random().toString(),
          type: 'bulk_processed',
          description: `Processed ${item.total_employees} employees for ${item.bs_month}`,
          timestamp: item.last_calculated || new Date().toISOString(),
          status: 'completed'
        })) || [],
        componentsSummary: {
          total_allowances: allowancesData.data?.pagination?.total || 0,
          total_deductions: deductionsData.data?.length || 0,
          active_assignments: allowancesData.data?.assignments?.length || 0,
          pending_approvals: 0
        },
        complianceAlerts: [
          {
            id: '1',
            type: 'info',
            message: 'Monthly payroll processing ready',
            count: statusData.data?.summary?.not_processed || 0,
            action: 'Start bulk processing'
          }
        ]
      }

      setDashboardData(dashboardData)
    } catch (error) {
      console.error('Error loading dashboard data:', error)
      toast({
        title: "Error",
        description: "Failed to load dashboard data",
        variant: "destructive"
      })
    } finally {
      setLoading(false)
    }
  }

  const handleBulkProcessing = async () => {
    try {
      setProcessing(true)
      
      const response = await fetch('/api/admin/payroll/bulk-process', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          action: 'process_monthly_all',
          year: bulkOptions.year,
          month: bulkOptions.month,
          dryRun: bulkOptions.dryRun
        })
      })

      const data = await response.json()

      if (data.success) {
        toast({
          title: "Success",
          description: bulkOptions.dryRun 
            ? `Dry run completed: ${data.data.summary.successful} employees calculated`
            : `Bulk processing completed: ${data.data.processed_count} employees processed`
        })
        
        if (!bulkOptions.dryRun) {
          loadDashboardData() // Refresh data after actual processing
        }
      } else {
        throw new Error(data.error)
      }
    } catch (error) {
      console.error('Error in bulk processing:', error)
      toast({
        title: "Error",
        description: "Failed to process payroll",
        variant: "destructive"
      })
    } finally {
      setProcessing(false)
      setBulkProcessingDialog(false)
    }
  }

  const handleBulkApproval = async () => {
    try {
      setProcessing(true)
      
      const response = await fetch('/api/admin/payroll/bulk-process', {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          action: 'bulk_approve',
          year: bulkOptions.year,
          month: bulkOptions.month
        })
      })

      const data = await response.json()

      if (data.success) {
        toast({
          title: "Success",
          description: `Approved ${data.data.approved_count} payroll records`
        })
        loadDashboardData()
      } else {
        throw new Error(data.error)
      }
    } catch (error) {
      console.error('Error in bulk approval:', error)
      toast({
        title: "Error",
        description: "Failed to approve payroll records",
        variant: "destructive"
      })
    } finally {
      setProcessing(false)
    }
  }

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <RefreshCw className="h-8 w-8 animate-spin" />
        <span className="ml-2">Loading dashboard...</span>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold">Enhanced Payroll Dashboard</h1>
          <p className="text-muted-foreground">
            {dashboardData?.overview.currentFiscalYear} - {dashboardData?.overview.currentPeriod}
          </p>
        </div>
        <div className="flex items-center space-x-2">
          <Button onClick={() => setBulkProcessingDialog(true)} disabled={processing}>
            <Calculator className="h-4 w-4 mr-2" />
            Bulk Process
          </Button>
          <Button variant="outline" onClick={handleBulkApproval} disabled={processing}>
            <CheckCircle className="h-4 w-4 mr-2" />
            Bulk Approve
          </Button>
          <Button variant="outline" onClick={loadDashboardData}>
            <RefreshCw className="h-4 w-4 mr-2" />
            Refresh
          </Button>
        </div>
      </div>

      {/* Overview Cards */}
      {dashboardData && (
        <>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            <CurrencyCard
              title="Total Payroll"
              amount={dashboardData.overview.totalPayrollAmount}
              subtitle="Current period"
              icon={<DollarSign className="h-4 w-4" />}
              variant="success"
            />
            
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Total Employees</CardTitle>
                <Users className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{dashboardData.overview.totalEmployees}</div>
                <p className="text-xs text-muted-foreground">
                  {dashboardData.overview.eligibleEmployees} eligible for payroll
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Processing Status</CardTitle>
                <BarChart3 className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{dashboardData.overview.processedEmployees}</div>
                <p className="text-xs text-muted-foreground">
                  {dashboardData.overview.pendingApprovals} pending approval
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Components</CardTitle>
                <Settings className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{dashboardData.componentsSummary.active_assignments}</div>
                <p className="text-xs text-muted-foreground">
                  Active allowance/deduction assignments
                </p>
              </CardContent>
            </Card>
          </div>

          {/* Processing Status Chart */}
          <Card>
            <CardHeader>
              <CardTitle>Payroll Processing Status</CardTitle>
              <CardDescription>Current period processing breakdown</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <span className="text-sm">Not Processed</span>
                  <Badge variant="secondary">{dashboardData.processingStatus.not_processed}</Badge>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-sm">Calculated</span>
                  <Badge variant="outline">{dashboardData.processingStatus.calculated}</Badge>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-sm">Approved</span>
                  <Badge variant="default">{dashboardData.processingStatus.approved}</Badge>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-sm">Processed</span>
                  <Badge variant="default">{dashboardData.processingStatus.processed}</Badge>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-sm">Paid</span>
                  <Badge variant="default">{dashboardData.processingStatus.paid}</Badge>
                </div>
              </div>
            </CardContent>
          </Card>
        </>
      )}

      {/* Bulk Processing Dialog */}
      <Dialog open={bulkProcessingDialog} onOpenChange={setBulkProcessingDialog}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Bulk Payroll Processing</DialogTitle>
            <DialogDescription>
              Process payroll for multiple employees at once
            </DialogDescription>
          </DialogHeader>
          <div className="space-y-4">
            <div className="grid grid-cols-2 gap-4">
              <div>
                <Label htmlFor="year">Year</Label>
                <Input
                  id="year"
                  type="number"
                  value={bulkOptions.year}
                  onChange={(e) => setBulkOptions(prev => ({ ...prev, year: parseInt(e.target.value) }))}
                />
              </div>
              <div>
                <Label htmlFor="month">Month</Label>
                <Input
                  id="month"
                  type="number"
                  min="1"
                  max="12"
                  value={bulkOptions.month}
                  onChange={(e) => setBulkOptions(prev => ({ ...prev, month: parseInt(e.target.value) }))}
                />
              </div>
            </div>
            <div className="flex items-center space-x-2">
              <input
                type="checkbox"
                id="dryRun"
                checked={bulkOptions.dryRun}
                onChange={(e) => setBulkOptions(prev => ({ ...prev, dryRun: e.target.checked }))}
              />
              <Label htmlFor="dryRun">Dry Run (calculate without saving)</Label>
            </div>
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setBulkProcessingDialog(false)}>
              Cancel
            </Button>
            <Button onClick={handleBulkProcessing} disabled={processing}>
              {processing ? 'Processing...' : bulkOptions.dryRun ? 'Run Calculation' : 'Process Payroll'}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  )
}
