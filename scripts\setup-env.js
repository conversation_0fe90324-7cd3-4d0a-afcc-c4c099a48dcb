#!/usr/bin/env node

// Interactive environment setup script
const readline = require('readline');
const fs = require('fs');
const path = require('path');

const rl = readline.createInterface({
  input: process.stdin,
  output: process.stdout
});

function askQuestion(question) {
  return new Promise((resolve) => {
    rl.question(question, (answer) => {
      resolve(answer.trim());
    });
  });
}

async function setupEnvironment() {
  console.log('🔧 Neon Database Environment Setup\n');
  
  console.log('📋 Instructions:');
  console.log('1. In the Neon console (opened in your browser):');
  console.log('   - Sign in or create an account');
  console.log('   - Create a new project or select existing one');
  console.log('   - Go to "Connection Details" or click "Connect"');
  console.log('   - Copy the connection string\n');
  
  console.log('💡 The connection string should look like:');
  console.log('   postgresql://username:<EMAIL>/dbname?sslmode=require\n');
  
  const connectionString = await askQuestion('🔗 Paste your Neon connection string here: ');
  
  if (!connectionString) {
    console.log('❌ No connection string provided. Exiting...');
    rl.close();
    return;
  }
  
  // Validate connection string format
  if (!connectionString.startsWith('postgresql://') || !connectionString.includes('neon.tech')) {
    console.log('⚠️  Warning: This doesn\'t look like a Neon connection string.');
    const proceed = await askQuestion('Do you want to continue anyway? (y/N): ');
    if (proceed.toLowerCase() !== 'y' && proceed.toLowerCase() !== 'yes') {
      console.log('❌ Setup cancelled.');
      rl.close();
      return;
    }
  }
  
  // Create .env.local content
  const envContent = `# Neon Database Configuration
DATABASE_URL="${connectionString}"

# Next.js Configuration
NODE_ENV="development"
NEXTAUTH_SECRET="your-nextauth-secret-here"

# Optional: Supabase Configuration (if using Supabase features)
# NEXT_PUBLIC_SUPABASE_URL="your-supabase-url"
# NEXT_PUBLIC_SUPABASE_ANON_KEY="your-supabase-anon-key"
`;
  
  try {
    // Write to .env.local
    fs.writeFileSync('.env.local', envContent);
    console.log('\n✅ .env.local file updated successfully!');
    
    // Mask the connection string for display
    const maskedConnection = connectionString.replace(/:[^:@]*@/, ':****@');
    console.log('🔗 Connection string set:', maskedConnection);
    
    console.log('\n🎉 Environment setup complete!');
    console.log('\n📋 Next steps:');
    console.log('1. Test connection: node scripts/test-connection.js');
    console.log('2. Setup database: node scripts/setup-database.js');
    console.log('3. Test auth system: node scripts/test-auth-system.js');
    console.log('4. Start application: npm run dev');
    
  } catch (error) {
    console.error('❌ Error writing .env.local file:', error.message);
  }
  
  rl.close();
}

setupEnvironment();
