#!/usr/bin/env node

// Quick script to check users in database
let neon, dotenv;

try {
  ({ neon } = require('@neondatabase/serverless'));
  dotenv = require('dotenv');
  dotenv.config({ path: '.env.local' });
} catch (error) {
  console.log('⚠️  Required packages not installed yet.');
  process.exit(1);
}

async function checkUsers() {
  console.log('🔍 Checking users in database...\n');
  
  if (!process.env.DATABASE_URL) {
    console.error('❌ DATABASE_URL not set');
    process.exit(1);
  }
  
  try {
    const sql = neon(process.env.DATABASE_URL);
    
    // Check all users
    const users = await sql`SELECT email, role, is_active, created_at FROM users ORDER BY created_at`;
    
    console.log(`📊 Found ${users.length} users:`);
    if (users.length === 0) {
      console.log('   (No users found)');
    } else {
      users.forEach((user, index) => {
        console.log(`   ${index + 1}. ${user.email} (${user.role}) ${user.is_active ? '✅' : '❌'}`);
      });
    }
    
    // Check permissions
    const permissions = await sql`SELECT COUNT(*) as count FROM permissions`;
    console.log(`\n📊 Permissions: ${permissions[0].count}`);
    
    // Check role_permissions
    const rolePerms = await sql`SELECT COUNT(*) as count FROM role_permissions`;
    console.log(`📊 Role permissions: ${rolePerms[0].count}`);
    
    // Check if we can manually insert a test user
    if (users.length === 0) {
      console.log('\n🔄 Attempting to create test admin user...');
      
      const bcrypt = require('bcryptjs');
      const passwordHash = await bcrypt.hash('admin123', 12);
      
      await sql`
        INSERT INTO users (
          email, password_hash, full_name, role, department, position, is_active, email_verified
        ) VALUES (
          '<EMAIL>', ${passwordHash}, 'System Administrator', 'admin', 'IT', 'System Administrator', true, true
        )
      `;
      
      console.log('✅ Test admin user created successfully!');
      console.log('📧 Email: <EMAIL>');
      console.log('🔑 Password: admin123');
    }
    
  } catch (error) {
    console.error('❌ Error:', error.message);
    
    if (error.message.includes('duplicate key')) {
      console.log('💡 User already exists - this is normal');
    }
  }
}

checkUsers();
