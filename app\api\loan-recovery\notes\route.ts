import { NextRequest, NextResponse } from "next/server"
import { AuthService } from "@/lib/auth-utils"
import { serverDb } from "@/lib/server-db"
import { z } from "zod"

// Validation schema for note creation
const createNoteSchema = z.object({
  loan_id: z.string().uuid("Invalid loan ID"),
  note_type: z.enum(["general", "call", "email", "meeting"]).default("general"),
  content: z.string().min(1, "Content is required").max(2000, "Content too long"),
})

export async function GET(request: NextRequest) {
  try {
    const sessionToken = request.cookies.get("session-token")?.value

    if (!sessionToken) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 })
    }

    const user = await AuthService.verifySession(sessionToken)

    if (!user || !["admin", "hr_manager"].includes(user.role)) {
      return NextResponse.json({ error: "Forbidden" }, { status: 403 })
    }

    const { searchParams } = new URL(request.url)
    const loan_id = searchParams.get("loan_id")
    const note_type = searchParams.get("note_type")
    const limit = parseInt(searchParams.get("limit") || "50")
    const offset = parseInt(searchParams.get("offset") || "0")

    let whereConditions = []
    let params = []

    if (loan_id) {
      whereConditions.push(`lcn.loan_id = $${params.length + 1}`)
      params.push(loan_id)
    }

    if (note_type) {
      whereConditions.push(`lcn.note_type = $${params.length + 1}`)
      params.push(note_type)
    }

    const whereClause = whereConditions.length > 0 
      ? `WHERE ${whereConditions.join(" AND ")}`
      : ""

    const notes = await serverDb.sql`
      SELECT 
        lcn.*,
        u.full_name as created_by_name,
        c.name as customer_name,
        lr.loan_amount,
        lr.current_stage
      FROM loan_conversation_notes lcn
      LEFT JOIN users u ON lcn.created_by = u.id
      LEFT JOIN loan_records lr ON lcn.loan_id = lr.id
      LEFT JOIN loan_recovery_customers c ON lr.customer_id = c.id
      ${whereClause ? serverDb.sql([whereClause], ...params) : serverDb.sql``}
      ORDER BY lcn.created_at DESC
      LIMIT ${limit} OFFSET ${offset}
    `

    return NextResponse.json({
      success: true,
      notes,
    })
  } catch (error) {
    console.error("Notes API error:", error)
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    )
  }
}

export async function POST(request: NextRequest) {
  try {
    const sessionToken = request.cookies.get("session-token")?.value

    if (!sessionToken) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 })
    }

    const user = await AuthService.verifySession(sessionToken)

    if (!user || !["admin", "hr_manager"].includes(user.role)) {
      return NextResponse.json({ error: "Forbidden" }, { status: 403 })
    }

    const body = await request.json()
    const validatedData = createNoteSchema.parse(body)

    // Verify loan exists
    const loan = await serverDb.sql`
      SELECT id FROM loan_records WHERE id = ${validatedData.loan_id}
    `

    if (loan.length === 0) {
      return NextResponse.json(
        { error: "Loan not found" },
        { status: 400 }
      )
    }

    const note = await serverDb.sql`
      INSERT INTO loan_conversation_notes (
        loan_id, note_type, content, created_by
      )
      VALUES (
        ${validatedData.loan_id},
        ${validatedData.note_type},
        ${validatedData.content},
        ${user.id}
      )
      RETURNING *
    `

    // Get note with user details
    const noteWithDetails = await serverDb.sql`
      SELECT 
        lcn.*,
        u.full_name as created_by_name
      FROM loan_conversation_notes lcn
      LEFT JOIN users u ON lcn.created_by = u.id
      WHERE lcn.id = ${note[0].id}
    `

    return NextResponse.json({
      success: true,
      note: noteWithDetails[0],
    })
  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: "Validation error", details: error.errors },
        { status: 400 }
      )
    }

    console.error("Create note API error:", error)
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    )
  }
}
