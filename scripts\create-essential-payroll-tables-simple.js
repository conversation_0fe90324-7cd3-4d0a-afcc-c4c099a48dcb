// Create essential payroll tables for auto-population functionality
const { neon } = require('@neondatabase/serverless');
require('dotenv').config({ path: '.env.local' });

async function createEssentialPayrollTables() {
  try {
    console.log('Creating essential payroll tables...\n');

    const sql = neon(process.env.DATABASE_URL);

    // 1. Add missing columns to users table
    console.log('1. Adding missing columns to users table...');
    
    const userColumns = [
      'ALTER TABLE users ADD COLUMN IF NOT EXISTS employee_type VARCHAR(20) DEFAULT \'full_time\' CHECK (employee_type IN (\'full_time\', \'part_time\', \'contract\', \'intern\', \'consultant\'));',
      'ALTER TABLE users ADD COLUMN IF NOT EXISTS employee_category VARCHAR(20) DEFAULT \'regular\' CHECK (employee_category IN (\'regular\', \'probation\', \'temporary\', \'seasonal\', \'project_based\'));',
      'ALTER TABLE users ADD COLUMN IF NOT EXISTS tax_identification_number VARCHAR(50);',
      'ALTER TABLE users ADD COLUMN IF NOT EXISTS citizenship_number VARCHAR(50);',
      'ALTER TABLE users ADD COLUMN IF NOT EXISTS pan_number VARCHAR(20);'
    ];

    for (const columnSQL of userColumns) {
      try {
        await sql.query(columnSQL);
        console.log('  ✅ Added column successfully');
      } catch (error) {
        if (error.message.includes('already exists')) {
          console.log('  ✅ Column already exists');
        } else {
          console.log('  ❌ Error:', error.message);
        }
      }
    }

    // 2. Create employee_bank_accounts table
    console.log('\n2. Creating employee_bank_accounts table...');
    const bankAccountsSQL = `
      CREATE TABLE IF NOT EXISTS employee_bank_accounts (
        id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
        user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
        bank_name VARCHAR(100) NOT NULL,
        bank_branch VARCHAR(100),
        account_number VARCHAR(50) NOT NULL,
        account_holder_name VARCHAR(100) NOT NULL,
        account_type VARCHAR(20) DEFAULT 'savings' CHECK (account_type IN ('savings', 'current', 'salary')),
        is_primary BOOLEAN DEFAULT FALSE,
        is_active BOOLEAN DEFAULT TRUE,
        created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
        updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
      );
    `;

    try {
      await sql.query(bankAccountsSQL);
      console.log('  ✅ employee_bank_accounts table created');
    } catch (error) {
      console.log('  ❌ Error:', error.message);
    }

    // 3. Create allowance_assignments table
    console.log('\n3. Creating allowance_assignments table...');
    const allowancesSQL = `
      CREATE TABLE IF NOT EXISTS allowance_assignments (
        id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
        user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
        allowance_type VARCHAR(50) NOT NULL CHECK (allowance_type IN ('travelling', 'phone', 'meal', 'transport', 'medical', 'education', 'custom')),
        allowance_name VARCHAR(100) NOT NULL,
        allowance_amount DECIMAL(10,2) NOT NULL DEFAULT 0,
        is_percentage BOOLEAN DEFAULT FALSE,
        percentage_base VARCHAR(20) DEFAULT 'base_salary' CHECK (percentage_base IN ('base_salary', 'gross_pay')),
        is_taxable BOOLEAN DEFAULT TRUE,
        effective_from DATE NOT NULL DEFAULT CURRENT_DATE,
        effective_to DATE,
        is_active BOOLEAN DEFAULT TRUE,
        created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
        updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
      );
    `;

    try {
      await sql.query(allowancesSQL);
      console.log('  ✅ allowance_assignments table created');
    } catch (error) {
      console.log('  ❌ Error:', error.message);
    }

    // 4. Create deduction_approvals table
    console.log('\n4. Creating deduction_approvals table...');
    const deductionsSQL = `
      CREATE TABLE IF NOT EXISTS deduction_approvals (
        id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
        user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
        component_id UUID REFERENCES payroll_components_master(id),
        deduction_name VARCHAR(100) NOT NULL,
        deduction_amount DECIMAL(10,2) NOT NULL DEFAULT 0,
        is_percentage BOOLEAN DEFAULT FALSE,
        percentage_base VARCHAR(20) DEFAULT 'base_salary' CHECK (percentage_base IN ('base_salary', 'gross_pay', 'net_pay')),
        is_taxable BOOLEAN DEFAULT FALSE,
        effective_from DATE NOT NULL DEFAULT CURRENT_DATE,
        effective_to DATE,
        status VARCHAR(20) DEFAULT 'approved' CHECK (status IN ('pending', 'approved', 'rejected', 'cancelled')),
        is_active BOOLEAN DEFAULT TRUE,
        created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
        updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
      );
    `;

    try {
      await sql.query(deductionsSQL);
      console.log('  ✅ deduction_approvals table created');
    } catch (error) {
      console.log('  ❌ Error:', error.message);
    }

    // 5. Create indexes for performance
    console.log('\n5. Creating indexes...');
    const indexes = [
      'CREATE INDEX IF NOT EXISTS idx_employee_bank_accounts_user ON employee_bank_accounts(user_id);',
      'CREATE INDEX IF NOT EXISTS idx_allowance_assignments_user ON allowance_assignments(user_id);',
      'CREATE INDEX IF NOT EXISTS idx_allowance_assignments_active ON allowance_assignments(user_id, is_active) WHERE is_active = TRUE;',
      'CREATE INDEX IF NOT EXISTS idx_deduction_approvals_user ON deduction_approvals(user_id);',
      'CREATE INDEX IF NOT EXISTS idx_deduction_approvals_active ON deduction_approvals(user_id, is_active) WHERE is_active = TRUE;'
    ];

    for (const indexSQL of indexes) {
      try {
        await sql.query(indexSQL);
        console.log('  ✅ Index created');
      } catch (error) {
        console.log('  ❌ Error:', error.message);
      }
    }

    // 6. Insert some sample data for testing
    console.log('\n6. Inserting sample allowances and deductions...');
    
    // Get the first user to add sample data
    const users = await sql`SELECT id, full_name FROM users WHERE is_active = TRUE LIMIT 1`;
    
    if (users.length > 0) {
      const userId = users[0].id;
      console.log(`  Adding sample data for user: ${users[0].full_name}`);

      // Sample allowances
      const sampleAllowances = [
        { type: 'transport', name: 'Transport Allowance', amount: 5000, is_percentage: false },
        { type: 'phone', name: 'Phone Allowance', amount: 2000, is_percentage: false },
        { type: 'meal', name: 'Meal Allowance', amount: 3000, is_percentage: false }
      ];

      for (const allowance of sampleAllowances) {
        try {
          await sql`
            INSERT INTO allowance_assignments (user_id, allowance_type, allowance_name, allowance_amount, is_percentage)
            VALUES (${userId}, ${allowance.type}, ${allowance.name}, ${allowance.amount}, ${allowance.is_percentage})
            ON CONFLICT DO NOTHING
          `;
          console.log(`    ✅ Added ${allowance.name}`);
        } catch (error) {
          console.log(`    ❌ Error adding ${allowance.name}:`, error.message);
        }
      }

      // Sample deductions
      const sampleDeductions = [
        { name: 'Provident Fund', amount: 10, is_percentage: true, percentage_base: 'base_salary' },
        { name: 'Late Penalty', amount: 500, is_percentage: false }
      ];

      for (const deduction of sampleDeductions) {
        try {
          await sql`
            INSERT INTO deduction_approvals (user_id, deduction_name, deduction_amount, is_percentage, percentage_base)
            VALUES (${userId}, ${deduction.name}, ${deduction.amount}, ${deduction.is_percentage}, ${deduction.percentage_base})
            ON CONFLICT DO NOTHING
          `;
          console.log(`    ✅ Added ${deduction.name}`);
        } catch (error) {
          console.log(`    ❌ Error adding ${deduction.name}:`, error.message);
        }
      }
    }

    // 7. Verify tables were created
    console.log('\n7. Verifying table creation...');
    const tables = ['employee_bank_accounts', 'allowance_assignments', 'deduction_approvals'];

    for (const table of tables) {
      try {
        const result = await sql`
          SELECT EXISTS (
            SELECT FROM information_schema.tables 
            WHERE table_schema = 'public' 
            AND table_name = ${table}
          );
        `;
        
        const exists = result[0].exists;
        console.log(`  Table ${table}: ${exists ? '✅ EXISTS' : '❌ MISSING'}`);
        
        if (exists) {
          const count = await sql`SELECT COUNT(*) as count FROM ${sql(table)}`;
          console.log(`    Records: ${count[0].count}`);
        }
        
      } catch (error) {
        console.log(`  ❌ Error checking table ${table}:`, error.message);
      }
    }

    console.log('\n✅ Essential payroll tables setup completed!');

  } catch (error) {
    console.error('❌ Error creating tables:', error.message);
  }
}

createEssentialPayrollTables();
