// Payslip Viewer Component
// Phase 4: User Interface Development - Payslip Generation & Viewer

"use client"

import React, { useState, useRef } from 'react'
import { 
  Download, 
  Print, 
  Eye, 
  Calendar, 
  User, 
  Building, 
  FileText,
  Share2,
  Mail
} from 'lucide-react'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Separator } from '@/components/ui/separator'
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog'
import { CurrencyDisplay } from '@/components/ui/currency-display'
import { NepaliCalendar } from '@/lib/nepali-calendar'
import { nprFormatter } from '@/lib/currency-formatter'

interface PayslipData {
  id: string
  employeeId: string
  employeeName: string
  employeeNameNepali?: string
  designation: string
  department: string
  payPeriod: string
  bsPayPeriod: string
  periodStart: string
  periodEnd: string
  payDate: string
  bsPayDate: string
  
  // Salary Details
  baseSalary: number
  overtimePay: number
  allowances: {
    id: string
    name: string
    nameNepali?: string
    amount: number
    type: 'fixed' | 'percentage'
  }[]
  bonuses: number
  grossPay: number
  
  // Deductions
  deductions: {
    id: string
    name: string
    nameNepali?: string
    amount: number
    type: 'fixed' | 'percentage'
  }[]
  taxes: number
  totalDeductions: number
  netPay: number
  
  // Attendance
  workingDays: number
  attendedDays: number
  absentDays: number
  overtimeHours: number
  lateHours: number
  
  // Company Details
  company: {
    name: string
    nameNepali?: string
    address: string
    addressNepali?: string
    phone: string
    email: string
    logo?: string
    panNumber?: string
    registrationNumber?: string
  }
  
  // Additional Info
  bankAccount?: {
    accountNumber: string
    bankName: string
    branchName: string
  }
  remarks?: string
  status: 'draft' | 'processed' | 'paid'
}

interface PayslipViewerProps {
  payslip: PayslipData
  showActions?: boolean
  printMode?: boolean
  language?: 'en' | 'ne'
}

export function PayslipViewer({ 
  payslip, 
  showActions = true, 
  printMode = false,
  language = 'en' 
}: PayslipViewerProps) {
  const [isPreviewOpen, setIsPreviewOpen] = useState(false)
  const printRef = useRef<HTMLDivElement>(null)

  const handlePrint = () => {
    if (printRef.current) {
      const printWindow = window.open('', '_blank')
      if (printWindow) {
        printWindow.document.write(`
          <!DOCTYPE html>
          <html>
            <head>
              <title>Payslip - ${payslip.employeeName} - ${payslip.payPeriod}</title>
              <style>
                body { font-family: Arial, sans-serif; margin: 0; padding: 20px; }
                .payslip { max-width: 800px; margin: 0 auto; }
                .header { text-align: center; margin-bottom: 30px; }
                .company-logo { max-height: 60px; margin-bottom: 10px; }
                .section { margin-bottom: 20px; }
                .section-title { font-weight: bold; margin-bottom: 10px; border-bottom: 1px solid #ccc; padding-bottom: 5px; }
                .row { display: flex; justify-content: space-between; margin-bottom: 5px; }
                .table { width: 100%; border-collapse: collapse; margin-bottom: 20px; }
                .table th, .table td { border: 1px solid #ddd; padding: 8px; text-align: left; }
                .table th { background-color: #f5f5f5; }
                .total-row { font-weight: bold; background-color: #f9f9f9; }
                .signature-section { margin-top: 40px; display: flex; justify-content: space-between; }
                .signature-box { text-align: center; width: 200px; }
                .signature-line { border-top: 1px solid #000; margin-top: 40px; padding-top: 5px; }
                @media print {
                  body { margin: 0; }
                  .no-print { display: none; }
                }
              </style>
            </head>
            <body>
              ${printRef.current.innerHTML}
            </body>
          </html>
        `)
        printWindow.document.close()
        printWindow.print()
      }
    }
  }

  const handleDownload = async () => {
    // In a real implementation, this would generate a PDF
    // For now, we'll simulate the download
    const element = document.createElement('a')
    const file = new Blob([generatePayslipText()], { type: 'text/plain' })
    element.href = URL.createObjectURL(file)
    element.download = `payslip-${payslip.employeeId}-${payslip.payPeriod.replace(/\s+/g, '-')}.txt`
    document.body.appendChild(element)
    element.click()
    document.body.removeChild(element)
  }

  const generatePayslipText = () => {
    return `
PAYSLIP
${payslip.company.name}
${payslip.company.address}

Employee: ${payslip.employeeName} (${payslip.employeeId})
Department: ${payslip.department}
Designation: ${payslip.designation}
Pay Period: ${payslip.payPeriod} (${payslip.bsPayPeriod})

EARNINGS:
Base Salary: ${nprFormatter.formatCurrency(payslip.baseSalary)}
Overtime Pay: ${nprFormatter.formatCurrency(payslip.overtimePay)}
${payslip.allowances.map(a => `${a.name}: ${nprFormatter.formatCurrency(a.amount)}`).join('\n')}
Bonuses: ${nprFormatter.formatCurrency(payslip.bonuses)}
Gross Pay: ${nprFormatter.formatCurrency(payslip.grossPay)}

DEDUCTIONS:
${payslip.deductions.map(d => `${d.name}: ${nprFormatter.formatCurrency(d.amount)}`).join('\n')}
Taxes: ${nprFormatter.formatCurrency(payslip.taxes)}
Total Deductions: ${nprFormatter.formatCurrency(payslip.totalDeductions)}

NET PAY: ${nprFormatter.formatCurrency(payslip.netPay)}

ATTENDANCE:
Working Days: ${payslip.workingDays}
Attended Days: ${payslip.attendedDays}
Absent Days: ${payslip.absentDays}
Overtime Hours: ${payslip.overtimeHours}

Generated on: ${new Date().toLocaleDateString()}
    `.trim()
  }

  const handleShare = async () => {
    if (navigator.share) {
      try {
        await navigator.share({
          title: `Payslip - ${payslip.employeeName}`,
          text: `Payslip for ${payslip.payPeriod}`,
          url: window.location.href
        })
      } catch (error) {
        console.log('Error sharing:', error)
      }
    } else {
      // Fallback: copy to clipboard
      navigator.clipboard.writeText(window.location.href)
    }
  }

  return (
    <div className="space-y-4">
      {/* Action Buttons */}
      {showActions && !printMode && (
        <div className="flex items-center gap-2 mb-4">
          <Dialog open={isPreviewOpen} onOpenChange={setIsPreviewOpen}>
            <DialogTrigger asChild>
              <Button variant="outline">
                <Eye className="h-4 w-4 mr-2" />
                Preview
              </Button>
            </DialogTrigger>
            <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
              <DialogHeader>
                <DialogTitle>Payslip Preview</DialogTitle>
              </DialogHeader>
              <PayslipViewer payslip={payslip} showActions={false} printMode={true} />
            </DialogContent>
          </Dialog>
          
          <Button variant="outline" onClick={handlePrint}>
            <Print className="h-4 w-4 mr-2" />
            Print
          </Button>
          
          <Button variant="outline" onClick={handleDownload}>
            <Download className="h-4 w-4 mr-2" />
            Download
          </Button>
          
          <Button variant="outline" onClick={handleShare}>
            <Share2 className="h-4 w-4 mr-2" />
            Share
          </Button>
        </div>
      )}

      {/* Payslip Content */}
      <div ref={printRef} className={`payslip ${printMode ? 'print-mode' : ''}`}>
        <Card className="max-w-4xl mx-auto">
          <CardContent className="p-8">
            {/* Header */}
            <div className="text-center mb-8">
              {payslip.company.logo && (
                <img 
                  src={payslip.company.logo} 
                  alt="Company Logo" 
                  className="h-16 mx-auto mb-4"
                />
              )}
              <h1 className="text-2xl font-bold text-gray-900">
                {language === 'ne' && payslip.company.nameNepali 
                  ? payslip.company.nameNepali 
                  : payslip.company.name}
              </h1>
              <p className="text-gray-600">
                {language === 'ne' && payslip.company.addressNepali 
                  ? payslip.company.addressNepali 
                  : payslip.company.address}
              </p>
              <p className="text-gray-600">
                Phone: {payslip.company.phone} | Email: {payslip.company.email}
              </p>
              {payslip.company.panNumber && (
                <p className="text-gray-600">PAN: {payslip.company.panNumber}</p>
              )}
            </div>

            <div className="text-center mb-8">
              <h2 className="text-xl font-semibold text-gray-900 mb-2">
                {language === 'ne' ? 'तलब पर्चा' : 'PAYSLIP'}
              </h2>
              <Badge variant="outline" className="text-sm">
                {payslip.payPeriod} ({payslip.bsPayPeriod})
              </Badge>
            </div>

            {/* Employee Information */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-8">
              <div className="space-y-3">
                <h3 className="font-semibold text-gray-900 border-b pb-2">
                  {language === 'ne' ? 'कर्मचारी विवरण' : 'Employee Details'}
                </h3>
                <div className="space-y-2 text-sm">
                  <div className="flex justify-between">
                    <span className="text-gray-600">
                      {language === 'ne' ? 'नाम:' : 'Name:'}
                    </span>
                    <span className="font-medium">
                      {language === 'ne' && payslip.employeeNameNepali 
                        ? payslip.employeeNameNepali 
                        : payslip.employeeName}
                    </span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600">
                      {language === 'ne' ? 'कर्मचारी ID:' : 'Employee ID:'}
                    </span>
                    <span className="font-medium">{payslip.employeeId}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600">
                      {language === 'ne' ? 'पद:' : 'Designation:'}
                    </span>
                    <span className="font-medium">{payslip.designation}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600">
                      {language === 'ne' ? 'विभाग:' : 'Department:'}
                    </span>
                    <span className="font-medium">{payslip.department}</span>
                  </div>
                </div>
              </div>

              <div className="space-y-3">
                <h3 className="font-semibold text-gray-900 border-b pb-2">
                  {language === 'ne' ? 'तलब अवधि' : 'Pay Period'}
                </h3>
                <div className="space-y-2 text-sm">
                  <div className="flex justify-between">
                    <span className="text-gray-600">
                      {language === 'ne' ? 'अवधि:' : 'Period:'}
                    </span>
                    <span className="font-medium">{payslip.payPeriod}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600">
                      {language === 'ne' ? 'बि.सं. अवधि:' : 'BS Period:'}
                    </span>
                    <span className="font-medium">{payslip.bsPayPeriod}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600">
                      {language === 'ne' ? 'तलब मिति:' : 'Pay Date:'}
                    </span>
                    <span className="font-medium">
                      {new Date(payslip.payDate).toLocaleDateString()} ({payslip.bsPayDate})
                    </span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600">
                      {language === 'ne' ? 'स्थिति:' : 'Status:'}
                    </span>
                    <Badge variant={payslip.status === 'paid' ? 'default' : 'secondary'}>
                      {payslip.status}
                    </Badge>
                  </div>
                </div>
              </div>
            </div>

            <Separator className="my-6" />

            {/* Earnings and Deductions */}
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8">
              {/* Earnings */}
              <div>
                <h3 className="font-semibold text-gray-900 mb-4 border-b pb-2">
                  {language === 'ne' ? 'आम्दानी' : 'EARNINGS'}
                </h3>
                <div className="space-y-3">
                  <div className="flex justify-between text-sm">
                    <span>{language === 'ne' ? 'आधारभूत तलब:' : 'Base Salary:'}</span>
                    <span className="font-medium">
                      <CurrencyDisplay amount={payslip.baseSalary} size="sm" />
                    </span>
                  </div>
                  
                  {payslip.overtimePay > 0 && (
                    <div className="flex justify-between text-sm">
                      <span>{language === 'ne' ? 'ओभरटाइम:' : 'Overtime Pay:'}</span>
                      <span className="font-medium">
                        <CurrencyDisplay amount={payslip.overtimePay} size="sm" />
                      </span>
                    </div>
                  )}
                  
                  {payslip.allowances.map((allowance) => (
                    <div key={allowance.id} className="flex justify-between text-sm">
                      <span>
                        {language === 'ne' && allowance.nameNepali 
                          ? allowance.nameNepali 
                          : allowance.name}:
                      </span>
                      <span className="font-medium">
                        <CurrencyDisplay amount={allowance.amount} size="sm" />
                      </span>
                    </div>
                  ))}
                  
                  {payslip.bonuses > 0 && (
                    <div className="flex justify-between text-sm">
                      <span>{language === 'ne' ? 'बोनस:' : 'Bonuses:'}</span>
                      <span className="font-medium">
                        <CurrencyDisplay amount={payslip.bonuses} size="sm" />
                      </span>
                    </div>
                  )}
                  
                  <Separator />
                  <div className="flex justify-between font-semibold">
                    <span>{language === 'ne' ? 'कुल आम्दानी:' : 'Gross Pay:'}</span>
                    <span>
                      <CurrencyDisplay amount={payslip.grossPay} />
                    </span>
                  </div>
                </div>
              </div>

              {/* Deductions */}
              <div>
                <h3 className="font-semibold text-gray-900 mb-4 border-b pb-2">
                  {language === 'ne' ? 'कटौती' : 'DEDUCTIONS'}
                </h3>
                <div className="space-y-3">
                  {payslip.deductions.map((deduction) => (
                    <div key={deduction.id} className="flex justify-between text-sm">
                      <span>
                        {language === 'ne' && deduction.nameNepali 
                          ? deduction.nameNepali 
                          : deduction.name}:
                      </span>
                      <span className="font-medium">
                        <CurrencyDisplay amount={deduction.amount} size="sm" />
                      </span>
                    </div>
                  ))}
                  
                  {payslip.taxes > 0 && (
                    <div className="flex justify-between text-sm">
                      <span>{language === 'ne' ? 'कर:' : 'Taxes:'}</span>
                      <span className="font-medium">
                        <CurrencyDisplay amount={payslip.taxes} size="sm" />
                      </span>
                    </div>
                  )}
                  
                  <Separator />
                  <div className="flex justify-between font-semibold">
                    <span>{language === 'ne' ? 'कुल कटौती:' : 'Total Deductions:'}</span>
                    <span>
                      <CurrencyDisplay amount={payslip.totalDeductions} />
                    </span>
                  </div>
                </div>
              </div>
            </div>

            {/* Net Pay */}
            <div className="bg-green-50 border border-green-200 rounded-lg p-6 mb-8">
              <div className="flex justify-between items-center">
                <span className="text-lg font-semibold text-green-800">
                  {language === 'ne' ? 'खुद तलब:' : 'NET PAY:'}
                </span>
                <span className="text-2xl font-bold text-green-800">
                  <CurrencyDisplay amount={payslip.netPay} />
                </span>
              </div>
              <div className="text-sm text-green-700 mt-2">
                {language === 'ne' ? 'शब्दमा:' : 'In Words:'} {nprFormatter.convertToWords(payslip.netPay, 'en')}
              </div>
            </div>

            {/* Attendance Summary */}
            <div className="mb-8">
              <h3 className="font-semibold text-gray-900 mb-4 border-b pb-2">
                {language === 'ne' ? 'उपस्थिति सारांश' : 'ATTENDANCE SUMMARY'}
              </h3>
              <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
                <div className="text-center">
                  <div className="font-semibold text-lg">{payslip.workingDays}</div>
                  <div className="text-gray-600">
                    {language === 'ne' ? 'कार्य दिन' : 'Working Days'}
                  </div>
                </div>
                <div className="text-center">
                  <div className="font-semibold text-lg text-green-600">{payslip.attendedDays}</div>
                  <div className="text-gray-600">
                    {language === 'ne' ? 'उपस्थित दिन' : 'Attended Days'}
                  </div>
                </div>
                <div className="text-center">
                  <div className="font-semibold text-lg text-red-600">{payslip.absentDays}</div>
                  <div className="text-gray-600">
                    {language === 'ne' ? 'अनुपस्थित दिन' : 'Absent Days'}
                  </div>
                </div>
                <div className="text-center">
                  <div className="font-semibold text-lg text-blue-600">{payslip.overtimeHours}</div>
                  <div className="text-gray-600">
                    {language === 'ne' ? 'ओभरटाइम घण्टा' : 'Overtime Hours'}
                  </div>
                </div>
              </div>
            </div>

            {/* Bank Details */}
            {payslip.bankAccount && (
              <div className="mb-8">
                <h3 className="font-semibold text-gray-900 mb-4 border-b pb-2">
                  {language === 'ne' ? 'बैंक विवरण' : 'BANK DETAILS'}
                </h3>
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
                  <div>
                    <span className="text-gray-600">
                      {language === 'ne' ? 'खाता नम्बर:' : 'Account Number:'}
                    </span>
                    <div className="font-medium">{payslip.bankAccount.accountNumber}</div>
                  </div>
                  <div>
                    <span className="text-gray-600">
                      {language === 'ne' ? 'बैंक:' : 'Bank:'}
                    </span>
                    <div className="font-medium">{payslip.bankAccount.bankName}</div>
                  </div>
                  <div>
                    <span className="text-gray-600">
                      {language === 'ne' ? 'शाखा:' : 'Branch:'}
                    </span>
                    <div className="font-medium">{payslip.bankAccount.branchName}</div>
                  </div>
                </div>
              </div>
            )}

            {/* Remarks */}
            {payslip.remarks && (
              <div className="mb-8">
                <h3 className="font-semibold text-gray-900 mb-2">
                  {language === 'ne' ? 'टिप्पणी:' : 'Remarks:'}
                </h3>
                <p className="text-sm text-gray-600 bg-gray-50 p-3 rounded">
                  {payslip.remarks}
                </p>
              </div>
            )}

            {/* Footer */}
            <div className="mt-12 pt-8 border-t">
              <div className="flex justify-between items-end">
                <div className="text-center">
                  <div className="w-48 border-t border-gray-400 pt-2">
                    <div className="text-sm text-gray-600">
                      {language === 'ne' ? 'कर्मचारी हस्ताक्षर' : 'Employee Signature'}
                    </div>
                  </div>
                </div>
                
                <div className="text-center">
                  <div className="w-48 border-t border-gray-400 pt-2">
                    <div className="text-sm text-gray-600">
                      {language === 'ne' ? 'अधिकृत हस्ताक्षर' : 'Authorized Signature'}
                    </div>
                  </div>
                </div>
              </div>
              
              <div className="text-center mt-8 text-xs text-gray-500">
                {language === 'ne' 
                  ? 'यो कम्प्युटर जेनेरेट गरिएको पेस्लिप हो र हस्ताक्षरको आवश्यकता छैन।'
                  : 'This is a computer generated payslip and does not require signature.'
                }
              </div>
              
              <div className="text-center mt-2 text-xs text-gray-500">
                {language === 'ne' 
                  ? `मुद्रण मिति: ${new Date().toLocaleDateString()}`
                  : `Generated on: ${new Date().toLocaleDateString()}`
                }
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}
