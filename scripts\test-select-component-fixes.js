#!/usr/bin/env node

/**
 * Test script to verify Select component fixes
 * This script checks for empty string values in SelectItem components
 */

const fs = require('fs');

async function testSelectComponentFixes() {
  console.log('🧪 Testing Select Component Fixes\n');
  console.log('=' .repeat(60));
  
  let allTestsPassed = true;
  
  function runTest(testName, testFunction) {
    console.log(`\n📋 ${testName}`);
    console.log('-' .repeat(40));
    try {
      const result = testFunction();
      if (result) {
        console.log('✅ PASSED');
      } else {
        console.log('❌ FAILED');
        allTestsPassed = false;
      }
      return result;
    } catch (error) {
      console.log(`❌ ERROR: ${error.message}`);
      allTestsPassed = false;
      return false;
    }
  }
  
  // Test 1: Check TaskModal component fixes
  runTest('TaskModal Component - Employee Assignment SelectItems', () => {
    const content = fs.readFileSync('components/task-modal.tsx', 'utf8');
    
    // Check for fixed values
    if (content.includes('value="loading"') && 
        content.includes('value="no-employees"') &&
        !content.includes('<SelectItem value="" disabled>')) {
      console.log('   ✅ Empty string values replaced with meaningful values');
      console.log('   ✅ Loading state: "loading"');
      console.log('   ✅ No employees state: "no-employees"');
      return true;
    } else {
      console.log('   ❌ Still contains empty string values in SelectItem');
      return false;
    }
  });
  
  // Test 2: Check Enhanced TaskModal component fixes
  runTest('Enhanced TaskModal Component - Project Selection', () => {
    const content = fs.readFileSync('components/enhanced-task-modal.tsx', 'utf8');
    
    if (content.includes('value="no-project"') && 
        content.includes('value === "no-project" ? undefined : value') &&
        !content.includes('<SelectItem value="">No project</SelectItem>')) {
      console.log('   ✅ Empty string value replaced with "no-project"');
      console.log('   ✅ Proper value handling logic implemented');
      return true;
    } else {
      console.log('   ❌ Still contains empty string values or missing logic');
      return false;
    }
  });
  
  // Test 3: Check Task Search Filter component fixes
  runTest('Task Search Filter Component - Filter SelectItems', () => {
    const content = fs.readFileSync('components/task-search-filter.tsx', 'utf8');
    
    if (content.includes('value="all-statuses"') && 
        content.includes('value="all-priorities"') &&
        content.includes('value="all-projects"') &&
        !content.includes('<SelectItem value="">All ')) {
      console.log('   ✅ All empty string values replaced');
      console.log('   ✅ Status filter: "all-statuses"');
      console.log('   ✅ Priority filter: "all-priorities"');
      console.log('   ✅ Project filter: "all-projects"');
      return true;
    } else {
      console.log('   ❌ Still contains empty string values in filter SelectItems');
      return false;
    }
  });
  
  // Test 4: Check updateFilter function logic
  runTest('Task Search Filter - updateFilter Logic', () => {
    const content = fs.readFileSync('components/task-search-filter.tsx', 'utf8');
    
    if (content.includes('value.startsWith(\'all-\') ? undefined : value') &&
        content.includes('const normalizedValue = value && value.startsWith(\'all-\') ? undefined : value')) {
      console.log('   ✅ updateFilter function handles "all-*" values correctly');
      console.log('   ✅ Converts "all-*" values to undefined for proper filtering');
      return true;
    } else {
      console.log('   ❌ updateFilter function not properly updated');
      return false;
    }
  });
  
  // Test 5: Check Select value props
  runTest('Task Search Filter - Select Value Props', () => {
    const content = fs.readFileSync('components/task-search-filter.tsx', 'utf8');
    
    if (content.includes('value={filters.status || "all-statuses"}') &&
        content.includes('value={filters.priority || "all-priorities"}') &&
        content.includes('value={filters.project_id || "all-projects"}')) {
      console.log('   ✅ Select value props updated to use non-empty defaults');
      return true;
    } else {
      console.log('   ❌ Select value props still using empty strings');
      return false;
    }
  });
  
  // Test 6: Check TaskModal handleSubmit logic
  runTest('TaskModal - handleSubmit Value Normalization', () => {
    const content = fs.readFileSync('components/task-modal.tsx', 'utf8');
    
    if (content.includes('assignedTo === "loading" || assignedTo === "no-employees" ? undefined : assignedTo') &&
        content.includes('const normalizedAssignedTo =')) {
      console.log('   ✅ handleSubmit normalizes special values to undefined');
      console.log('   ✅ Prevents "loading" and "no-employees" from being saved');
      return true;
    } else {
      console.log('   ❌ handleSubmit not properly handling special values');
      return false;
    }
  });
  
  // Test 7: Check Simple Task Manager component
  runTest('Simple Task Manager - Filter SelectItems', () => {
    const content = fs.readFileSync('components/simple-task-manager.tsx', 'utf8');
    
    if (content.includes('value="all">All Status') && 
        content.includes('value="all">All Priority') &&
        !content.includes('<SelectItem value="">')) {
      console.log('   ✅ Simple Task Manager uses "all" values (acceptable)');
      console.log('   ✅ No empty string values found');
      return true;
    } else {
      console.log('   ❌ Simple Task Manager may have empty string values');
      return false;
    }
  });
  
  // Test 8: Check for any remaining empty SelectItem values
  runTest('Global Check - No Remaining Empty SelectItem Values', () => {
    const filesToCheck = [
      'components/task-modal.tsx',
      'components/enhanced-task-modal.tsx',
      'components/task-search-filter.tsx',
      'components/simple-task-manager.tsx'
    ];
    
    let foundEmptyValues = false;
    const emptyValuePattern = /<SelectItem\s+value=""\s*[^>]*>/g;
    
    for (const file of filesToCheck) {
      if (fs.existsSync(file)) {
        const content = fs.readFileSync(file, 'utf8');
        const matches = content.match(emptyValuePattern);
        
        if (matches) {
          console.log(`   ❌ Found empty SelectItem values in ${file}:`);
          matches.forEach(match => console.log(`      ${match}`));
          foundEmptyValues = true;
        }
      }
    }
    
    if (!foundEmptyValues) {
      console.log('   ✅ No empty SelectItem values found in any component');
      return true;
    } else {
      return false;
    }
  });
  
  // Test 9: Check component syntax integrity
  runTest('Component Syntax Integrity', () => {
    const filesToCheck = [
      'components/task-modal.tsx',
      'components/enhanced-task-modal.tsx',
      'components/task-search-filter.tsx'
    ];
    
    let syntaxErrors = false;
    
    for (const file of filesToCheck) {
      if (fs.existsSync(file)) {
        const content = fs.readFileSync(file, 'utf8');
        
        // Basic syntax checks
        const openBraces = (content.match(/{/g) || []).length;
        const closeBraces = (content.match(/}/g) || []).length;
        const openParens = (content.match(/\(/g) || []).length;
        const closeParens = (content.match(/\)/g) || []).length;
        
        if (openBraces !== closeBraces || openParens !== closeParens) {
          console.log(`   ❌ Syntax errors in ${file}`);
          console.log(`      Braces: ${openBraces}/${closeBraces}, Parentheses: ${openParens}/${closeParens}`);
          syntaxErrors = true;
        }
      }
    }
    
    if (!syntaxErrors) {
      console.log('   ✅ All components have valid syntax');
      return true;
    } else {
      return false;
    }
  });
  
  // Final Summary
  console.log('\n' + '=' .repeat(60));
  console.log('🎯 SELECT COMPONENT FIXES SUMMARY');
  console.log('=' .repeat(60));
  
  if (allTestsPassed) {
    console.log('\n🎉 ALL SELECT COMPONENT FIXES SUCCESSFUL! 🎉');
    console.log('\n✨ Select Component Error Resolution:');
    console.log('   ✅ TaskModal: Empty employee assignment values fixed');
    console.log('   ✅ Enhanced TaskModal: Empty project selection value fixed');
    console.log('   ✅ Task Search Filter: All empty filter values fixed');
    console.log('   ✅ Value normalization logic implemented');
    console.log('   ✅ Proper handling of special states (loading, no-data)');
    console.log('   ✅ No remaining empty string SelectItem values');
    
    console.log('\n🚀 Expected Results:');
    console.log('   • No more "Select.Item must have a value prop" errors');
    console.log('   • Tasks load and display correctly in dashboard');
    console.log('   • Task creation modal opens without errors');
    console.log('   • All dropdown selections work properly');
    console.log('   • Employee assignment dropdown functions correctly');
    console.log('   • Filter dropdowns work without runtime errors');
    
    console.log('\n📋 Testing Checklist:');
    console.log('   1. Start development server: npm run dev');
    console.log('   2. Navigate to dashboard');
    console.log('   3. Verify tasks load without Select component errors');
    console.log('   4. Click "Add Task" button');
    console.log('   5. Test all dropdown selections in task modal');
    console.log('   6. Test filter dropdowns in task search');
    console.log('   7. Verify task assignment to employees works');
    
  } else {
    console.log('\n⚠️  SOME SELECT COMPONENT FIXES INCOMPLETE');
    console.log('\n🔧 Please review the failed checks above.');
    console.log('💡 The application may still have Select component errors.');
  }
  
  console.log('\n' + '=' .repeat(60));
}

testSelectComponentFixes().catch(console.error);
