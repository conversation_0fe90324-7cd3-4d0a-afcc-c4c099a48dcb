#!/usr/bin/env node

/**
 * Setup script for comprehensive user management system
 * This script initializes the database with the new schema and sample data
 */

const { neon } = require('@neondatabase/serverless');
const fs = require('fs');
const path = require('path');
require('dotenv').config({ path: '.env.local' });

async function setupUserManagement() {
  console.log('🚀 Setting up comprehensive user management system...\n');
  
  // Check if DATABASE_URL is set
  if (!process.env.DATABASE_URL) {
    console.error('❌ ERROR: DATABASE_URL environment variable is not set');
    console.log('📝 Please update your .env.local file with your Neon connection string');
    process.exit(1);
  }
  
  console.log('✅ DATABASE_URL found in environment');
  
  try {
    const sql = neon(process.env.DATABASE_URL);
    
    // Test connection first
    console.log('🔄 Testing database connection...');
    await sql`SELECT 1`;
    console.log('✅ Database connection successful!\n');
    
    // Read and execute schema file
    console.log('🔄 Setting up database schema...');
    const schemaPath = path.join(__dirname, 'setup-comprehensive-user-management.sql');
    
    if (!fs.existsSync(schemaPath)) {
      console.error('❌ Schema file not found:', schemaPath);
      process.exit(1);
    }
    
    const schemaSQL = fs.readFileSync(schemaPath, 'utf8');
    
    // Split the SQL into individual statements and execute them
    const statements = schemaSQL
      .split(';')
      .map(stmt => stmt.trim())
      .filter(stmt => stmt.length > 0 && !stmt.startsWith('--'));
    
    for (const statement of statements) {
      if (statement.trim()) {
        try {
          await sql.unsafe(statement);
        } catch (error) {
          // Ignore "already exists" errors
          if (!error.message.includes('already exists') && !error.message.includes('duplicate key')) {
            console.warn('⚠️  Warning executing statement:', error.message);
          }
        }
      }
    }
    
    console.log('✅ Database schema created successfully!');
    
    // Read and execute sample data file
    console.log('🔄 Inserting sample data...');
    const sampleDataPath = path.join(__dirname, 'insert-sample-users.sql');
    
    if (!fs.existsSync(sampleDataPath)) {
      console.error('❌ Sample data file not found:', sampleDataPath);
      process.exit(1);
    }
    
    const sampleDataSQL = fs.readFileSync(sampleDataPath, 'utf8');
    
    // Split and execute sample data statements
    const dataStatements = sampleDataSQL
      .split(';')
      .map(stmt => stmt.trim())
      .filter(stmt => stmt.length > 0 && !stmt.startsWith('--'));
    
    for (const statement of dataStatements) {
      if (statement.trim()) {
        try {
          await sql.unsafe(statement);
        } catch (error) {
          // Ignore "already exists" errors for sample data
          if (!error.message.includes('duplicate key') && !error.message.includes('already exists')) {
            console.warn('⚠️  Warning inserting sample data:', error.message);
          }
        }
      }
    }
    
    console.log('✅ Sample data inserted successfully!');
    
    // Verify the setup
    console.log('\n🔍 Verifying setup...');
    
    // Check tables
    const tables = await sql`
      SELECT table_name 
      FROM information_schema.tables 
      WHERE table_schema = 'public' 
      ORDER BY table_name
    `;
    
    console.log(`📊 Created ${tables.length} tables:`);
    tables.forEach(table => {
      console.log(`   - ${table.table_name}`);
    });
    
    // Check users
    const users = await sql`
      SELECT email, role, department, is_active 
      FROM users 
      ORDER BY role, email
    `;
    
    console.log(`\n👥 Created ${users.length} users:`);
    users.forEach(user => {
      console.log(`   - ${user.email} (${user.role}) - ${user.department || 'No Department'} ${user.is_active ? '✅' : '❌'}`);
    });
    
    // Check departments
    const departments = await sql`
      SELECT name, location, is_active 
      FROM departments 
      ORDER BY name
    `;
    
    console.log(`\n🏢 Created ${departments.length} departments:`);
    departments.forEach(dept => {
      console.log(`   - ${dept.name} (${dept.location || 'No Location'}) ${dept.is_active ? '✅' : '❌'}`);
    });
    
    // Check permissions
    const permissions = await sql`
      SELECT COUNT(*) as count 
      FROM permissions
    `;
    
    console.log(`\n🔐 Created ${permissions[0].count} permissions`);
    
    console.log('\n🎉 User management system setup complete!');
    console.log('\n📋 Login Credentials (all use password: admin123):');
    
    const activeUsers = users.filter(u => u.is_active);
    activeUsers.forEach(user => {
      console.log(`   ${user.email} / admin123 (${user.role})`);
    });
    
    console.log('\n📖 Next Steps:');
    console.log('1. Start your development server: npm run dev');
    console.log('2. Navigate to /admin/users to access the user management dashboard');
    console.log('3. Login with any of the credentials above');
    console.log('4. Explore the comprehensive user management features');
    
  } catch (error) {
    console.error('❌ Setup failed:', error.message);
    console.error('Full error:', error);
    process.exit(1);
  }
}

// Run the setup
if (require.main === module) {
  setupUserManagement();
}

module.exports = { setupUserManagement };
