"use client"

import type React from "react"
import { TaskCard } from "@/components/task-card"

interface Task {
  id: string
  title: string
  description: string
  status: string
  userId: string
  priority?: string
}

interface KanbanColumnProps {
  title: string
  tasks?: Task[]
  onEditTask?: (task: Task) => void
  onDeleteTask?: (taskId: string) => void
  onTaskClick?: (taskId: string) => void
  onUpdateTaskStatus?: (taskId: string, newStatus: string) => void
  isAdmin?: boolean
  currentUserId?: string
  columnId: string
  isUpdating?: boolean
}

export function KanbanColumn({
  title,
  tasks = [],
  onEditTask = () => {},
  onDeleteTask = () => {},
  onTaskClick = () => {},
  onUpdateTaskStatus = () => {},
  isAdmin = false,
  currentUserId = "",
  columnId,
  isUpdating = false,
}: KanbanColumnProps) {
  // Ensure tasks is an array
  const tasksArray = Array.isArray(tasks) ? tasks : []

  const handleDragOver = (e: React.DragEvent) => {
    e.preventDefault()
  }

  const handleDrop = (e: React.DragEvent) => {
    e.preventDefault()
    const taskId = e.dataTransfer.getData("taskId")
    if (taskId) {
      onUpdateTaskStatus(taskId, columnId)
    }
  }

  return (
    <div
      className={`flex flex-col h-full bg-gray-100/80 dark:bg-gray-800/80 rounded-lg p-3 min-h-[200px] ${
        isUpdating ? "opacity-50 pointer-events-none" : ""
      }`}
      onDragOver={handleDragOver}
      onDrop={handleDrop}
    >
      <div className="flex items-center justify-between mb-3">
        <h3 className="font-medium text-sm text-gray-700 dark:text-gray-300">{title}</h3>
        <span className="bg-gray-200 dark:bg-gray-700 text-gray-600 dark:text-gray-300 text-xs px-2 py-1 rounded-full">
          {tasksArray.length}
        </span>
      </div>
      <div className="space-y-2 overflow-y-auto">
        {tasksArray.length === 0 ? (
          <div className="text-center py-4 text-sm text-gray-500 dark:text-gray-400">No tasks</div>
        ) : (
          tasksArray.map((task) => (
            <TaskCard
              key={task.id}
              task={task}
              onEdit={() => onEditTask(task)}
              onDelete={() => onDeleteTask(task.id)}
              onClick={() => onTaskClick(task.id)}
              isAdmin={isAdmin}
              isOwnedByCurrentUser={task.assigned_to === currentUserId}
            />
          ))
        )}
      </div>
    </div>
  )
}


