require('dotenv').config({ path: '.env.local' });
const { neon } = require('@neondatabase/serverless');
const fs = require('fs');

async function runBasePayrollSchema() {
  try {
    const sql = neon(process.env.DATABASE_URL);
    
    console.log('🔄 Running base payroll schema...');
    
    // Read the base payroll schema
    const schemaSQL = fs.readFileSync('./scripts/01-enhance-payroll-schema.sql', 'utf8');
    
    // Split by semicolon and filter out empty statements and comments
    const statements = schemaSQL
      .split(';')
      .map(stmt => stmt.trim())
      .filter(stmt => stmt.length > 0 && !stmt.startsWith('--') && !stmt.startsWith('SELECT'));
    
    console.log(`Found ${statements.length} SQL statements to execute`);
    
    let successCount = 0;
    let skipCount = 0;
    let errorCount = 0;
    
    for (let i = 0; i < statements.length; i++) {
      const statement = statements[i];
      if (statement) {
        try {
          await sql([statement]);
          successCount++;
          console.log(`✅ Statement ${i + 1}/${statements.length} executed successfully`);
        } catch (error) {
          if (error.message.includes('already exists') || 
              error.message.includes('duplicate') ||
              error.message.includes('does not exist') ||
              error.message.includes('column') && error.message.includes('already exists')) {
            skipCount++;
            console.log(`⚠️  Statement ${i + 1}: ${error.message.split('\n')[0]} (skipped)`);
          } else {
            errorCount++;
            console.error(`❌ Statement ${i + 1} failed: ${error.message.split('\n')[0]}`);
          }
        }
      }
    }
    
    console.log(`\n📊 Base payroll schema summary:`);
    console.log(`   ✅ Successful: ${successCount}`);
    console.log(`   ⚠️  Skipped: ${skipCount}`);
    console.log(`   ❌ Errors: ${errorCount}`);
    
    // Verify the base schema
    console.log('\n🔍 Verifying base payroll schema...');
    
    try {
      const tables = await sql`
        SELECT table_name 
        FROM information_schema.tables 
        WHERE table_name IN ('payroll_components_master', 'employee_pay_structure', 'payroll_periods', 'payroll_settings')
        ORDER BY table_name
      `;
      
      console.log('✅ Base payroll tables created:', tables.map(t => t.table_name));
      
      if (tables.length > 0) {
        console.log('\n🎉 Base payroll schema completed successfully!');
        console.log('Ready to run enhanced employee payroll schema.');
        return true;
      } else {
        console.log('⚠️  Some base tables may not have been created properly.');
        return false;
      }
      
    } catch (verifyError) {
      console.error('❌ Verification failed:', verifyError.message);
      return false;
    }
    
  } catch (error) {
    console.error('❌ Base payroll schema failed:', error);
    return false;
  }
}

runBasePayrollSchema();
