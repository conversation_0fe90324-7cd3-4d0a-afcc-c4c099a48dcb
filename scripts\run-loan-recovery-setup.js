const { neon } = require('@neondatabase/serverless');
require('dotenv').config({ path: '.env.local' });

if (!process.env.DATABASE_URL) {
  console.error('❌ DATABASE_URL environment variable is required');
  process.exit(1);
}

const sql = neon(process.env.DATABASE_URL);

async function setupLoanRecoverySystem() {
  try {
    console.log('🚀 Setting up Loan Recovery Management System...\n');

    // Step 1: Create enum types
    console.log('📋 Creating enum types...');
    await sql`
      DO $$ BEGIN
        CREATE TYPE recovery_stage AS ENUM ('early', 'assertive', 'escalation', 'legal_recovery', 'complete');
      EXCEPTION
        WHEN duplicate_object THEN null;
      END $$;
    `;
    
    await sql`
      DO $$ BEGIN
        CREATE TYPE note_type AS ENUM ('general', 'call', 'email', 'meeting');
      EXCEPTION
        WHEN duplicate_object THEN null;
      END $$;
    `;
    
    await sql`
      DO $$ BEGIN
        CREATE TYPE reminder_status AS ENUM ('pending', 'completed', 'cancelled');
      EXCEPTION
        WHEN duplicate_object THEN null;
      END $$;
    `;
    console.log('✅ Enum types created\n');

    // Step 2: Create tables
    console.log('📋 Creating tables...');
    
    // Customers table
    await sql`
      CREATE TABLE IF NOT EXISTS loan_recovery_customers (
        id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
        name VARCHAR(255) NOT NULL,
        phone VARCHAR(20) NOT NULL,
        email VARCHAR(255),
        address TEXT,
        employee_id VARCHAR(50),
        created_by UUID NOT NULL REFERENCES users(id),
        created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
        updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
      )
    `;

    // Loan records table
    await sql`
      CREATE TABLE IF NOT EXISTS loan_records (
        id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
        customer_id UUID NOT NULL REFERENCES loan_recovery_customers(id) ON DELETE CASCADE,
        loan_amount DECIMAL(15,2) NOT NULL,
        amount_paid DECIMAL(15,2) DEFAULT 0,
        due_date DATE NOT NULL,
        due_date_bs VARCHAR(20),
        current_stage recovery_stage NOT NULL DEFAULT 'early',
        stage_order INTEGER NOT NULL DEFAULT 1,
        created_by UUID NOT NULL REFERENCES users(id),
        updated_by UUID REFERENCES users(id),
        created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
        updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
        CONSTRAINT positive_amounts CHECK (loan_amount > 0 AND amount_paid >= 0),
        CONSTRAINT valid_payment CHECK (amount_paid <= loan_amount)
      )
    `;

    // Stage transitions table
    await sql`
      CREATE TABLE IF NOT EXISTS loan_stage_transitions (
        id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
        loan_id UUID NOT NULL REFERENCES loan_records(id) ON DELETE CASCADE,
        from_stage recovery_stage,
        to_stage recovery_stage NOT NULL,
        transitioned_by UUID NOT NULL REFERENCES users(id),
        transition_date TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
        notes TEXT,
        CONSTRAINT different_stages CHECK (from_stage IS NULL OR from_stage != to_stage)
      )
    `;

    // Conversation notes table
    await sql`
      CREATE TABLE IF NOT EXISTS loan_conversation_notes (
        id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
        loan_id UUID NOT NULL REFERENCES loan_records(id) ON DELETE CASCADE,
        note_type note_type NOT NULL DEFAULT 'general',
        content TEXT NOT NULL,
        created_by UUID NOT NULL REFERENCES users(id),
        created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
        updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
      )
    `;

    // Reminders table
    await sql`
      CREATE TABLE IF NOT EXISTS loan_reminders (
        id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
        loan_id UUID NOT NULL REFERENCES loan_records(id) ON DELETE CASCADE,
        title VARCHAR(255) NOT NULL,
        reminder_date DATE NOT NULL,
        reminder_date_bs VARCHAR(20),
        status reminder_status NOT NULL DEFAULT 'pending',
        created_by UUID NOT NULL REFERENCES users(id),
        completed_by UUID REFERENCES users(id),
        completed_at TIMESTAMP WITH TIME ZONE,
        created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
        updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
      )
    `;

    console.log('✅ Tables created\n');

    // Step 3: Create indexes
    console.log('📋 Creating indexes...');
    await sql`CREATE INDEX IF NOT EXISTS idx_loan_recovery_customers_phone ON loan_recovery_customers(phone)`;
    await sql`CREATE INDEX IF NOT EXISTS idx_loan_recovery_customers_email ON loan_recovery_customers(email)`;
    await sql`CREATE INDEX IF NOT EXISTS idx_loan_recovery_customers_name ON loan_recovery_customers(name)`;
    await sql`CREATE INDEX IF NOT EXISTS idx_loan_records_customer_id ON loan_records(customer_id)`;
    await sql`CREATE INDEX IF NOT EXISTS idx_loan_records_current_stage ON loan_records(current_stage)`;
    await sql`CREATE INDEX IF NOT EXISTS idx_loan_records_due_date ON loan_records(due_date)`;
    await sql`CREATE INDEX IF NOT EXISTS idx_loan_records_stage_order ON loan_records(current_stage, stage_order)`;
    await sql`CREATE INDEX IF NOT EXISTS idx_loan_stage_transitions_loan_id ON loan_stage_transitions(loan_id)`;
    await sql`CREATE INDEX IF NOT EXISTS idx_loan_conversation_notes_loan_id ON loan_conversation_notes(loan_id)`;
    await sql`CREATE INDEX IF NOT EXISTS idx_loan_reminders_loan_id ON loan_reminders(loan_id)`;
    console.log('✅ Indexes created\n');

    // Step 4: Get admin user
    console.log('👤 Finding admin user...');
    const adminUsers = await sql`
      SELECT id, full_name FROM users 
      WHERE role = 'admin' 
      ORDER BY created_at 
      LIMIT 1
    `;

    if (adminUsers.length === 0) {
      console.log('⚠️  No admin user found. Please create an admin user first.');
      return;
    }

    const adminUser = adminUsers[0];
    console.log(`✅ Using admin user: ${adminUser.full_name}\n`);

    // Step 5: Create sample data
    console.log('👥 Creating sample customers...');
    const customers = [
      { name: 'राम बहादुर शेर्पा', phone: '9841234567', email: '<EMAIL>', address: 'काठमाडौं, नेपाल' },
      { name: 'हरि यादव', phone: '9861234567', email: '<EMAIL>', address: 'जनकपुर, नेपाल' },
      { name: 'सीता देवी पौडेल', phone: '9851234567', email: '<EMAIL>', address: 'पोखरा, नेपाल' },
      { name: 'गीता तामाङ', phone: '9871234567', email: '<EMAIL>', address: 'धरान, नेपाल' },
      { name: 'सुरेश गुरुङ', phone: '9881234567', email: '<EMAIL>', address: 'बुटवल, नेपाल' },
      { name: 'कमला खत्री', phone: '9891234567', email: '<EMAIL>', address: 'भैरहवा, नेपाल' }
    ];

    const createdCustomers = [];
    for (const customer of customers) {
      const result = await sql`
        INSERT INTO loan_recovery_customers (name, phone, email, address, created_by)
        VALUES (${customer.name}, ${customer.phone}, ${customer.email}, ${customer.address}, ${adminUser.id})
        RETURNING id, name
      `;
      createdCustomers.push(result[0]);
    }
    console.log(`✅ Created ${createdCustomers.length} customers\n`);

    // Step 6: Create sample loans
    console.log('💰 Creating sample loans...');
    const loans = [
      { customer_idx: 0, amount: 350000, paid: 50000, due: '2024-06-15', due_bs: '2081-03-01', stage: 'early' },
      { customer_idx: 1, amount: 49800, paid: 200, due: '2025-07-09', due_bs: '2082-03-25', stage: 'assertive' },
      { customer_idx: 2, amount: 800000, paid: 0, due: '2023-12-20', due_bs: '2080-09-05', stage: 'escalation' },
      { customer_idx: 3, amount: 650000, paid: 0, due: '2023-10-15', due_bs: '2080-06-30', stage: 'legal_recovery' },
      { customer_idx: 4, amount: 250000, paid: 50000, due: '2024-08-20', due_bs: '2081-05-05', stage: 'early' },
      { customer_idx: 5, amount: 650000, paid: 0, due: '2024-05-15', due_bs: '2081-02-01', stage: 'escalation' }
    ];

    const createdLoans = [];
    for (let i = 0; i < loans.length; i++) {
      const loan = loans[i];
      const customer = createdCustomers[loan.customer_idx];
      const result = await sql`
        INSERT INTO loan_records (
          customer_id, loan_amount, amount_paid, due_date, due_date_bs, 
          current_stage, stage_order, created_by, updated_by
        )
        VALUES (
          ${customer.id}, ${loan.amount}, ${loan.paid}, ${loan.due}, ${loan.due_bs}, 
          ${loan.stage}, ${i + 1}, ${adminUser.id}, ${adminUser.id}
        )
        RETURNING id
      `;
      createdLoans.push(result[0]);
    }
    console.log(`✅ Created ${createdLoans.length} loans\n`);

    // Step 7: Add sample notes and reminders
    console.log('📝 Adding sample notes...');
    await sql`
      INSERT INTO loan_conversation_notes (loan_id, note_type, content, created_by)
      VALUES (${createdLoans[1].id}, 'meeting', 'Meet on Sunday', ${adminUser.id})
    `;

    await sql`
      INSERT INTO loan_reminders (loan_id, title, reminder_date, reminder_date_bs, created_by)
      VALUES (${createdLoans[1].id}, 'Call on Sunday for meeting.', '2025-07-27', '2082-04-12', ${adminUser.id})
    `;

    console.log('✅ Sample data created\n');

    console.log('🎉 Loan Recovery System setup completed successfully!');

  } catch (error) {
    console.error('❌ Setup failed:', error);
    process.exit(1);
  }
}

setupLoanRecoverySystem();
