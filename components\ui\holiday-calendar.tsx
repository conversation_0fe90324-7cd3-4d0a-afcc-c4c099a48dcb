// Holiday Calendar Component
// Phase 3: Nepal Localization Implementation - Holiday Calendar

"use client"

import React, { useState, useEffect } from 'react'
import { Calendar, Plus, Filter, Download, Upload, Star, Clock } from 'lucide-react'
import { But<PERSON> } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Input } from '@/components/ui/input'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'
import { Switch } from '@/components/ui/switch'
import { holidayManager, Holiday, HolidayCalendar, HolidayStats } from '@/lib/holiday-manager'
import { NepaliCalendar } from '@/lib/nepali-calendar'
import { NepaliDatePicker } from './nepali-date-picker'
import { cn } from '@/lib/utils'

interface HolidayCalendarProps {
  year?: number
  onHolidaySelect?: (holiday: Holiday) => void
  showAddButton?: boolean
  showFilters?: boolean
  className?: string
}

interface HolidayFormProps {
  holiday?: Holiday
  onSave?: (holiday: Holiday) => void
  onCancel?: () => void
}

export function HolidayCalendarComponent({
  year = new Date().getFullYear(),
  onHolidaySelect,
  showAddButton = true,
  showFilters = true,
  className
}: HolidayCalendarProps) {
  const [holidayCalendar, setHolidayCalendar] = useState<HolidayCalendar | null>(null)
  const [holidayStats, setHolidayStats] = useState<HolidayStats | null>(null)
  const [filteredHolidays, setFilteredHolidays] = useState<Holiday[]>([])
  const [selectedYear, setSelectedYear] = useState(year)
  const [filterType, setFilterType] = useState<string>('all')
  const [filterCategory, setFilterCategory] = useState<string>('all')
  const [searchTerm, setSearchTerm] = useState('')
  const [showAddDialog, setShowAddDialog] = useState(false)
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    loadHolidayData()
  }, [selectedYear])

  useEffect(() => {
    applyFilters()
  }, [holidayCalendar, filterType, filterCategory, searchTerm])

  const loadHolidayData = async () => {
    setLoading(true)
    try {
      const [calendar, stats] = await Promise.all([
        holidayManager.generateHolidayCalendar(selectedYear),
        holidayManager.getHolidayStats(selectedYear)
      ])
      
      setHolidayCalendar(calendar)
      setHolidayStats(stats)
    } catch (error) {
      console.error('Error loading holiday data:', error)
    } finally {
      setLoading(false)
    }
  }

  const applyFilters = () => {
    if (!holidayCalendar) return

    let filtered = holidayCalendar.holidays

    // Filter by type
    if (filterType !== 'all') {
      filtered = filtered.filter(h => h.type === filterType)
    }

    // Filter by category
    if (filterCategory !== 'all') {
      filtered = filtered.filter(h => h.category === filterCategory)
    }

    // Filter by search term
    if (searchTerm) {
      const term = searchTerm.toLowerCase()
      filtered = filtered.filter(h => 
        h.name.toLowerCase().includes(term) ||
        h.nameNepali.toLowerCase().includes(term) ||
        (h.description && h.description.toLowerCase().includes(term))
      )
    }

    setFilteredHolidays(filtered)
  }

  const getHolidayTypeColor = (type: string) => {
    switch (type) {
      case 'public': return 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200'
      case 'festival': return 'bg-orange-100 text-orange-800 dark:bg-orange-900 dark:text-orange-200'
      case 'observance': return 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200'
      case 'bank': return 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200'
      case 'government': return 'bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-200'
      default: return 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200'
    }
  }

  const formatHolidayDate = (holiday: Holiday) => {
    const adDate = new Date(holiday.date).toLocaleDateString('en-US', {
      weekday: 'short',
      month: 'short',
      day: 'numeric'
    })
    return `${adDate} (${holiday.bsDateString})`
  }

  const availableYears = Array.from({ length: 10 }, (_, i) => new Date().getFullYear() - 5 + i)

  if (loading) {
    return (
      <Card className={className}>
        <CardContent className="p-6">
          <div className="flex items-center justify-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
          </div>
        </CardContent>
      </Card>
    )
  }

  return (
    <div className={cn("space-y-6", className)}>
      {/* Header with controls */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <CardTitle className="flex items-center gap-2">
              <Calendar className="h-5 w-5" />
              Holiday Calendar {selectedYear}
            </CardTitle>
            
            <div className="flex items-center gap-2">
              <Select value={selectedYear.toString()} onValueChange={(value) => setSelectedYear(parseInt(value))}>
                <SelectTrigger className="w-32">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  {availableYears.map(year => (
                    <SelectItem key={year} value={year.toString()}>
                      {year}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
              
              {showAddButton && (
                <Dialog open={showAddDialog} onOpenChange={setShowAddDialog}>
                  <DialogTrigger asChild>
                    <Button size="sm">
                      <Plus className="h-4 w-4 mr-1" />
                      Add Holiday
                    </Button>
                  </DialogTrigger>
                  <DialogContent className="max-w-md">
                    <DialogHeader>
                      <DialogTitle>Add New Holiday</DialogTitle>
                    </DialogHeader>
                    <HolidayForm
                      onSave={(holiday) => {
                        setShowAddDialog(false)
                        loadHolidayData() // Refresh data
                      }}
                      onCancel={() => setShowAddDialog(false)}
                    />
                  </DialogContent>
                </Dialog>
              )}
            </div>
          </div>
        </CardHeader>
        
        {/* Filters */}
        {showFilters && (
          <CardContent className="pt-0">
            <div className="flex flex-wrap gap-4">
              <div className="flex-1 min-w-48">
                <Input
                  placeholder="Search holidays..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="w-full"
                />
              </div>
              
              <Select value={filterType} onValueChange={setFilterType}>
                <SelectTrigger className="w-40">
                  <SelectValue placeholder="Filter by type" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Types</SelectItem>
                  <SelectItem value="public">Public</SelectItem>
                  <SelectItem value="festival">Festival</SelectItem>
                  <SelectItem value="observance">Observance</SelectItem>
                  <SelectItem value="bank">Bank</SelectItem>
                  <SelectItem value="government">Government</SelectItem>
                </SelectContent>
              </Select>
              
              <Select value={filterCategory} onValueChange={setFilterCategory}>
                <SelectTrigger className="w-40">
                  <SelectValue placeholder="Filter by category" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Categories</SelectItem>
                  <SelectItem value="national">National</SelectItem>
                  <SelectItem value="religious">Religious</SelectItem>
                  <SelectItem value="cultural">Cultural</SelectItem>
                  <SelectItem value="international">International</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </CardContent>
        )}
      </Card>

      {/* Statistics */}
      {holidayStats && (
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
          <Card>
            <CardContent className="p-4 text-center">
              <div className="text-2xl font-bold">{holidayStats.totalHolidays}</div>
              <div className="text-sm text-muted-foreground">Total Holidays</div>
            </CardContent>
          </Card>
          <Card>
            <CardContent className="p-4 text-center">
              <div className="text-2xl font-bold text-red-600">{holidayStats.byType.public || 0}</div>
              <div className="text-sm text-muted-foreground">Public Holidays</div>
            </CardContent>
          </Card>
          <Card>
            <CardContent className="p-4 text-center">
              <div className="text-2xl font-bold text-orange-600">{holidayStats.byType.festival || 0}</div>
              <div className="text-sm text-muted-foreground">Festivals</div>
            </CardContent>
          </Card>
          <Card>
            <CardContent className="p-4 text-center">
              <div className="text-2xl font-bold text-blue-600">{holidayStats.byType.observance || 0}</div>
              <div className="text-sm text-muted-foreground">Observances</div>
            </CardContent>
          </Card>
        </div>
      )}

      {/* Holiday List */}
      <Tabs defaultValue="list" className="w-full">
        <TabsList className="grid w-full grid-cols-3">
          <TabsTrigger value="list">Holiday List</TabsTrigger>
          <TabsTrigger value="upcoming">Upcoming</TabsTrigger>
          <TabsTrigger value="long-weekends">Long Weekends</TabsTrigger>
        </TabsList>
        
        <TabsContent value="list" className="space-y-4">
          <div className="space-y-2">
            {filteredHolidays.map((holiday) => (
              <Card 
                key={holiday.id} 
                className="cursor-pointer hover:shadow-md transition-shadow"
                onClick={() => onHolidaySelect?.(holiday)}
              >
                <CardContent className="p-4">
                  <div className="flex items-center justify-between">
                    <div className="space-y-1">
                      <div className="flex items-center gap-2">
                        <h3 className="font-medium">{holiday.name}</h3>
                        {holiday.nameNepali !== holiday.name && (
                          <span className="text-sm text-muted-foreground">({holiday.nameNepali})</span>
                        )}
                      </div>
                      <div className="text-sm text-muted-foreground">
                        {formatHolidayDate(holiday)}
                      </div>
                      {holiday.description && (
                        <div className="text-xs text-muted-foreground">
                          {holiday.description}
                        </div>
                      )}
                    </div>
                    
                    <div className="flex items-center gap-2">
                      <Badge className={getHolidayTypeColor(holiday.type)}>
                        {holiday.type}
                      </Badge>
                      {holiday.isOptional && (
                        <Badge variant="outline" className="text-xs">
                          Optional
                        </Badge>
                      )}
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </TabsContent>
        
        <TabsContent value="upcoming" className="space-y-4">
          {holidayStats?.upcomingHolidays.map((holiday) => (
            <Card key={holiday.id}>
              <CardContent className="p-4">
                <div className="flex items-center gap-3">
                  <Clock className="h-4 w-4 text-muted-foreground" />
                  <div className="flex-1">
                    <div className="font-medium">{holiday.name}</div>
                    <div className="text-sm text-muted-foreground">
                      {formatHolidayDate(holiday)}
                    </div>
                  </div>
                  <Badge className={getHolidayTypeColor(holiday.type)}>
                    {holiday.type}
                  </Badge>
                </div>
              </CardContent>
            </Card>
          ))}
        </TabsContent>
        
        <TabsContent value="long-weekends" className="space-y-4">
          {holidayCalendar?.longWeekends.map((weekend, index) => (
            <Card key={index}>
              <CardContent className="p-4">
                <div className="space-y-2">
                  <div className="flex items-center justify-between">
                    <h3 className="font-medium">{weekend.totalDays}-Day Long Weekend</h3>
                    <Badge variant="outline">{weekend.totalDays} days</Badge>
                  </div>
                  <div className="text-sm text-muted-foreground">
                    {new Date(weekend.startDate).toLocaleDateString()} - {new Date(weekend.endDate).toLocaleDateString()}
                  </div>
                  <div className="text-xs text-muted-foreground">
                    {weekend.description}
                  </div>
                  <div className="flex flex-wrap gap-1">
                    {weekend.holidays.map((holiday) => (
                      <Badge key={holiday.id} variant="secondary" className="text-xs">
                        {holiday.name}
                      </Badge>
                    ))}
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
        </TabsContent>
      </Tabs>
    </div>
  )
}

export function HolidayForm({ holiday, onSave, onCancel }: HolidayFormProps) {
  const [formData, setFormData] = useState({
    name: holiday?.name || '',
    nameNepali: holiday?.nameNepali || '',
    date: holiday?.date || '',
    type: holiday?.type || 'public',
    category: holiday?.category || 'national',
    description: holiday?.description || '',
    isOptional: holiday?.isOptional || false,
    affectsPayroll: holiday?.affectsPayroll !== false,
    compensatoryOff: holiday?.compensatoryOff || false
  })

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    
    try {
      const holidayData = {
        ...formData,
        id: holiday?.id || '',
        bsDate: NepaliCalendar.adToBS(formData.date),
        bsDateString: NepaliCalendar.formatBSDate(NepaliCalendar.adToBS(formData.date)),
        isFixed: true,
        isActive: true
      }

      if (holiday?.id) {
        // Update existing holiday
        const updated = await holidayManager.updateHoliday(holiday.id, holidayData)
        onSave?.(updated)
      } else {
        // Create new holiday
        const created = await holidayManager.createHoliday(holidayData)
        onSave?.(created)
      }
    } catch (error) {
      console.error('Error saving holiday:', error)
    }
  }

  return (
    <form onSubmit={handleSubmit} className="space-y-4">
      <div className="space-y-2">
        <Label htmlFor="name">Holiday Name</Label>
        <Input
          id="name"
          value={formData.name}
          onChange={(e) => setFormData({ ...formData, name: e.target.value })}
          required
        />
      </div>

      <div className="space-y-2">
        <Label htmlFor="nameNepali">Nepali Name</Label>
        <Input
          id="nameNepali"
          value={formData.nameNepali}
          onChange={(e) => setFormData({ ...formData, nameNepali: e.target.value })}
        />
      </div>

      <div className="space-y-2">
        <Label htmlFor="date">Date</Label>
        <NepaliDatePicker
          value={formData.date ? new Date(formData.date) : undefined}
          onChange={(date) => setFormData({ ...formData, date: date?.toISOString().split('T')[0] || '' })}
          required
        />
      </div>

      <div className="grid grid-cols-2 gap-4">
        <div className="space-y-2">
          <Label htmlFor="type">Type</Label>
          <Select value={formData.type} onValueChange={(value) => setFormData({ ...formData, type: value })}>
            <SelectTrigger>
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="public">Public</SelectItem>
              <SelectItem value="festival">Festival</SelectItem>
              <SelectItem value="observance">Observance</SelectItem>
              <SelectItem value="bank">Bank</SelectItem>
              <SelectItem value="government">Government</SelectItem>
            </SelectContent>
          </Select>
        </div>

        <div className="space-y-2">
          <Label htmlFor="category">Category</Label>
          <Select value={formData.category} onValueChange={(value) => setFormData({ ...formData, category: value })}>
            <SelectTrigger>
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="national">National</SelectItem>
              <SelectItem value="religious">Religious</SelectItem>
              <SelectItem value="cultural">Cultural</SelectItem>
              <SelectItem value="international">International</SelectItem>
            </SelectContent>
          </Select>
        </div>
      </div>

      <div className="space-y-2">
        <Label htmlFor="description">Description</Label>
        <Textarea
          id="description"
          value={formData.description}
          onChange={(e) => setFormData({ ...formData, description: e.target.value })}
          rows={3}
        />
      </div>

      <div className="space-y-3">
        <div className="flex items-center space-x-2">
          <Switch
            id="isOptional"
            checked={formData.isOptional}
            onCheckedChange={(checked) => setFormData({ ...formData, isOptional: checked })}
          />
          <Label htmlFor="isOptional">Optional Holiday</Label>
        </div>

        <div className="flex items-center space-x-2">
          <Switch
            id="affectsPayroll"
            checked={formData.affectsPayroll}
            onCheckedChange={(checked) => setFormData({ ...formData, affectsPayroll: checked })}
          />
          <Label htmlFor="affectsPayroll">Affects Payroll</Label>
        </div>

        <div className="flex items-center space-x-2">
          <Switch
            id="compensatoryOff"
            checked={formData.compensatoryOff}
            onCheckedChange={(checked) => setFormData({ ...formData, compensatoryOff: checked })}
          />
          <Label htmlFor="compensatoryOff">Compensatory Off</Label>
        </div>
      </div>

      <div className="flex justify-end gap-2">
        <Button type="button" variant="outline" onClick={onCancel}>
          Cancel
        </Button>
        <Button type="submit">
          {holiday ? 'Update' : 'Create'} Holiday
        </Button>
      </div>
    </form>
  )
}

export default HolidayCalendarComponent
