// Nepal Tax Calculator
// Compliant with Nepal Income Tax Act and employment regulations

export interface NepalTaxSlab {
  min_amount: number;
  max_amount: number;
  tax_rate: number;
  description: string;
}

export interface NepalTaxCalculation {
  annual_income: number;
  taxable_income: number;
  tax_exemption: number;
  total_tax: number;
  monthly_tax: number;
  effective_tax_rate: number;
  marginal_tax_rate: number;
  tax_breakdown: Array<{
    slab: NepalTaxSlab;
    taxable_amount: number;
    tax_amount: number;
  }>;
}

export interface NepalPayrollCompliance {
  provident_fund: {
    employee_contribution: number;
    employer_contribution: number;
    total_contribution: number;
  };
  social_security_fund: {
    employee_contribution: number;
    employer_contribution: number;
    total_contribution: number;
  };
  festival_bonus: number;
  leave_encashment: number;
  gratuity: number;
  overtime_calculation: {
    regular_hours: number;
    overtime_hours: number;
    overtime_rate: number;
    overtime_pay: number;
  };
}

export interface NepalFiscalYear {
  fiscal_year: string;
  start_date: string; // BS date
  end_date: string; // BS date
  ad_start_date: string; // AD date
  ad_end_date: string; // AD date
}

export class NepalTaxCalculator {
  
  // Nepal Income Tax Slabs for FY 2080/81 (2023/24)
  private readonly taxSlabs: NepalTaxSlab[] = [
    {
      min_amount: 0,
      max_amount: 500000,
      tax_rate: 0.01,
      description: "First NPR 5,00,000 - 1%"
    },
    {
      min_amount: 500000,
      max_amount: 700000,
      tax_rate: 0.10,
      description: "NPR 5,00,001 to 7,00,000 - 10%"
    },
    {
      min_amount: 700000,
      max_amount: 1000000,
      tax_rate: 0.20,
      description: "NPR 7,00,001 to 10,00,000 - 20%"
    },
    {
      min_amount: 1000000,
      max_amount: 2000000,
      tax_rate: 0.30,
      description: "NPR 10,00,001 to 20,00,000 - 30%"
    },
    {
      min_amount: 2000000,
      max_amount: Infinity,
      tax_rate: 0.36,
      description: "Above NPR 20,00,000 - 36%"
    }
  ];

  // Standard deductions and exemptions
  private readonly standardExemption = 500000; // NPR 5,00,000 standard exemption
  private readonly providentFundRate = 0.10; // 10% employee + 10% employer
  private readonly socialSecurityRate = 0.11; // 11% total (employee + employer)
  private readonly overtimeMultiplier = 1.5; // 1.5x for overtime
  private readonly standardWorkingHours = 8; // 8 hours per day
  private readonly standardWorkingDays = 6; // 6 days per week

  // Calculate Nepal-compliant income tax
  calculateIncomeTax(annualIncome: number, exemptions: number = 0): NepalTaxCalculation {
    const totalExemption = this.standardExemption + exemptions;
    const taxableIncome = Math.max(0, annualIncome - totalExemption);
    
    let totalTax = 0;
    let remainingIncome = taxableIncome;
    const taxBreakdown = [];
    
    for (const slab of this.taxSlabs) {
      if (remainingIncome <= 0) break;
      
      const slabRange = slab.max_amount - slab.min_amount;
      const taxableInThisSlab = Math.min(remainingIncome, slabRange);
      
      if (taxableIncome > slab.min_amount) {
        const taxAmount = taxableInThisSlab * slab.tax_rate;
        totalTax += taxAmount;
        
        taxBreakdown.push({
          slab: slab,
          taxable_amount: taxableInThisSlab,
          tax_amount: taxAmount
        });
        
        remainingIncome -= taxableInThisSlab;
      }
    }
    
    const effectiveTaxRate = annualIncome > 0 ? (totalTax / annualIncome) * 100 : 0;
    const marginalTaxRate = this.getMarginalTaxRate(taxableIncome);
    
    return {
      annual_income: annualIncome,
      taxable_income: taxableIncome,
      tax_exemption: totalExemption,
      total_tax: totalTax,
      monthly_tax: totalTax / 12,
      effective_tax_rate: effectiveTaxRate,
      marginal_tax_rate: marginalTaxRate,
      tax_breakdown: taxBreakdown
    };
  }
  
  // Get marginal tax rate for given income
  private getMarginalTaxRate(taxableIncome: number): number {
    for (const slab of this.taxSlabs) {
      if (taxableIncome >= slab.min_amount && taxableIncome < slab.max_amount) {
        return slab.tax_rate * 100;
      }
    }
    return this.taxSlabs[this.taxSlabs.length - 1].tax_rate * 100;
  }
  
  // Calculate Nepal-compliant payroll components
  calculatePayrollCompliance(
    monthlySalary: number,
    workingDays: number,
    actualDays: number,
    regularHours: number,
    overtimeHours: number,
    yearsOfService: number
  ): NepalPayrollCompliance {
    
    // Provident Fund calculation (10% employee + 10% employer)
    const pfEmployeeContribution = monthlySalary * this.providentFundRate;
    const pfEmployerContribution = monthlySalary * this.providentFundRate;
    
    // Social Security Fund calculation (varies by salary range)
    const ssfRate = this.calculateSSFRate(monthlySalary);
    const ssfEmployeeContribution = monthlySalary * ssfRate.employee;
    const ssfEmployerContribution = monthlySalary * ssfRate.employer;
    
    // Festival bonus calculation (100% of basic salary during Dashain)
    const festivalBonus = monthlySalary; // Paid once a year during Dashain
    
    // Leave encashment (for unused leave days)
    const dailyRate = monthlySalary / workingDays;
    const leaveEncashment = 0; // Calculate based on unused leave days
    
    // Gratuity calculation (for employees with 1+ years of service)
    const gratuity = yearsOfService >= 1 ? (monthlySalary * yearsOfService * 15) / 365 : 0;
    
    // Overtime calculation
    const hourlyRate = monthlySalary / (workingDays * this.standardWorkingHours);
    const overtimeRate = hourlyRate * this.overtimeMultiplier;
    const overtimePay = overtimeHours * overtimeRate;
    
    return {
      provident_fund: {
        employee_contribution: pfEmployeeContribution,
        employer_contribution: pfEmployerContribution,
        total_contribution: pfEmployeeContribution + pfEmployerContribution
      },
      social_security_fund: {
        employee_contribution: ssfEmployeeContribution,
        employer_contribution: ssfEmployerContribution,
        total_contribution: ssfEmployeeContribution + ssfEmployerContribution
      },
      festival_bonus: festivalBonus,
      leave_encashment: leaveEncashment,
      gratuity: gratuity,
      overtime_calculation: {
        regular_hours: regularHours,
        overtime_hours: overtimeHours,
        overtime_rate: overtimeRate,
        overtime_pay: overtimePay
      }
    };
  }
  
  // Calculate Social Security Fund rates based on salary
  private calculateSSFRate(monthlySalary: number): { employee: number; employer: number } {
    // SSF rates vary by salary range in Nepal
    if (monthlySalary <= 15000) {
      return { employee: 0.11, employer: 0.20 };
    } else if (monthlySalary <= 25000) {
      return { employee: 0.11, employer: 0.20 };
    } else {
      return { employee: 0.11, employer: 0.20 };
    }
  }
  
  // Get current Nepal fiscal year
  getCurrentFiscalYear(): NepalFiscalYear {
    const currentDate = new Date();
    const currentYear = currentDate.getFullYear();
    
    // Nepal fiscal year runs from Shrawan 1 to Ashad 32 (roughly July 16 to July 15)
    const fiscalYearStart = new Date(currentYear, 6, 16); // July 16
    
    let fiscalYear: string;
    let startDate: string;
    let endDate: string;
    let adStartDate: string;
    let adEndDate: string;
    
    if (currentDate >= fiscalYearStart) {
      // Current fiscal year
      fiscalYear = `${currentYear}/${(currentYear + 1).toString().slice(-2)}`;
      startDate = `${currentYear + 57}/04/01`; // BS date
      endDate = `${currentYear + 58}/03/32`; // BS date
      adStartDate = `${currentYear}-07-16`;
      adEndDate = `${currentYear + 1}-07-15`;
    } else {
      // Previous fiscal year
      fiscalYear = `${currentYear - 1}/${currentYear.toString().slice(-2)}`;
      startDate = `${currentYear + 56}/04/01`; // BS date
      endDate = `${currentYear + 57}/03/32`; // BS date
      adStartDate = `${currentYear - 1}-07-16`;
      adEndDate = `${currentYear}-07-15`;
    }
    
    return {
      fiscal_year: fiscalYear,
      start_date: startDate,
      end_date: endDate,
      ad_start_date: adStartDate,
      ad_end_date: adEndDate
    };
  }
  
  // Convert AD date to BS date (simplified conversion)
  convertADToBS(adDate: string): string {
    const date = new Date(adDate);
    const bsYear = date.getFullYear() + 57; // Approximate conversion
    const bsMonth = date.getMonth() + 1;
    const bsDay = date.getDate();
    
    return `${bsYear}/${bsMonth.toString().padStart(2, '0')}/${bsDay.toString().padStart(2, '0')}`;
  }
  
  // Convert BS date to AD date (simplified conversion)
  convertBSToAD(bsDate: string): string {
    const [year, month, day] = bsDate.split('/').map(Number);
    const adYear = year - 57; // Approximate conversion
    const adDate = new Date(adYear, month - 1, day);
    
    return adDate.toISOString().split('T')[0];
  }
  
  // Calculate monthly payroll period in BS
  getMonthlyPayrollPeriod(year: number, month: number): {
    bs_period: string;
    ad_start_date: string;
    ad_end_date: string;
    working_days: number;
  } {
    const startDate = new Date(year, month - 1, 1);
    const endDate = new Date(year, month, 0);
    
    // Calculate working days (excluding Saturdays)
    let workingDays = 0;
    for (let date = new Date(startDate); date <= endDate; date.setDate(date.getDate() + 1)) {
      const dayOfWeek = date.getDay();
      if (dayOfWeek !== 6) { // Exclude Saturday (6)
        workingDays++;
      }
    }
    
    const bsStartDate = this.convertADToBS(startDate.toISOString().split('T')[0]);
    const bsEndDate = this.convertADToBS(endDate.toISOString().split('T')[0]);
    
    return {
      bs_period: `${bsStartDate} to ${bsEndDate}`,
      ad_start_date: startDate.toISOString().split('T')[0],
      ad_end_date: endDate.toISOString().split('T')[0],
      working_days: workingDays
    };
  }
  
  // Validate Nepal compliance for payroll
  validatePayrollCompliance(payrollData: any): {
    is_compliant: boolean;
    violations: string[];
    recommendations: string[];
  } {
    const violations = [];
    const recommendations = [];
    
    // Check minimum wage compliance
    if (payrollData.monthly_salary < 15000) {
      violations.push('Monthly salary below minimum wage (NPR 15,000)');
    }
    
    // Check provident fund compliance
    if (!payrollData.provident_fund || payrollData.provident_fund < payrollData.monthly_salary * 0.10) {
      violations.push('Provident fund contribution below required 10%');
    }
    
    // Check overtime compliance
    if (payrollData.overtime_hours > 4 * 30) { // Max 4 hours per day
      violations.push('Overtime hours exceed legal limit (4 hours per day)');
    }
    
    // Check working hours compliance
    if (payrollData.total_hours > 48 * 4) { // Max 48 hours per week
      violations.push('Total working hours exceed legal limit (48 hours per week)');
    }
    
    // Recommendations
    if (payrollData.festival_bonus === 0) {
      recommendations.push('Consider providing festival bonus during Dashain');
    }
    
    if (payrollData.years_of_service >= 1 && payrollData.gratuity === 0) {
      recommendations.push('Employee eligible for gratuity calculation');
    }
    
    return {
      is_compliant: violations.length === 0,
      violations,
      recommendations
    };
  }
}

export const nepalTaxCalculator = new NepalTaxCalculator();
