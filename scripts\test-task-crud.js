#!/usr/bin/env node

const { neon } = require('@neondatabase/serverless');
require('dotenv').config({ path: '.env.local' });

async function testTaskCRUD() {
  console.log('🧪 TESTING TASK CRUD OPERATIONS');
  console.log('================================\n');
  
  if (!process.env.DATABASE_URL) {
    console.error('❌ ERROR: DATABASE_URL environment variable is not set');
    process.exit(1);
  }
  
  try {
    const sql = neon(process.env.DATABASE_URL);
    
    // Test connection
    console.log('🔄 Testing database connection...');
    await sql`SELECT 1`;
    console.log('✅ Database connection successful!\n');
    
    // Get admin user for testing
    console.log('🔄 Finding admin user for testing...');
    const adminUsers = await sql`
      SELECT id, email, full_name, role 
      FROM users 
      WHERE role = 'admin' AND is_active = true 
      LIMIT 1
    `;
    
    if (adminUsers.length === 0) {
      console.error('❌ No admin user found for testing');
      process.exit(1);
    }
    
    const adminUser = adminUsers[0];
    console.log(`✅ Using admin user: ${adminUser.full_name} (${adminUser.email})\n`);
    
    // Test CREATE operation
    console.log('🔄 Testing CREATE operation...');
    const createResult = await sql`
      INSERT INTO tasks (
        title, description, assigned_to, assigned_by, priority, status
      )
      VALUES (
        'Test Task - CRUD Test',
        'This is a test task created during CRUD testing',
        ${adminUser.id},
        ${adminUser.id},
        'high',
        'todo'
      )
      RETURNING *
    `;
    
    const testTask = createResult[0];
    console.log(`✅ CREATE successful - Task ID: ${testTask.id}`);
    console.log(`   Title: ${testTask.title}`);
    console.log(`   Status: ${testTask.status}`);
    console.log(`   Priority: ${testTask.priority}\n`);
    
    // Test READ operation
    console.log('🔄 Testing READ operation...');
    const readResult = await sql`
      SELECT t.*, u1.full_name as assigned_to_name, u2.full_name as assigned_by_name
      FROM tasks t
      LEFT JOIN users u1 ON t.assigned_to = u1.id
      LEFT JOIN users u2 ON t.assigned_by = u2.id
      WHERE t.id = ${testTask.id}
    `;
    
    if (readResult.length > 0) {
      const task = readResult[0];
      console.log(`✅ READ successful`);
      console.log(`   Title: ${task.title}`);
      console.log(`   Assigned to: ${task.assigned_to_name}`);
      console.log(`   Assigned by: ${task.assigned_by_name}`);
      console.log(`   Created: ${task.created_at}\n`);
    } else {
      console.error('❌ READ failed - Task not found');
    }
    
    // Test UPDATE operation
    console.log('🔄 Testing UPDATE operation...');
    const updateResult = await sql`
      UPDATE tasks 
      SET 
        status = 'in_progress',
        priority = 'urgent',
        description = 'Updated description during CRUD testing',
        updated_at = NOW()
      WHERE id = ${testTask.id}
      RETURNING *
    `;
    
    if (updateResult.length > 0) {
      const updatedTask = updateResult[0];
      console.log(`✅ UPDATE successful`);
      console.log(`   Status: ${testTask.status} → ${updatedTask.status}`);
      console.log(`   Priority: ${testTask.priority} → ${updatedTask.priority}`);
      console.log(`   Updated: ${updatedTask.updated_at}\n`);
    } else {
      console.error('❌ UPDATE failed');
    }
    
    // Test role-based access simulation
    console.log('🔄 Testing role-based access patterns...');
    
    // Admin should see all tasks
    const allTasks = await sql`SELECT COUNT(*) as count FROM tasks`;
    console.log(`✅ Admin view - Total tasks visible: ${allTasks[0].count}`);
    
    // User should see only their tasks (simulated)
    const userTasks = await sql`
      SELECT COUNT(*) as count 
      FROM tasks 
      WHERE assigned_to = ${adminUser.id} OR assigned_by = ${adminUser.id}
    `;
    console.log(`✅ User view - Tasks for ${adminUser.full_name}: ${userTasks[0].count}\n`);
    
    // Test DELETE operation
    console.log('🔄 Testing DELETE operation...');
    const deleteResult = await sql`
      DELETE FROM tasks 
      WHERE id = ${testTask.id}
      RETURNING id
    `;
    
    if (deleteResult.length > 0) {
      console.log(`✅ DELETE successful - Removed task ${deleteResult[0].id}\n`);
    } else {
      console.error('❌ DELETE failed');
    }
    
    // Verify deletion
    console.log('🔄 Verifying deletion...');
    const verifyResult = await sql`
      SELECT COUNT(*) as count 
      FROM tasks 
      WHERE id = ${testTask.id}
    `;
    
    if (verifyResult[0].count === 0) {
      console.log('✅ Deletion verified - Task no longer exists\n');
    } else {
      console.error('❌ Deletion verification failed - Task still exists');
    }
    
    console.log('🎉 TASK CRUD TESTING COMPLETED SUCCESSFULLY!');
    console.log('\n📋 SUMMARY:');
    console.log('✅ CREATE operation: Working');
    console.log('✅ READ operation: Working');
    console.log('✅ UPDATE operation: Working');
    console.log('✅ DELETE operation: Working');
    console.log('✅ Role-based queries: Working');
    console.log('\n💡 RECOMMENDATIONS:');
    console.log('1. Consider implementing RLS policies for additional security');
    console.log('2. Current application-level permissions in API routes are sufficient');
    console.log('3. Database schema is properly configured for the application');
    
  } catch (error) {
    console.error('❌ Error during CRUD testing:', error.message);
    console.error('Stack trace:', error.stack);
    process.exit(1);
  }
}

testTaskCRUD();
