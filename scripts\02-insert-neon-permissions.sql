-- Insert permissions for different resources and actions
INSERT INTO permissions (name, description, resource, action) VALUES
-- User management permissions
('manage_users', 'Create, update, and delete users', 'users', 'manage'),
('view_users', 'View user information', 'users', 'read'),
('edit_user_roles', 'Modify user roles and permissions', 'users', 'edit_roles'),

-- Attendance permissions
('manage_attendance', 'Create, update, and delete attendance records', 'attendance', 'manage'),
('view_attendance', 'View attendance records', 'attendance', 'read'),
('edit_own_attendance', 'Edit own attendance records', 'attendance', 'edit_own'),

-- Task management permissions
('manage_tasks', 'Create, update, and delete tasks', 'tasks', 'manage'),
('view_tasks', 'View tasks', 'tasks', 'read'),
('assign_tasks', 'Assign tasks to users', 'tasks', 'assign'),
('edit_own_tasks', 'Edit own assigned tasks', 'tasks', 'edit_own'),

-- Payroll permissions
('manage_payroll', 'Create, update, and process payroll', 'payroll', 'manage'),
('view_payroll', 'View payroll information', 'payroll', 'read'),
('view_own_payroll', 'View own payroll information', 'payroll', 'read_own'),

-- Reports permissions
('view_reports', 'View system reports and analytics', 'reports', 'read'),
('generate_reports', 'Generate and export reports', 'reports', 'generate'),

-- System permissions
('admin_access', 'Full administrative access', 'system', 'admin'),
('hr_access', 'HR management access', 'system', 'hr'),
('manager_access', 'Manager level access', 'system', 'manager')
ON CONFLICT (name) DO NOTHING;

-- Assign permissions to roles
-- Admin role - full access
INSERT INTO role_permissions (role, permission_id) 
SELECT 'admin', id FROM permissions
ON CONFLICT (role, permission_id) DO NOTHING;

-- HR Manager role
INSERT INTO role_permissions (role, permission_id) 
SELECT 'hr_manager', id FROM permissions 
WHERE name IN (
    'manage_users', 'view_users', 'edit_user_roles',
    'manage_attendance', 'view_attendance',
    'view_tasks', 'assign_tasks',
    'manage_payroll', 'view_payroll',
    'view_reports', 'generate_reports',
    'hr_access'
)
ON CONFLICT (role, permission_id) DO NOTHING;

-- Manager role
INSERT INTO role_permissions (role, permission_id) 
SELECT 'manager', id FROM permissions 
WHERE name IN (
    'view_users',
    'view_attendance', 'edit_own_attendance',
    'manage_tasks', 'view_tasks', 'assign_tasks',
    'view_payroll',
    'view_reports',
    'manager_access'
)
ON CONFLICT (role, permission_id) DO NOTHING;

-- Staff role
INSERT INTO role_permissions (role, permission_id) 
SELECT 'staff', id FROM permissions 
WHERE name IN (
    'view_attendance', 'edit_own_attendance',
    'view_tasks', 'edit_own_tasks',
    'view_own_payroll'
)
ON CONFLICT (role, permission_id) DO NOTHING;
