const { neon } = require('@neondatabase/serverless');
require('dotenv').config({ path: '.env.local' });

async function checkTables() {
  try {
    const sql = neon(process.env.DATABASE_URL);
    console.log('🔍 Checking all tables in database...');
    
    const tables = await sql`
      SELECT table_name 
      FROM information_schema.tables 
      WHERE table_schema = 'public'
      ORDER BY table_name
    `;
    
    console.log('📋 All tables found:');
    tables.forEach(t => console.log('   -', t.table_name));
    
    console.log('\n🔍 Checking specifically for file management tables...');
    const fileTables = await sql`
      SELECT table_name 
      FROM information_schema.tables 
      WHERE table_schema = 'public' 
      AND table_name LIKE '%file%'
      ORDER BY table_name
    `;
    
    console.log('📋 File-related tables:');
    fileTables.forEach(t => console.log('   -', t.table_name));
    
  } catch (error) {
    console.error('❌ Error:', error);
  }
}

checkTables();
