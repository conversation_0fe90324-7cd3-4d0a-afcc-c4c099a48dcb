-- ============================================================================
-- ENHANCED EMPLOYEE PAYROLL SCHEMA
-- Comprehensive employee data structure for advanced payroll management
-- ============================================================================

-- ============================================================================
-- Step 1: Enhance users table with comprehensive payroll fields
-- ============================================================================

-- Add comprehensive employee payroll fields
ALTER TABLE users ADD COLUMN IF NOT EXISTS employee_type VARCHAR(20) DEFAULT 'full_time' 
    CHECK (employee_type IN ('full_time', 'part_time', 'contract', 'intern', 'consultant'));
ALTER TABLE users ADD COLUMN IF NOT EXISTS employee_category VARCHAR(30) DEFAULT 'regular' 
    CHECK (employee_category IN ('regular', 'probation', 'temporary', 'seasonal', 'project_based'));
ALTER TABLE users ADD COLUMN IF NOT EXISTS tax_identification_number VARCHAR(50);
ALTER TABLE users ADD COLUMN IF NOT EXISTS citizenship_number VARCHAR(20);
ALTER TABLE users ADD COLUMN IF NOT EXISTS pan_number VARCHAR(20);
ALTER TABLE users ADD COLUMN IF NOT EXISTS probation_period_months INTEGER DEFAULT 3;
ALTER TABLE users ADD COLUMN IF NOT EXISTS confirmation_date DATE;
ALTER TABLE users ADD COLUMN IF NOT EXISTS employment_status VARCHAR(20) DEFAULT 'active' 
    CHECK (employment_status IN ('active', 'inactive', 'terminated', 'resigned', 'retired'));
ALTER TABLE users ADD COLUMN IF NOT EXISTS termination_date DATE;
ALTER TABLE users ADD COLUMN IF NOT EXISTS termination_reason TEXT;
ALTER TABLE users ADD COLUMN IF NOT EXISTS pay_grade VARCHAR(10);
ALTER TABLE users ADD COLUMN IF NOT EXISTS salary_currency VARCHAR(5) DEFAULT 'NPR';
ALTER TABLE users ADD COLUMN IF NOT EXISTS joining_bonus DECIMAL(10,2) DEFAULT 0;
ALTER TABLE users ADD COLUMN IF NOT EXISTS notice_period_days INTEGER DEFAULT 30;

-- ============================================================================
-- Step 2: Create employee_bank_accounts table
-- ============================================================================

CREATE TABLE IF NOT EXISTS employee_bank_accounts (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    bank_name VARCHAR(100) NOT NULL,
    bank_branch VARCHAR(100),
    account_number VARCHAR(50) NOT NULL,
    account_holder_name VARCHAR(200) NOT NULL,
    account_type VARCHAR(20) DEFAULT 'savings' 
        CHECK (account_type IN ('savings', 'current', 'salary')),
    swift_code VARCHAR(20),
    is_primary BOOLEAN DEFAULT TRUE,
    is_active BOOLEAN DEFAULT TRUE,
    verified_at TIMESTAMP WITH TIME ZONE,
    verified_by UUID REFERENCES users(id),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(user_id, account_number, bank_name)
);

-- ============================================================================
-- Step 3: Create employee_addresses table
-- ============================================================================

CREATE TABLE IF NOT EXISTS employee_addresses (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    address_type VARCHAR(20) NOT NULL CHECK (address_type IN ('permanent', 'temporary', 'emergency')),
    address_line_1 VARCHAR(200) NOT NULL,
    address_line_2 VARCHAR(200),
    city VARCHAR(100),
    district VARCHAR(100),
    province VARCHAR(100),
    postal_code VARCHAR(20),
    country VARCHAR(100) DEFAULT 'Nepal',
    is_primary BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- ============================================================================
-- Step 4: Create employee_emergency_contacts table
-- ============================================================================

CREATE TABLE IF NOT EXISTS employee_emergency_contacts (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    contact_name VARCHAR(200) NOT NULL,
    relationship VARCHAR(50) NOT NULL,
    phone_number VARCHAR(20) NOT NULL,
    email VARCHAR(200),
    address TEXT,
    is_primary BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- ============================================================================
-- Step 5: Create payroll_audit_trail table
-- ============================================================================

CREATE TABLE IF NOT EXISTS payroll_audit_trail (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    table_name VARCHAR(100) NOT NULL,
    record_id UUID NOT NULL,
    action VARCHAR(20) NOT NULL CHECK (action IN ('INSERT', 'UPDATE', 'DELETE')),
    field_name VARCHAR(100),
    old_value TEXT,
    new_value TEXT,
    changed_by UUID REFERENCES users(id),
    change_reason TEXT,
    ip_address INET,
    user_agent TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- ============================================================================
-- Step 6: Create deduction_approvals table for audit trail
-- ============================================================================

CREATE TABLE IF NOT EXISTS deduction_approvals (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    component_id UUID NOT NULL REFERENCES payroll_components_master(id),
    deduction_amount DECIMAL(10,2) NOT NULL,
    deduction_reason TEXT NOT NULL, -- Mandatory reason field
    requested_by UUID NOT NULL REFERENCES users(id),
    approved_by UUID REFERENCES users(id),
    status VARCHAR(20) DEFAULT 'pending' 
        CHECK (status IN ('pending', 'approved', 'rejected', 'cancelled')),
    request_date DATE NOT NULL DEFAULT CURRENT_DATE,
    approval_date DATE,
    effective_from DATE NOT NULL,
    effective_to DATE,
    notes TEXT,
    rejection_reason TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- ============================================================================
-- Step 7: Create allowance_assignments table
-- ============================================================================

CREATE TABLE IF NOT EXISTS allowance_assignments (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    allowance_type VARCHAR(50) NOT NULL 
        CHECK (allowance_type IN ('travelling', 'phone', 'meal', 'transport', 'medical', 'education', 'custom')),
    allowance_name VARCHAR(100) NOT NULL,
    calculation_type VARCHAR(20) NOT NULL CHECK (calculation_type IN ('fixed', 'percentage', 'per_km', 'per_day')),
    amount DECIMAL(10,2) DEFAULT 0,
    percentage DECIMAL(5,2) DEFAULT 0,
    percentage_base VARCHAR(30) CHECK (percentage_base IN ('base_salary', 'gross_pay')),
    per_unit_rate DECIMAL(8,2) DEFAULT 0, -- For per_km or per_day calculations
    monthly_limit DECIMAL(10,2),
    is_taxable BOOLEAN DEFAULT TRUE,
    requires_receipt BOOLEAN DEFAULT FALSE,
    auto_calculate BOOLEAN DEFAULT TRUE,
    effective_from DATE NOT NULL,
    effective_to DATE,
    is_active BOOLEAN DEFAULT TRUE,
    assigned_by UUID REFERENCES users(id),
    approved_by UUID REFERENCES users(id),
    approval_date DATE,
    notes TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- ============================================================================
-- Step 8: Create monthly_payroll_summary table
-- ============================================================================

CREATE TABLE IF NOT EXISTS monthly_payroll_summary (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    payroll_period_id UUID REFERENCES payroll_periods(id),
    fiscal_year VARCHAR(10) NOT NULL,
    bs_month VARCHAR(20) NOT NULL,
    ad_month_start DATE NOT NULL,
    ad_month_end DATE NOT NULL,
    
    -- Attendance summary
    total_working_days INTEGER DEFAULT 0,
    days_present INTEGER DEFAULT 0,
    days_absent INTEGER DEFAULT 0,
    days_late INTEGER DEFAULT 0,
    days_half_day INTEGER DEFAULT 0,
    days_on_leave INTEGER DEFAULT 0,
    total_hours_worked DECIMAL(6,2) DEFAULT 0,
    regular_hours DECIMAL(6,2) DEFAULT 0,
    overtime_hours DECIMAL(6,2) DEFAULT 0,
    
    -- Salary components
    base_salary DECIMAL(10,2) DEFAULT 0,
    overtime_pay DECIMAL(10,2) DEFAULT 0,
    total_allowances DECIMAL(10,2) DEFAULT 0,
    total_deductions DECIMAL(10,2) DEFAULT 0,
    gross_pay DECIMAL(10,2) DEFAULT 0,
    tax_deductions DECIMAL(10,2) DEFAULT 0,
    provident_fund DECIMAL(10,2) DEFAULT 0,
    net_pay DECIMAL(10,2) DEFAULT 0,
    
    -- Bonuses and penalties
    attendance_bonus DECIMAL(10,2) DEFAULT 0,
    festival_bonus DECIMAL(10,2) DEFAULT 0,
    performance_bonus DECIMAL(10,2) DEFAULT 0,
    late_penalty DECIMAL(10,2) DEFAULT 0,
    
    -- Status and processing
    status VARCHAR(20) DEFAULT 'draft' CHECK (status IN ('draft', 'calculated', 'approved', 'processed', 'paid')),
    calculated_at TIMESTAMP WITH TIME ZONE,
    calculated_by UUID REFERENCES users(id),
    approved_at TIMESTAMP WITH TIME ZONE,
    approved_by UUID REFERENCES users(id),
    processed_at TIMESTAMP WITH TIME ZONE,
    processed_by UUID REFERENCES users(id),
    
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    UNIQUE(user_id, fiscal_year, bs_month)
);

-- ============================================================================
-- Step 9: Insert default allowance types
-- ============================================================================

INSERT INTO payroll_components_master (name, code, type, category, calculation_type, description, is_active, effective_from) VALUES
('Travelling Allowance', 'TRAVEL_ALLOW', 'allowance', 'company_policy', 'fixed', 'Monthly travelling allowance for employees', TRUE, CURRENT_DATE),
('Phone Allowance', 'PHONE_ALLOW', 'allowance', 'company_policy', 'fixed', 'Monthly phone/communication allowance', TRUE, CURRENT_DATE),
('Meal Allowance', 'MEAL_ALLOW', 'allowance', 'company_policy', 'fixed', 'Daily meal allowance for employees', TRUE, CURRENT_DATE),
('Transport Allowance', 'TRANSPORT_ALLOW', 'allowance', 'company_policy', 'fixed', 'Monthly transport allowance', TRUE, CURRENT_DATE),
('Medical Allowance', 'MEDICAL_ALLOW', 'allowance', 'company_policy', 'fixed', 'Annual medical allowance', TRUE, CURRENT_DATE),
('Education Allowance', 'EDU_ALLOW', 'allowance', 'company_policy', 'percentage', 'Education allowance for employee development', TRUE, CURRENT_DATE)
ON CONFLICT (code) DO NOTHING;

-- Insert default deduction types
INSERT INTO payroll_components_master (name, code, type, category, calculation_type, is_statutory, description, is_active, effective_from) VALUES
('Income Tax', 'INCOME_TAX', 'deduction', 'statutory', 'formula', TRUE, 'Nepal Income Tax deduction', TRUE, CURRENT_DATE),
('Provident Fund', 'PF_DEDUCTION', 'deduction', 'statutory', 'percentage', TRUE, 'Employee Provident Fund contribution', TRUE, CURRENT_DATE),
('Insurance Premium', 'INSURANCE', 'deduction', 'voluntary', 'fixed', FALSE, 'Health/Life insurance premium deduction', TRUE, CURRENT_DATE),
('Loan EMI', 'LOAN_EMI', 'deduction', 'voluntary', 'fixed', FALSE, 'Employee loan EMI deduction', TRUE, CURRENT_DATE),
('Advance Salary Recovery', 'ADVANCE_RECOVERY', 'deduction', 'company_policy', 'fixed', FALSE, 'Recovery of advance salary payments', TRUE, CURRENT_DATE),
('Late Fine', 'LATE_FINE', 'deduction', 'company_policy', 'formula', FALSE, 'Penalty for late attendance', TRUE, CURRENT_DATE)
ON CONFLICT (code) DO NOTHING;

-- ============================================================================
-- Step 10: Create indexes for performance optimization
-- ============================================================================

-- Employee bank accounts indexes
CREATE INDEX IF NOT EXISTS idx_employee_bank_accounts_user ON employee_bank_accounts(user_id);
CREATE INDEX IF NOT EXISTS idx_employee_bank_accounts_primary ON employee_bank_accounts(user_id, is_primary) WHERE is_primary = TRUE;
CREATE INDEX IF NOT EXISTS idx_employee_bank_accounts_active ON employee_bank_accounts(is_active) WHERE is_active = TRUE;

-- Employee addresses indexes
CREATE INDEX IF NOT EXISTS idx_employee_addresses_user ON employee_addresses(user_id);
CREATE INDEX IF NOT EXISTS idx_employee_addresses_type ON employee_addresses(user_id, address_type);

-- Employee emergency contacts indexes
CREATE INDEX IF NOT EXISTS idx_employee_emergency_contacts_user ON employee_emergency_contacts(user_id);

-- Payroll audit trail indexes
CREATE INDEX IF NOT EXISTS idx_payroll_audit_trail_table_record ON payroll_audit_trail(table_name, record_id);
CREATE INDEX IF NOT EXISTS idx_payroll_audit_trail_changed_by ON payroll_audit_trail(changed_by);
CREATE INDEX IF NOT EXISTS idx_payroll_audit_trail_created_at ON payroll_audit_trail(created_at);

-- Deduction approvals indexes
CREATE INDEX IF NOT EXISTS idx_deduction_approvals_user ON deduction_approvals(user_id);
CREATE INDEX IF NOT EXISTS idx_deduction_approvals_status ON deduction_approvals(status);
CREATE INDEX IF NOT EXISTS idx_deduction_approvals_requested_by ON deduction_approvals(requested_by);
CREATE INDEX IF NOT EXISTS idx_deduction_approvals_effective ON deduction_approvals(effective_from, effective_to);

-- Allowance assignments indexes
CREATE INDEX IF NOT EXISTS idx_allowance_assignments_user ON allowance_assignments(user_id);
CREATE INDEX IF NOT EXISTS idx_allowance_assignments_type ON allowance_assignments(allowance_type);
CREATE INDEX IF NOT EXISTS idx_allowance_assignments_active ON allowance_assignments(user_id, is_active) WHERE is_active = TRUE;
CREATE INDEX IF NOT EXISTS idx_allowance_assignments_effective ON allowance_assignments(effective_from, effective_to);

-- Monthly payroll summary indexes
CREATE INDEX IF NOT EXISTS idx_monthly_payroll_summary_user ON monthly_payroll_summary(user_id);
CREATE INDEX IF NOT EXISTS idx_monthly_payroll_summary_period ON monthly_payroll_summary(fiscal_year, bs_month);
CREATE INDEX IF NOT EXISTS idx_monthly_payroll_summary_status ON monthly_payroll_summary(status);

-- ============================================================================
-- Step 11: Create triggers for updated_at columns
-- ============================================================================

-- Employee bank accounts trigger
DROP TRIGGER IF EXISTS update_employee_bank_accounts_updated_at ON employee_bank_accounts;
CREATE TRIGGER update_employee_bank_accounts_updated_at
    BEFORE UPDATE ON employee_bank_accounts
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Employee addresses trigger
DROP TRIGGER IF EXISTS update_employee_addresses_updated_at ON employee_addresses;
CREATE TRIGGER update_employee_addresses_updated_at
    BEFORE UPDATE ON employee_addresses
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Employee emergency contacts trigger
DROP TRIGGER IF EXISTS update_employee_emergency_contacts_updated_at ON employee_emergency_contacts;
CREATE TRIGGER update_employee_emergency_contacts_updated_at
    BEFORE UPDATE ON employee_emergency_contacts
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Deduction approvals trigger
DROP TRIGGER IF EXISTS update_deduction_approvals_updated_at ON deduction_approvals;
CREATE TRIGGER update_deduction_approvals_updated_at
    BEFORE UPDATE ON deduction_approvals
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Allowance assignments trigger
DROP TRIGGER IF EXISTS update_allowance_assignments_updated_at ON allowance_assignments;
CREATE TRIGGER update_allowance_assignments_updated_at
    BEFORE UPDATE ON allowance_assignments
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Monthly payroll summary trigger
DROP TRIGGER IF EXISTS update_monthly_payroll_summary_updated_at ON monthly_payroll_summary;
CREATE TRIGGER update_monthly_payroll_summary_updated_at
    BEFORE UPDATE ON monthly_payroll_summary
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- ============================================================================
-- Step 12: Create audit trail trigger function
-- ============================================================================

CREATE OR REPLACE FUNCTION create_audit_trail()
RETURNS TRIGGER AS $$
BEGIN
    IF TG_OP = 'DELETE' THEN
        INSERT INTO payroll_audit_trail (
            table_name, record_id, action, changed_by, change_reason, created_at
        ) VALUES (
            TG_TABLE_NAME, OLD.id, 'DELETE',
            COALESCE(current_setting('app.current_user_id', true)::UUID, NULL),
            COALESCE(current_setting('app.change_reason', true), 'Record deleted'),
            NOW()
        );
        RETURN OLD;
    ELSIF TG_OP = 'UPDATE' THEN
        INSERT INTO payroll_audit_trail (
            table_name, record_id, action, changed_by, change_reason, created_at
        ) VALUES (
            TG_TABLE_NAME, NEW.id, 'UPDATE',
            COALESCE(current_setting('app.current_user_id', true)::UUID, NULL),
            COALESCE(current_setting('app.change_reason', true), 'Record updated'),
            NOW()
        );
        RETURN NEW;
    ELSIF TG_OP = 'INSERT' THEN
        INSERT INTO payroll_audit_trail (
            table_name, record_id, action, changed_by, change_reason, created_at
        ) VALUES (
            TG_TABLE_NAME, NEW.id, 'INSERT',
            COALESCE(current_setting('app.current_user_id', true)::UUID, NULL),
            COALESCE(current_setting('app.change_reason', true), 'Record created'),
            NOW()
        );
        RETURN NEW;
    END IF;
    RETURN NULL;
END;
$$ LANGUAGE plpgsql;

-- ============================================================================
-- Step 13: Apply audit trail triggers to critical tables
-- ============================================================================

-- Deduction approvals audit trail
DROP TRIGGER IF EXISTS audit_deduction_approvals ON deduction_approvals;
CREATE TRIGGER audit_deduction_approvals
    AFTER INSERT OR UPDATE OR DELETE ON deduction_approvals
    FOR EACH ROW EXECUTE FUNCTION create_audit_trail();

-- Employee component assignments audit trail
DROP TRIGGER IF EXISTS audit_employee_component_assignments ON employee_component_assignments;
CREATE TRIGGER audit_employee_component_assignments
    AFTER INSERT OR UPDATE OR DELETE ON employee_component_assignments
    FOR EACH ROW EXECUTE FUNCTION create_audit_trail();

-- Employee pay structure audit trail
DROP TRIGGER IF EXISTS audit_employee_pay_structure ON employee_pay_structure;
CREATE TRIGGER audit_employee_pay_structure
    AFTER INSERT OR UPDATE OR DELETE ON employee_pay_structure
    FOR EACH ROW EXECUTE FUNCTION create_audit_trail();

-- Monthly payroll summary audit trail
DROP TRIGGER IF EXISTS audit_monthly_payroll_summary ON monthly_payroll_summary;
CREATE TRIGGER audit_monthly_payroll_summary
    AFTER INSERT OR UPDATE OR DELETE ON monthly_payroll_summary
    FOR EACH ROW EXECUTE FUNCTION create_audit_trail();

-- ============================================================================
-- SUCCESS CRITERIA
-- ============================================================================
-- After successful completion:
-- ✅ Enhanced users table with comprehensive employee payroll fields
-- ✅ Employee bank accounts table created for salary transfer information
-- ✅ Employee addresses table created for comprehensive employee data
-- ✅ Employee emergency contacts table created
-- ✅ Payroll audit trail system implemented
-- ✅ Deduction approvals table with mandatory reason field
-- ✅ Allowance assignments table for flexible allowance management
-- ✅ Monthly payroll summary table for Nepal-compliant payroll cycles
-- ✅ Default allowance and deduction types inserted
-- ✅ All necessary indexes created for performance
-- ✅ Audit trail triggers configured for critical tables
-- ✅ Updated_at triggers configured for all new tables
