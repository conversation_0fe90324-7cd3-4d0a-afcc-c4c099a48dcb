// Apply the enhanced payroll schema to create missing tables
const { neon } = require('@neondatabase/serverless');
const fs = require('fs');
const path = require('path');
require('dotenv').config({ path: '.env.local' });

async function applyEnhancedPayrollSchema() {
  try {
    console.log('Applying enhanced payroll schema...\n');

    const sql = neon(process.env.DATABASE_URL);

    // Read the enhanced payroll schema file
    const schemaPath = path.join(__dirname, '02-enhanced-employee-payroll-schema.sql');
    const schemaSQL = fs.readFileSync(schemaPath, 'utf8');

    console.log('Schema file loaded, executing SQL...');

    // Split the SQL into individual statements and execute them
    const statements = schemaSQL
      .split(';')
      .map(stmt => stmt.trim())
      .filter(stmt => stmt.length > 0 && !stmt.startsWith('--'));

    console.log(`Found ${statements.length} SQL statements to execute\n`);

    let successCount = 0;
    let errorCount = 0;

    for (let i = 0; i < statements.length; i++) {
      const statement = statements[i];
      
      // Skip comments and empty statements
      if (statement.startsWith('--') || statement.trim().length === 0) {
        continue;
      }

      try {
        console.log(`Executing statement ${i + 1}/${statements.length}...`);
        
        // For CREATE TABLE statements, show which table is being created
        if (statement.toUpperCase().includes('CREATE TABLE')) {
          const tableMatch = statement.match(/CREATE TABLE\s+(?:IF NOT EXISTS\s+)?(\w+)/i);
          if (tableMatch) {
            console.log(`  Creating table: ${tableMatch[1]}`);
          }
        }

        await sql.query(statement);
        successCount++;
        console.log('  ✅ Success');
        
      } catch (error) {
        errorCount++;
        console.log(`  ❌ Error: ${error.message}`);
        
        // Continue with other statements even if one fails
        if (error.message.includes('already exists')) {
          console.log('  (Table/column already exists, continuing...)');
        }
      }
    }

    console.log(`\nSchema application completed:`);
    console.log(`✅ Successful statements: ${successCount}`);
    console.log(`❌ Failed statements: ${errorCount}`);

    // Verify the tables were created
    console.log('\nVerifying table creation...');
    const tables = [
      'employee_bank_accounts',
      'allowance_assignments', 
      'deduction_approvals'
    ];

    for (const table of tables) {
      try {
        const result = await sql`
          SELECT EXISTS (
            SELECT FROM information_schema.tables 
            WHERE table_schema = 'public' 
            AND table_name = ${table}
          );
        `;
        
        const exists = result[0].exists;
        console.log(`Table ${table}: ${exists ? '✅ EXISTS' : '❌ STILL MISSING'}`);
        
      } catch (error) {
        console.log(`❌ Error checking table ${table}:`, error.message);
      }
    }

    // Check if the new columns were added to users table
    console.log('\nChecking users table columns...');
    try {
      const userColumns = await sql`
        SELECT column_name
        FROM information_schema.columns 
        WHERE table_schema = 'public' 
        AND table_name = 'users'
        AND column_name IN ('employee_type', 'employee_category', 'tax_identification_number', 'citizenship_number', 'pan_number')
        ORDER BY column_name;
      `;
      
      console.log(`New users table columns found: ${userColumns.map(c => c.column_name).join(', ')}`);
      
    } catch (error) {
      console.log('❌ Error checking users table columns:', error.message);
    }

  } catch (error) {
    console.error('❌ Error applying schema:', error.message);
  }
}

applyEnhancedPayrollSchema();
