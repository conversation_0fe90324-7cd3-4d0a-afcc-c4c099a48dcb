// Nepal-Compliant Payroll Processing API Endpoints
// Admin endpoints for Nepal-compliant monthly payroll processing

import { NextRequest, NextResponse } from 'next/server';
import { nepalPayrollProcessor } from '@/lib/nepal-payroll-processor';
import { nepalTaxCalculator } from '@/lib/nepal-tax-calculator';
import { AuthService } from '@/lib/auth-utils';
import { db } from '@/lib/neon';

// GET - Get payroll records, tax calculations, or compliance reports
export async function GET(request: NextRequest) {
  try {
    const sessionToken = request.cookies.get("session-token")?.value;

    if (!sessionToken) {
      return NextResponse.json({
        success: false,
        error: 'Unauthorized'
      }, { status: 401 });
    }

    const user = await AuthService.verifySession(sessionToken);

    if (!user || (user.role !== 'admin' && user.role !== 'hr_manager')) {
      return NextResponse.json({
        success: false,
        error: 'Insufficient permissions'
      }, { status: 403 });
    }

    const { searchParams } = new URL(request.url);
    const action = searchParams.get('action');
    const year = parseInt(searchParams.get('year') || new Date().getFullYear().toString());
    const month = parseInt(searchParams.get('month') || (new Date().getMonth() + 1).toString());
    const userId = searchParams.get('userId');

    if (action === 'fiscal_year') {
      // Get current Nepal fiscal year information
      const fiscalYear = nepalTaxCalculator.getCurrentFiscalYear();
      return NextResponse.json({
        success: true,
        data: fiscalYear
      });
    } else if (action === 'tax_calculation' && userId) {
      // Calculate tax for specific employee
      const employee = await db.sql`
        SELECT salary FROM users WHERE id = ${userId}
      `;
      
      if (employee.length === 0) {
        return NextResponse.json({
          success: false,
          error: 'Employee not found'
        }, { status: 404 });
      }
      
      const annualIncome = (employee[0].salary || 0) * 12;
      const taxCalculation = nepalTaxCalculator.calculateIncomeTax(annualIncome);
      
      return NextResponse.json({
        success: true,
        data: taxCalculation
      });
    } else if (action === 'payroll_records') {
      // Get existing payroll records for the period
      const fiscalYear = nepalTaxCalculator.getCurrentFiscalYear();
      const payrollPeriod = `${year}-${month.toString().padStart(2, '0')}`;
      
      const records = await db.sql`
        SELECT 
          mps.*,
          u.full_name,
          u.email,
          u.department,
          u.position
        FROM monthly_payroll_summary mps
        JOIN users u ON mps.user_id = u.id
        WHERE mps.fiscal_year = ${fiscalYear.fiscal_year}
          AND mps.bs_month LIKE ${payrollPeriod + '%'}
        ORDER BY u.full_name
      `;
      
      return NextResponse.json({
        success: true,
        data: records
      });
    } else if (action === 'compliance_report') {
      // Generate compliance report for the period
      const fiscalYear = nepalTaxCalculator.getCurrentFiscalYear();
      const payrollPeriod = `${year}-${month.toString().padStart(2, '0')}`;
      
      const records = await db.sql`
        SELECT 
          mps.*,
          u.full_name,
          u.department
        FROM monthly_payroll_summary mps
        JOIN users u ON mps.user_id = u.id
        WHERE mps.fiscal_year = ${fiscalYear.fiscal_year}
          AND mps.bs_month LIKE ${payrollPeriod + '%'}
      `;
      
      // Generate compliance statistics
      const totalEmployees = records.length;
      const totalGrossPay = records.reduce((sum, r) => sum + (r.gross_pay || 0), 0);
      const totalNetPay = records.reduce((sum, r) => sum + (r.net_pay || 0), 0);
      const totalTax = records.reduce((sum, r) => sum + (r.tax_deductions || 0), 0);
      const totalPF = records.reduce((sum, r) => sum + (r.provident_fund || 0), 0);
      
      const complianceReport = {
        period: payrollPeriod,
        fiscal_year: fiscalYear.fiscal_year,
        total_employees: totalEmployees,
        total_gross_pay: totalGrossPay,
        total_net_pay: totalNetPay,
        total_tax_deducted: totalTax,
        total_pf_deducted: totalPF,
        average_salary: totalEmployees > 0 ? totalGrossPay / totalEmployees : 0,
        records: records
      };
      
      return NextResponse.json({
        success: true,
        data: complianceReport
      });
    } else if (action === 'payroll_summary') {
      // Get payroll summary for multiple periods
      const fiscalYear = nepalTaxCalculator.getCurrentFiscalYear();
      
      const summaries = await db.sql`
        SELECT 
          bs_month,
          COUNT(*) as total_employees,
          SUM(gross_pay) as total_gross_pay,
          SUM(net_pay) as total_net_pay,
          SUM(tax_deductions) as total_tax,
          SUM(provident_fund) as total_pf,
          AVG(gross_pay) as average_gross_pay
        FROM monthly_payroll_summary
        WHERE fiscal_year = ${fiscalYear.fiscal_year}
        GROUP BY bs_month
        ORDER BY bs_month DESC
      `;
      
      return NextResponse.json({
        success: true,
        data: summaries
      });
    } else {
      return NextResponse.json({
        success: false,
        error: 'Invalid action parameter'
      }, { status: 400 });
    }

  } catch (error) {
    console.error('Error in Nepal-compliant payroll GET:', error);
    return NextResponse.json({
      success: false,
      error: error.message || 'Internal server error'
    }, { status: 500 });
  }
}

// POST - Process Nepal-compliant payroll
export async function POST(request: NextRequest) {
  try {
    const sessionToken = request.cookies.get("session-token")?.value;

    if (!sessionToken) {
      return NextResponse.json({
        success: false,
        error: 'Unauthorized'
      }, { status: 401 });
    }

    const user = await AuthService.verifySession(sessionToken);

    if (!user || (user.role !== 'admin' && user.role !== 'hr_manager')) {
      return NextResponse.json({
        success: false,
        error: 'Insufficient permissions'
      }, { status: 403 });
    }

    const body = await request.json();
    const { action, year, month, userId } = body;

    if (action === 'process_monthly') {
      // Process monthly payroll for all employees
      if (!year || !month) {
        return NextResponse.json({
          success: false,
          error: 'Year and month are required'
        }, { status: 400 });
      }

      const result = await nepalPayrollProcessor.processMonthlyPayroll(year, month, user.id);

      return NextResponse.json({
        success: true,
        data: result,
        message: `Monthly payroll processed: ${result.processed_count} employees processed, ${result.failed_count} failed`
      });

    } else if (action === 'process_employee') {
      // Process payroll for single employee
      if (!userId || !year || !month) {
        return NextResponse.json({
          success: false,
          error: 'User ID, year, and month are required'
        }, { status: 400 });
      }

      const fiscalYear = nepalTaxCalculator.getCurrentFiscalYear();
      const payrollPeriod = nepalTaxCalculator.getMonthlyPayrollPeriod(year, month);

      const payrollRecord = await nepalPayrollProcessor.processEmployeePayroll(
        userId,
        year,
        month,
        fiscalYear,
        payrollPeriod,
        user.id
      );

      return NextResponse.json({
        success: true,
        data: payrollRecord,
        message: 'Employee payroll processed successfully'
      });

    } else if (action === 'validate_compliance') {
      // Validate payroll compliance
      const { payrollData } = body;

      if (!payrollData) {
        return NextResponse.json({
          success: false,
          error: 'Payroll data is required'
        }, { status: 400 });
      }

      const complianceValidation = nepalTaxCalculator.validatePayrollCompliance(payrollData);

      return NextResponse.json({
        success: true,
        data: complianceValidation
      });

    } else {
      return NextResponse.json({
        success: false,
        error: 'Invalid action'
      }, { status: 400 });
    }

  } catch (error) {
    console.error('Error in Nepal-compliant payroll POST:', error);
    return NextResponse.json({
      success: false,
      error: error.message || 'Internal server error'
    }, { status: 500 });
  }
}

// PUT - Update payroll status or approve payroll
export async function PUT(request: NextRequest) {
  try {
    const sessionToken = request.cookies.get("session-token")?.value;

    if (!sessionToken) {
      return NextResponse.json({
        success: false,
        error: 'Unauthorized'
      }, { status: 401 });
    }

    const user = await AuthService.verifySession(sessionToken);

    if (!user || (user.role !== 'admin' && user.role !== 'hr_manager')) {
      return NextResponse.json({
        success: false,
        error: 'Insufficient permissions'
      }, { status: 403 });
    }

    const body = await request.json();
    const { action, payrollIds, status } = body;

    if (action === 'update_status') {
      if (!payrollIds || !Array.isArray(payrollIds) || !status) {
        return NextResponse.json({
          success: false,
          error: 'Payroll IDs array and status are required'
        }, { status: 400 });
      }

      // Validate status
      if (!['approved', 'processed', 'paid'].includes(status)) {
        return NextResponse.json({
          success: false,
          error: 'Invalid status'
        }, { status: 400 });
      }

      const result = await nepalPayrollProcessor.updatePayrollStatus(payrollIds, status, user.id);

      return NextResponse.json({
        success: true,
        data: result,
        message: `Payroll status updated: ${result.updated} records updated, ${result.failed} failed`
      });

    } else {
      return NextResponse.json({
        success: false,
        error: 'Invalid action'
      }, { status: 400 });
    }

  } catch (error) {
    console.error('Error in Nepal-compliant payroll PUT:', error);
    return NextResponse.json({
      success: false,
      error: 'Internal server error'
    }, { status: 500 });
  }
}
