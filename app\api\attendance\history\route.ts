import { type NextRequest, NextResponse } from "next/server"
import { AuthService } from "@/lib/auth-utils"
import { db } from "@/lib/neon"

export async function GET(request: NextRequest) {
  try {
    const sessionToken = request.cookies.get("session-token")?.value

    if (!sessionToken) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 })
    }

    const user = await AuthService.verifySession(sessionToken)

    if (!user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 })
    }

    const { searchParams } = new URL(request.url)
    const limit = Number.parseInt(searchParams.get("limit") || "30")

    const history = await db.getUserAttendanceHistory(user.id, limit)
    const stats = await db.getUserAttendanceStats(user.id)

    return NextResponse.json({
      success: true,
      history,
      stats,
    })
  } catch (error) {
    console.error("Attendance history API error:", error)
    return NextResponse.json({ error: "Internal server error" }, { status: 500 })
  }
}
