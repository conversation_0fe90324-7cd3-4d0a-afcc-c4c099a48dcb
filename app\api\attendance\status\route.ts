import { type NextRequest, NextResponse } from "next/server"
import { AuthService } from "@/lib/auth-utils"
import { db } from "@/lib/neon"

export async function GET(request: NextRequest) {
  try {
    const sessionToken = request.cookies.get("session-token")?.value

    if (!sessionToken) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 })
    }

    const user = await AuthService.verifySession(sessionToken)

    if (!user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 })
    }

    // Get comprehensive attendance status
    const attendanceStatus = await db.getCurrentAttendanceStatus(user.id)

    return NextResponse.json({
      success: true,
      attendance: attendanceStatus.activeSession || attendanceStatus.todayEntries[attendanceStatus.todayEntries.length - 1] || null,
      isCheckedIn: attendanceStatus.isCheckedIn,
      todayEntries: attendanceStatus.todayEntries,
      totalHoursToday: attendanceStatus.totalHoursToday,
      remainingCheckIns: attendanceStatus.remainingCheckIns,
      remainingCheckOuts: attendanceStatus.remainingCheckOuts,
      activeSession: attendanceStatus.activeSession,
      dailySummary: attendanceStatus.dailySummary,
      warnings: attendanceStatus.warnings
    })
  } catch (error) {
    console.error("Attendance status API error:", error)
    return NextResponse.json({ error: "Internal server error" }, { status: 500 })
  }
}
