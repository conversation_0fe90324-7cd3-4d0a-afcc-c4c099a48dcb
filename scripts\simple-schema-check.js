require('dotenv').config({ path: '.env.local' });
const { neon } = require('@neondatabase/serverless');

async function simpleCheck() {
  try {
    const sql = neon(process.env.DATABASE_URL);
    
    console.log('🔍 Checking all existing tables...');
    
    const allTables = await sql`
      SELECT table_name 
      FROM information_schema.tables 
      WHERE table_schema = 'public'
      ORDER BY table_name
    `;
    
    console.log('All existing tables:', allTables.map(t => t.table_name));
    
    // Check users table structure
    const userColumns = await sql`
      SELECT column_name, data_type, is_nullable
      FROM information_schema.columns 
      WHERE table_name = 'users'
      ORDER BY ordinal_position
    `;
    
    console.log('\nUsers table structure:');
    userColumns.forEach(col => {
      console.log(`  - ${col.column_name}: ${col.data_type} (${col.is_nullable === 'YES' ? 'nullable' : 'not null'})`);
    });
    
  } catch (error) {
    console.error('❌ Check failed:', error.message);
  }
}

simpleCheck();
