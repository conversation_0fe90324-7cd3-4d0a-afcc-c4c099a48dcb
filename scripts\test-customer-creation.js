require('dotenv').config({ path: '.env.local' })
const { neon } = require('@neondatabase/serverless')

// Initialize database connection
const sql = neon(process.env.DATABASE_URL)

async function testCustomerCreation() {
  console.log('🔍 Testing customer creation API...')

  try {
    // First, get an admin user for authentication
    const adminUsers = await sql`
      SELECT id, email, role FROM users
      WHERE role IN ('admin', 'hr_manager')
      LIMIT 1
    `

    if (adminUsers.length === 0) {
      console.error('❌ No admin users found. Creating a test admin user...')
      
      // Create a test admin user
      const newAdmin = await sql`
        INSERT INTO users (email, password_hash, full_name, role, is_active)
        VALUES ('<EMAIL>', '$2a$10$dummy.hash', 'Test Admin', 'admin', true)
        RETURNING id, email, role
      `
      
      console.log('✅ Created test admin user:', newAdmin[0])
      adminUser = newAdmin[0]
    } else {
      adminUser = adminUsers[0]
      console.log('✅ Found admin user:', adminUser)
    }

    // Test customer creation data
    const testCustomerData = {
      name: 'Test Customer ' + Date.now(),
      phone: '9800000' + Math.floor(Math.random() * 1000).toString().padStart(3, '0'),
      email: '<EMAIL>',
      address: 'Test Address'
    }

    console.log('📞 Testing customer creation with data:', testCustomerData)

    // Test customer creation
    const customer = await sql`
      INSERT INTO loan_recovery_customers (
        name, phone, email, address, created_by
      )
      VALUES (
        ${testCustomerData.name},
        ${testCustomerData.phone},
        ${testCustomerData.email},
        ${testCustomerData.address},
        ${adminUser.id}
      )
      RETURNING *
    `

    console.log('✅ Customer created successfully:', customer[0])

    // Test loan creation data
    const testLoanData = {
      customer_id: customer[0].id,
      loan_amount: 50000,
      amount_paid: 0,
      due_date: '2024-12-31',
      due_date_bs: '2081-09-15'
    }

    console.log('💰 Testing loan creation with data:', testLoanData)

    // Get the next stage_order for the 'early' stage
    const maxOrderResult = await sql`
      SELECT COALESCE(MAX(stage_order), 0) as max_order
      FROM loan_records
      WHERE current_stage = 'early'
    `
    const nextOrder = maxOrderResult[0].max_order + 1

    // Test loan creation
    const loan = await sql`
      INSERT INTO loan_records (
        customer_id, loan_amount, amount_paid, due_date, due_date_bs,
        current_stage, stage_order, created_by, updated_by
      )
      VALUES (
        ${testLoanData.customer_id},
        ${testLoanData.loan_amount},
        ${testLoanData.amount_paid},
        ${testLoanData.due_date},
        ${testLoanData.due_date_bs},
        'early',
        ${nextOrder},
        ${adminUser.id},
        ${adminUser.id}
      )
      RETURNING *
    `

    console.log('✅ Loan created successfully:', loan[0])

    // Test the complete workflow
    const loanWithDetails = await sql`
      SELECT
        lr.*,
        c.name as customer_name,
        c.phone as customer_phone,
        c.email as customer_email,
        (lr.loan_amount - lr.amount_paid) as outstanding_amount
      FROM loan_records lr
      LEFT JOIN loan_recovery_customers c ON lr.customer_id = c.id
      WHERE lr.id = ${loan[0].id}
    `

    console.log('✅ Complete loan with customer details:', loanWithDetails[0])

    console.log('\n🎉 All customer and loan creation tests passed!')

  } catch (error) {
    console.error('💥 Test failed:', error)
    process.exit(1)
  }
}

testCustomerCreation()
