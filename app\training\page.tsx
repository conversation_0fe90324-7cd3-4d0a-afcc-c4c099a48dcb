"use client"

import { Label } from "@/components/ui/label"

import { useState } from "react"
import { use<PERSON>out<PERSON> } from "next/navigation"
import { AppHeader } from "@/components/app-header"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Progress } from "@/components/ui/progress"
import { Ta<PERSON>, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Search, BookOpen, FileText, Award, Clock, CheckCircle, Play, Filter, Plus } from "lucide-react"
import { Input } from "@/components/ui/input"
import { Badge } from "@/components/ui/badge"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { ChatbotButton } from "@/components/chatbot-button"

const initialCourses = [
  {
    id: "1",
    title: "Product Knowledge Fundamentals",
    description: "Learn the core features and benefits of our financial products",
    progress: 75,
    duration: "2 hours",
    modules: 8,
    completed: 6,
    category: "product",
    featured: true,
    difficulty: "beginner",
  },
  {
    id: "2",
    title: "Advanced Sales Techniques",
    description: "Master the art of consultative selling and objection handling",
    progress: 30,
    duration: "3 hours",
    modules: 10,
    completed: 3,
    category: "sales",
    difficulty: "advanced",
  },
  {
    id: "3",
    title: "Client Relationship Management",
    description: "Build lasting relationships with clients through effective communication",
    progress: 0,
    duration: "1.5 hours",
    modules: 6,
    completed: 0,
    category: "client",
    difficulty: "intermediate",
  },
  {
    id: "4",
    title: "Digital Marketing Essentials",
    description: "Learn how to leverage digital channels for lead generation",
    progress: 100,
    duration: "2.5 hours",
    modules: 9,
    completed: 9,
    category: "marketing",
    difficulty: "beginner",
  },
  {
    id: "5",
    title: "Financial Planning Basics",
    description: "Understand the fundamentals of financial planning and advisory",
    progress: 50,
    duration: "4 hours",
    modules: 12,
    completed: 6,
    category: "product",
    difficulty: "intermediate",
  },
]

export default function TrainingPage() {
  const router = useRouter()
  const [searchTerm, setSearchTerm] = useState("")
  const [categoryFilter, setCategoryFilter] = useState("all")
  const [difficultyFilter, setDifficultyFilter] = useState("all")
  const [progressFilter, setProgressFilter] = useState("all")
  const [showFilters, setShowFilters] = useState(false)

  // Filter courses based on search term and filters
  const filteredCourses = initialCourses
    .filter(
      (course) =>
        course.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
        course.description.toLowerCase().includes(searchTerm.toLowerCase()),
    )
    .filter((course) => (categoryFilter === "all" ? true : course.category === categoryFilter))
    .filter((course) => (difficultyFilter === "all" ? true : course.difficulty === difficultyFilter))
    .filter((course) => {
      if (progressFilter === "all") return true
      if (progressFilter === "inprogress") return course.progress > 0 && course.progress < 100
      if (progressFilter === "completed") return course.progress === 100
      if (progressFilter === "notstarted") return course.progress === 0
      return true
    })

  // Featured courses should always appear at the top
  const featuredCourses = filteredCourses.filter((course) => course.featured)
  const regularCourses = filteredCourses.filter((course) => !course.featured)

  const handleAddTraining = () => {
    router.push("/training/add")
  }

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900 flex flex-col">
      <AppHeader />

      <div className="p-4">
        <div className="flex items-center justify-between mb-4">
          <h2 className="text-xl font-semibold text-teal-800 dark:text-teal-300">Training Courses</h2>
          <div className="flex items-center gap-2">
            <Button
              variant="outline"
              size="sm"
              className="flex items-center gap-1"
              onClick={() => setShowFilters(!showFilters)}
            >
              <Filter className="h-4 w-4" />
              <span>Filter</span>
            </Button>
            <Button className="bg-exobank-green hover:bg-exobank-green/90 text-white" onClick={handleAddTraining}>
              <Plus className="h-4 w-4 mr-1" /> Add Course
            </Button>
          </div>
        </div>

        <div className="mb-4">
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
            <Input
              placeholder="Search courses..."
              className="pl-10"
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
            />
          </div>
        </div>

        {showFilters && (
          <Card className="mb-4 dark:bg-gray-800">
            <CardContent className="p-4">
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div>
                  <Label className="text-sm text-teal-700 dark:text-teal-400">Category</Label>
                  <Select value={categoryFilter} onValueChange={setCategoryFilter}>
                    <SelectTrigger>
                      <SelectValue placeholder="Select category" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">All Categories</SelectItem>
                      <SelectItem value="product">Product</SelectItem>
                      <SelectItem value="sales">Sales</SelectItem>
                      <SelectItem value="client">Client</SelectItem>
                      <SelectItem value="marketing">Marketing</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                <div>
                  <Label className="text-sm text-teal-700 dark:text-teal-400">Difficulty</Label>
                  <Select value={difficultyFilter} onValueChange={setDifficultyFilter}>
                    <SelectTrigger>
                      <SelectValue placeholder="Select difficulty" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">All Levels</SelectItem>
                      <SelectItem value="beginner">Beginner</SelectItem>
                      <SelectItem value="intermediate">Intermediate</SelectItem>
                      <SelectItem value="advanced">Advanced</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                <div>
                  <Label className="text-sm text-teal-700 dark:text-teal-400">Progress</Label>
                  <Select value={progressFilter} onValueChange={setProgressFilter}>
                    <SelectTrigger>
                      <SelectValue placeholder="Select progress" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">All</SelectItem>
                      <SelectItem value="notstarted">Not Started</SelectItem>
                      <SelectItem value="inprogress">In Progress</SelectItem>
                      <SelectItem value="completed">Completed</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>
            </CardContent>
          </Card>
        )}

        <Tabs defaultValue="all" value={categoryFilter} onValueChange={setCategoryFilter} className="mb-6">
          <TabsList className="grid w-full grid-cols-4">
            <TabsTrigger value="all">All</TabsTrigger>
            <TabsTrigger value="product">Product</TabsTrigger>
            <TabsTrigger value="sales">Sales</TabsTrigger>
            <TabsTrigger value="client">Client</TabsTrigger>
          </TabsList>
        </Tabs>

        {/* Featured Course */}
        {featuredCourses.map((course) => (
          <Card key={course.id} className="mb-6 border-2 border-exobank-green dark:bg-gray-800">
            <CardHeader className="pb-2">
              <div className="flex justify-between items-start">
                <div>
                  <Badge className="mb-2 bg-exobank-green text-white">Featured</Badge>
                  <CardTitle className="text-lg text-teal-800 dark:text-teal-300">{course.title}</CardTitle>
                  <CardDescription className="text-teal-600 dark:text-teal-400">{course.description}</CardDescription>
                </div>
                <BookOpen className="h-6 w-6 text-exobank-green" />
              </div>
            </CardHeader>
            <CardContent className="pb-2">
              <div className="flex items-center justify-between mb-2 text-sm">
                <div className="flex items-center gap-1">
                  <Clock className="h-4 w-4 text-gray-400" />
                  <span className="dark:text-gray-300">{course.duration}</span>
                </div>
                <div className="flex items-center gap-1">
                  <FileText className="h-4 w-4 text-gray-400" />
                  <span className="dark:text-gray-300">{course.modules} Modules</span>
                </div>
                <div className="flex items-center gap-1">
                  <CheckCircle className="h-4 w-4 text-gray-400" />
                  <span className="dark:text-gray-300">
                    {course.completed}/{course.modules} Completed
                  </span>
                </div>
              </div>
              <div className="space-y-1">
                <div className="flex justify-between text-xs">
                  <span className="text-teal-600 dark:text-teal-400">Progress</span>
                  <span className="text-teal-600 dark:text-teal-400">{course.progress}%</span>
                </div>
                <Progress value={course.progress} className="h-2" />
              </div>
            </CardContent>
            <CardFooter className="pt-2">
              <Button className="w-full bg-exobank-green hover:bg-exobank-green/90 text-white">
                <Play className="h-4 w-4 mr-2" /> Continue Learning
              </Button>
            </CardFooter>
          </Card>
        ))}

        {filteredCourses.length === 0 ? (
          <Card className="p-8 text-center dark:bg-gray-800">
            <p className="text-gray-500 dark:text-gray-400">No courses match your search criteria</p>
          </Card>
        ) : (
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {regularCourses.map((course) => (
              <Card
                key={course.id}
                className={`${course.progress === 100 ? "border-green-300 bg-green-50 dark:bg-green-900/20" : "dark:bg-gray-800"}`}
              >
                <CardHeader className="p-4 pb-2">
                  <div className="flex justify-between items-start">
                    <div>
                      <CardTitle className="text-base text-teal-800 dark:text-teal-300">{course.title}</CardTitle>
                      <CardDescription className="text-xs text-teal-600 dark:text-teal-400">
                        {course.description}
                      </CardDescription>
                    </div>
                    {course.progress === 100 ? (
                      <Badge className="bg-green-500 text-white">Completed</Badge>
                    ) : (
                      <Badge className="bg-blue-500 text-white">{course.difficulty}</Badge>
                    )}
                  </div>
                </CardHeader>
                <CardContent className="p-4 pt-2 pb-2">
                  <div className="flex items-center justify-between mb-2 text-xs">
                    <div className="flex items-center gap-1">
                      <Clock className="h-3 w-3 text-gray-400" />
                      <span className="dark:text-gray-300">{course.duration}</span>
                    </div>
                    <div className="flex items-center gap-1">
                      <FileText className="h-3 w-3 text-gray-400" />
                      <span className="dark:text-gray-300">{course.modules} Modules</span>
                    </div>
                  </div>
                  {course.progress < 100 && (
                    <div className="space-y-1">
                      <div className="flex justify-between text-xs">
                        <span className="text-teal-600 dark:text-teal-400">Progress</span>
                        <span className="text-teal-600 dark:text-teal-400">{course.progress}%</span>
                      </div>
                      <Progress value={course.progress} className="h-1.5" />
                    </div>
                  )}
                </CardContent>
                <CardFooter className="p-4 pt-2">
                  <Button
                    variant={course.progress === 100 ? "outline" : "default"}
                    className={`w-full text-xs ${
                      course.progress === 100
                        ? "border-green-500 text-green-600 dark:text-green-400 dark:border-green-400"
                        : "bg-exobank-green hover:bg-exobank-green/90 text-white"
                    }`}
                  >
                    {course.progress === 100 ? (
                      <>
                        <Award className="h-3 w-3 mr-1" /> View Certificate
                      </>
                    ) : course.progress > 0 ? (
                      <>
                        <Play className="h-3 w-3 mr-1" /> Continue
                      </>
                    ) : (
                      <>
                        <Play className="h-3 w-3 mr-1" /> Start Course
                      </>
                    )}
                  </Button>
                </CardFooter>
              </Card>
            ))}
          </div>
        )}
      </div>

      <ChatbotButton />
    </div>
  )
}
