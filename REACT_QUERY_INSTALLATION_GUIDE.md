# React Query Installation Guide

## Issue Diagnosis

The Next.js application is failing to compile because the React Query dependencies are missing from `node_modules`, even though they are listed in `package.json`.

## Current Status

✅ **Temporary Fix Applied**: The application has been modified with fallback implementations to prevent compilation errors.

❌ **Missing Dependencies**: 
- `@tanstack/react-query`
- `@tanstack/react-query-devtools`

## Solution Steps

### Step 1: Stop the Development Server
If the development server is running, stop it by pressing `Ctrl+C` in the terminal.

### Step 2: Install Missing Dependencies

Choose one of the following methods:

#### Method A: Using npm (Recommended)
```bash
npm install @tanstack/react-query @tanstack/react-query-devtools
```

#### Method B: Using yarn
```bash
yarn add @tanstack/react-query @tanstack/react-query-devtools
```

#### Method C: Using pnpm
```bash
pnpm add @tanstack/react-query @tanstack/react-query-devtools
```

### Step 3: Clear Cache (if needed)
If installation fails, try clearing the cache:

```bash
# For npm
npm cache clean --force

# For yarn
yarn cache clean

# For pnpm
pnpm store prune
```

### Step 4: Delete node_modules and Reinstall (if needed)
If the issue persists:

```bash
# Delete node_modules and package-lock.json
rm -rf node_modules package-lock.json

# Reinstall all dependencies
npm install
```

### Step 5: Verify Installation
Check that the packages are installed:

```bash
npm list @tanstack/react-query @tanstack/react-query-devtools
```

### Step 6: Remove Temporary Fallbacks
Once the packages are properly installed, you can remove the temporary fallback code from:

- `lib/react-query-provider.tsx`
- `hooks/use-tasks.ts`
- `hooks/use-projects.ts`
- `hooks/use-comments.ts`
- `hooks/use-attachments.ts`

And restore the original imports:

```typescript
import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query"
import { QueryClient, QueryClientProvider } from "@tanstack/react-query"
import { ReactQueryDevtools } from "@tanstack/react-query-devtools"
```

### Step 7: Restart Development Server
```bash
npm run dev
```

## Verification

After successful installation, you should see:

1. ✅ No compilation errors related to React Query
2. ✅ The kanban board loads with real data from the API
3. ✅ Task operations (create, update, delete) work properly
4. ✅ Real-time updates function correctly

## Troubleshooting

### If installation still fails:

1. **Check Node.js version**: Ensure you're using Node.js 16+ 
   ```bash
   node --version
   ```

2. **Check npm version**: Ensure you're using npm 7+
   ```bash
   npm --version
   ```

3. **Check network connectivity**: Ensure you can reach npm registry
   ```bash
   npm ping
   ```

4. **Try alternative registry**: If corporate firewall blocks npm
   ```bash
   npm install --registry https://registry.npmjs.org/
   ```

### If you see TypeScript errors:

1. **Restart TypeScript server** in your IDE
2. **Clear TypeScript cache**:
   ```bash
   rm -rf .next
   npm run build
   ```

## Expected Behavior After Fix

Once React Query is properly installed, the task management system will:

- ✅ Load tasks from the database via API calls
- ✅ Support real-time updates with 5-second polling
- ✅ Enable optimistic updates for drag-and-drop operations
- ✅ Provide proper error handling and loading states
- ✅ Cache data efficiently to improve performance
- ✅ Support advanced features like comments, attachments, and projects

## Files Modified (Temporary)

The following files have temporary fallback implementations:

1. `lib/react-query-provider.tsx` - React Query provider with fallbacks
2. `hooks/use-tasks.ts` - Task management hooks with fallbacks
3. `hooks/use-projects.ts` - Project management hooks with fallbacks
4. `hooks/use-comments.ts` - Comment system hooks with fallbacks
5. `hooks/use-attachments.ts` - Attachment system hooks with fallbacks

These should be restored to their original implementations once React Query is installed.

## Next Steps

1. Install the missing dependencies using the commands above
2. Verify the installation worked
3. Remove the temporary fallback code
4. Test the full task management functionality
5. Enjoy your fully functional kanban board with database integration!
