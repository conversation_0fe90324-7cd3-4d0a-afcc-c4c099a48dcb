"use client"

import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, DialogTitle } from "@/components/ui/dialog"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "@/components/ui/tabs"
import { <PERSON><PERSON><PERSON>, Users, BookOpen, Calculator, Bot, BarChart3, BookUser, Megaphone } from "lucide-react"

interface FeatureDescriptionProps {
  isOpen: boolean
  onClose: () => void
}

export function FeatureDescription({ isOpen, onClose }: FeatureDescriptionProps) {
  const features = [
    {
      id: "kanban",
      name: "Kanban Board",
      icon: Kanban,
      description:
        "Visualize your workflow and manage tasks efficiently with our customizable Kanban board. Drag and drop tasks between columns, assign them to team members, and track progress in real-time. Perfect for agile teams and project management.",
      keyPoints: [
        "Drag-and-drop interface for easy task management",
        "Customizable columns for different workflow stages",
        "Task assignment and tracking capabilities",
        "Real-time updates and collaboration",
        "Filter and search functionality to quickly find tasks",
      ],
    },
    {
      id: "leads",
      name: "Lead <PERSON>",
      icon: Users,
      description:
        "Capture, track, and nurture leads through your sales pipeline. Our lead management system helps you organize prospect information, track interactions, and convert more leads into customers with automated follow-ups and reminders.",
      keyPoints: [
        "Lead capture forms and qualification tools",
        "Contact information storage and organization",
        "Interaction tracking and history",
        "Lead scoring and prioritization",
        "Automated follow-up sequences and reminders",
      ],
    },
    {
      id: "training",
      name: "Training Hub",
      icon: BookOpen,
      description:
        "Centralize your team's learning resources in one accessible location. Our training hub offers course management, progress tracking, and certification capabilities to ensure your team stays up-to-date with the latest skills and knowledge.",
      keyPoints: [
        "Course creation and management tools",
        "Video, document, and quiz capabilities",
        "Progress tracking and reporting",
        "Certification and badge system",
        "Mobile-friendly learning experience",
      ],
    },
    {
      id: "calculator",
      name: "Calculators",
      icon: Calculator,
      description:
        "Make informed decisions with our suite of industry-specific calculators. From ROI and pricing to loan amortization and conversion rates, our calculators help you crunch numbers quickly and accurately to support your business decisions.",
      keyPoints: [
        "ROI and financial calculators",
        "Pricing and margin tools",
        "Loan and payment calculators",
        "Conversion and metric tools",
        "Customizable formulas for your specific needs",
      ],
    },
    {
      id: "assistant",
      name: "AI Assistant",
      icon: Bot,
      description:
        "Get instant help and insights with our AI-powered assistant. It can answer questions, generate content, analyze data, and automate routine tasks, saving you time and enhancing productivity across your organization.",
      keyPoints: [
        "Natural language processing for conversational interactions",
        "Content generation and summarization",
        "Data analysis and insight extraction",
        "Task automation and scheduling",
        "Integration with other platform features",
      ],
    },
    {
      id: "analytics",
      name: "Analytics",
      icon: BarChart3,
      description:
        "Gain valuable insights into your business performance with our comprehensive analytics dashboard. Visualize key metrics, track trends, and make data-driven decisions with customizable reports and real-time data.",
      keyPoints: [
        "Interactive dashboards and visualizations",
        "Custom report generation",
        "Real-time data monitoring",
        "Trend analysis and forecasting",
        "Export and sharing capabilities",
      ],
    },
    {
      id: "crm",
      name: "CRM",
      icon: BookUser,
      description:
        "Manage your customer relationships effectively with our integrated CRM system. Track interactions, manage deals, and nurture relationships throughout the customer lifecycle to improve retention and maximize lifetime value.",
      keyPoints: [
        "Contact and company management",
        "Deal tracking and pipeline visualization",
        "Communication history and notes",
        "Task management and reminders",
        "Integration with email and calendar",
      ],
    },
    {
      id: "campaign",
      name: "Campaign Management",
      icon: Megaphone,
      description:
        "Plan, execute, and measure marketing campaigns across multiple channels. Our campaign management tools help you create targeted campaigns, automate delivery, and analyze results to optimize your marketing efforts.",
      keyPoints: [
        "Multi-channel campaign planning",
        "Content creation and scheduling",
        "Audience segmentation and targeting",
        "Performance tracking and analytics",
        "A/B testing and optimization tools",
      ],
    },
  ]

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-[600px] max-h-[80vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="text-xl">Core Features</DialogTitle>
        </DialogHeader>

        <Tabs defaultValue="kanban" className="mt-4">
          <TabsList className="grid grid-cols-4 mb-4 overflow-x-auto">
            {features.slice(0, 8).map((feature) => (
              <TabsTrigger key={feature.id} value={feature.id} className="flex items-center gap-1">
                <feature.icon className="h-4 w-4" />
                <span className="hidden sm:inline">{feature.name}</span>
              </TabsTrigger>
            ))}
          </TabsList>

          {features.map((feature) => (
            <TabsContent key={feature.id} value={feature.id} className="space-y-4">
              <div className="flex items-center gap-2">
                <feature.icon className="h-6 w-6 text-purple-600" />
                <h3 className="text-lg font-semibold">{feature.name}</h3>
              </div>

              <p className="text-gray-700">{feature.description}</p>

              <div>
                <h4 className="font-medium mb-2">Key Features:</h4>
                <ul className="space-y-1">
                  {feature.keyPoints.map((point, index) => (
                    <li key={index} className="flex items-start">
                      <span className="text-purple-600 mr-2">•</span>
                      <span>{point}</span>
                    </li>
                  ))}
                </ul>
              </div>
            </TabsContent>
          ))}
        </Tabs>
      </DialogContent>
    </Dialog>
  )
}
