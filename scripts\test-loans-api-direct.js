const { neon } = require('@neondatabase/serverless');
require('dotenv').config({ path: '.env.local' });

const sql = neon(process.env.DATABASE_URL);

async function testLoansAPILogic() {
  try {
    console.log('🔍 Testing loans API logic directly...\n');

    // Test the exact query from the API
    console.log('1. Testing the main query...');
    const loans = await sql`
      SELECT 
        lr.*,
        c.name as customer_name,
        c.phone as customer_phone,
        c.email as customer_email,
        c.address as customer_address,
        u.full_name as created_by_name,
        (lr.loan_amount - lr.amount_paid) as outstanding_amount,
        CASE 
          WHEN lr.due_date < CURRENT_DATE THEN 
            CURRENT_DATE - lr.due_date
          ELSE 0
        END as days_overdue
      FROM loan_records lr
      LEFT JOIN loan_recovery_customers c ON lr.customer_id = c.id
      LEFT JOIN users u ON lr.created_by = u.id
      WHERE lr.current_stage != 'complete'
      ORDER BY 
        CASE lr.current_stage
          WHEN 'early' THEN 1
          WHEN 'assertive' THEN 2
          WHEN 'escalation' THEN 3
          WHEN 'legal_recovery' THEN 4
          WHEN 'complete' THEN 5
        END,
        lr.stage_order,
        lr.due_date ASC
    `;

    console.log(`✅ Query successful! Found ${loans.length} loans`);

    // Test grouping by stage
    console.log('\n2. Testing stage grouping...');
    const loansByStage = {
      early: [],
      assertive: [],
      escalation: [],
      legal_recovery: [],
      complete: []
    };

    loans.forEach(loan => {
      if (loansByStage[loan.current_stage]) {
        loansByStage[loan.current_stage].push(loan);
      }
    });

    console.log('Loans by stage:');
    Object.keys(loansByStage).forEach(stage => {
      console.log(`  ${stage}: ${loansByStage[stage].length} loans`);
    });

    // Test the API response format
    console.log('\n3. Testing API response format...');
    const apiResponse = {
      success: true,
      loans: loansByStage,
      total: loans.length,
    };

    console.log('API Response structure:', {
      success: apiResponse.success,
      totalLoans: apiResponse.total,
      stageBreakdown: Object.keys(apiResponse.loans).map(stage => ({
        stage,
        count: apiResponse.loans[stage].length
      }))
    });

    if (loans.length > 0) {
      console.log('\n4. Sample loan data:');
      const sampleLoan = loans[0];
      console.log({
        id: sampleLoan.id,
        customer_name: sampleLoan.customer_name,
        loan_amount: sampleLoan.loan_amount,
        outstanding_amount: sampleLoan.outstanding_amount,
        current_stage: sampleLoan.current_stage,
        days_overdue: sampleLoan.days_overdue
      });
    }

    console.log('\n✅ All tests passed! The loans API logic is working correctly.');

  } catch (error) {
    console.error('❌ Test failed:', error);
    console.error('Error details:', error.message);
    if (error.stack) {
      console.error('Stack trace:', error.stack);
    }
  }
}

testLoansAPILogic();
