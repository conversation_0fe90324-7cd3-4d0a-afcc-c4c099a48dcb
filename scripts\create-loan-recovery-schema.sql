-- Loan Recovery Management System Database Schema
-- This script creates all necessary tables for the loan recovery system

-- Create enum types for loan recovery system
CREATE TYPE recovery_stage AS ENUM ('early', 'assertive', 'escalation', 'legal_recovery', 'complete');
CREATE TYPE note_type AS ENUM ('general', 'call', 'email', 'meeting');
CREATE TYPE reminder_status AS ENUM ('pending', 'completed', 'cancelled');

-- Loan Recovery Customers table
CREATE TABLE IF NOT EXISTS loan_recovery_customers (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name VARCHAR(255) NOT NULL,
    phone VARCHAR(20) NOT NULL,
    email VARCHAR(255),
    address TEXT,
    employee_id VARCHAR(50), -- Optional employee ID if they are an employee
    created_by UUID NOT NULL REFERENCES users(id),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Loan Records table
CREATE TABLE IF NOT EXISTS loan_records (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    customer_id UUID NOT NULL REFERENCES loan_recovery_customers(id) ON DELETE CASCADE,
    loan_amount DECIMAL(15,2) NOT NULL,
    amount_paid DECIMAL(15,2) DEFAULT 0,
    due_date DATE NOT NULL,
    due_date_bs VARCHAR(20), -- Nepali date in BS format (YYYY-MM-DD)
    current_stage recovery_stage NOT NULL DEFAULT 'early',
    stage_order INTEGER NOT NULL DEFAULT 1, -- For ordering within stages
    created_by UUID NOT NULL REFERENCES users(id),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    -- Computed column for outstanding amount
    CONSTRAINT positive_amounts CHECK (loan_amount > 0 AND amount_paid >= 0),
    CONSTRAINT valid_payment CHECK (amount_paid <= loan_amount)
);

-- Stage Transition History table
CREATE TABLE IF NOT EXISTS loan_stage_transitions (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    loan_id UUID NOT NULL REFERENCES loan_records(id) ON DELETE CASCADE,
    from_stage recovery_stage,
    to_stage recovery_stage NOT NULL,
    transitioned_by UUID NOT NULL REFERENCES users(id),
    transition_date TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    notes TEXT,
    
    -- Ensure we don't have invalid transitions
    CONSTRAINT different_stages CHECK (from_stage IS NULL OR from_stage != to_stage)
);

-- Conversation Notes table
CREATE TABLE IF NOT EXISTS loan_conversation_notes (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    loan_id UUID NOT NULL REFERENCES loan_records(id) ON DELETE CASCADE,
    note_type note_type NOT NULL DEFAULT 'general',
    content TEXT NOT NULL,
    created_by UUID NOT NULL REFERENCES users(id),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Reminders table
CREATE TABLE IF NOT EXISTS loan_reminders (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    loan_id UUID NOT NULL REFERENCES loan_records(id) ON DELETE CASCADE,
    title VARCHAR(255) NOT NULL,
    reminder_date DATE NOT NULL,
    reminder_date_bs VARCHAR(20), -- Nepali date in BS format
    status reminder_status NOT NULL DEFAULT 'pending',
    created_by UUID NOT NULL REFERENCES users(id),
    completed_by UUID REFERENCES users(id),
    completed_at TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_loan_recovery_customers_phone ON loan_recovery_customers(phone);
CREATE INDEX IF NOT EXISTS idx_loan_recovery_customers_email ON loan_recovery_customers(email);
CREATE INDEX IF NOT EXISTS idx_loan_recovery_customers_name ON loan_recovery_customers(name);

CREATE INDEX IF NOT EXISTS idx_loan_records_customer_id ON loan_records(customer_id);
CREATE INDEX IF NOT EXISTS idx_loan_records_current_stage ON loan_records(current_stage);
CREATE INDEX IF NOT EXISTS idx_loan_records_due_date ON loan_records(due_date);
CREATE INDEX IF NOT EXISTS idx_loan_records_stage_order ON loan_records(current_stage, stage_order);

CREATE INDEX IF NOT EXISTS idx_loan_stage_transitions_loan_id ON loan_stage_transitions(loan_id);
CREATE INDEX IF NOT EXISTS idx_loan_stage_transitions_date ON loan_stage_transitions(transition_date);

CREATE INDEX IF NOT EXISTS idx_loan_conversation_notes_loan_id ON loan_conversation_notes(loan_id);
CREATE INDEX IF NOT EXISTS idx_loan_conversation_notes_type ON loan_conversation_notes(note_type);
CREATE INDEX IF NOT EXISTS idx_loan_conversation_notes_created_at ON loan_conversation_notes(created_at);

CREATE INDEX IF NOT EXISTS idx_loan_reminders_loan_id ON loan_reminders(loan_id);
CREATE INDEX IF NOT EXISTS idx_loan_reminders_date ON loan_reminders(reminder_date);
CREATE INDEX IF NOT EXISTS idx_loan_reminders_status ON loan_reminders(status);

-- Create triggers for updating timestamps
CREATE OR REPLACE FUNCTION update_loan_recovery_timestamp()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER update_loan_recovery_customers_timestamp
    BEFORE UPDATE ON loan_recovery_customers
    FOR EACH ROW EXECUTE FUNCTION update_loan_recovery_timestamp();

CREATE TRIGGER update_loan_records_timestamp
    BEFORE UPDATE ON loan_records
    FOR EACH ROW EXECUTE FUNCTION update_loan_recovery_timestamp();

CREATE TRIGGER update_loan_conversation_notes_timestamp
    BEFORE UPDATE ON loan_conversation_notes
    FOR EACH ROW EXECUTE FUNCTION update_loan_recovery_timestamp();

CREATE TRIGGER update_loan_reminders_timestamp
    BEFORE UPDATE ON loan_reminders
    FOR EACH ROW EXECUTE FUNCTION update_loan_recovery_timestamp();

-- Create function to automatically log stage transitions
CREATE OR REPLACE FUNCTION log_stage_transition()
RETURNS TRIGGER AS $$
BEGIN
    -- Only log if stage actually changed
    IF OLD.current_stage IS DISTINCT FROM NEW.current_stage THEN
        INSERT INTO loan_stage_transitions (loan_id, from_stage, to_stage, transitioned_by)
        VALUES (NEW.id, OLD.current_stage, NEW.current_stage, NEW.updated_by);
    END IF;
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Add updated_by column to loan_records for tracking who made changes
ALTER TABLE loan_records ADD COLUMN IF NOT EXISTS updated_by UUID REFERENCES users(id);

-- Create trigger for automatic stage transition logging
CREATE TRIGGER log_loan_stage_transition
    AFTER UPDATE ON loan_records
    FOR EACH ROW EXECUTE FUNCTION log_stage_transition();
