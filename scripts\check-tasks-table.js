#!/usr/bin/env node

const { neon } = require('@neondatabase/serverless');
require('dotenv').config({ path: '.env.local' });

async function checkTasksTable() {
  console.log('🔍 EXAMINING TASKS TABLE STRUCTURE');
  console.log('===================================\n');
  
  if (!process.env.DATABASE_URL) {
    console.error('❌ ERROR: DATABASE_URL environment variable is not set');
    process.exit(1);
  }
  
  try {
    const sql = neon(process.env.DATABASE_URL);
    
    // Test connection
    console.log('🔄 Testing database connection...');
    await sql`SELECT 1`;
    console.log('✅ Database connection successful!\n');
    
    // Get table structure
    console.log('📋 TASKS TABLE COLUMNS:');
    const columns = await sql`
      SELECT column_name, data_type, is_nullable, column_default, character_maximum_length
      FROM information_schema.columns 
      WHERE table_name = 'tasks' AND table_schema = 'public'
      ORDER BY ordinal_position
    `;
    
    columns.forEach(col => {
      const length = col.character_maximum_length ? `(${col.character_maximum_length})` : '';
      const nullable = col.is_nullable === 'NO' ? 'NOT NULL' : 'NULL';
      const defaultVal = col.column_default ? `DEFAULT ${col.column_default}` : '';
      console.log(`  - ${col.column_name}: ${col.data_type}${length} ${nullable} ${defaultVal}`);
    });
    
    // Check constraints
    console.log('\n🔒 TABLE CONSTRAINTS:');
    const constraints = await sql`
      SELECT constraint_name, constraint_type
      FROM information_schema.table_constraints
      WHERE table_name = 'tasks' AND table_schema = 'public'
    `;
    
    constraints.forEach(constraint => {
      console.log(`  - ${constraint.constraint_name}: ${constraint.constraint_type}`);
    });
    
    // Check check constraints specifically
    console.log('\n✅ CHECK CONSTRAINTS:');
    const checkConstraints = await sql`
      SELECT cc.constraint_name, cc.check_clause
      FROM information_schema.check_constraints cc
      JOIN information_schema.table_constraints tc ON cc.constraint_name = tc.constraint_name
      WHERE tc.table_name = 'tasks' AND tc.table_schema = 'public'
    `;
    
    if (checkConstraints.length === 0) {
      console.log('  - No check constraints found');
    } else {
      checkConstraints.forEach(constraint => {
        console.log(`  - ${constraint.constraint_name}: ${constraint.check_clause}`);
      });
    }
    
    // Check RLS status
    console.log('\n🔐 ROW LEVEL SECURITY STATUS:');
    const rlsStatus = await sql`
      SELECT relname, relrowsecurity, relforcerowsecurity
      FROM pg_class 
      WHERE relname = 'tasks'
    `;
    
    if (rlsStatus.length > 0) {
      const status = rlsStatus[0];
      console.log(`  - Row Security: ${status.relrowsecurity ? 'ENABLED' : 'DISABLED'}`);
      console.log(`  - Force Row Security: ${status.relforcerowsecurity ? 'ENABLED' : 'DISABLED'}`);
    } else {
      console.log('  - Could not determine RLS status');
    }
    
    // Check RLS policies
    console.log('\n🛡️ RLS POLICIES:');
    const policies = await sql`
      SELECT schemaname, tablename, policyname, permissive, roles, cmd, qual, with_check
      FROM pg_policies 
      WHERE tablename = 'tasks'
    `;
    
    if (policies.length === 0) {
      console.log('  ⚠️ No RLS policies found for tasks table');
    } else {
      policies.forEach(policy => {
        console.log(`  - Policy: ${policy.policyname}`);
        console.log(`    Command: ${policy.cmd}`);
        console.log(`    Roles: ${policy.roles}`);
        console.log(`    Condition: ${policy.qual || 'No condition'}`);
        console.log(`    With Check: ${policy.with_check || 'No check'}`);
        console.log('');
      });
    }
    
    // Sample data check
    console.log('📊 DATA SUMMARY:');
    const taskCount = await sql`SELECT COUNT(*) as count FROM tasks`;
    console.log(`  - Total tasks: ${taskCount[0].count}`);
    
    if (taskCount[0].count > 0) {
      const statusBreakdown = await sql`
        SELECT status, COUNT(*) as count 
        FROM tasks 
        GROUP BY status 
        ORDER BY count DESC
      `;
      
      console.log('  - Status breakdown:');
      statusBreakdown.forEach(status => {
        console.log(`    * ${status.status}: ${status.count}`);
      });
    }
    
    console.log('\n🎉 TASKS TABLE ANALYSIS COMPLETED!');
    
  } catch (error) {
    console.error('❌ Error:', error.message);
    process.exit(1);
  }
}

checkTasksTable();
