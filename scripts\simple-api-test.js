#!/usr/bin/env node

/**
 * Simple API test using Node.js built-in modules
 */

const http = require('http');
const https = require('https');
const { URL } = require('url');

function makeRequest(url, options = {}) {
  return new Promise((resolve, reject) => {
    const urlObj = new URL(url);
    const isHttps = urlObj.protocol === 'https:';
    const client = isHttps ? https : http;
    
    const requestOptions = {
      hostname: urlObj.hostname,
      port: urlObj.port || (isHttps ? 443 : 80),
      path: urlObj.pathname + urlObj.search,
      method: options.method || 'GET',
      headers: options.headers || {},
      timeout: options.timeout || 10000
    };
    
    const req = client.request(requestOptions, (res) => {
      let data = '';
      
      res.on('data', (chunk) => {
        data += chunk;
      });
      
      res.on('end', () => {
        try {
          const jsonData = JSON.parse(data);
          resolve({
            status: res.statusCode,
            headers: res.headers,
            data: jsonData
          });
        } catch (e) {
          resolve({
            status: res.statusCode,
            headers: res.headers,
            data: data
          });
        }
      });
    });
    
    req.on('error', (err) => {
      reject(err);
    });
    
    req.on('timeout', () => {
      req.destroy();
      reject(new Error('Request timeout'));
    });
    
    if (options.body) {
      req.write(options.body);
    }
    
    req.end();
  });
}

async function testApiEndpoints() {
  console.log('🧪 Simple API Endpoints Test\n');
  
  const BASE_URL = 'http://localhost:3002';
  
  // Test 1: Projects API
  console.log('1. Testing Projects API...');
  try {
    const response = await makeRequest(`${BASE_URL}/api/projects`);
    console.log(`   Status: ${response.status}`);
    
    if (response.status === 500) {
      console.log('   ❌ Server Error - Database/SQL issue still exists');
      console.log('   Error:', response.data);
    } else if (response.status === 401) {
      console.log('   ✅ Authentication required (expected)');
    } else {
      console.log('   ✅ API responding correctly');
    }
  } catch (error) {
    console.log('   ❌ Connection Error:', error.message);
  }
  
  // Test 2: Tasks API
  console.log('\n2. Testing Tasks API...');
  try {
    const response = await makeRequest(`${BASE_URL}/api/tasks`);
    console.log(`   Status: ${response.status}`);
    
    if (response.status === 500) {
      console.log('   ❌ Server Error - SQL syntax issue still exists');
      console.log('   Error:', response.data);
    } else if (response.status === 401) {
      console.log('   ✅ Authentication required (expected)');
    } else {
      console.log('   ✅ API responding correctly');
    }
  } catch (error) {
    console.log('   ❌ Connection Error:', error.message);
  }
  
  // Test 3: Admin Users API
  console.log('\n3. Testing Admin Users API...');
  try {
    const response = await makeRequest(`${BASE_URL}/api/admin/users`);
    console.log(`   Status: ${response.status}`);
    
    if (response.status === 500) {
      console.log('   ❌ Server Error');
      console.log('   Error:', response.data);
    } else if (response.status === 401 || response.status === 403) {
      console.log('   ✅ Authentication/Authorization required (expected)');
    } else {
      console.log('   ✅ API responding correctly');
    }
  } catch (error) {
    console.log('   ❌ Connection Error:', error.message);
  }
  
  console.log('\n🎯 Test Summary:');
  console.log('If all APIs return 401/403 (auth required), the database fixes are working!');
  console.log('If any API returns 500 (server error), there are still issues to resolve.');
  console.log('\n🌐 Application URL: http://localhost:3002');
}

testApiEndpoints().catch(console.error);
