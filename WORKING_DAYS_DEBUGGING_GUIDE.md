# Working Days Configuration - Comprehensive Debugging Guide

## 🚨 **Current Issue**
The Payroll Settings page is not displaying the working days configuration table, likely due to missing database tables or empty data.

## 🔧 **Step-by-Step Fix Process**

### **Step 1: Execute Database Fix Script**

1. **Open Neon Console:**
   - Go to [Neon Console](https://console.neon.tech)
   - Navigate to your project
   - Click "SQL Editor" or "Query" tab

2. **Run the Diagnostic Script:**
   ```sql
   -- Copy and paste the entire content of scripts/diagnose-and-fix-working-days.sql
   -- This will diagnose the issue and fix it automatically
   ```

3. **Expected Output:**
   ```
   NOTICE: === DIAGNOSTIC RESULTS ===
   NOTICE: working_days_configuration table exists: true
   NOTICE: attendance_calculation_settings table exists: true
   NOTICE: working_days_configuration records: 12
   NOTICE: attendance_calculation_settings records: 11
   NOTICE: === END DIAGNOSTIC ===
   
   NOTICE: === FINAL VERIFICATION ===
   NOTICE: Working days configurations for 2081-82: 12
   NOTICE: Attendance calculation settings: 11
   NOTICE: ✅ SUCCESS: All data is properly configured!
   NOTICE: The Payroll Settings page should now work correctly.
   NOTICE: === END VERIFICATION ===
   ```

### **Step 2: Test API Endpoint**

1. **Using Browser Developer Tools:**
   ```javascript
   // Open browser console on your app and run:
   fetch('/api/admin/payroll/working-days?fiscal_year=2081-82', {
     method: 'GET',
     credentials: 'include'
   })
   .then(response => response.json())
   .then(data => {
     console.log('API Response:', data);
     if (data.success && data.data.working_days_config.length > 0) {
       console.log('✅ API is working correctly');
       console.log('Working days configs:', data.data.working_days_config.length);
     } else {
       console.log('❌ API issue detected');
     }
   })
   .catch(error => console.error('API Error:', error));
   ```

2. **Expected Response:**
   ```json
   {
     "success": true,
     "data": {
       "working_days_config": [
         {
           "id": "uuid-here",
           "fiscal_year": "2081-82",
           "bs_month": 1,
           "bs_month_name": "Baisakh",
           "total_days_in_month": 31,
           "working_days": 22,
           "public_holidays": 2,
           "weekend_days": 7,
           "late_penalty_type": "half_day",
           "late_penalty_amount": 0,
           "half_day_calculation_method": "fifty_percent"
         }
         // ... 11 more months
       ],
       "attendance_settings": [
         // ... settings array
       ]
     }
   }
   ```

### **Step 3: Test UI Functionality**

1. **Navigate to Payroll Settings:**
   - Go to `/admin/payroll/settings`
   - Check browser console for any errors

2. **Debug UI Issues:**
   ```javascript
   // In browser console, check the component state:
   // Look for React DevTools or check network tab
   
   // Check if data is being fetched:
   console.log('Checking network requests...');
   // Look in Network tab for /api/admin/payroll/working-days request
   ```

3. **Expected UI Behavior:**
   - Page loads without errors
   - "Working Days" tab shows a table with 12 rows
   - Each row shows a Nepali month with working days configuration
   - "Add Month" button is available
   - Edit/Delete buttons work on each row

### **Step 4: Common Issues and Solutions**

#### **Issue 1: API Returns Empty Array**
```javascript
// Check in browser console:
fetch('/api/admin/payroll/working-days?fiscal_year=2081-82')
  .then(r => r.json())
  .then(d => console.log('Empty check:', d.data.working_days_config.length));

// If length is 0, re-run the database script
```

**Solution:** Re-execute the database script, focusing on the INSERT statements.

#### **Issue 2: Authentication Errors**
```javascript
// Check if user is authenticated:
fetch('/api/admin/payroll/working-days')
  .then(r => {
    if (r.status === 401) console.log('❌ Not authenticated');
    if (r.status === 403) console.log('❌ Not authorized (need admin/hr_manager role)');
    return r.json();
  })
  .then(d => console.log('Auth check:', d));
```

**Solution:** Ensure you're logged in as admin or hr_manager role.

#### **Issue 3: Database Connection Errors**
```sql
-- Test database connection in Neon SQL Editor:
SELECT COUNT(*) FROM working_days_configuration;
SELECT COUNT(*) FROM attendance_calculation_settings;
```

**Solution:** Check Neon database status and connection string.

#### **Issue 4: RLS Policy Issues**
```sql
-- Check if RLS policies are working:
SELECT * FROM working_days_configuration LIMIT 1;
-- If this returns no rows but COUNT(*) shows data, RLS might be blocking access
```

**Solution:** Re-run the RLS policy creation part of the script.

### **Step 5: Manual Data Verification**

Run these queries in Neon SQL Editor to verify data:

```sql
-- 1. Check table existence
SELECT table_name FROM information_schema.tables 
WHERE table_schema = 'public' 
AND table_name IN ('working_days_configuration', 'attendance_calculation_settings');

-- 2. Check data count
SELECT 
  (SELECT COUNT(*) FROM working_days_configuration) as working_days_count,
  (SELECT COUNT(*) FROM attendance_calculation_settings) as settings_count;

-- 3. View sample data
SELECT fiscal_year, bs_month_name, working_days 
FROM working_days_configuration 
WHERE fiscal_year = '2081-82' 
ORDER BY bs_month;

-- 4. Test functions
SELECT get_working_days_for_period('2081-82', 1) as baisakh_days;
```

### **Step 6: Browser Console Debugging**

1. **Open Developer Tools (F12)**
2. **Go to Console tab**
3. **Navigate to `/admin/payroll/settings`**
4. **Look for errors:**
   ```
   ❌ Failed to fetch
   ❌ 404 Not Found
   ❌ 500 Internal Server Error
   ❌ TypeError: Cannot read property...
   ```

5. **Check Network tab:**
   - Look for `/api/admin/payroll/working-days` request
   - Check status code (should be 200)
   - Check response data

### **Step 7: Restart Development Server**

After database changes, restart your Next.js server:
```bash
# Stop the server (Ctrl+C)
# Then restart:
npm run dev
# or
yarn dev
```

## ✅ **Success Indicators**

After completing the fix, you should see:

1. **Database Level:**
   - ✅ 12 working days configurations for fiscal year 2081-82
   - ✅ 11 attendance calculation settings
   - ✅ All functions created and working

2. **API Level:**
   - ✅ GET `/api/admin/payroll/working-days` returns 200 status
   - ✅ Response contains working_days_config array with 12 items
   - ✅ Response contains attendance_settings array with 11 items

3. **UI Level:**
   - ✅ `/admin/payroll/settings` page loads without errors
   - ✅ Working Days tab shows table with 12 Nepali months
   - ✅ Each row shows month name, working days, holidays, etc.
   - ✅ Add/Edit/Delete operations work correctly

## 🚨 **If Issues Persist**

1. **Check server logs** for detailed error messages
2. **Verify environment variables** are correct
3. **Check database connection string** in your app
4. **Ensure user has admin/hr_manager role** in the database
5. **Clear browser cache** and try again

## 📞 **Quick Test Commands**

```bash
# Test API directly with curl (replace with your session token):
curl -X GET "http://localhost:3000/api/admin/payroll/working-days?fiscal_year=2081-82" \
     -H "Cookie: session-token=your-session-token-here"

# Expected: JSON response with success: true and data arrays
```

The working days configuration system should be fully operational after following this guide! 🎉
