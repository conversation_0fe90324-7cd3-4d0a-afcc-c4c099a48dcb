"use client"

import React from 'react'
import { NepaliDatePicker } from './nepali-date-picker'
import { ModalAwareNepaliDatePicker } from './modal-aware-nepali-date-picker'
import { Label } from './label'

interface DualCalendarInputProps {
  value?: string // ISO date string (YYYY-MM-DD)
  onChange?: (value: string) => void
  label?: string
  placeholder?: string
  required?: boolean
  disabled?: boolean
  error?: string
  className?: string
  minDate?: Date
  maxDate?: Date
  showBothCalendars?: boolean
  allowManualInput?: boolean
  highlightHolidays?: boolean
  highlightWorkingDays?: boolean
  /**
   * Enable smart positioning for modals (default: auto-detect)
   * When true, uses modal-aware positioning
   * When false, uses standard positioning
   * When undefined, auto-detects if inside a modal
   */
  enableSmartPositioning?: boolean
  /**
   * Preferred positioning side when smart positioning is enabled (default: "right" for horizontal-priority)
   */
  preferredSide?: "top" | "bottom" | "left" | "right"
}

/**
 * DualCalendarInput - A form-friendly wrapper around NepaliDatePicker
 * 
 * This component provides a simplified interface for forms that need dual calendar functionality.
 * It handles the conversion between Date objects (used by NepaliDatePicker) and ISO date strings
 * (commonly used in forms and APIs).
 * 
 * Features:
 * - Dual calendar support (English/Nepali)
 * - Manual input with calendar type toggle
 * - Form validation integration
 * - Holiday and working day highlighting
 * - Responsive design
 */
export function DualCalendarInput({
  value,
  onChange,
  label,
  placeholder = "Select date",
  required = false,
  disabled = false,
  error,
  className,
  minDate,
  maxDate,
  showBothCalendars = true,
  allowManualInput = true,
  highlightHolidays = true,
  highlightWorkingDays = true,
  enableSmartPositioning,
  preferredSide = "right",
}: DualCalendarInputProps) {

  // Convert ISO string to Date object for NepaliDatePicker
  const dateValue = value ? new Date(value) : undefined

  // Auto-detect if we should use smart positioning
  const shouldUseSmartPositioning = React.useMemo(() => {
    if (enableSmartPositioning !== undefined) {
      return enableSmartPositioning
    }

    // Auto-detect if we're inside a modal by checking for common modal indicators
    if (typeof window === 'undefined') return false

    // This will be checked at render time to detect modal context
    return true // Default to smart positioning for better UX
  }, [enableSmartPositioning])

  // Handle date change from NepaliDatePicker
  const handleDateChange = (date: Date | undefined) => {
    console.log("📅 DualCalendarInput date change:", { date, currentValue: value })

    if (date) {
      // Convert Date to ISO string (YYYY-MM-DD format)
      const isoString = date.toISOString().split('T')[0]
      console.log("📅 Converted to ISO string:", isoString)
      onChange?.(isoString)
    } else {
      console.log("📅 Date cleared")
      onChange?.('')
    }
  }

  // Choose the appropriate date picker component
  const DatePickerComponent = shouldUseSmartPositioning ? ModalAwareNepaliDatePicker : NepaliDatePicker

  return (
    <div className={className}>
      <DatePickerComponent
        value={dateValue}
        onChange={handleDateChange}
        label={label}
        placeholder={placeholder}
        required={required}
        disabled={disabled}
        error={error}
        minDate={minDate}
        maxDate={maxDate}
        showBothCalendars={showBothCalendars}
        allowManualInput={allowManualInput}
        highlightHolidays={highlightHolidays}
        highlightWorkingDays={highlightWorkingDays}
        {...(shouldUseSmartPositioning && {
          enableSmartPositioning: true,
          preferredSide: preferredSide
        })}
      />
    </div>
  )
}

export default DualCalendarInput
