-- CORRECTED MANUAL FIX FOR ATTENDANCE TABLE DATA TYPE MISMATCH
-- Use this corrected version instead of the previous one

-- ============================================================================
-- STEP 4 CORRECTED: MIGRATE EXISTING DATA (IF ANY)
-- ============================================================================
-- Convert existing TIME data to TIMESTAMP WITH TIME ZONE using proper PostgreSQL syntax

UPDATE attendance 
SET 
    check_in_time_temp = CASE 
        WHEN check_in_time IS NOT NULL THEN 
            (date::text || ' ' || check_in_time::text)::TIMESTAMP WITH TIME ZONE
        ELSE NULL 
    END,
    check_out_time_temp = CASE 
        WHEN check_out_time IS NOT NULL THEN 
            (date::text || ' ' || check_out_time::text)::TIMESTAMP WITH TIME ZONE
        ELSE NULL 
    END;

-- Alternative approach if the above doesn't work:
-- UPDATE attendance 
-- SET 
--     check_in_time_temp = CASE 
--         WHEN check_in_time IS NOT NULL THEN 
--             TIMESTAMP WITH TIME ZONE (date + check_in_time)
--         ELSE NULL 
--     END,
--     check_out_time_temp = CASE 
--         WHEN check_out_time IS NOT NULL THEN 
--             TIMESTAMP WITH TIME ZONE (date + check_out_time)
--         ELSE NULL 
--     END;

-- ============================================================================
-- ALTERNATIVE SIMPLE APPROACH (if data migration is complex)
-- ============================================================================
-- If you don't have existing data or want to start fresh, you can:

-- 1. Check if there's any important data:
-- SELECT COUNT(*) FROM attendance;

-- 2. If no important data exists, you can simply:
-- TRUNCATE TABLE attendance;  -- This removes all data but keeps the table structure

-- 3. Then continue with dropping and renaming columns as in the original steps

-- ============================================================================
-- STEP-BY-STEP EXECUTION GUIDE
-- ============================================================================

-- Run this first to see what data exists:
SELECT COUNT(*) as total_records FROM attendance;

-- If total_records = 0, you can skip the data migration and go straight to:
-- ALTER TABLE attendance DROP COLUMN check_in_time;
-- ALTER TABLE attendance DROP COLUMN check_out_time;
-- ALTER TABLE attendance RENAME COLUMN check_in_time_temp TO check_in_time;
-- ALTER TABLE attendance RENAME COLUMN check_out_time_temp TO check_out_time;

-- If you have data, use the corrected UPDATE statement above

-- ============================================================================
-- VERIFICATION AFTER FIX
-- ============================================================================
-- Run this to confirm the fix worked:
SELECT 
    table_name,
    column_name, 
    data_type, 
    is_nullable
FROM information_schema.columns 
WHERE table_name = 'attendance' 
AND column_name IN ('check_in_time', 'check_out_time')
ORDER BY column_name;

-- Expected result: Both columns should show 'timestamp with time zone'
