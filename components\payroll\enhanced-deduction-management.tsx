// Enhanced Deduction Management Interface
// Integrates with Phase 1 deduction APIs for comprehensive deduction management

"use client"

import React, { useState, useEffect } from 'react'
import {
  Plus,
  Edit,
  Trash2,
  DollarSign,
  Users,
  AlertTriangle,
  Clock,
  Search,
  Filter,
  CheckCircle,
  Eye,
  Settings,
  Download,
  Calculator,
  TrendingDown,
  Shield,
  FileText
} from 'lucide-react'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar'
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog'
import { CurrencyDisplay } from '@/components/ui/currency-display'
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert'
import { toast } from '@/hooks/use-toast'

interface DeductionComponent {
  id: string
  name: string
  code: string
  category: string
  calculation_type: string
  fixed_amount?: number
  percentage?: number
  percentage_base?: string
  is_taxable: boolean
  is_statutory: boolean
  description?: string
  is_active: boolean
  effective_from: string
  effective_to?: string
}

interface EmployeeDeductionAssignment {
  assignment_id: string
  user_id: string
  component_id: string
  is_active: boolean
  effective_from: string
  effective_to?: string
  override_amount?: number
  override_percentage?: number
  assigned_by: string
  approved_by?: string
  approval_date?: string
  notes?: string
  created_at: string
  name: string
  code: string
  category: string
  calculation_type: string
  fixed_amount?: number
  percentage?: number
  percentage_base?: string
  is_statutory: boolean
  description?: string
  employee_name: string
  assigned_by_name?: string
  approved_by_name?: string
}

interface LatePenalty {
  user_id: string
  full_name: string
  employee_id: string
  late_days: number
  total_late_minutes: number
  penalty_per_minute: number
  calculated_penalty: number
}

interface StatutoryDeduction {
  name: string
  code: string
  calculation_type: string
  percentage?: number
  fixed_amount?: number
  assigned_employees: number
  total_monthly_deduction: number
}

export function EnhancedDeductionManagement() {
  const [deductionComponents, setDeductionComponents] = useState<DeductionComponent[]>([])
  const [employeeAssignments, setEmployeeAssignments] = useState<EmployeeDeductionAssignment[]>([])
  const [latePenalties, setLatePenalties] = useState<LatePenalty[]>([])
  const [statutoryDeductions, setStatutoryDeductions] = useState<StatutoryDeduction[]>([])
  const [loading, setLoading] = useState(true)
  const [searchTerm, setSearchTerm] = useState('')
  const [categoryFilter, setCategoryFilter] = useState('all')
  const [activeTab, setActiveTab] = useState('assignments')
  
  // Dialog states
  const [showAssignDialog, setShowAssignDialog] = useState(false)
  const [showLatePenaltyDialog, setShowLatePenaltyDialog] = useState(false)
  const [editingAssignment, setEditingAssignment] = useState<EmployeeDeductionAssignment | null>(null)
  
  // Form states
  const [assignmentForm, setAssignmentForm] = useState({
    userId: '',
    componentId: '',
    effectiveFrom: new Date().toISOString().split('T')[0],
    effectiveTo: '',
    overrideAmount: '',
    overridePercentage: '',
    notes: '',
    reason: '',
    requiresApproval: true
  })

  const [latePenaltyForm, setLatePenaltyForm] = useState({
    startDate: new Date(new Date().getFullYear(), new Date().getMonth(), 1).toISOString().split('T')[0],
    endDate: new Date().toISOString().split('T')[0],
    applyPenalties: false
  })

  useEffect(() => {
    loadDeductionData()
  }, [])

  const loadDeductionData = async () => {
    try {
      setLoading(true)
      
      // Load available deduction components
      const componentsResponse = await fetch('/api/admin/payroll/deductions?action=available_deductions')
      const componentsData = await componentsResponse.json()
      
      if (componentsData.success) {
        setDeductionComponents(componentsData.data)
      }

      // Load employee assignments
      const assignmentsResponse = await fetch('/api/admin/payroll/allowances?action=all_assignments&limit=100')
      const assignmentsData = await assignmentsResponse.json()
      
      if (assignmentsData.success) {
        // Filter for deductions only
        const deductionAssignments = assignmentsData.data.assignments.filter((a: any) => 
          deductionComponents.some(d => d.id === a.component_id)
        )
        setEmployeeAssignments(deductionAssignments)
      }

      // Load statutory deductions summary
      const statutoryResponse = await fetch('/api/admin/payroll/deductions?action=statutory_deductions')
      const statutoryData = await statutoryResponse.json()
      
      if (statutoryData.success) {
        setStatutoryDeductions(statutoryData.data)
      }

    } catch (error) {
      console.error('Error loading deduction data:', error)
      toast({
        title: "Error",
        description: "Failed to load deduction data",
        variant: "destructive"
      })
    } finally {
      setLoading(false)
    }
  }

  const handleCreateAssignment = async () => {
    try {
      const response = await fetch('/api/admin/payroll/deductions', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          userId: assignmentForm.userId,
          componentId: assignmentForm.componentId,
          effectiveFrom: assignmentForm.effectiveFrom,
          effectiveTo: assignmentForm.effectiveTo || null,
          overrideAmount: assignmentForm.overrideAmount ? parseFloat(assignmentForm.overrideAmount) : null,
          overridePercentage: assignmentForm.overridePercentage ? parseFloat(assignmentForm.overridePercentage) : null,
          notes: assignmentForm.notes,
          reason: assignmentForm.reason,
          requiresApproval: assignmentForm.requiresApproval
        })
      })

      const data = await response.json()

      if (data.success) {
        toast({
          title: "Success",
          description: data.message
        })
        setShowAssignDialog(false)
        resetAssignmentForm()
        loadDeductionData()
      } else {
        throw new Error(data.error)
      }
    } catch (error) {
      console.error('Error creating assignment:', error)
      toast({
        title: "Error",
        description: "Failed to create deduction assignment",
        variant: "destructive"
      })
    }
  }

  const handleProcessLatePenalties = async () => {
    try {
      // First, calculate late penalties
      const calculateResponse = await fetch('/api/admin/payroll/deductions?action=late_penalties&startDate=' + 
        latePenaltyForm.startDate + '&endDate=' + latePenaltyForm.endDate)
      const calculateData = await calculateResponse.json()

      if (calculateData.success) {
        setLatePenalties(calculateData.data)
        
        if (latePenaltyForm.applyPenalties) {
          // Apply penalties as deduction assignments
          const applyResponse = await fetch('/api/admin/payroll/deductions', {
            method: 'PUT',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({
              action: 'process_late_penalties',
              startDate: latePenaltyForm.startDate,
              endDate: latePenaltyForm.endDate,
              applyPenalties: true
            })
          })

          const applyData = await applyResponse.json()

          if (applyData.success) {
            toast({
              title: "Success",
              description: `Late penalties applied for ${applyData.data.processed.length} employees`
            })
            loadDeductionData()
          }
        } else {
          toast({
            title: "Calculation Complete",
            description: `Late penalties calculated for ${calculateData.data.length} employees`
          })
        }
      } else {
        throw new Error(calculateData.error)
      }
    } catch (error) {
      console.error('Error processing late penalties:', error)
      toast({
        title: "Error",
        description: "Failed to process late penalties",
        variant: "destructive"
      })
    }
  }

  const handleApproveAssignment = async (assignmentId: string) => {
    try {
      const response = await fetch('/api/admin/payroll/deductions', {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          assignmentId,
          action: 'approve'
        })
      })

      const data = await response.json()

      if (data.success) {
        toast({
          title: "Success",
          description: "Deduction assignment approved"
        })
        loadDeductionData()
      } else {
        throw new Error(data.error)
      }
    } catch (error) {
      console.error('Error approving assignment:', error)
      toast({
        title: "Error",
        description: "Failed to approve assignment",
        variant: "destructive"
      })
    }
  }

  const resetAssignmentForm = () => {
    setAssignmentForm({
      userId: '',
      componentId: '',
      effectiveFrom: new Date().toISOString().split('T')[0],
      effectiveTo: '',
      overrideAmount: '',
      overridePercentage: '',
      notes: '',
      reason: '',
      requiresApproval: true
    })
  }

  const filteredAssignments = employeeAssignments.filter(assignment => {
    const matchesSearch = assignment.employee_name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         assignment.name.toLowerCase().includes(searchTerm.toLowerCase())
    const matchesCategory = categoryFilter === 'all' || assignment.category === categoryFilter
    return matchesSearch && matchesCategory
  })

  const getStatusBadge = (assignment: EmployeeDeductionAssignment) => {
    if (!assignment.approved_by) {
      return <Badge variant="outline">Pending Approval</Badge>
    }
    if (!assignment.is_active) {
      return <Badge variant="secondary">Inactive</Badge>
    }
    if (assignment.is_statutory) {
      return <Badge variant="default">Statutory</Badge>
    }
    return <Badge variant="default">Active</Badge>
  }

  const getCategoryBadge = (category: string) => {
    const variants: Record<string, any> = {
      statutory: 'default',
      voluntary: 'secondary',
      company_policy: 'outline'
    }
    return <Badge variant={variants[category] || 'outline'}>{category}</Badge>
  }

  if (loading) {
    return <div className="flex items-center justify-center h-64">Loading deduction data...</div>
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold">Deduction Management</h1>
          <p className="text-muted-foreground">Manage employee deductions and penalties</p>
        </div>
        <div className="flex items-center space-x-2">
          <Button onClick={() => setShowAssignDialog(true)}>
            <Plus className="h-4 w-4 mr-2" />
            Assign Deduction
          </Button>
          <Button variant="outline" onClick={() => setShowLatePenaltyDialog(true)}>
            <Calculator className="h-4 w-4 mr-2" />
            Late Penalties
          </Button>
          <Button variant="outline">
            <Download className="h-4 w-4 mr-2" />
            Export
          </Button>
        </div>
      </div>

      {/* Summary Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Deductions</CardTitle>
            <TrendingDown className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{deductionComponents.length}</div>
            <p className="text-xs text-muted-foreground">Available deduction types</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Active Assignments</CardTitle>
            <Users className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{employeeAssignments.filter(a => a.is_active).length}</div>
            <p className="text-xs text-muted-foreground">Current active assignments</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Statutory Deductions</CardTitle>
            <Shield className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{statutoryDeductions.length}</div>
            <p className="text-xs text-muted-foreground">Mandatory deductions</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Pending Approvals</CardTitle>
            <Clock className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{employeeAssignments.filter(a => !a.approved_by).length}</div>
            <p className="text-xs text-muted-foreground">Awaiting approval</p>
          </CardContent>
        </Card>
      </div>

      {/* Main Content */}
      <Tabs value={activeTab} onValueChange={setActiveTab}>
        <TabsList>
          <TabsTrigger value="assignments">Employee Assignments</TabsTrigger>
          <TabsTrigger value="statutory">Statutory Deductions</TabsTrigger>
          <TabsTrigger value="components">Deduction Types</TabsTrigger>
        </TabsList>

        <TabsContent value="assignments" className="space-y-4">
          {/* Search and Filters */}
          <div className="flex items-center space-x-4">
            <div className="relative flex-1">
              <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
              <Input
                placeholder="Search employees or deductions..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-8"
              />
            </div>
            <Select value={categoryFilter} onValueChange={setCategoryFilter}>
              <SelectTrigger className="w-48">
                <SelectValue placeholder="Filter by category" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Categories</SelectItem>
                <SelectItem value="statutory">Statutory</SelectItem>
                <SelectItem value="voluntary">Voluntary</SelectItem>
                <SelectItem value="company_policy">Company Policy</SelectItem>
              </SelectContent>
            </Select>
          </div>

          {/* Assignments Table */}
          <Card>
            <CardContent className="p-0">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Employee</TableHead>
                    <TableHead>Deduction</TableHead>
                    <TableHead>Category</TableHead>
                    <TableHead>Amount</TableHead>
                    <TableHead>Status</TableHead>
                    <TableHead>Effective From</TableHead>
                    <TableHead>Actions</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {filteredAssignments.map((assignment) => (
                    <TableRow key={assignment.assignment_id}>
                      <TableCell>
                        <div className="flex items-center space-x-2">
                          <Avatar className="h-8 w-8">
                            <AvatarFallback>
                              {assignment.employee_name.split(' ').map(n => n[0]).join('')}
                            </AvatarFallback>
                          </Avatar>
                          <div>
                            <div className="font-medium">{assignment.employee_name}</div>
                            <div className="text-sm text-muted-foreground">{assignment.code}</div>
                          </div>
                        </div>
                      </TableCell>
                      <TableCell>
                        <div>
                          <div className="font-medium">{assignment.name}</div>
                          <div className="text-sm text-muted-foreground">{assignment.description}</div>
                        </div>
                      </TableCell>
                      <TableCell>{getCategoryBadge(assignment.category)}</TableCell>
                      <TableCell>
                        {assignment.override_amount && (
                          <CurrencyDisplay amount={assignment.override_amount} />
                        )}
                        {assignment.calculation_type === 'percentage' && assignment.percentage && (
                          <span>{assignment.percentage}% of {assignment.percentage_base}</span>
                        )}
                        {assignment.calculation_type === 'fixed' && assignment.fixed_amount && (
                          <CurrencyDisplay amount={assignment.fixed_amount} />
                        )}
                      </TableCell>
                      <TableCell>{getStatusBadge(assignment)}</TableCell>
                      <TableCell>{new Date(assignment.effective_from).toLocaleDateString()}</TableCell>
                      <TableCell>
                        <div className="flex items-center space-x-2">
                          {!assignment.approved_by && (
                            <Button
                              size="sm"
                              onClick={() => handleApproveAssignment(assignment.assignment_id)}
                            >
                              <CheckCircle className="h-4 w-4" />
                            </Button>
                          )}
                          <Button
                            size="sm"
                            variant="outline"
                            onClick={() => setEditingAssignment(assignment)}
                          >
                            <Edit className="h-4 w-4" />
                          </Button>
                        </div>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="statutory" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Statutory Deductions Summary</CardTitle>
              <CardDescription>Overview of mandatory deductions and compliance</CardDescription>
            </CardHeader>
            <CardContent className="p-0">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Deduction</TableHead>
                    <TableHead>Type</TableHead>
                    <TableHead>Rate</TableHead>
                    <TableHead>Employees</TableHead>
                    <TableHead>Monthly Total</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {statutoryDeductions.map((deduction) => (
                    <TableRow key={deduction.code}>
                      <TableCell>
                        <div>
                          <div className="font-medium">{deduction.name}</div>
                          <div className="text-sm text-muted-foreground">{deduction.code}</div>
                        </div>
                      </TableCell>
                      <TableCell>
                        <Badge variant="default">{deduction.calculation_type}</Badge>
                      </TableCell>
                      <TableCell>
                        {deduction.percentage && <span>{deduction.percentage}%</span>}
                        {deduction.fixed_amount && <CurrencyDisplay amount={deduction.fixed_amount} />}
                      </TableCell>
                      <TableCell>{deduction.assigned_employees}</TableCell>
                      <TableCell>
                        <CurrencyDisplay amount={deduction.total_monthly_deduction} />
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="components" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Available Deduction Types</CardTitle>
              <CardDescription>Configure deduction types and their calculation methods</CardDescription>
            </CardHeader>
            <CardContent className="p-0">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Name</TableHead>
                    <TableHead>Code</TableHead>
                    <TableHead>Category</TableHead>
                    <TableHead>Calculation</TableHead>
                    <TableHead>Amount/Rate</TableHead>
                    <TableHead>Status</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {deductionComponents.map((component) => (
                    <TableRow key={component.id}>
                      <TableCell>
                        <div>
                          <div className="font-medium">{component.name}</div>
                          <div className="text-sm text-muted-foreground">{component.description}</div>
                        </div>
                      </TableCell>
                      <TableCell>{component.code}</TableCell>
                      <TableCell>{getCategoryBadge(component.category)}</TableCell>
                      <TableCell>{component.calculation_type}</TableCell>
                      <TableCell>
                        {component.calculation_type === 'fixed' && component.fixed_amount && (
                          <CurrencyDisplay amount={component.fixed_amount} />
                        )}
                        {component.calculation_type === 'percentage' && component.percentage && (
                          <span>{component.percentage}%</span>
                        )}
                      </TableCell>
                      <TableCell>
                        <Badge variant={component.is_active ? "default" : "secondary"}>
                          {component.is_active ? "Active" : "Inactive"}
                        </Badge>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>

      {/* Assignment Dialog */}
      <Dialog open={showAssignDialog} onOpenChange={setShowAssignDialog}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Assign Deduction</DialogTitle>
            <DialogDescription>Assign a deduction to an employee</DialogDescription>
          </DialogHeader>
          <div className="space-y-4">
            <div>
              <Label htmlFor="reason">Reason for Deduction</Label>
              <Input
                id="reason"
                placeholder="Reason for this deduction"
                value={assignmentForm.reason}
                onChange={(e) => setAssignmentForm(prev => ({ ...prev, reason: e.target.value }))}
              />
            </div>
            
            <div>
              <Label htmlFor="deduction">Deduction Type</Label>
              <Select value={assignmentForm.componentId} onValueChange={(value) => 
                setAssignmentForm(prev => ({ ...prev, componentId: value }))
              }>
                <SelectTrigger>
                  <SelectValue placeholder="Select deduction" />
                </SelectTrigger>
                <SelectContent>
                  {deductionComponents.map((component) => (
                    <SelectItem key={component.id} value={component.id}>
                      {component.name} ({component.code})
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div className="grid grid-cols-2 gap-4">
              <div>
                <Label htmlFor="effectiveFrom">Effective From</Label>
                <Input
                  id="effectiveFrom"
                  type="date"
                  value={assignmentForm.effectiveFrom}
                  onChange={(e) => setAssignmentForm(prev => ({ ...prev, effectiveFrom: e.target.value }))}
                />
              </div>
              <div>
                <Label htmlFor="effectiveTo">Effective To (Optional)</Label>
                <Input
                  id="effectiveTo"
                  type="date"
                  value={assignmentForm.effectiveTo}
                  onChange={(e) => setAssignmentForm(prev => ({ ...prev, effectiveTo: e.target.value }))}
                />
              </div>
            </div>

            <div>
              <Label htmlFor="notes">Additional Notes</Label>
              <Input
                id="notes"
                placeholder="Additional notes"
                value={assignmentForm.notes}
                onChange={(e) => setAssignmentForm(prev => ({ ...prev, notes: e.target.value }))}
              />
            </div>

            <Alert>
              <AlertTriangle className="h-4 w-4" />
              <AlertTitle>Approval Required</AlertTitle>
              <AlertDescription>
                Deduction assignments require approval before becoming active.
              </AlertDescription>
            </Alert>
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setShowAssignDialog(false)}>
              Cancel
            </Button>
            <Button onClick={handleCreateAssignment}>
              Assign Deduction
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Late Penalty Dialog */}
      <Dialog open={showLatePenaltyDialog} onOpenChange={setShowLatePenaltyDialog}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Process Late Penalties</DialogTitle>
            <DialogDescription>Calculate and apply late penalties for a period</DialogDescription>
          </DialogHeader>
          <div className="space-y-4">
            <div className="grid grid-cols-2 gap-4">
              <div>
                <Label htmlFor="startDate">Start Date</Label>
                <Input
                  id="startDate"
                  type="date"
                  value={latePenaltyForm.startDate}
                  onChange={(e) => setLatePenaltyForm(prev => ({ ...prev, startDate: e.target.value }))}
                />
              </div>
              <div>
                <Label htmlFor="endDate">End Date</Label>
                <Input
                  id="endDate"
                  type="date"
                  value={latePenaltyForm.endDate}
                  onChange={(e) => setLatePenaltyForm(prev => ({ ...prev, endDate: e.target.value }))}
                />
              </div>
            </div>

            <div className="flex items-center space-x-2">
              <input
                type="checkbox"
                id="applyPenalties"
                checked={latePenaltyForm.applyPenalties}
                onChange={(e) => setLatePenaltyForm(prev => ({ ...prev, applyPenalties: e.target.checked }))}
              />
              <Label htmlFor="applyPenalties">Apply penalties (create deduction assignments)</Label>
            </div>

            {latePenalties.length > 0 && (
              <div className="space-y-2">
                <h4 className="font-medium">Calculated Penalties:</h4>
                <div className="max-h-40 overflow-y-auto">
                  {latePenalties.map((penalty) => (
                    <div key={penalty.user_id} className="flex justify-between text-sm">
                      <span>{penalty.full_name}</span>
                      <span>{penalty.late_days} days, <CurrencyDisplay amount={penalty.calculated_penalty} /></span>
                    </div>
                  ))}
                </div>
              </div>
            )}
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setShowLatePenaltyDialog(false)}>
              Cancel
            </Button>
            <Button onClick={handleProcessLatePenalties}>
              {latePenaltyForm.applyPenalties ? 'Apply Penalties' : 'Calculate Penalties'}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  )
}
