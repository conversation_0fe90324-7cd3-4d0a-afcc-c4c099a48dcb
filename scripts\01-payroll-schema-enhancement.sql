-- ============================================================================
-- PAYROLL SYSTEM DATABASE SCHEMA ENHANCEMENT - PHASE 1
-- Task 1.1: Database Schema Completion
-- ============================================================================

-- Enable Row Level Security on existing tables
ALTER TABLE payroll ENABLE ROW LEVEL SECURITY;
ALTER TABLE users ENABLE ROW LEVEL SECURITY;
ALTER TABLE attendance ENABLE ROW LEVEL SECURITY;

-- ============================================================================
-- Step 1: Create missing payroll workflow tables
-- ============================================================================

-- Payroll approval workflow table
CREATE TABLE IF NOT EXISTS payroll_approvals (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    payroll_id UUID NOT NULL REFERENCES payroll(id) ON DELETE CASCADE,
    approver_id UUID NOT NULL REFERENCES users(id),
    approval_status VARCHAR(20) NOT NULL CHECK (approval_status IN ('pending', 'approved', 'rejected', 'cancelled')),
    approval_date TIMESTAMP WITH TIME ZONE,
    comments TEXT,
    approval_level INTEGER DEFAULT 1, -- Support multi-level approvals
    is_final_approval BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    -- Ensure one approval record per payroll per approver per level
    UNIQUE(payroll_id, approver_id, approval_level)
);

-- Payroll disbursement tracking table
CREATE TABLE IF NOT EXISTS payroll_disbursements (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    payroll_id UUID NOT NULL REFERENCES payroll(id) ON DELETE CASCADE,
    disbursement_method VARCHAR(50) NOT NULL CHECK (disbursement_method IN ('bank_transfer', 'cash', 'cheque', 'digital_wallet')),
    bank_reference VARCHAR(100),
    transaction_id VARCHAR(100),
    disbursement_date DATE,
    disbursement_amount DECIMAL(10,2) NOT NULL,
    status VARCHAR(20) NOT NULL CHECK (status IN ('pending', 'processing', 'completed', 'failed', 'cancelled')),
    failure_reason TEXT,
    processed_by UUID REFERENCES users(id),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Enhanced audit logging table for payroll operations
CREATE TABLE IF NOT EXISTS payroll_audit_log (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    table_name VARCHAR(50) NOT NULL,
    record_id UUID NOT NULL,
    action VARCHAR(20) NOT NULL CHECK (action IN ('INSERT', 'UPDATE', 'DELETE', 'SELECT')),
    old_values JSONB,
    new_values JSONB,
    changed_by UUID REFERENCES users(id),
    changed_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    ip_address INET,
    user_agent TEXT,
    session_id VARCHAR(255),
    
    -- Index for performance
    INDEX (table_name, record_id),
    INDEX (changed_by, changed_at),
    INDEX (changed_at)
);

-- ============================================================================
-- Step 2: Create enhanced payroll components tables
-- ============================================================================

-- Master table for payroll components (allowances and deductions)
CREATE TABLE IF NOT EXISTS payroll_components_master (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name VARCHAR(100) NOT NULL,
    code VARCHAR(50) NOT NULL UNIQUE,
    type VARCHAR(20) NOT NULL CHECK (type IN ('deduction', 'allowance')),
    category VARCHAR(30) NOT NULL CHECK (category IN ('statutory', 'voluntary', 'company_policy', 'custom')),
    calculation_type VARCHAR(20) NOT NULL CHECK (calculation_type IN ('fixed', 'percentage', 'formula', 'conditional')),

    -- Calculation parameters
    fixed_amount DECIMAL(10,2),
    percentage DECIMAL(5,2),
    percentage_base VARCHAR(30) CHECK (percentage_base IN ('base_salary', 'gross_pay', 'net_pay', 'total_earnings')),
    formula TEXT,
    conditions JSONB,

    -- Tax and compliance
    is_taxable BOOLEAN DEFAULT TRUE,
    is_statutory BOOLEAN DEFAULT FALSE,
    affects_provident_fund BOOLEAN DEFAULT TRUE,
    affects_gratuity BOOLEAN DEFAULT TRUE,

    -- Applicability
    applicable_to_pay_structures JSONB DEFAULT '[]',
    applicable_to_employee_categories JSONB DEFAULT '[]',
    applicable_to_departments JSONB DEFAULT '[]',

    -- Limits and validations
    minimum_amount DECIMAL(10,2),
    maximum_amount DECIMAL(10,2),
    minimum_salary_threshold DECIMAL(10,2),
    maximum_salary_threshold DECIMAL(10,2),

    -- Metadata
    description TEXT,
    is_active BOOLEAN DEFAULT TRUE,
    effective_from DATE NOT NULL,
    effective_to DATE,
    created_by UUID REFERENCES users(id),
    approved_by UUID REFERENCES users(id),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Employee component assignments table
CREATE TABLE IF NOT EXISTS employee_component_assignments (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    component_id UUID NOT NULL REFERENCES payroll_components_master(id) ON DELETE CASCADE,
    is_active BOOLEAN DEFAULT TRUE,
    effective_from DATE NOT NULL,
    effective_to DATE,

    -- Override values
    override_amount DECIMAL(10,2),
    override_percentage DECIMAL(5,2),
    override_conditions JSONB,

    -- Approval
    assigned_by UUID REFERENCES users(id),
    approved_by UUID REFERENCES users(id),
    approval_date DATE,
    notes TEXT,

    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),

    UNIQUE(user_id, component_id, effective_from)
);

-- ============================================================================
-- Step 3: Create payroll periods and settings tables
-- ============================================================================

-- Payroll periods for fiscal year management
CREATE TABLE IF NOT EXISTS payroll_periods (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    period_name VARCHAR(100) NOT NULL, -- e.g., "Shrawan 2081", "Q1 2024-25"
    period_type VARCHAR(20) NOT NULL CHECK (period_type IN ('monthly', 'quarterly', 'yearly')),
    fiscal_year VARCHAR(10) NOT NULL, -- e.g., "2081-82"
    bs_start_date VARCHAR(10) NOT NULL, -- Bikram Sambat format
    bs_end_date VARCHAR(10) NOT NULL,   -- Bikram Sambat format
    ad_start_date DATE NOT NULL,
    ad_end_date DATE NOT NULL,
    working_days INTEGER DEFAULT 0,
    public_holidays INTEGER DEFAULT 0,
    is_current_period BOOLEAN DEFAULT FALSE,
    is_closed BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(period_name, fiscal_year)
);

-- Payroll system settings
CREATE TABLE IF NOT EXISTS payroll_settings (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    setting_key VARCHAR(100) NOT NULL UNIQUE,
    setting_value TEXT NOT NULL,
    setting_type VARCHAR(20) NOT NULL CHECK (setting_type IN ('string', 'number', 'boolean', 'json')),
    description TEXT,
    is_system_setting BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- ============================================================================
-- Step 4: Create monthly payroll summary table
-- ============================================================================

CREATE TABLE IF NOT EXISTS monthly_payroll_summary (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    fiscal_year VARCHAR(10) NOT NULL,
    bs_month VARCHAR(20) NOT NULL, -- e.g., "Shrawan 2081"
    ad_month_start DATE NOT NULL,
    ad_month_end DATE NOT NULL,
    
    -- Attendance summary
    total_working_days INTEGER DEFAULT 0,
    days_present INTEGER DEFAULT 0,
    days_absent INTEGER DEFAULT 0,
    days_late INTEGER DEFAULT 0,
    days_half_day INTEGER DEFAULT 0,
    days_on_leave INTEGER DEFAULT 0,
    total_hours_worked DECIMAL(6,2) DEFAULT 0,
    regular_hours DECIMAL(6,2) DEFAULT 0,
    overtime_hours DECIMAL(6,2) DEFAULT 0,
    
    -- Salary components
    base_salary DECIMAL(10,2) NOT NULL,
    overtime_pay DECIMAL(10,2) DEFAULT 0,
    total_allowances DECIMAL(10,2) DEFAULT 0,
    total_deductions DECIMAL(10,2) DEFAULT 0,
    gross_pay DECIMAL(10,2) NOT NULL,
    tax_deductions DECIMAL(10,2) DEFAULT 0,
    provident_fund DECIMAL(10,2) DEFAULT 0,
    net_pay DECIMAL(10,2) NOT NULL,
    
    -- Bonuses and penalties
    attendance_bonus DECIMAL(10,2) DEFAULT 0,
    late_penalty DECIMAL(10,2) DEFAULT 0,
    
    -- Status and metadata
    status VARCHAR(20) NOT NULL DEFAULT 'draft' CHECK (status IN ('draft', 'calculated', 'approved', 'processed', 'paid')),
    calculated_at TIMESTAMP WITH TIME ZONE,
    calculated_by UUID REFERENCES users(id),
    approved_at TIMESTAMP WITH TIME ZONE,
    approved_by UUID REFERENCES users(id),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    UNIQUE(user_id, fiscal_year, bs_month)
);

-- ============================================================================
-- Step 5: Create indexes for performance optimization
-- ============================================================================

-- Payroll approvals indexes
CREATE INDEX IF NOT EXISTS idx_payroll_approvals_payroll_id ON payroll_approvals(payroll_id);
CREATE INDEX IF NOT EXISTS idx_payroll_approvals_approver ON payroll_approvals(approver_id);
CREATE INDEX IF NOT EXISTS idx_payroll_approvals_status ON payroll_approvals(approval_status);

-- Payroll disbursements indexes
CREATE INDEX IF NOT EXISTS idx_payroll_disbursements_payroll_id ON payroll_disbursements(payroll_id);
CREATE INDEX IF NOT EXISTS idx_payroll_disbursements_status ON payroll_disbursements(status);
CREATE INDEX IF NOT EXISTS idx_payroll_disbursements_date ON payroll_disbursements(disbursement_date);

-- Audit log indexes
CREATE INDEX IF NOT EXISTS idx_payroll_audit_log_table_record ON payroll_audit_log(table_name, record_id);
CREATE INDEX IF NOT EXISTS idx_payroll_audit_log_user_date ON payroll_audit_log(changed_by, changed_at);
CREATE INDEX IF NOT EXISTS idx_payroll_audit_log_date ON payroll_audit_log(changed_at);

-- Component master indexes
CREATE INDEX IF NOT EXISTS idx_payroll_components_master_code ON payroll_components_master(code);
CREATE INDEX IF NOT EXISTS idx_payroll_components_master_type ON payroll_components_master(type);
CREATE INDEX IF NOT EXISTS idx_payroll_components_master_active ON payroll_components_master(is_active) WHERE is_active = TRUE;

-- Employee component assignments indexes
CREATE INDEX IF NOT EXISTS idx_employee_component_assignments_user ON employee_component_assignments(user_id);
CREATE INDEX IF NOT EXISTS idx_employee_component_assignments_component ON employee_component_assignments(component_id);
CREATE INDEX IF NOT EXISTS idx_employee_component_assignments_active ON employee_component_assignments(user_id, is_active) WHERE is_active = TRUE;

-- Monthly payroll summary indexes
CREATE INDEX IF NOT EXISTS idx_monthly_payroll_summary_user_fiscal ON monthly_payroll_summary(user_id, fiscal_year);
CREATE INDEX IF NOT EXISTS idx_monthly_payroll_summary_status ON monthly_payroll_summary(status);
CREATE INDEX IF NOT EXISTS idx_monthly_payroll_summary_month ON monthly_payroll_summary(ad_month_start, ad_month_end);

-- ============================================================================
-- SUCCESS VERIFICATION
-- ============================================================================

-- Verify all tables were created
SELECT table_name 
FROM information_schema.tables 
WHERE table_name IN (
    'payroll_approvals', 
    'payroll_disbursements', 
    'payroll_audit_log',
    'payroll_components_master',
    'employee_component_assignments',
    'payroll_periods',
    'payroll_settings',
    'monthly_payroll_summary'
)
ORDER BY table_name;
