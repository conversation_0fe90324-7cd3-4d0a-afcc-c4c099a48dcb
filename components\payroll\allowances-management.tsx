"use client"

import React, { useState, useEffect } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Textarea } from '@/components/ui/textarea'
import { Badge } from '@/components/ui/badge'
import { Ta<PERSON>, Ta<PERSON>Content, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog'
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table'
import { toast } from 'sonner'
import { Plus, Edit, Trash2, DollarSign, <PERSON>, TrendingUp, Award, Search } from 'lucide-react'

interface AllowanceType {
  id: string
  name: string
  code: string
  category: string
  calculation_type: string
  fixed_amount?: number
  percentage?: number
  percentage_base?: string
  is_taxable: boolean
  is_statutory: boolean
  description?: string
  is_active: boolean
}

interface AllowanceAssignment {
  id: string
  user_id: string
  allowance_type: string
  allowance_name: string
  calculation_type: string
  amount: number
  percentage?: number
  percentage_base?: string
  is_taxable: boolean
  effective_from: string
  effective_to?: string
  is_active: boolean
  notes?: string
}

interface EmployeeAllowanceSummary {
  user_id: string
  full_name: string
  email: string
  department?: string
  position?: string
  base_salary?: number
  total_allowances: number
  allowance_count: number
  allowances: AllowanceAssignment[]
}

interface AllowanceStatistics {
  totalAllowanceTypes: number
  totalActiveAssignments: number
  totalAllowanceAmount: number
  mostUsedAllowanceType: string
  averageAllowancePerEmployee: number
}

export function AllowancesManagement() {
  const [allowanceTypes, setAllowanceTypes] = useState<AllowanceType[]>([])
  const [employeeSummary, setEmployeeSummary] = useState<EmployeeAllowanceSummary[]>([])
  const [statistics, setStatistics] = useState<AllowanceStatistics | null>(null)
  const [loading, setLoading] = useState(true)
  const [searchTerm, setSearchTerm] = useState('')
  const [showCreateTypeDialog, setShowCreateTypeDialog] = useState(false)
  const [showAssignDialog, setShowAssignDialog] = useState(false)
  const [selectedEmployee, setSelectedEmployee] = useState<string>('')

  const [newAllowanceType, setNewAllowanceType] = useState({
    name: '',
    code: '',
    category: 'company_policy',
    calculation_type: 'fixed',
    fixed_amount: 0,
    percentage: 0,
    percentage_base: 'base_salary',
    is_taxable: true,
    is_statutory: false,
    description: ''
  })

  const [newAssignment, setNewAssignment] = useState({
    allowance_type: 'travelling',
    allowance_name: '',
    calculation_type: 'fixed',
    amount: 0,
    percentage: 0,
    percentage_base: 'base_salary',
    is_taxable: true,
    description: ''
  })

  useEffect(() => {
    fetchData()
  }, [])

  const fetchData = async () => {
    try {
      setLoading(true)
      
      // Fetch allowance types
      const typesResponse = await fetch('/api/admin/allowances?action=types')
      const typesData = await typesResponse.json()
      
      // Fetch employee summary
      const summaryResponse = await fetch('/api/admin/allowances?action=summary')
      const summaryData = await summaryResponse.json()
      
      // Fetch statistics
      const statsResponse = await fetch('/api/admin/allowances?action=statistics')
      const statsData = await statsResponse.json()

      if (typesData.success) {
        setAllowanceTypes(typesData.data)
      }
      
      if (summaryData.success) {
        setEmployeeSummary(summaryData.data)
      }
      
      if (statsData.success) {
        setStatistics(statsData.data)
      }

    } catch (error) {
      console.error('Error fetching allowances data:', error)
      toast.error('Failed to fetch allowances data')
    } finally {
      setLoading(false)
    }
  }

  const handleCreateAllowanceType = async () => {
    try {
      const response = await fetch('/api/admin/allowances', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          action: 'create_type',
          allowanceType: newAllowanceType
        })
      })

      const data = await response.json()

      if (data.success) {
        toast.success('Allowance type created successfully')
        setShowCreateTypeDialog(false)
        setNewAllowanceType({
          name: '',
          code: '',
          category: 'company_policy',
          calculation_type: 'fixed',
          fixed_amount: 0,
          percentage: 0,
          percentage_base: 'base_salary',
          is_taxable: true,
          is_statutory: false,
          description: ''
        })
        fetchData()
      } else {
        toast.error(data.error || 'Failed to create allowance type')
      }
    } catch (error) {
      console.error('Error creating allowance type:', error)
      toast.error('Failed to create allowance type')
    }
  }

  const handleAssignAllowance = async () => {
    try {
      if (!selectedEmployee) {
        toast.error('Please select an employee')
        return
      }

      const response = await fetch('/api/admin/allowances', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          action: 'assign',
          userId: selectedEmployee,
          allowanceConfig: newAssignment
        })
      })

      const data = await response.json()

      if (data.success) {
        toast.success('Allowance assigned successfully')
        setShowAssignDialog(false)
        setSelectedEmployee('')
        setNewAssignment({
          allowance_type: 'travelling',
          allowance_name: '',
          calculation_type: 'fixed',
          amount: 0,
          percentage: 0,
          percentage_base: 'base_salary',
          is_taxable: true,
          description: ''
        })
        fetchData()
      } else {
        toast.error(data.error || 'Failed to assign allowance')
      }
    } catch (error) {
      console.error('Error assigning allowance:', error)
      toast.error('Failed to assign allowance')
    }
  }

  const handleRemoveAllowance = async (assignmentId: string) => {
    try {
      const response = await fetch(`/api/admin/allowances?assignmentId=${assignmentId}`, {
        method: 'DELETE'
      })

      const data = await response.json()

      if (data.success) {
        toast.success('Allowance removed successfully')
        fetchData()
      } else {
        toast.error(data.error || 'Failed to remove allowance')
      }
    } catch (error) {
      console.error('Error removing allowance:', error)
      toast.error('Failed to remove allowance')
    }
  }

  const filteredEmployees = employeeSummary.filter(employee =>
    employee.full_name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    employee.email.toLowerCase().includes(searchTerm.toLowerCase()) ||
    employee.department?.toLowerCase().includes(searchTerm.toLowerCase())
  )

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-3xl font-bold">Allowances Management</h2>
          <p className="text-muted-foreground">
            Manage allowance types and employee allowance assignments
          </p>
        </div>
        <div className="flex items-center space-x-2">
          <Dialog open={showCreateTypeDialog} onOpenChange={setShowCreateTypeDialog}>
            <DialogTrigger asChild>
              <Button>
                <Plus className="h-4 w-4 mr-2" />
                Create Allowance Type
              </Button>
            </DialogTrigger>
            <DialogContent>
              <DialogHeader>
                <DialogTitle>Create New Allowance Type</DialogTitle>
                <DialogDescription>Define a new allowance type for the organization</DialogDescription>
              </DialogHeader>
              <div className="space-y-4">
                <div className="grid grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="name">Allowance Name</Label>
                    <Input 
                      id="name" 
                      value={newAllowanceType.name}
                      onChange={(e) => setNewAllowanceType({...newAllowanceType, name: e.target.value})}
                      placeholder="e.g., Travelling Allowance"
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="code">Code</Label>
                    <Input 
                      id="code" 
                      value={newAllowanceType.code}
                      onChange={(e) => setNewAllowanceType({...newAllowanceType, code: e.target.value.toUpperCase()})}
                      placeholder="e.g., TRAVEL_ALLOW"
                    />
                  </div>
                </div>
                <div className="grid grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="category">Category</Label>
                    <Select value={newAllowanceType.category} onValueChange={(value) => setNewAllowanceType({...newAllowanceType, category: value})}>
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="statutory">Statutory</SelectItem>
                        <SelectItem value="voluntary">Voluntary</SelectItem>
                        <SelectItem value="company_policy">Company Policy</SelectItem>
                        <SelectItem value="custom">Custom</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="calculation_type">Calculation Type</Label>
                    <Select value={newAllowanceType.calculation_type} onValueChange={(value) => setNewAllowanceType({...newAllowanceType, calculation_type: value})}>
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="fixed">Fixed Amount</SelectItem>
                        <SelectItem value="percentage">Percentage</SelectItem>
                        <SelectItem value="formula">Formula</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                </div>
                {newAllowanceType.calculation_type === 'fixed' && (
                  <div className="space-y-2">
                    <Label htmlFor="fixed_amount">Fixed Amount (NPR)</Label>
                    <Input 
                      id="fixed_amount" 
                      type="number"
                      value={newAllowanceType.fixed_amount}
                      onChange={(e) => setNewAllowanceType({...newAllowanceType, fixed_amount: parseFloat(e.target.value) || 0})}
                    />
                  </div>
                )}
                {newAllowanceType.calculation_type === 'percentage' && (
                  <div className="grid grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <Label htmlFor="percentage">Percentage (%)</Label>
                      <Input 
                        id="percentage" 
                        type="number"
                        value={newAllowanceType.percentage}
                        onChange={(e) => setNewAllowanceType({...newAllowanceType, percentage: parseFloat(e.target.value) || 0})}
                      />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="percentage_base">Percentage Base</Label>
                      <Select value={newAllowanceType.percentage_base} onValueChange={(value) => setNewAllowanceType({...newAllowanceType, percentage_base: value})}>
                        <SelectTrigger>
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="base_salary">Base Salary</SelectItem>
                          <SelectItem value="gross_pay">Gross Pay</SelectItem>
                          <SelectItem value="net_pay">Net Pay</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                  </div>
                )}
                <div className="space-y-2">
                  <Label htmlFor="description">Description</Label>
                  <Textarea 
                    id="description" 
                    value={newAllowanceType.description}
                    onChange={(e) => setNewAllowanceType({...newAllowanceType, description: e.target.value})}
                    placeholder="Description of the allowance type"
                  />
                </div>
                <div className="flex items-center space-x-4">
                  <div className="flex items-center space-x-2">
                    <input 
                      type="checkbox" 
                      id="is_taxable"
                      checked={newAllowanceType.is_taxable}
                      onChange={(e) => setNewAllowanceType({...newAllowanceType, is_taxable: e.target.checked})}
                    />
                    <Label htmlFor="is_taxable">Taxable</Label>
                  </div>
                  <div className="flex items-center space-x-2">
                    <input 
                      type="checkbox" 
                      id="is_statutory"
                      checked={newAllowanceType.is_statutory}
                      onChange={(e) => setNewAllowanceType({...newAllowanceType, is_statutory: e.target.checked})}
                    />
                    <Label htmlFor="is_statutory">Statutory</Label>
                  </div>
                </div>
              </div>
              <DialogFooter>
                <Button variant="outline" onClick={() => setShowCreateTypeDialog(false)}>
                  Cancel
                </Button>
                <Button onClick={handleCreateAllowanceType}>
                  Create Allowance Type
                </Button>
              </DialogFooter>
            </DialogContent>
          </Dialog>
          
          <Dialog open={showAssignDialog} onOpenChange={setShowAssignDialog}>
            <DialogTrigger asChild>
              <Button variant="outline">
                <Award className="h-4 w-4 mr-2" />
                Assign Allowance
              </Button>
            </DialogTrigger>
            <DialogContent>
              <DialogHeader>
                <DialogTitle>Assign Allowance to Employee</DialogTitle>
                <DialogDescription>Assign an allowance to a specific employee</DialogDescription>
              </DialogHeader>
              <div className="space-y-4">
                <div className="space-y-2">
                  <Label htmlFor="employee">Select Employee</Label>
                  <Select value={selectedEmployee} onValueChange={setSelectedEmployee}>
                    <SelectTrigger>
                      <SelectValue placeholder="Choose an employee" />
                    </SelectTrigger>
                    <SelectContent>
                      {employeeSummary.map((employee) => (
                        <SelectItem key={employee.user_id} value={employee.user_id}>
                          {employee.full_name} - {employee.department}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
                <div className="grid grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="allowance_type">Allowance Type</Label>
                    <Select value={newAssignment.allowance_type} onValueChange={(value) => setNewAssignment({...newAssignment, allowance_type: value})}>
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="travelling">Travelling</SelectItem>
                        <SelectItem value="phone">Phone</SelectItem>
                        <SelectItem value="meal">Meal</SelectItem>
                        <SelectItem value="transport">Transport</SelectItem>
                        <SelectItem value="medical">Medical</SelectItem>
                        <SelectItem value="education">Education</SelectItem>
                        <SelectItem value="custom">Custom</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="allowance_name">Allowance Name</Label>
                    <Input 
                      id="allowance_name" 
                      value={newAssignment.allowance_name}
                      onChange={(e) => setNewAssignment({...newAssignment, allowance_name: e.target.value})}
                      placeholder="e.g., Monthly Travel Allowance"
                    />
                  </div>
                </div>
                <div className="space-y-2">
                  <Label htmlFor="calc_type">Calculation Type</Label>
                  <Select value={newAssignment.calculation_type} onValueChange={(value) => setNewAssignment({...newAssignment, calculation_type: value})}>
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="fixed">Fixed Amount</SelectItem>
                      <SelectItem value="percentage">Percentage</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                {newAssignment.calculation_type === 'fixed' ? (
                  <div className="space-y-2">
                    <Label htmlFor="amount">Amount (NPR)</Label>
                    <Input 
                      id="amount" 
                      type="number"
                      value={newAssignment.amount}
                      onChange={(e) => setNewAssignment({...newAssignment, amount: parseFloat(e.target.value) || 0})}
                    />
                  </div>
                ) : (
                  <div className="grid grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <Label htmlFor="percentage">Percentage (%)</Label>
                      <Input 
                        id="percentage" 
                        type="number"
                        value={newAssignment.percentage}
                        onChange={(e) => setNewAssignment({...newAssignment, percentage: parseFloat(e.target.value) || 0})}
                      />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="percentage_base">Percentage Base</Label>
                      <Select value={newAssignment.percentage_base} onValueChange={(value) => setNewAssignment({...newAssignment, percentage_base: value})}>
                        <SelectTrigger>
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="base_salary">Base Salary</SelectItem>
                          <SelectItem value="gross_pay">Gross Pay</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                  </div>
                )}
                <div className="flex items-center space-x-2">
                  <input 
                    type="checkbox" 
                    id="assign_is_taxable"
                    checked={newAssignment.is_taxable}
                    onChange={(e) => setNewAssignment({...newAssignment, is_taxable: e.target.checked})}
                  />
                  <Label htmlFor="assign_is_taxable">Taxable</Label>
                </div>
              </div>
              <DialogFooter>
                <Button variant="outline" onClick={() => setShowAssignDialog(false)}>
                  Cancel
                </Button>
                <Button onClick={handleAssignAllowance}>
                  Assign Allowance
                </Button>
              </DialogFooter>
            </DialogContent>
          </Dialog>
        </div>
      </div>

      {/* Statistics Cards */}
      {statistics && (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-4">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Allowance Types</CardTitle>
              <Award className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{statistics.totalAllowanceTypes}</div>
            </CardContent>
          </Card>
          
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Active Assignments</CardTitle>
              <Users className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{statistics.totalActiveAssignments}</div>
            </CardContent>
          </Card>
          
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Total Amount</CardTitle>
              <DollarSign className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">NPR {statistics.totalAllowanceAmount.toLocaleString()}</div>
            </CardContent>
          </Card>
          
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Average per Employee</CardTitle>
              <TrendingUp className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">NPR {statistics.averageAllowancePerEmployee.toLocaleString()}</div>
            </CardContent>
          </Card>
          
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Most Used Type</CardTitle>
              <Award className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-lg font-bold">{statistics.mostUsedAllowanceType}</div>
            </CardContent>
          </Card>
        </div>
      )}

      <Tabs defaultValue="employees" className="space-y-4">
        <TabsList>
          <TabsTrigger value="employees">Employee Allowances</TabsTrigger>
          <TabsTrigger value="types">Allowance Types</TabsTrigger>
        </TabsList>

        <TabsContent value="employees" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Employee Allowance Summary</CardTitle>
              <CardDescription>View and manage allowances assigned to employees</CardDescription>
            </CardHeader>
            <CardContent>
              {/* Search */}
              <div className="flex items-center space-x-4 mb-6">
                <div className="relative flex-1">
                  <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
                  <Input
                    placeholder="Search employees..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="pl-8"
                  />
                </div>
              </div>

              <div className="rounded-md border">
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Employee</TableHead>
                      <TableHead>Department</TableHead>
                      <TableHead>Base Salary</TableHead>
                      <TableHead>Total Allowances</TableHead>
                      <TableHead>Allowance Count</TableHead>
                      <TableHead>Actions</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {filteredEmployees.map((employee) => (
                      <TableRow key={employee.user_id}>
                        <TableCell>
                          <div className="space-y-1">
                            <div className="font-medium">{employee.full_name}</div>
                            <div className="text-sm text-muted-foreground">{employee.email}</div>
                          </div>
                        </TableCell>
                        <TableCell>
                          <Badge variant="outline">{employee.department || 'Not assigned'}</Badge>
                        </TableCell>
                        <TableCell>
                          {employee.base_salary ? `NPR ${employee.base_salary.toLocaleString()}` : 'Not set'}
                        </TableCell>
                        <TableCell>
                          <div className="font-medium">NPR {employee.total_allowances.toLocaleString()}</div>
                        </TableCell>
                        <TableCell>
                          <Badge>{employee.allowance_count}</Badge>
                        </TableCell>
                        <TableCell>
                          <div className="flex items-center space-x-2">
                            <Button
                              size="sm"
                              variant="outline"
                              onClick={() => {
                                setSelectedEmployee(employee.user_id)
                                setShowAssignDialog(true)
                              }}
                            >
                              <Plus className="h-4 w-4 mr-1" />
                              Add
                            </Button>
                          </div>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="types" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Allowance Types</CardTitle>
              <CardDescription>Manage available allowance types for the organization</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="rounded-md border">
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Name</TableHead>
                      <TableHead>Code</TableHead>
                      <TableHead>Category</TableHead>
                      <TableHead>Calculation</TableHead>
                      <TableHead>Taxable</TableHead>
                      <TableHead>Status</TableHead>
                      <TableHead>Actions</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {allowanceTypes.map((type) => (
                      <TableRow key={type.id}>
                        <TableCell>
                          <div className="space-y-1">
                            <div className="font-medium">{type.name}</div>
                            <div className="text-sm text-muted-foreground">{type.description}</div>
                          </div>
                        </TableCell>
                        <TableCell>
                          <Badge variant="outline">{type.code}</Badge>
                        </TableCell>
                        <TableCell>
                          <Badge variant="secondary">{type.category.replace('_', ' ')}</Badge>
                        </TableCell>
                        <TableCell>
                          <div className="space-y-1">
                            <div className="text-sm">{type.calculation_type}</div>
                            {type.calculation_type === 'fixed' && type.fixed_amount && (
                              <div className="text-xs text-muted-foreground">NPR {type.fixed_amount}</div>
                            )}
                            {type.calculation_type === 'percentage' && type.percentage && (
                              <div className="text-xs text-muted-foreground">{type.percentage}% of {type.percentage_base}</div>
                            )}
                          </div>
                        </TableCell>
                        <TableCell>
                          <Badge variant={type.is_taxable ? 'default' : 'secondary'}>
                            {type.is_taxable ? 'Taxable' : 'Non-taxable'}
                          </Badge>
                        </TableCell>
                        <TableCell>
                          <Badge variant={type.is_active ? 'default' : 'destructive'}>
                            {type.is_active ? 'Active' : 'Inactive'}
                          </Badge>
                        </TableCell>
                        <TableCell>
                          <div className="flex items-center space-x-2">
                            <Button size="sm" variant="outline">
                              <Edit className="h-4 w-4" />
                            </Button>
                          </div>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  )
}
