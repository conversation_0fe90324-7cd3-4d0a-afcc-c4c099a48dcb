-- Create default admin user (password: admin123)
-- Password hash for 'admin123' using bcrypt with salt rounds 12
INSERT INTO users (
    email, 
    password_hash, 
    full_name, 
    role, 
    department, 
    position, 
    is_active, 
    email_verified
) VALUES (
    '<EMAIL>',
    '$2b$12$LQv3c1yqBWVHxkd0LHAkCOYz6TtxMQJqhN8/LewdBPj/VcSAg/9S.',
    'System Administrator',
    'admin',
    'IT',
    'System Administrator',
    true,
    true
), (
    '<EMAIL>',
    '$2b$12$LQv3c1yqBWVHxkd0LHAkCOYz6TtxMQJqhN8/LewdBPj/VcSAg/9S.',
    'HR Manager',
    'hr_manager',
    'Human Resources',
    'HR Manager',
    true,
    true
), (
    '<EMAIL>',
    '$2b$12$LQv3c1yqBWVHxkd0LHAkCOYz6TtxMQJqhN8/LewdBPj/VcSAg/9S.',
    'Department Manager',
    'manager',
    'Operations',
    'Operations Manager',
    true,
    true
), (
    '<EMAIL>',
    '$2b$12$LQv3c1yqBWVHxkd0LHAkCOYz6TtxMQJqhN8/LewdBPj/VcSAg/9S.',
    'Staff Member',
    'staff',
    'Operations',
    'Staff Member',
    true,
    true
)
ON CONFLICT (email) DO NOTHING;

-- Insert some sample tasks
INSERT INTO tasks (title, description, assigned_by, status, priority) VALUES
('Setup Database', 'Initialize the database with proper schema', (SELECT id FROM users WHERE email = '<EMAIL>'), 'completed', 'high'),
('Create User Accounts', 'Set up initial user accounts for testing', (SELECT id FROM users WHERE email = '<EMAIL>'), 'completed', 'medium'),
('Configure Authentication', 'Implement role-based authentication system', (SELECT id FROM users WHERE email = '<EMAIL>'), 'in_progress', 'high'),
('Design Dashboard', 'Create responsive dashboard interface', (SELECT id FROM users WHERE email = '<EMAIL>'), 'todo', 'medium')
ON CONFLICT DO NOTHING;

-- Insert sample attendance records for today
INSERT INTO attendance (user_id, date, check_in_time, status, hours_worked) VALUES
((SELECT id FROM users WHERE email = '<EMAIL>'), CURRENT_DATE, '09:00:00', 'present', 8.0),
((SELECT id FROM users WHERE email = '<EMAIL>'), CURRENT_DATE, '09:15:00', 'late', 7.75),
((SELECT id FROM users WHERE email = '<EMAIL>'), CURRENT_DATE, '08:45:00', 'present', 8.25),
((SELECT id FROM users WHERE email = '<EMAIL>'), CURRENT_DATE, '09:00:00', 'present', 8.0)
ON CONFLICT (user_id, date) DO NOTHING;
