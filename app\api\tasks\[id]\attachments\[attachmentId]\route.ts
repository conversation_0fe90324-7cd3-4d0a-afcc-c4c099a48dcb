import { NextRequest, NextResponse } from "next/server"
import { AuthService } from "@/lib/auth-utils"
import { serverDb } from "@/lib/server-db"
import { unlink } from "fs/promises"
import { join } from "path"

// DELETE /api/tasks/[id]/attachments/[attachmentId] - Delete attachment
export async function DELETE(
  request: NextRequest, 
  { params }: { params: { id: string, attachmentId: string } }
) {
  try {
    const sessionToken = request.cookies.get("session-token")?.value

    if (!sessionToken) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 })
    }

    const user = await AuthService.verifySession(sessionToken)

    if (!user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 })
    }

    const { id: taskId, attachmentId } = params

    // Get attachment with task info
    const attachmentResult = await serverDb.sql`
      SELECT 
        a.*,
        t.assigned_to as task_assigned_to,
        t.assigned_by as task_assigned_by
      FROM task_attachments a
      LEFT JOIN tasks t ON a.task_id = t.id
      WHERE a.id = ${attachmentId} AND a.task_id = ${taskId}
    `

    if (attachmentResult.length === 0) {
      return NextResponse.json({ error: "Attachment not found" }, { status: 404 })
    }

    const attachment = attachmentResult[0]

    // Check if user is assigned to this task via task_assignments
    const isAssigned = await serverDb.sql`
      SELECT EXISTS (
        SELECT 1 FROM task_assignments 
        WHERE task_id = ${taskId} AND user_id = ${user.id}
      )
    `

    // Check permissions - only admin, hr_manager, uploader, task assignee, or task creator can delete
    const canDelete =
      ["admin", "hr_manager"].includes(user.role) ||
      attachment.user_id === user.id ||
      attachment.task_assigned_to === user.id ||
      attachment.task_assigned_by === user.id ||
      isAssigned[0].exists

    if (!canDelete) {
      return NextResponse.json({ 
        error: "You don't have permission to delete this attachment" 
      }, { status: 403 })
    }

    // Delete file from filesystem
    try {
      const filePath = join(process.cwd(), 'public', attachment.file_path)
      await unlink(filePath)
    } catch (fileError) {
      console.warn("Could not delete file from filesystem:", fileError)
      // Continue with database deletion even if file deletion fails
    }

    // Delete from database
    await serverDb.sql`
      DELETE FROM task_attachments WHERE id = ${attachmentId}
    `

    return NextResponse.json({
      success: true,
      message: "Attachment deleted successfully"
    })

  } catch (error) {
    console.error("Delete attachment error:", error)
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    )
  }
}

// GET /api/tasks/[id]/attachments/[attachmentId] - Get specific attachment
export async function GET(
  request: NextRequest, 
  { params }: { params: { id: string, attachmentId: string } }
) {
  try {
    const sessionToken = request.cookies.get("session-token")?.value

    if (!sessionToken) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 })
    }

    const user = await AuthService.verifySession(sessionToken)

    if (!user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 })
    }

    const { id: taskId, attachmentId } = params

    // Get attachment with task and user info
    const attachmentResult = await serverDb.sql`
      SELECT
        a.*,
        u.full_name as uploaded_by_name,
        u.email as uploaded_by_email,
        t.assigned_to as task_assigned_to,
        t.assigned_by as task_assigned_by
      FROM task_attachments a
      LEFT JOIN users u ON a.user_id = u.id
      LEFT JOIN tasks t ON a.task_id = t.id
      WHERE a.id = ${attachmentId} AND a.task_id = ${taskId}
    `

    if (attachmentResult.length === 0) {
      return NextResponse.json({ error: "Attachment not found" }, { status: 404 })
    }

    const attachment = attachmentResult[0]

    // Check if user is assigned to this task via task_assignments
    const isAssigned = await serverDb.sql`
      SELECT EXISTS (
        SELECT 1 FROM task_assignments 
        WHERE task_id = ${taskId} AND user_id = ${user.id}
      )
    `

    // Check access permissions
    const hasAccess = 
      ["admin", "hr_manager"].includes(user.role) ||
      attachment.task_assigned_to === user.id ||
      attachment.task_assigned_by === user.id ||
      isAssigned[0].exists

    if (!hasAccess) {
      return NextResponse.json({ error: "Access denied" }, { status: 403 })
    }

    return NextResponse.json({
      success: true,
      data: attachment
    })

  } catch (error) {
    console.error("Get attachment error:", error)
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    )
  }
}
