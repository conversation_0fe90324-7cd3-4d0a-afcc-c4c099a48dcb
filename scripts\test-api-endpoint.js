// Test the loan recovery API endpoint directly
const fetch = require('node-fetch');

async function testAPIEndpoint() {
  try {
    console.log('🔍 Testing loan recovery API endpoint...\n');

    // Test the API endpoint
    const response = await fetch('http://localhost:3002/api/loan-recovery/loans?include_complete=false', {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
      },
    });

    console.log('Response status:', response.status);
    console.log('Response headers:', Object.fromEntries(response.headers.entries()));

    const data = await response.text();
    console.log('Response body:', data);

    if (response.ok) {
      const jsonData = JSON.parse(data);
      console.log('\n✅ API endpoint is working!');
      console.log('Loans data:', jsonData);
    } else {
      console.log('\n❌ API endpoint returned error');
    }

  } catch (error) {
    console.error('❌ Test failed:', error);
  }
}

testAPIEndpoint();
