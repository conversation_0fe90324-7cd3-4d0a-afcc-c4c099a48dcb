// API Endpoints Tests
// Phase 5: Testing and Documentation - API Testing

import { describe, it, expect, beforeEach, afterEach, jest } from '@jest/globals'
import { NextRequest } from 'next/server'

// Mock the database
jest.mock('@/lib/neon', () => ({
  db: {
    sql: jest.fn(),
    getUserById: jest.fn(),
    getAttendanceForPayrollPeriod: jest.fn(),
    createPayrollRecord: jest.fn(),
    updatePayrollRecord: jest.fn(),
    getPayrollHistory: jest.fn(),
    getPayrollSettings: jest.fn(),
    updatePayrollSettings: jest.fn()
  }
}))

// Import API handlers
import { POST as calculatePayrollPOST } from '@/app/api/payroll/calculate/route'
import { GET as getPayrollHistoryGET } from '@/app/api/payroll/history/route'
import { GET as getPayrollSettingsGET, PUT as updatePayrollSettingsPUT } from '@/app/api/payroll/settings/route'

describe('Payroll API Endpoints', () => {
  beforeEach(() => {
    jest.clearAllMocks()
  })

  describe('POST /api/payroll/calculate', () => {
    it('should calculate payroll for valid employee data', async () => {
      const mockEmployee = {
        id: 'EMP001',
        payStructure: 'monthly',
        baseSalary: 65000,
        allowances: [],
        deductions: []
      }

      const mockAttendance = {
        workingDays: 26,
        attendedDays: 26,
        overtimeHours: 0,
        lateHours: 0,
        earlyLeaveHours: 0
      }

      const requestBody = {
        employeeId: 'EMP001',
        periodStart: '2024-11-01',
        periodEnd: '2024-11-30',
        attendanceData: mockAttendance
      }

      const request = new NextRequest('http://localhost:3000/api/payroll/calculate', {
        method: 'POST',
        body: JSON.stringify(requestBody),
        headers: {
          'Content-Type': 'application/json'
        }
      })

      // Mock database responses
      const { db } = require('@/lib/neon')
      db.getUserById.mockResolvedValue(mockEmployee)
      db.getAttendanceForPayrollPeriod.mockResolvedValue([mockAttendance])

      const response = await calculatePayrollPOST(request)
      const data = await response.json()

      expect(response.status).toBe(200)
      expect(data.success).toBe(true)
      expect(data.payroll).toBeDefined()
      expect(data.payroll.basePay).toBe(65000)
      expect(data.payroll.grossPay).toBeGreaterThanOrEqual(65000)
      expect(data.payroll.netPay).toBeLessThan(data.payroll.grossPay)
    })

    it('should return 400 for missing employee ID', async () => {
      const requestBody = {
        periodStart: '2024-11-01',
        periodEnd: '2024-11-30'
      }

      const request = new NextRequest('http://localhost:3000/api/payroll/calculate', {
        method: 'POST',
        body: JSON.stringify(requestBody),
        headers: {
          'Content-Type': 'application/json'
        }
      })

      const response = await calculatePayrollPOST(request)
      const data = await response.json()

      expect(response.status).toBe(400)
      expect(data.success).toBe(false)
      expect(data.error).toContain('Employee ID is required')
    })

    it('should return 404 for non-existent employee', async () => {
      const requestBody = {
        employeeId: 'INVALID',
        periodStart: '2024-11-01',
        periodEnd: '2024-11-30'
      }

      const request = new NextRequest('http://localhost:3000/api/payroll/calculate', {
        method: 'POST',
        body: JSON.stringify(requestBody),
        headers: {
          'Content-Type': 'application/json'
        }
      })

      const { db } = require('@/lib/neon')
      db.getUserById.mockResolvedValue(null)

      const response = await calculatePayrollPOST(request)
      const data = await response.json()

      expect(response.status).toBe(404)
      expect(data.success).toBe(false)
      expect(data.error).toContain('Employee not found')
    })

    it('should return 400 for invalid date range', async () => {
      const requestBody = {
        employeeId: 'EMP001',
        periodStart: '2024-11-30',
        periodEnd: '2024-11-01' // End before start
      }

      const request = new NextRequest('http://localhost:3000/api/payroll/calculate', {
        method: 'POST',
        body: JSON.stringify(requestBody),
        headers: {
          'Content-Type': 'application/json'
        }
      })

      const response = await calculatePayrollPOST(request)
      const data = await response.json()

      expect(response.status).toBe(400)
      expect(data.success).toBe(false)
      expect(data.error).toContain('Invalid date range')
    })

    it('should handle overtime calculations correctly', async () => {
      const mockEmployee = {
        id: 'EMP001',
        payStructure: 'hourly',
        hourlyRate: 500,
        allowances: [],
        deductions: []
      }

      const mockAttendance = {
        workingDays: 26,
        attendedDays: 26,
        totalHours: 218, // 208 regular + 10 overtime
        overtimeHours: 10,
        lateHours: 0,
        earlyLeaveHours: 0
      }

      const requestBody = {
        employeeId: 'EMP001',
        periodStart: '2024-11-01',
        periodEnd: '2024-11-30',
        attendanceData: mockAttendance
      }

      const request = new NextRequest('http://localhost:3000/api/payroll/calculate', {
        method: 'POST',
        body: JSON.stringify(requestBody),
        headers: {
          'Content-Type': 'application/json'
        }
      })

      const { db } = require('@/lib/neon')
      db.getUserById.mockResolvedValue(mockEmployee)
      db.getAttendanceForPayrollPeriod.mockResolvedValue([mockAttendance])

      const response = await calculatePayrollPOST(request)
      const data = await response.json()

      expect(response.status).toBe(200)
      expect(data.payroll.overtimePay).toBeGreaterThan(0)
      expect(data.payroll.grossPay).toBeGreaterThan(data.payroll.basePay)
    })

    it('should apply allowances and deductions correctly', async () => {
      const mockEmployee = {
        id: 'EMP001',
        payStructure: 'monthly',
        baseSalary: 65000,
        allowances: [
          { id: 'transport', amount: 5000, type: 'fixed' }
        ],
        deductions: [
          { id: 'pf', amount: 10, type: 'percentage' }
        ]
      }

      const mockAttendance = {
        workingDays: 26,
        attendedDays: 26,
        overtimeHours: 0,
        lateHours: 0,
        earlyLeaveHours: 0
      }

      const requestBody = {
        employeeId: 'EMP001',
        periodStart: '2024-11-01',
        periodEnd: '2024-11-30',
        attendanceData: mockAttendance
      }

      const request = new NextRequest('http://localhost:3000/api/payroll/calculate', {
        method: 'POST',
        body: JSON.stringify(requestBody),
        headers: {
          'Content-Type': 'application/json'
        }
      })

      const { db } = require('@/lib/neon')
      db.getUserById.mockResolvedValue(mockEmployee)
      db.getAttendanceForPayrollPeriod.mockResolvedValue([mockAttendance])

      const response = await calculatePayrollPOST(request)
      const data = await response.json()

      expect(response.status).toBe(200)
      expect(data.payroll.allowances).toBe(5000)
      expect(data.payroll.deductions).toBe(6500) // 10% of 65000
      expect(data.payroll.grossPay).toBe(70000) // 65000 + 5000
      expect(data.payroll.netPay).toBe(63500) // 70000 - 6500
    })
  })

  describe('GET /api/payroll/history', () => {
    it('should return payroll history for valid employee', async () => {
      const mockHistory = [
        {
          id: 'PAY001',
          employeeId: 'EMP001',
          payPeriod: 'November 2024',
          grossPay: 70000,
          netPay: 63500,
          status: 'paid'
        },
        {
          id: 'PAY002',
          employeeId: 'EMP001',
          payPeriod: 'October 2024',
          grossPay: 68000,
          netPay: 61500,
          status: 'paid'
        }
      ]

      const request = new NextRequest('http://localhost:3000/api/payroll/history?employeeId=EMP001')

      const { db } = require('@/lib/neon')
      db.getPayrollHistory.mockResolvedValue(mockHistory)

      const response = await getPayrollHistoryGET(request)
      const data = await response.json()

      expect(response.status).toBe(200)
      expect(data.success).toBe(true)
      expect(data.history).toHaveLength(2)
      expect(data.history[0].id).toBe('PAY001')
    })

    it('should return 400 for missing employee ID', async () => {
      const request = new NextRequest('http://localhost:3000/api/payroll/history')

      const response = await getPayrollHistoryGET(request)
      const data = await response.json()

      expect(response.status).toBe(400)
      expect(data.success).toBe(false)
      expect(data.error).toContain('Employee ID is required')
    })

    it('should handle pagination correctly', async () => {
      const mockHistory = Array.from({ length: 15 }, (_, i) => ({
        id: `PAY${i.toString().padStart(3, '0')}`,
        employeeId: 'EMP001',
        payPeriod: `Period ${i}`,
        grossPay: 70000,
        netPay: 63500,
        status: 'paid'
      }))

      const request = new NextRequest('http://localhost:3000/api/payroll/history?employeeId=EMP001&page=1&limit=10')

      const { db } = require('@/lib/neon')
      db.getPayrollHistory.mockResolvedValue(mockHistory.slice(0, 10))

      const response = await getPayrollHistoryGET(request)
      const data = await response.json()

      expect(response.status).toBe(200)
      expect(data.history).toHaveLength(10)
    })

    it('should filter by date range', async () => {
      const mockHistory = [
        {
          id: 'PAY001',
          employeeId: 'EMP001',
          payPeriod: 'November 2024',
          periodStart: '2024-11-01',
          periodEnd: '2024-11-30',
          grossPay: 70000,
          netPay: 63500,
          status: 'paid'
        }
      ]

      const request = new NextRequest('http://localhost:3000/api/payroll/history?employeeId=EMP001&startDate=2024-11-01&endDate=2024-11-30')

      const { db } = require('@/lib/neon')
      db.getPayrollHistory.mockResolvedValue(mockHistory)

      const response = await getPayrollHistoryGET(request)
      const data = await response.json()

      expect(response.status).toBe(200)
      expect(data.history).toHaveLength(1)
    })
  })

  describe('GET /api/payroll/settings', () => {
    it('should return payroll settings', async () => {
      const mockSettings = {
        currency: 'NPR',
        fiscalYear: '2081-82',
        payrollFrequency: 'monthly',
        overtimeRates: {
          first4Hours: 1.5,
          next4Hours: 2.0,
          beyond8Hours: 2.5
        },
        minimumWage: 17300,
        taxRates: {
          incomeTax: 10,
          socialSecurity: 10
        }
      }

      const request = new NextRequest('http://localhost:3000/api/payroll/settings')

      const { db } = require('@/lib/neon')
      db.getPayrollSettings.mockResolvedValue(mockSettings)

      const response = await getPayrollSettingsGET(request)
      const data = await response.json()

      expect(response.status).toBe(200)
      expect(data.success).toBe(true)
      expect(data.settings.currency).toBe('NPR')
      expect(data.settings.fiscalYear).toBe('2081-82')
    })

    it('should handle missing settings gracefully', async () => {
      const request = new NextRequest('http://localhost:3000/api/payroll/settings')

      const { db } = require('@/lib/neon')
      db.getPayrollSettings.mockResolvedValue(null)

      const response = await getPayrollSettingsGET(request)
      const data = await response.json()

      expect(response.status).toBe(200)
      expect(data.success).toBe(true)
      expect(data.settings).toBeDefined() // Should return default settings
    })
  })

  describe('PUT /api/payroll/settings', () => {
    it('should update payroll settings', async () => {
      const updatedSettings = {
        currency: 'NPR',
        fiscalYear: '2082-83',
        payrollFrequency: 'monthly',
        overtimeRates: {
          first4Hours: 1.5,
          next4Hours: 2.0,
          beyond8Hours: 2.5
        },
        minimumWage: 18000
      }

      const request = new NextRequest('http://localhost:3000/api/payroll/settings', {
        method: 'PUT',
        body: JSON.stringify(updatedSettings),
        headers: {
          'Content-Type': 'application/json'
        }
      })

      const { db } = require('@/lib/neon')
      db.updatePayrollSettings.mockResolvedValue(updatedSettings)

      const response = await updatePayrollSettingsPUT(request)
      const data = await response.json()

      expect(response.status).toBe(200)
      expect(data.success).toBe(true)
      expect(data.settings.fiscalYear).toBe('2082-83')
      expect(data.settings.minimumWage).toBe(18000)
    })

    it('should validate settings before updating', async () => {
      const invalidSettings = {
        currency: 'INVALID',
        minimumWage: -1000 // Invalid negative wage
      }

      const request = new NextRequest('http://localhost:3000/api/payroll/settings', {
        method: 'PUT',
        body: JSON.stringify(invalidSettings),
        headers: {
          'Content-Type': 'application/json'
        }
      })

      const response = await updatePayrollSettingsPUT(request)
      const data = await response.json()

      expect(response.status).toBe(400)
      expect(data.success).toBe(false)
      expect(data.error).toContain('Invalid')
    })
  })

  describe('Error Handling', () => {
    it('should handle database connection errors', async () => {
      const request = new NextRequest('http://localhost:3000/api/payroll/calculate', {
        method: 'POST',
        body: JSON.stringify({
          employeeId: 'EMP001',
          periodStart: '2024-11-01',
          periodEnd: '2024-11-30'
        }),
        headers: {
          'Content-Type': 'application/json'
        }
      })

      const { db } = require('@/lib/neon')
      db.getUserById.mockRejectedValue(new Error('Database connection failed'))

      const response = await calculatePayrollPOST(request)
      const data = await response.json()

      expect(response.status).toBe(500)
      expect(data.success).toBe(false)
      expect(data.error).toContain('Internal server error')
    })

    it('should handle malformed JSON requests', async () => {
      const request = new NextRequest('http://localhost:3000/api/payroll/calculate', {
        method: 'POST',
        body: 'invalid json',
        headers: {
          'Content-Type': 'application/json'
        }
      })

      const response = await calculatePayrollPOST(request)
      const data = await response.json()

      expect(response.status).toBe(400)
      expect(data.success).toBe(false)
      expect(data.error).toContain('Invalid JSON')
    })

    it('should handle missing request body', async () => {
      const request = new NextRequest('http://localhost:3000/api/payroll/calculate', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        }
      })

      const response = await calculatePayrollPOST(request)
      const data = await response.json()

      expect(response.status).toBe(400)
      expect(data.success).toBe(false)
      expect(data.error).toContain('Request body is required')
    })
  })

  describe('Performance Tests', () => {
    it('should handle bulk payroll calculations efficiently', async () => {
      const employees = Array.from({ length: 100 }, (_, i) => ({
        id: `EMP${i.toString().padStart(3, '0')}`,
        payStructure: 'monthly',
        baseSalary: 65000 + (i * 1000),
        allowances: [],
        deductions: []
      }))

      const startTime = Date.now()

      const promises = employees.map(employee => {
        const request = new NextRequest('http://localhost:3000/api/payroll/calculate', {
          method: 'POST',
          body: JSON.stringify({
            employeeId: employee.id,
            periodStart: '2024-11-01',
            periodEnd: '2024-11-30'
          }),
          headers: {
            'Content-Type': 'application/json'
          }
        })

        const { db } = require('@/lib/neon')
        db.getUserById.mockResolvedValue(employee)
        db.getAttendanceForPayrollPeriod.mockResolvedValue([{
          workingDays: 26,
          attendedDays: 26,
          overtimeHours: 0
        }])

        return calculatePayrollPOST(request)
      })

      const responses = await Promise.all(promises)
      const endTime = Date.now()
      const duration = endTime - startTime

      expect(responses).toHaveLength(100)
      expect(responses.every(r => r.status === 200)).toBe(true)
      expect(duration).toBeLessThan(10000) // Should complete within 10 seconds
    })

    it('should handle concurrent requests without conflicts', async () => {
      const request = new NextRequest('http://localhost:3000/api/payroll/calculate', {
        method: 'POST',
        body: JSON.stringify({
          employeeId: 'EMP001',
          periodStart: '2024-11-01',
          periodEnd: '2024-11-30'
        }),
        headers: {
          'Content-Type': 'application/json'
        }
      })

      const { db } = require('@/lib/neon')
      db.getUserById.mockResolvedValue({
        id: 'EMP001',
        payStructure: 'monthly',
        baseSalary: 65000,
        allowances: [],
        deductions: []
      })
      db.getAttendanceForPayrollPeriod.mockResolvedValue([{
        workingDays: 26,
        attendedDays: 26,
        overtimeHours: 0
      }])

      // Simulate 10 concurrent requests
      const promises = Array.from({ length: 10 }, () => calculatePayrollPOST(request))
      const responses = await Promise.all(promises)

      expect(responses).toHaveLength(10)
      expect(responses.every(r => r.status === 200)).toBe(true)
    })
  })
})
