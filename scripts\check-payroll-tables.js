// Check if the payroll-related tables exist in the database
const { neon } = require('@neondatabase/serverless');
require('dotenv').config({ path: '.env.local' });

async function checkPayrollTables() {
  try {
    console.log('Checking payroll-related tables...\n');
    console.log('Database URL:', process.env.DATABASE_URL ? 'Found' : 'Missing');

    const sql = neon(process.env.DATABASE_URL);

    // Check if the required tables exist
    const tables = [
      'employee_bank_accounts',
      'allowance_assignments', 
      'deduction_approvals',
      'payroll_components_master'
    ];

    for (const table of tables) {
      try {
        const result = await sql`
          SELECT EXISTS (
            SELECT FROM information_schema.tables 
            WHERE table_schema = 'public' 
            AND table_name = ${table}
          );
        `;
        
        const exists = result[0].exists;
        console.log(`Table ${table}: ${exists ? '✅ EXISTS' : '❌ MISSING'}`);
        
        if (exists) {
          // Check table structure
          const columns = await sql`
            SELECT column_name, data_type, is_nullable
            FROM information_schema.columns 
            WHERE table_schema = 'public' 
            AND table_name = ${table}
            ORDER BY ordinal_position;
          `;
          
          console.log(`  Columns (${columns.length}):`);
          columns.forEach(col => {
            console.log(`    - ${col.column_name}: ${col.data_type} ${col.is_nullable === 'YES' ? '(nullable)' : '(not null)'}`);
          });
          
          // Check if there's any data
          const count = await sql`SELECT COUNT(*) as count FROM ${sql(table)}`;
          console.log(`  Records: ${count[0].count}\n`);
        } else {
          console.log('');
        }
      } catch (error) {
        console.log(`❌ Error checking table ${table}:`, error.message);
      }
    }

    // Also check the users table structure to see what payroll fields are available
    console.log('Checking users table payroll-related columns...');
    try {
      const userColumns = await sql`
        SELECT column_name, data_type, is_nullable
        FROM information_schema.columns 
        WHERE table_schema = 'public' 
        AND table_name = 'users'
        AND column_name IN ('salary', 'employee_type', 'employee_category', 'employment_status', 'pay_grade', 'joining_bonus')
        ORDER BY ordinal_position;
      `;
      
      console.log(`Users table payroll columns (${userColumns.length}):`);
      userColumns.forEach(col => {
        console.log(`  - ${col.column_name}: ${col.data_type} ${col.is_nullable === 'YES' ? '(nullable)' : '(not null)'}`);
      });

      // Check if there are any users with salary data
      const usersWithSalary = await sql`
        SELECT id, full_name, salary, employee_type, department, position
        FROM users 
        WHERE salary IS NOT NULL 
        LIMIT 5;
      `;
      
      console.log(`\nUsers with salary data (${usersWithSalary.length}):`);
      usersWithSalary.forEach(user => {
        console.log(`  - ${user.full_name}: ${user.salary} NPR (${user.position || 'No position'} in ${user.department || 'No dept'})`);
      });

    } catch (error) {
      console.log('❌ Error checking users table:', error.message);
    }

  } catch (error) {
    console.error('❌ Database connection error:', error.message);
  }
}

checkPayrollTables();
