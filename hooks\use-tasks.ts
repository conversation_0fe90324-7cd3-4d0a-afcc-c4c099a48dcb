import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query"
import { toast } from "@/hooks/use-toast"

// Types
interface TaskAssignment {
  id: string
  full_name: string
  email: string
  employee_id?: string
  department?: string
  position?: string
  is_primary: boolean
  assigned_at?: string
  assigned_by_name?: string
}

interface SubTask {
  id: string
  parent_task_id: string
  title: string
  description?: string
  status: "todo" | "in_progress" | "completed" | "cancelled"
  assigned_to?: string
  assigned_by: string
  due_date?: string
  position: number
  completed_at?: string
  created_at: string
  updated_at: string
  // Extended fields from joins
  assigned_to_name?: string
  assigned_to_email?: string
  assigned_to_employee_id?: string
  assigned_by_name?: string
}

interface Task {
  id: string
  title: string
  description?: string
  status: "todo" | "in_progress" | "completed" | "cancelled"
  priority: "low" | "medium" | "high" | "urgent"
  assigned_to?: string
  assigned_by: string
  due_date?: string
  project_id?: string
  estimated_hours?: number
  actual_hours?: number
  position?: number
  completion_percentage?: number
  tags?: string[]
  created_at: string
  updated_at: string
  // Extended fields from joins
  assigned_to_name?: string
  assigned_to_email?: string
  created_by_name?: string
  created_by_email?: string
  project_name?: string
  project_color?: string
  comment_count?: number
  attachment_count?: number
  subtask_count?: number
  completed_subtasks?: number
  total_time_minutes?: number
  // Multi-user assignment fields
  assignments?: TaskAssignment[]
  assigned_users?: TaskAssignment[]
  subtask_stats?: {
    total_subtasks: number
    completed_subtasks: number
  }
}

interface TaskFilters {
  status?: string
  priority?: string
  assigned_to?: string
  project_id?: string
  search?: string
  page?: number
  limit?: number
}

interface CreateTaskData {
  title: string
  description?: string
  assigned_to?: string // Legacy single assignment
  assigned_users?: string[] // New multi-user assignment
  primary_assignee?: string // Primary assignee for multi-user tasks
  priority?: "low" | "medium" | "high" | "urgent"
  due_date?: string
  project_id?: string
  estimated_hours?: number
  tags?: string[]
}

interface UpdateTaskData extends Partial<CreateTaskData> {
  status?: "todo" | "in_progress" | "completed" | "cancelled"
  completion_percentage?: number
  actual_hours?: number
}

// API functions
const taskApi = {
  async getTasks(filters: TaskFilters = {}) {
    const params = new URLSearchParams()
    Object.entries(filters).forEach(([key, value]) => {
      if (value !== undefined && value !== null && value !== "") {
        params.append(key, value.toString())
      }
    })

    const response = await fetch(`/api/tasks?${params.toString()}`, {
      credentials: "include",
    })

    if (!response.ok) {
      throw new Error("Failed to fetch tasks")
    }

    return response.json()
  },

  async getTask(id: string) {
    const response = await fetch(`/api/tasks/${id}`, {
      credentials: "include",
    })

    if (!response.ok) {
      throw new Error("Failed to fetch task")
    }

    return response.json()
  },

  async createTask(data: CreateTaskData) {
    const response = await fetch("/api/tasks", {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      credentials: "include",
      body: JSON.stringify(data),
    })

    if (!response.ok) {
      const error = await response.json()
      throw new Error(error.error || "Failed to create task")
    }

    return response.json()
  },

  async updateTask(id: string, data: UpdateTaskData) {
    const response = await fetch(`/api/tasks/${id}`, {
      method: "PUT",
      headers: {
        "Content-Type": "application/json",
      },
      credentials: "include",
      body: JSON.stringify(data),
    })

    if (!response.ok) {
      const error = await response.json()
      throw new Error(error.error || "Failed to update task")
    }

    return response.json()
  },

  async updateTaskStatus(id: string, status: string, position?: number) {
    const response = await fetch(`/api/tasks/${id}/status`, {
      method: "PATCH",
      headers: {
        "Content-Type": "application/json",
      },
      credentials: "include",
      body: JSON.stringify({ status, position }),
    })

    if (!response.ok) {
      const error = await response.json()
      throw new Error(error.error || "Failed to update task status")
    }

    return response.json()
  },

  async deleteTask(id: string) {
    const response = await fetch(`/api/tasks/${id}`, {
      method: "DELETE",
      credentials: "include",
    })

    if (!response.ok) {
      const error = await response.json()
      throw new Error(error.error || "Failed to delete task")
    }

    return response.json()
  },

  // Task Assignment API functions
  async getTaskAssignments(taskId: string) {
    const response = await fetch(`/api/tasks/${taskId}/assignments`, {
      method: "GET",
      credentials: "include",
    })

    if (!response.ok) {
      const error = await response.json()
      throw new Error(error.error || "Failed to get task assignments")
    }

    return response.json()
  },

  async updateTaskAssignments(taskId: string, data: { user_ids: string[], is_primary?: string }) {
    const response = await fetch(`/api/tasks/${taskId}/assignments`, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      credentials: "include",
      body: JSON.stringify(data),
    })

    if (!response.ok) {
      const error = await response.json()
      throw new Error(error.error || "Failed to update task assignments")
    }

    return response.json()
  },

  async removeTaskAssignment(taskId: string, userId: string) {
    const response = await fetch(`/api/tasks/${taskId}/assignments?user_id=${userId}`, {
      method: "DELETE",
      credentials: "include",
    })

    if (!response.ok) {
      const error = await response.json()
      throw new Error(error.error || "Failed to remove task assignment")
    }

    return response.json()
  },

  // Sub-task API functions
  async getSubTasks(taskId: string) {
    const response = await fetch(`/api/tasks/${taskId}/subtasks`, {
      method: "GET",
      credentials: "include",
    })

    if (!response.ok) {
      const error = await response.json()
      throw new Error(error.error || "Failed to get sub-tasks")
    }

    return response.json()
  },

  async createSubTask(taskId: string, data: {
    title: string
    description?: string
    assigned_to?: string
    due_date?: string
    position?: number
  }) {
    const response = await fetch(`/api/tasks/${taskId}/subtasks`, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      credentials: "include",
      body: JSON.stringify(data),
    })

    if (!response.ok) {
      const error = await response.json()
      throw new Error(error.error || "Failed to create sub-task")
    }

    return response.json()
  },

  async updateSubTask(taskId: string, subtaskId: string, data: {
    title?: string
    description?: string
    assigned_to?: string
    status?: "todo" | "in_progress" | "completed" | "cancelled"
    due_date?: string
    position?: number
  }) {
    const response = await fetch(`/api/tasks/${taskId}/subtasks/${subtaskId}`, {
      method: "PUT",
      headers: {
        "Content-Type": "application/json",
      },
      credentials: "include",
      body: JSON.stringify(data),
    })

    if (!response.ok) {
      const error = await response.json()
      throw new Error(error.error || "Failed to update sub-task")
    }

    return response.json()
  },

  async deleteSubTask(taskId: string, subtaskId: string) {
    const response = await fetch(`/api/tasks/${taskId}/subtasks/${subtaskId}`, {
      method: "DELETE",
      credentials: "include",
    })

    if (!response.ok) {
      const error = await response.json()
      throw new Error(error.error || "Failed to delete sub-task")
    }

    return response.json()
  },

  // Task Attachment API functions
  async getTaskAttachments(taskId: string) {
    const response = await fetch(`/api/tasks/${taskId}/attachments`, {
      method: "GET",
      credentials: "include",
    })

    if (!response.ok) {
      const error = await response.json()
      throw new Error(error.error || "Failed to get task attachments")
    }

    return response.json()
  },

  async uploadTaskAttachment(taskId: string, file: File) {
    const formData = new FormData()
    formData.append("file", file)

    const response = await fetch(`/api/tasks/${taskId}/attachments`, {
      method: "POST",
      credentials: "include",
      body: formData,
    })

    if (!response.ok) {
      const error = await response.json()
      throw new Error(error.error || "Failed to upload attachment")
    }

    return response.json()
  },

  async deleteTaskAttachment(taskId: string, attachmentId: string) {
    const response = await fetch(`/api/tasks/${taskId}/attachments/${attachmentId}`, {
      method: "DELETE",
      credentials: "include",
    })

    if (!response.ok) {
      const error = await response.json()
      throw new Error(error.error || "Failed to delete attachment")
    }

    return response.json()
  },
}

// React Query hooks
export function useTasks(filters: TaskFilters = {}, options: { realTime?: boolean } = {}) {
  return useQuery({
    queryKey: ["tasks", filters],
    queryFn: () => taskApi.getTasks(filters),
    staleTime: options.realTime ? 0 : 30 * 1000, // No stale time for real-time
    refetchInterval: options.realTime ? 5 * 1000 : false, // Poll every 5 seconds for real-time
  })
}

export function useTask(id: string) {
  return useQuery({
    queryKey: ["task", id],
    queryFn: () => taskApi.getTask(id),
    enabled: !!id,
  })
}

export function useTaskAssignments(taskId: string) {
  return useQuery({
    queryKey: ["task-assignments", taskId],
    queryFn: () => taskApi.getTaskAssignments(taskId),
    enabled: !!taskId,
    staleTime: 30000,
  })
}

export function useSubTasks(taskId: string) {
  return useQuery({
    queryKey: ["sub-tasks", taskId],
    queryFn: () => taskApi.getSubTasks(taskId),
    enabled: !!taskId,
    staleTime: 30000,
  })
}

export function useCreateTask() {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: taskApi.createTask,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["tasks"] })
      toast({
        title: "Success",
        description: "Task created successfully",
      })
    },
    onError: (error: Error) => {
      toast({
        title: "Error",
        description: error.message,
        variant: "destructive",
      })
    },
  })
}

export function useUpdateTask() {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: ({ id, data }: { id: string; data: UpdateTaskData }) =>
      taskApi.updateTask(id, data),
    onSuccess: (response, { id }) => {
      queryClient.invalidateQueries({ queryKey: ["tasks"] })
      queryClient.invalidateQueries({ queryKey: ["task", id] })
      toast({
        title: "Success",
        description: "Task updated successfully",
      })
    },
    onError: (error: Error) => {
      toast({
        title: "Error",
        description: error.message,
        variant: "destructive",
      })
    },
  })
}

export function useUpdateTaskStatus() {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: ({ id, status, position }: { id: string; status: string; position?: number }) =>
      taskApi.updateTaskStatus(id, status, position),
    onMutate: async ({ id, status }) => {
      // Cancel outgoing refetches
      await queryClient.cancelQueries({ queryKey: ["tasks"] })

      // Snapshot the previous value
      const previousTasks = queryClient.getQueryData(["tasks"])

      // Optimistically update the cache
      queryClient.setQueryData(["tasks"], (old: any) => {
        if (!old?.data?.tasks) return old

        return {
          ...old,
          data: {
            ...old.data,
            tasks: old.data.tasks.map((task: Task) =>
              task.id === id ? { ...task, status } : task
            ),
          },
        }
      })

      return { previousTasks }
    },
    onError: (error: Error, variables, context) => {
      // Rollback on error
      if (context?.previousTasks) {
        queryClient.setQueryData(["tasks"], context.previousTasks)
      }
      toast({
        title: "Error",
        description: error.message,
        variant: "destructive",
      })
    },
    onSettled: () => {
      queryClient.invalidateQueries({ queryKey: ["tasks"] })
    },
  })
}

export function useDeleteTask() {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: taskApi.deleteTask,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["tasks"] })
      toast({
        title: "Success",
        description: "Task deleted successfully",
      })
    },
    onError: (error: Error) => {
      toast({
        title: "Error",
        description: error.message,
        variant: "destructive",
      })
    },
  })
}

// Task Assignment Mutation Hooks
export function useUpdateTaskAssignments() {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: ({ taskId, data }: { taskId: string; data: { user_ids: string[], is_primary?: string } }) =>
      taskApi.updateTaskAssignments(taskId, data),
    onSuccess: (response, { taskId }) => {
      queryClient.invalidateQueries({ queryKey: ["tasks"] })
      queryClient.invalidateQueries({ queryKey: ["task", taskId] })
      queryClient.invalidateQueries({ queryKey: ["task-assignments", taskId] })
      toast({
        title: "Success",
        description: "Task assignments updated successfully",
      })
    },
    onError: (error: Error) => {
      toast({
        title: "Error",
        description: error.message,
        variant: "destructive",
      })
    },
  })
}

export function useRemoveTaskAssignment() {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: ({ taskId, userId }: { taskId: string; userId: string }) =>
      taskApi.removeTaskAssignment(taskId, userId),
    onSuccess: (response, { taskId }) => {
      queryClient.invalidateQueries({ queryKey: ["tasks"] })
      queryClient.invalidateQueries({ queryKey: ["task", taskId] })
      queryClient.invalidateQueries({ queryKey: ["task-assignments", taskId] })
      toast({
        title: "Success",
        description: "User removed from task successfully",
      })
    },
    onError: (error: Error) => {
      toast({
        title: "Error",
        description: error.message,
        variant: "destructive",
      })
    },
  })
}

// Sub-task Mutation Hooks
export function useCreateSubTask() {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: ({ taskId, data }: {
      taskId: string;
      data: {
        title: string
        description?: string
        assigned_to?: string
        due_date?: string
        position?: number
      }
    }) => taskApi.createSubTask(taskId, data),
    onSuccess: (response, { taskId }) => {
      queryClient.invalidateQueries({ queryKey: ["tasks"] })
      queryClient.invalidateQueries({ queryKey: ["task", taskId] })
      queryClient.invalidateQueries({ queryKey: ["sub-tasks", taskId] })
      toast({
        title: "Success",
        description: "Sub-task created successfully",
      })
    },
    onError: (error: Error) => {
      toast({
        title: "Error",
        description: error.message,
        variant: "destructive",
      })
    },
  })
}

export function useUpdateSubTask() {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: ({ taskId, subtaskId, data }: {
      taskId: string;
      subtaskId: string;
      data: {
        title?: string
        description?: string
        assigned_to?: string
        status?: "todo" | "in_progress" | "completed" | "cancelled"
        due_date?: string
        position?: number
      }
    }) => taskApi.updateSubTask(taskId, subtaskId, data),
    onSuccess: (response, { taskId }) => {
      queryClient.invalidateQueries({ queryKey: ["tasks"] })
      queryClient.invalidateQueries({ queryKey: ["task", taskId] })
      queryClient.invalidateQueries({ queryKey: ["sub-tasks", taskId] })
      toast({
        title: "Success",
        description: "Sub-task updated successfully",
      })
    },
    onError: (error: Error) => {
      toast({
        title: "Error",
        description: error.message,
        variant: "destructive",
      })
    },
  })
}

export function useDeleteSubTask() {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: ({ taskId, subtaskId }: { taskId: string; subtaskId: string }) =>
      taskApi.deleteSubTask(taskId, subtaskId),
    onSuccess: (response, { taskId }) => {
      queryClient.invalidateQueries({ queryKey: ["tasks"] })
      queryClient.invalidateQueries({ queryKey: ["task", taskId] })
      queryClient.invalidateQueries({ queryKey: ["sub-tasks", taskId] })
      toast({
        title: "Success",
        description: "Sub-task deleted successfully",
      })
    },
    onError: (error: Error) => {
      toast({
        title: "Error",
        description: error.message,
        variant: "destructive",
      })
    },
  })
}
