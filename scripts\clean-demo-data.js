#!/usr/bin/env node

/**
 * Clean Demo Data Script
 * 
 * This script removes all demo/sample data from the loan recovery system
 * while preserving the database schema and structure.
 */

const { neon } = require('@neondatabase/serverless');
require('dotenv').config({ path: '.env.local' });

const sql = neon(process.env.DATABASE_URL);

async function cleanDemoData() {
  try {
    console.log('🧹 Starting demo data cleanup...\n');

    // Step 1: Get count of existing data
    console.log('📊 Checking current data...');
    
    const customerCount = await sql`SELECT COUNT(*) as count FROM loan_recovery_customers`;
    const loanCount = await sql`SELECT COUNT(*) as count FROM loan_records`;
    const noteCount = await sql`SELECT COUNT(*) as count FROM loan_conversation_notes`;
    const reminderCount = await sql`SELECT COUNT(*) as count FROM loan_reminders`;
    
    console.log(`   📋 Current data counts:`);
    console.log(`   - Customers: ${customerCount[0].count}`);
    console.log(`   - Loans: ${loanCount[0].count}`);
    console.log(`   - Notes: ${noteCount[0].count}`);
    console.log(`   - Reminders: ${reminderCount[0].count}\n`);

    if (customerCount[0].count === '0') {
      console.log('✅ No data found to clean. Database is already clean.\n');
      return;
    }

    // Step 2: Identify demo data by common patterns
    console.log('🔍 Identifying demo data...');
    
    // Demo customers typically have these phone numbers or names
    const demoCustomers = await sql`
      SELECT id, name, phone FROM loan_recovery_customers 
      WHERE 
        phone IN ('9841234567', '9851234567', '9861234567', '9871234567', '9881234567', '9891234567')
        OR name LIKE '%राम बहादुर%'
        OR name LIKE '%सीता देवी%'
        OR name LIKE '%हरि यादव%'
        OR name LIKE '%गीता तामाङ%'
        OR name LIKE '%सुरेश गुरुङ%'
        OR name LIKE '%कमला खत्री%'
        OR email LIKE '%@email.com'
    `;

    console.log(`   🎯 Found ${demoCustomers.length} demo customers to remove\n`);

    if (demoCustomers.length === 0) {
      console.log('✅ No demo data found. All data appears to be real.\n');
      return;
    }

    // Step 3: Show what will be deleted
    console.log('📋 Demo customers to be removed:');
    demoCustomers.forEach((customer, index) => {
      console.log(`   ${index + 1}. ${customer.name} (${customer.phone})`);
    });
    console.log('');

    // Step 4: Delete demo data (cascading deletes will handle related records)
    console.log('🗑️ Removing demo data...');
    
    const customerIds = demoCustomers.map(c => c.id);
    
    if (customerIds.length > 0) {
      // Delete customers (this will cascade to loans, notes, reminders, etc.)
      const deleteResult = await sql`
        DELETE FROM loan_recovery_customers 
        WHERE id = ANY(${customerIds})
      `;
      
      console.log(`   ✅ Removed ${demoCustomers.length} demo customers and all related data\n`);
    }

    // Step 5: Verify cleanup
    console.log('✅ Verifying cleanup...');
    
    const finalCustomerCount = await sql`SELECT COUNT(*) as count FROM loan_recovery_customers`;
    const finalLoanCount = await sql`SELECT COUNT(*) as count FROM loan_records`;
    const finalNoteCount = await sql`SELECT COUNT(*) as count FROM loan_conversation_notes`;
    const finalReminderCount = await sql`SELECT COUNT(*) as count FROM loan_reminders`;
    
    console.log(`   📋 Final data counts:`);
    console.log(`   - Customers: ${finalCustomerCount[0].count}`);
    console.log(`   - Loans: ${finalLoanCount[0].count}`);
    console.log(`   - Notes: ${finalNoteCount[0].count}`);
    console.log(`   - Reminders: ${finalReminderCount[0].count}\n`);

    console.log('🎉 Demo data cleanup completed successfully!');
    console.log('💡 The loan recovery system now contains only real data.\n');

  } catch (error) {
    console.error('❌ Cleanup failed:', error);
    process.exit(1);
  }
}

// Run the cleanup if this script is executed directly
if (require.main === module) {
  cleanDemoData();
}

module.exports = { cleanDemoData };
