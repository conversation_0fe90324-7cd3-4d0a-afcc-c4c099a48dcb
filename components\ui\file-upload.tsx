"use client"

import React, { useState, useRef } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Badge } from "@/components/ui/badge"
import { Card, CardContent } from "@/components/ui/card"
import { Upload, X, File, Image, FileText, PenTool, Download, Trash2, Eye, ZoomIn } from "lucide-react"
import { Dialog, DialogContent, DialogHeader, DialogTitle } from "@/components/ui/dialog"

interface FileUploadProps {
  fileType: 'profile_picture' | 'document' | 'contract' | 'signature'
  label: string
  description?: string
  accept?: string
  maxSizeMB?: number
  onFileSelect?: (file: File | null) => void
  existingFiles?: UserFile[]
  onFileDelete?: (fileId: string) => void
  disabled?: boolean
  required?: boolean
  hideExistingFiles?: boolean
}

interface UserFile {
  id: string
  original_filename: string
  file_size: number
  mime_type: string
  created_at: string
  download_count: number
}

const getFileIcon = (mimeType: string | null | undefined) => {
  // Handle null, undefined, or empty mimeType
  if (!mimeType || typeof mimeType !== 'string') {
    return <File className="h-4 w-4" />
  }

  if (mimeType.startsWith('image/')) return <Image className="h-4 w-4" />
  if (mimeType.includes('pdf')) return <FileText className="h-4 w-4" />
  return <File className="h-4 w-4" />
}

const getFileTypeIcon = (fileType: string) => {
  switch (fileType) {
    case 'profile_picture': return <Image className="h-4 w-4" />
    case 'document': return <FileText className="h-4 w-4" />
    case 'contract': return <FileText className="h-4 w-4" />
    case 'signature': return <PenTool className="h-4 w-4" />
    default: return <File className="h-4 w-4" />
  }
}

const formatFileSize = (bytes: number): string => {
  if (bytes === 0) return '0 Bytes'
  const k = 1024
  const sizes = ['Bytes', 'KB', 'MB', 'GB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
}

const formatDate = (dateString: string): string => {
  return new Date(dateString).toLocaleDateString('en-US', {
    year: 'numeric',
    month: 'short',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit'
  })
}

const isImageFile = (mimeType: string): boolean => {
  return mimeType.startsWith('image/')
}

const getImagePreviewUrl = async (fileId: string): Promise<string> => {
  const response = await fetch(`/api/admin/files/${fileId}`)
  if (response.ok) {
    const blob = await response.blob()
    return URL.createObjectURL(blob)
  }
  throw new Error('Failed to load image')
}

export function FileUpload({
  fileType,
  label,
  description,
  accept,
  maxSizeMB = 10,
  onFileSelect,
  existingFiles = [],
  onFileDelete,
  disabled = false,
  required = false,
  hideExistingFiles = false
}: FileUploadProps) {
  const [selectedFile, setSelectedFile] = useState<File | null>(null)
  const [dragOver, setDragOver] = useState(false)
  const [error, setError] = useState<string>("")
  const [previewImage, setPreviewImage] = useState<{ url: string; filename: string } | null>(null)
  const fileInputRef = useRef<HTMLInputElement>(null)

  const validateFile = (file: File): string | null => {
    // Check file size
    if (file.size > maxSizeMB * 1024 * 1024) {
      return `File size must be less than ${maxSizeMB}MB`
    }

    // Check file type based on fileType
    const allowedTypes: Record<string, string[]> = {
      profile_picture: ['image/jpeg', 'image/png', 'image/gif', 'image/webp'],
      document: ['application/pdf', 'application/msword', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document', 'image/jpeg', 'image/png'],
      contract: ['application/pdf', 'application/msword', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document', 'image/jpeg', 'image/png'],
      signature: ['image/jpeg', 'image/png', 'image/gif', 'image/webp']
    }

    if (!allowedTypes[fileType]?.includes(file.type)) {
      return `Invalid file type. Allowed types: ${allowedTypes[fileType]?.join(', ')}`
    }

    return null
  }

  const handleFileSelect = (file: File) => {
    setError("")
    
    const validationError = validateFile(file)
    if (validationError) {
      setError(validationError)
      return
    }

    setSelectedFile(file)
    onFileSelect?.(file)
  }

  const handleFileInputChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0]
    if (file) {
      handleFileSelect(file)
    }
  }

  const handleDrop = (event: React.DragEvent) => {
    event.preventDefault()
    setDragOver(false)
    
    const file = event.dataTransfer.files[0]
    if (file) {
      handleFileSelect(file)
    }
  }

  const handleDragOver = (event: React.DragEvent) => {
    event.preventDefault()
    setDragOver(true)
  }

  const handleDragLeave = (event: React.DragEvent) => {
    event.preventDefault()
    setDragOver(false)
  }

  const clearSelection = () => {
    setSelectedFile(null)
    setError("")
    onFileSelect?.(null)
    if (fileInputRef.current) {
      fileInputRef.current.value = ""
    }
  }

  const handleDownload = async (fileId: string, filename: string) => {
    try {
      const response = await fetch(`/api/admin/files/${fileId}`)
      if (response.ok) {
        const blob = await response.blob()
        const url = window.URL.createObjectURL(blob)
        const a = document.createElement('a')
        a.href = url
        a.download = filename
        document.body.appendChild(a)
        a.click()
        window.URL.revokeObjectURL(url)
        document.body.removeChild(a)
      } else {
        console.error('Failed to download file')
      }
    } catch (error) {
      console.error('Error downloading file:', error)
    }
  }

  const handleImagePreview = async (fileId: string, filename: string) => {
    try {
      const url = await getImagePreviewUrl(fileId)
      setPreviewImage({ url, filename })
    } catch (error) {
      console.error('Error loading image preview:', error)
    }
  }

  return (
    <div className="space-y-4">
      <div>
        <Label htmlFor={`file-${fileType}`} className="flex items-center gap-2">
          {getFileTypeIcon(fileType)}
          {label}
          {required && <span className="text-red-500">*</span>}
        </Label>
        {description && (
          <p className="text-sm text-muted-foreground mt-1">{description}</p>
        )}
      </div>

      {/* File Upload Area */}
      <Card className={`border-2 border-dashed transition-colors ${
        dragOver ? 'border-primary bg-primary/5' : 'border-muted-foreground/25'
      } ${disabled ? 'opacity-50 cursor-not-allowed' : 'cursor-pointer'}`}>
        <CardContent className="p-6">
          <div
            className="text-center"
            onDrop={handleDrop}
            onDragOver={handleDragOver}
            onDragLeave={handleDragLeave}
            onClick={() => !disabled && fileInputRef.current?.click()}
          >
            <Upload className="mx-auto h-12 w-12 text-muted-foreground mb-4" />
            <p className="text-sm text-muted-foreground mb-2">
              {dragOver ? 'Drop file here' : 'Click to upload or drag and drop'}
            </p>
            <p className="text-xs text-muted-foreground">
              Max size: {maxSizeMB}MB
            </p>
            <Input
              ref={fileInputRef}
              type="file"
              accept={accept}
              onChange={handleFileInputChange}
              className="hidden"
              disabled={disabled}
            />
          </div>
        </CardContent>
      </Card>

      {/* Selected File Preview */}
      {selectedFile && (
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-3">
                {getFileIcon(selectedFile.type)}
                <div>
                  <p className="text-sm font-medium">{selectedFile.name}</p>
                  <p className="text-xs text-muted-foreground">
                    {formatFileSize(selectedFile.size)}
                  </p>
                </div>
              </div>
              <Button
                variant="ghost"
                size="sm"
                onClick={clearSelection}
                disabled={disabled}
              >
                <X className="h-4 w-4" />
              </Button>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Error Message */}
      {error && (
        <div className="text-sm text-red-600 bg-red-50 p-2 rounded">
          {error}
        </div>
      )}

      {/* Existing Files */}
      {!hideExistingFiles && existingFiles.length > 0 && (
        <div className="space-y-2">
          <Label className="text-sm font-medium">Existing Files</Label>
          {existingFiles.map((file) => (
            <Card key={file.id}>
              <CardContent className="p-3">
                {isImageFile(file.mime_type) ? (
                  // Image file with thumbnail preview
                  <div className="space-y-3">
                    <div className="flex flex-col sm:flex-row items-start gap-4">
                      <div className="relative group flex-shrink-0">
                        <img
                          src={`/api/admin/files/${file.id}`}
                          alt={file.original_filename}
                          className="w-32 h-32 sm:w-36 sm:h-36 md:w-40 md:h-40 object-cover rounded-lg border cursor-pointer hover:opacity-80 transition-opacity shadow-sm"
                          onClick={() => handleImagePreview(file.id, file.original_filename)}
                          onError={(e) => {
                            // Fallback to icon if image fails to load
                            e.currentTarget.style.display = 'none'
                            e.currentTarget.nextElementSibling?.classList.remove('hidden')
                          }}
                        />
                        <div className="hidden">
                          {getFileIcon(file.mime_type)}
                        </div>
                        <div className="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-20 rounded-lg flex items-center justify-center transition-all">
                          <ZoomIn className="h-6 w-6 text-white opacity-0 group-hover:opacity-100 transition-opacity" />
                        </div>
                      </div>
                      <div className="flex-1 min-w-0 space-y-2">
                        <div>
                          <p className="text-sm font-medium truncate">{file.original_filename}</p>
                          <p className="text-xs text-muted-foreground">
                            {formatFileSize(file.file_size)} • {formatDate(file.created_at)} • Downloaded {file.download_count} times
                          </p>
                        </div>
                        <div className="flex items-center gap-2">
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => handleImagePreview(file.id, file.original_filename)}
                            title="Preview image"
                            className="text-xs"
                          >
                            <Eye className="h-4 w-4 mr-1" />
                            Preview
                          </Button>
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => handleDownload(file.id, file.original_filename)}
                            title="Download file"
                            className="text-xs"
                          >
                            <Download className="h-4 w-4 mr-1" />
                            Download
                          </Button>
                          {onFileDelete && (
                            <Button
                              variant="outline"
                              size="sm"
                              onClick={() => onFileDelete(file.id)}
                              className="text-red-600 hover:text-red-700 text-xs"
                              title="Delete file"
                            >
                              <Trash2 className="h-4 w-4 mr-1" />
                              Delete
                            </Button>
                          )}
                        </div>
                      </div>
                    </div>
                  </div>
                ) : (
                  // Non-image file with regular display
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-3">
                      {getFileIcon(file.mime_type)}
                      <div>
                        <p className="text-sm font-medium">{file.original_filename}</p>
                        <p className="text-xs text-muted-foreground">
                          {formatFileSize(file.file_size)} • {formatDate(file.created_at)} • Downloaded {file.download_count} times
                        </p>
                      </div>
                    </div>
                    <div className="flex items-center gap-1">
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => handleDownload(file.id, file.original_filename)}
                        title="Download file"
                      >
                        <Download className="h-4 w-4" />
                      </Button>
                      {onFileDelete && (
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => onFileDelete(file.id)}
                          className="text-red-600 hover:text-red-700"
                          title="Delete file"
                        >
                          <Trash2 className="h-4 w-4" />
                        </Button>
                      )}
                    </div>
                  </div>
                )}
              </CardContent>
            </Card>
          ))}
        </div>
      )}

      {/* Image Preview Modal */}
      <Dialog open={!!previewImage} onOpenChange={() => setPreviewImage(null)}>
        <DialogContent className="max-w-4xl max-h-[90vh] overflow-hidden">
          <DialogHeader>
            <DialogTitle>{previewImage?.filename}</DialogTitle>
          </DialogHeader>
          <div className="flex items-center justify-center p-4">
            {previewImage && (
              <img
                src={previewImage.url}
                alt={previewImage.filename}
                className="max-w-full max-h-[70vh] object-contain rounded-lg"
                onLoad={() => {
                  // Clean up the object URL when the image loads
                  if (previewImage.url.startsWith('blob:')) {
                    setTimeout(() => URL.revokeObjectURL(previewImage.url), 1000)
                  }
                }}
              />
            )}
          </div>
        </DialogContent>
      </Dialog>
    </div>
  )
}
