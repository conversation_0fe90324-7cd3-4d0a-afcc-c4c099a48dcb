// Payroll Deductions Management API
// Admin endpoints for managing employee deductions

import { NextRequest, NextResponse } from 'next/server';
import { AuthService } from '@/lib/auth-utils';
import { neon } from '@neondatabase/serverless';

const sql = neon(process.env.DATABASE_URL!);

// GET - List employee deductions or available deduction types
export async function GET(request: NextRequest) {
  try {
    const sessionToken = request.cookies.get("session-token")?.value;

    if (!sessionToken) {
      return NextResponse.json({
        success: false,
        error: 'Unauthorized'
      }, { status: 401 });
    }

    const user = await AuthService.verifySession(sessionToken);

    if (!user || !["admin", "hr_manager"].includes(user.role)) {
      return NextResponse.json({
        success: false,
        error: 'Insufficient permissions'
      }, { status: 403 });
    }

    const { searchParams } = new URL(request.url);
    const action = searchParams.get('action') || 'list_assignments';
    const userId = searchParams.get('userId');
    const category = searchParams.get('category'); // statutory, voluntary, company_policy

    if (action === 'available_deductions') {
      // Get all available deduction components
      const deductions = await sql`
        SELECT
          id, name, code, category, calculation_type,
          fixed_amount, percentage, percentage_base,
          is_statutory, description, is_active,
          effective_from, effective_to
        FROM payroll_components_master
        WHERE type = 'deduction' AND is_active = true
        ${category ? sql`AND category = ${category}` : sql``}
        ORDER BY category, name
      `;

      return NextResponse.json({
        success: true,
        data: deductions,
        message: 'Available deductions retrieved successfully'
      });

    } else if (action === 'employee_deductions' && userId) {
      // Get deductions assigned to specific employee
      const employeeDeductions = await sql`
        SELECT 
          eca.id as assignment_id,
          eca.user_id,
          eca.component_id,
          eca.is_active,
          eca.effective_from,
          eca.effective_to,
          eca.override_amount,
          eca.override_percentage,
          eca.assigned_by,
          eca.approved_by,
          eca.approval_date,
          eca.notes,
          eca.created_at,
          pcm.name,
          pcm.code,
          pcm.category,
          pcm.calculation_type,
          pcm.fixed_amount,
          pcm.percentage,
          pcm.percentage_base,
          pcm.is_statutory,
          pcm.description,
          u.full_name as employee_name,
          assigned_user.full_name as assigned_by_name,
          approved_user.full_name as approved_by_name
        FROM employee_component_assignments eca
        JOIN payroll_components_master pcm ON eca.component_id = pcm.id
        JOIN users u ON eca.user_id = u.id
        LEFT JOIN users assigned_user ON eca.assigned_by = assigned_user.id
        LEFT JOIN users approved_user ON eca.approved_by = approved_user.id
        WHERE eca.user_id = ${userId} 
          AND pcm.type = 'deduction'
          AND (eca.effective_to IS NULL OR eca.effective_to >= CURRENT_DATE)
        ORDER BY pcm.category, eca.created_at DESC
      `;

      return NextResponse.json({
        success: true,
        data: employeeDeductions,
        message: 'Employee deductions retrieved successfully'
      });

    } else if (action === 'late_penalties') {
      // Get late penalty calculations for employees
      const startDate = searchParams.get('startDate');
      const endDate = searchParams.get('endDate');

      if (!startDate || !endDate) {
        return NextResponse.json({
          success: false,
          error: 'Start date and end date are required for late penalties'
        }, { status: 400 });
      }

      const latePenalties = await sql`
        SELECT 
          a.user_id,
          u.full_name,
          u.employee_id,
          COUNT(CASE WHEN a.status = 'late' THEN 1 END) as late_days,
          SUM(
            CASE 
              WHEN a.status = 'late' AND a.check_in_time IS NOT NULL 
              THEN EXTRACT(EPOCH FROM (a.check_in_time::time - '09:00:00'::time)) / 60
              ELSE 0 
            END
          ) as total_late_minutes,
          (
            SELECT setting_value::numeric 
            FROM payroll_settings 
            WHERE setting_key = 'late_penalty_per_minute'
          ) as penalty_per_minute
        FROM attendance a
        JOIN users u ON a.user_id = u.id
        WHERE a.date BETWEEN ${startDate} AND ${endDate}
          AND u.employment_status = 'active'
        GROUP BY a.user_id, u.full_name, u.employee_id
        HAVING COUNT(CASE WHEN a.status = 'late' THEN 1 END) > 0
        ORDER BY total_late_minutes DESC
      `;

      // Calculate penalty amounts
      const penaltiesWithAmounts = latePenalties.map(penalty => ({
        ...penalty,
        calculated_penalty: Math.round(penalty.total_late_minutes * penalty.penalty_per_minute)
      }));

      return NextResponse.json({
        success: true,
        data: penaltiesWithAmounts,
        message: 'Late penalties calculated successfully'
      });

    } else if (action === 'statutory_deductions') {
      // Get statutory deductions summary
      const statutoryDeductions = await sql`
        SELECT 
          pcm.name,
          pcm.code,
          pcm.calculation_type,
          pcm.percentage,
          pcm.fixed_amount,
          COUNT(eca.id) as assigned_employees,
          SUM(
            CASE 
              WHEN pcm.calculation_type = 'percentage' 
              THEN (u.salary * pcm.percentage / 100)
              ELSE pcm.fixed_amount 
            END
          ) as total_monthly_deduction
        FROM payroll_components_master pcm
        LEFT JOIN employee_component_assignments eca ON pcm.id = eca.component_id 
          AND eca.is_active = true
        LEFT JOIN users u ON eca.user_id = u.id AND u.employment_status = 'active'
        WHERE pcm.type = 'deduction' 
          AND pcm.is_statutory = true 
          AND pcm.is_active = true
        GROUP BY pcm.id, pcm.name, pcm.code, pcm.calculation_type, pcm.percentage, pcm.fixed_amount
        ORDER BY pcm.name
      `;

      return NextResponse.json({
        success: true,
        data: statutoryDeductions,
        message: 'Statutory deductions summary retrieved successfully'
      });

    } else {
      return NextResponse.json({
        success: false,
        error: 'Invalid action or missing parameters'
      }, { status: 400 });
    }

  } catch (error) {
    console.error('Error in deductions GET:', error);
    return NextResponse.json({
      success: false,
      error: error.message || 'Internal server error'
    }, { status: 500 });
  }
}

// POST - Create new deduction assignment
export async function POST(request: NextRequest) {
  try {
    const sessionToken = request.cookies.get("session-token")?.value;

    if (!sessionToken) {
      return NextResponse.json({
        success: false,
        error: 'Unauthorized'
      }, { status: 401 });
    }

    const user = await AuthService.verifySession(sessionToken);

    if (!user || !["admin", "hr_manager"].includes(user.role)) {
      return NextResponse.json({
        success: false,
        error: 'Insufficient permissions'
      }, { status: 403 });
    }

    const body = await request.json();
    const {
      userId,
      componentId,
      effectiveFrom,
      effectiveTo,
      overrideAmount,
      overridePercentage,
      notes,
      requiresApproval = true, // Deductions typically require approval
      reason
    } = body;

    // Validate required fields
    if (!userId || !componentId || !effectiveFrom) {
      return NextResponse.json({
        success: false,
        error: 'User ID, component ID, and effective from date are required'
      }, { status: 400 });
    }

    // Verify the component exists and is a deduction
    const component = await sql`
      SELECT id, name, type, category, is_statutory, is_active 
      FROM payroll_components_master 
      WHERE id = ${componentId} AND type = 'deduction' AND is_active = true
    `;

    if (component.length === 0) {
      return NextResponse.json({
        success: false,
        error: 'Invalid or inactive deduction component'
      }, { status: 400 });
    }

    // Verify the user exists
    const employee = await sql`
      SELECT id, full_name, employment_status 
      FROM users 
      WHERE id = ${userId} AND employment_status = 'active'
    `;

    if (employee.length === 0) {
      return NextResponse.json({
        success: false,
        error: 'Employee not found or inactive'
      }, { status: 400 });
    }

    // For statutory deductions, check if already assigned
    if (component[0].is_statutory) {
      const existing = await sql`
        SELECT id 
        FROM employee_component_assignments 
        WHERE user_id = ${userId} 
          AND component_id = ${componentId}
          AND is_active = true
      `;

      if (existing.length > 0) {
        return NextResponse.json({
          success: false,
          error: 'Statutory deduction already assigned to this employee'
        }, { status: 400 });
      }
    }

    // Create the assignment
    const assignment = await sql`
      INSERT INTO employee_component_assignments (
        user_id, component_id, is_active, effective_from, effective_to,
        override_amount, override_percentage, assigned_by, 
        approved_by, approval_date, notes
      )
      VALUES (
        ${userId}, ${componentId}, true, ${effectiveFrom}, ${effectiveTo || null},
        ${overrideAmount || null}, ${overridePercentage || null}, ${user.id},
        ${requiresApproval ? null : user.id}, 
        ${requiresApproval ? null : new Date().toISOString()},
        ${notes || reason || null}
      )
      RETURNING *
    `;

    // Get the complete assignment details
    const assignmentDetails = await sql`
      SELECT 
        eca.*,
        pcm.name as deduction_name,
        pcm.code,
        pcm.category,
        pcm.is_statutory,
        u.full_name as employee_name
      FROM employee_component_assignments eca
      JOIN payroll_components_master pcm ON eca.component_id = pcm.id
      JOIN users u ON eca.user_id = u.id
      WHERE eca.id = ${assignment[0].id}
    `;

    return NextResponse.json({
      success: true,
      data: assignmentDetails[0],
      message: `Deduction assignment created successfully${requiresApproval ? ' (pending approval)' : ''}`
    });

  } catch (error) {
    console.error('Error in deductions POST:', error);
    return NextResponse.json({
      success: false,
      error: error.message || 'Internal server error'
    }, { status: 500 });
  }
}

// PUT - Update deduction assignment or process bulk late penalties
export async function PUT(request: NextRequest) {
  try {
    const sessionToken = request.cookies.get("session-token")?.value;

    if (!sessionToken) {
      return NextResponse.json({
        success: false,
        error: 'Unauthorized'
      }, { status: 401 });
    }

    const user = await AuthService.verifySession(sessionToken);

    if (!user || !["admin", "hr_manager"].includes(user.role)) {
      return NextResponse.json({
        success: false,
        error: 'Insufficient permissions'
      }, { status: 403 });
    }

    const body = await request.json();
    const { action = 'update' } = body;

    if (action === 'process_late_penalties') {
      // Process late penalties for a period
      const { startDate, endDate, applyPenalties = false } = body;

      if (!startDate || !endDate) {
        return NextResponse.json({
          success: false,
          error: 'Start date and end date are required'
        }, { status: 400 });
      }

      // Get late penalty component
      const latePenaltyComponent = await sql`
        SELECT id FROM payroll_components_master 
        WHERE code = 'LATE_PENALTY' AND type = 'deduction' AND is_active = true
      `;

      if (latePenaltyComponent.length === 0) {
        return NextResponse.json({
          success: false,
          error: 'Late penalty component not found'
        }, { status: 400 });
      }

      // Calculate late penalties
      const latePenalties = await sql`
        SELECT 
          a.user_id,
          u.full_name,
          COUNT(CASE WHEN a.status = 'late' THEN 1 END) as late_days,
          SUM(
            CASE 
              WHEN a.status = 'late' AND a.check_in_time IS NOT NULL 
              THEN EXTRACT(EPOCH FROM (a.check_in_time::time - '09:00:00'::time)) / 60
              ELSE 0 
            END
          ) as total_late_minutes
        FROM attendance a
        JOIN users u ON a.user_id = u.id
        WHERE a.date BETWEEN ${startDate} AND ${endDate}
          AND u.employment_status = 'active'
        GROUP BY a.user_id, u.full_name
        HAVING COUNT(CASE WHEN a.status = 'late' THEN 1 END) > 0
      `;

      const penaltyPerMinute = await sql`
        SELECT setting_value::numeric as value 
        FROM payroll_settings 
        WHERE setting_key = 'late_penalty_per_minute'
      `;

      const penaltyRate = penaltyPerMinute[0]?.value || 10;
      const processedPenalties = [];

      if (applyPenalties) {
        // Apply penalties as deduction assignments
        for (const penalty of latePenalties) {
          const penaltyAmount = Math.round(penalty.total_late_minutes * penaltyRate);
          
          if (penaltyAmount > 0) {
            const assignment = await sql`
              INSERT INTO employee_component_assignments (
                user_id, component_id, is_active, effective_from, effective_to,
                override_amount, assigned_by, approved_by, approval_date, notes
              )
              VALUES (
                ${penalty.user_id}, ${latePenaltyComponent[0].id}, true, 
                ${startDate}, ${endDate}, ${penaltyAmount}, ${user.id}, 
                ${user.id}, NOW(), 
                ${'Late penalty for ' + penalty.late_days + ' days, ' + penalty.total_late_minutes + ' minutes total'}
              )
              RETURNING *
            `;

            processedPenalties.push({
              ...penalty,
              penalty_amount: penaltyAmount,
              assignment_id: assignment[0].id
            });
          }
        }
      }

      return NextResponse.json({
        success: true,
        data: {
          penalties: latePenalties.map(p => ({
            ...p,
            penalty_amount: Math.round(p.total_late_minutes * penaltyRate)
          })),
          processed: processedPenalties,
          penalty_rate: penaltyRate
        },
        message: applyPenalties 
          ? `Late penalties applied for ${processedPenalties.length} employees`
          : `Late penalties calculated for ${latePenalties.length} employees`
      });

    } else {
      // Regular update logic (similar to allowances)
      const {
        assignmentId,
        effectiveFrom,
        effectiveTo,
        overrideAmount,
        overridePercentage,
        notes,
        isActive,
        action = 'update'
      } = body;

      if (!assignmentId) {
        return NextResponse.json({
          success: false,
          error: 'Assignment ID is required'
        }, { status: 400 });
      }

      if (action === 'approve') {
        const updated = await sql`
          UPDATE employee_component_assignments 
          SET 
            approved_by = ${user.id},
            approval_date = NOW(),
            updated_at = NOW()
          WHERE id = ${assignmentId} AND approved_by IS NULL
          RETURNING *
        `;

        if (updated.length === 0) {
          return NextResponse.json({
            success: false,
            error: 'Assignment not found or already approved'
          }, { status: 404 });
        }

        return NextResponse.json({
          success: true,
          data: updated[0],
          message: 'Deduction assignment approved successfully'
        });

      } else {
        const updated = await sql`
          UPDATE employee_component_assignments 
          SET 
            effective_from = COALESCE(${effectiveFrom}, effective_from),
            effective_to = ${effectiveTo},
            override_amount = ${overrideAmount},
            override_percentage = ${overridePercentage},
            notes = COALESCE(${notes}, notes),
            is_active = COALESCE(${isActive}, is_active),
            updated_at = NOW()
          WHERE id = ${assignmentId}
          RETURNING *
        `;

        if (updated.length === 0) {
          return NextResponse.json({
            success: false,
            error: 'Assignment not found'
          }, { status: 404 });
        }

        return NextResponse.json({
          success: true,
          data: updated[0],
          message: 'Deduction assignment updated successfully'
        });
      }
    }

  } catch (error) {
    console.error('Error in deductions PUT:', error);
    return NextResponse.json({
      success: false,
      error: error.message || 'Internal server error'
    }, { status: 500 });
  }
}

// DELETE - Remove deduction assignment
export async function DELETE(request: NextRequest) {
  try {
    const sessionToken = request.cookies.get("session-token")?.value;

    if (!sessionToken) {
      return NextResponse.json({
        success: false,
        error: 'Unauthorized'
      }, { status: 401 });
    }

    const user = await AuthService.verifySession(sessionToken);

    if (!user || user.role !== "admin") {
      return NextResponse.json({
        success: false,
        error: 'Admin access required'
      }, { status: 403 });
    }

    const { searchParams } = new URL(request.url);
    const assignmentId = searchParams.get('assignmentId');

    if (!assignmentId) {
      return NextResponse.json({
        success: false,
        error: 'Assignment ID is required'
      }, { status: 400 });
    }

    // Soft delete by setting effective_to to current date and is_active to false
    const deleted = await sql`
      UPDATE employee_component_assignments 
      SET 
        is_active = false,
        effective_to = CURRENT_DATE,
        updated_at = NOW()
      WHERE id = ${assignmentId}
      RETURNING *
    `;

    if (deleted.length === 0) {
      return NextResponse.json({
        success: false,
        error: 'Assignment not found'
      }, { status: 404 });
    }

    return NextResponse.json({
      success: true,
      data: deleted[0],
      message: 'Deduction assignment removed successfully'
    });

  } catch (error) {
    console.error('Error in deductions DELETE:', error);
    return NextResponse.json({
      success: false,
      error: error.message || 'Internal server error'
    }, { status: 500 });
  }
}
