-- ============================================================================
-- POPULATE NEPAL CONFIGURATION DATA
-- Phase 1: Nepal Configuration Setup - Database Population
-- ============================================================================

-- Step 1: Insert comprehensive Nepali calendar data for 2024-2026
INSERT INTO nepali_calendar_config (bs_year, bs_month, bs_day, ad_date, is_holiday, holiday_name, holiday_type, is_working_day) VALUES

-- 2081 BS (2024-25 AD) - Key dates
(2081, 1, 1, '2024-04-14', TRUE, 'Nepali New Year', 'public', FALSE),
(2081, 1, 7, '2024-04-20', TRUE, 'Democracy Day', 'public', FALSE),
(2081, 1, 18, '2024-05-01', TRUE, 'Labor Day', 'public', FALSE),
(2081, 2, 15, '2024-05-29', TRUE, 'Republic Day', 'public', FALSE),
(2081, 6, 3, '2024-09-19', TRUE, 'Constitution Day', 'public', FALSE),

-- Major festivals 2081 BS
(2081, 5, 15, '2024-08-30', TRUE, 'Janai Purnima', 'festival', FALSE),
(2081, 5, 23, '2024-09-07', TRUE, 'Krishna Janmashtami', 'festival', FALSE),
(2081, 6, 15, '2024-10-01', TRUE, 'Indra Jatra', 'festival', FALSE),
(2081, 7, 10, '2024-10-26', TRUE, 'Dashain (Ghatasthapana)', 'festival', FALSE),
(2081, 7, 15, '2024-10-31', TRUE, 'Dashain (Vijaya Dashami)', 'festival', FALSE),
(2081, 8, 15, '2024-11-30', TRUE, 'Tihar (Laxmi Puja)', 'festival', FALSE),
(2081, 8, 16, '2024-12-01', TRUE, 'Tihar (Gai Tihar)', 'festival', FALSE),
(2081, 8, 17, '2024-12-02', TRUE, 'Tihar (Govardhan Puja)', 'festival', FALSE),

-- 2082 BS (2025-26 AD) - Key dates
(2082, 1, 1, '2025-04-14', TRUE, 'Nepali New Year', 'public', FALSE),
(2082, 1, 7, '2025-04-20', TRUE, 'Democracy Day', 'public', FALSE),
(2082, 1, 18, '2025-05-01', TRUE, 'Labor Day', 'public', FALSE),
(2082, 2, 15, '2025-05-29', TRUE, 'Republic Day', 'public', FALSE),
(2082, 6, 3, '2025-09-19', TRUE, 'Constitution Day', 'public', FALSE),

-- 2083 BS (2026-27 AD) - Key dates
(2083, 1, 1, '2026-04-14', TRUE, 'Nepali New Year', 'public', FALSE),
(2083, 1, 7, '2026-04-20', TRUE, 'Democracy Day', 'public', FALSE),
(2083, 1, 18, '2026-05-01', TRUE, 'Labor Day', 'public', FALSE),
(2083, 2, 15, '2026-05-29', TRUE, 'Republic Day', 'public', FALSE),
(2083, 6, 3, '2026-09-19', TRUE, 'Constitution Day', 'public', FALSE)

ON CONFLICT (ad_date) DO NOTHING;

-- Step 2: Insert payroll periods for fiscal years
INSERT INTO payroll_periods (period_name, period_type, fiscal_year, bs_start_date, bs_end_date, ad_start_date, ad_end_date, working_days, public_holidays, is_current_period) VALUES

-- Fiscal Year 2081-82 (2024-25) - Monthly periods
('Shrawan 2081', 'monthly', '2081-82', '2081-04-01', '2081-04-31', '2024-07-16', '2024-08-15', 26, 0, TRUE),
('Bhadra 2081', 'monthly', '2081-82', '2081-05-01', '2081-05-31', '2024-08-16', '2024-09-15', 26, 0, FALSE),
('Ashwin 2081', 'monthly', '2081-82', '2081-06-01', '2081-06-31', '2024-09-16', '2024-10-16', 25, 1, FALSE),
('Kartik 2081', 'monthly', '2081-82', '2081-07-01', '2081-07-30', '2024-10-17', '2024-11-15', 20, 5, FALSE), -- Dashain period
('Mangsir 2081', 'monthly', '2081-82', '2081-08-01', '2081-08-29', '2024-11-16', '2024-12-14', 22, 3, FALSE), -- Tihar period
('Poush 2081', 'monthly', '2081-82', '2081-09-01', '2081-09-30', '2024-12-15', '2025-01-13', 26, 0, FALSE),
('Magh 2081', 'monthly', '2081-82', '2081-10-01', '2081-10-30', '2025-01-14', '2025-02-12', 26, 0, FALSE),
('Falgun 2081', 'monthly', '2081-82', '2081-11-01', '2081-11-30', '2025-02-13', '2025-03-14', 26, 0, FALSE),
('Chaitra 2081', 'monthly', '2081-82', '2081-12-01', '2081-12-30', '2025-03-15', '2025-04-13', 26, 0, FALSE),
('Baishakh 2082', 'monthly', '2081-82', '2082-01-01', '2082-01-31', '2025-04-14', '2025-05-14', 25, 1, FALSE),
('Jestha 2082', 'monthly', '2081-82', '2082-02-01', '2082-02-31', '2025-05-15', '2025-06-14', 25, 1, FALSE),
('Ashadh 2082', 'monthly', '2081-82', '2082-03-01', '2082-03-32', '2025-06-15', '2025-07-15', 26, 0, FALSE),

-- Quarterly periods for 2081-82
('Q1 2081-82', 'quarterly', '2081-82', '2081-04-01', '2081-06-31', '2024-07-16', '2024-10-16', 77, 1, FALSE),
('Q2 2081-82', 'quarterly', '2081-82', '2081-07-01', '2081-09-30', '2024-10-17', '2025-01-13', 68, 8, FALSE),
('Q3 2081-82', 'quarterly', '2081-82', '2081-10-01', '2081-12-30', '2025-01-14', '2025-04-13', 78, 0, FALSE),
('Q4 2081-82', 'quarterly', '2081-82', '2082-01-01', '2082-03-32', '2025-04-14', '2025-07-15', 76, 2, FALSE),

-- Full fiscal year
('FY 2081-82', 'yearly', '2081-82', '2081-04-01', '2082-03-32', '2024-07-16', '2025-07-15', 299, 11, FALSE)

ON CONFLICT (period_name, fiscal_year) DO NOTHING;

-- Step 3: Update payroll settings with Nepal-specific values
INSERT INTO payroll_settings (setting_key, setting_value, setting_type, description, is_system_setting) VALUES

-- Working time settings
('standard_working_hours_per_day', '8', 'number', 'Standard working hours per day in Nepal', TRUE),
('standard_working_days_per_week', '6', 'number', 'Standard working days per week (Sun-Fri)', TRUE),
('weekly_off_days', '[6]', 'json', 'Weekly off days (0=Sunday, 6=Saturday)', TRUE),
('overtime_threshold_daily', '8', 'number', 'Daily hours after which overtime applies', TRUE),
('overtime_rate_multiplier', '1.5', 'number', 'Overtime rate multiplier (1.5x normal rate)', TRUE),
('max_working_hours_per_day', '12', 'number', 'Maximum allowed working hours per day', TRUE),
('max_overtime_hours_per_day', '4', 'number', 'Maximum overtime hours per day', TRUE),

-- Salary and deduction settings
('minimum_wage_monthly', '17300', 'number', 'Minimum wage per month in NPR (2024)', TRUE),
('provident_fund_rate_employee', '10', 'number', 'Employee PF contribution rate (%)', TRUE),
('provident_fund_rate_employer', '10', 'number', 'Employer PF contribution rate (%)', TRUE),
('social_security_fund_rate', '11', 'number', 'Social Security Fund contribution rate (%)', TRUE),
('income_tax_threshold_annual', '500000', 'number', 'Annual income tax threshold in NPR', TRUE),

-- Currency and formatting
('currency_code', 'NPR', 'string', 'Currency code for Nepal', TRUE),
('currency_symbol', 'रू', 'string', 'Currency symbol for display', TRUE),
('use_indian_numbering', 'true', 'boolean', 'Use Indian numbering system (lakhs/crores)', TRUE),
('decimal_places', '2', 'number', 'Decimal places for currency display', TRUE),

-- Fiscal year settings
('fiscal_year_start_month_bs', '4', 'number', 'Fiscal year start month in BS (Shrawan)', TRUE),
('fiscal_year_start_day_bs', '1', 'number', 'Fiscal year start day in BS', TRUE),
('current_fiscal_year', '2081-82', 'string', 'Current fiscal year', FALSE),

-- Attendance and bonus settings
('attendance_bonus_threshold', '95', 'number', 'Attendance percentage for bonus eligibility', TRUE),
('attendance_bonus_amount', '2000', 'number', 'Attendance bonus amount in NPR', TRUE),
('late_penalty_per_minute', '10', 'number', 'Late penalty amount per minute in NPR', TRUE),
('early_departure_penalty', '500', 'number', 'Early departure penalty in NPR', TRUE),

-- Leave settings
('annual_leave_days', '18', 'number', 'Annual leave days as per Nepal Labor Act', TRUE),
('sick_leave_days', '12', 'number', 'Sick leave days per year', TRUE),
('maternity_leave_days', '98', 'number', 'Maternity leave days', TRUE),
('paternity_leave_days', '15', 'number', 'Paternity leave days', TRUE),

-- Festival and holiday settings
('festival_bonus_months', '["7", "8"]', 'json', 'Months for festival bonus (Dashain/Tihar)', TRUE),
('festival_bonus_percentage', '100', 'number', 'Festival bonus as percentage of basic salary', TRUE),

-- Payroll processing settings
('payroll_processing_day', '1', 'number', 'Day of month for payroll processing', TRUE),
('payroll_payment_day', '5', 'number', 'Day of month for salary payment', TRUE),
('payroll_cutoff_day', '25', 'number', 'Attendance cutoff day for payroll', TRUE),

-- System settings
('enable_bs_calendar', 'true', 'boolean', 'Enable Bikram Sambat calendar display', TRUE),
('default_language', 'en', 'string', 'Default language (en/ne)', TRUE),
('enable_dual_calendar_display', 'true', 'boolean', 'Show both AD and BS dates', TRUE)

ON CONFLICT (setting_key) DO UPDATE SET 
    setting_value = EXCLUDED.setting_value,
    description = EXCLUDED.description,
    updated_at = NOW();

-- Step 4: Create default employee pay structures for existing users
INSERT INTO employee_pay_structure (
    user_id, 
    pay_structure_type, 
    base_salary, 
    hourly_rate, 
    daily_rate, 
    overtime_rate_multiplier, 
    provident_fund_percentage, 
    effective_from, 
    is_active, 
    created_by
)
SELECT 
    u.id as user_id,
    'monthly' as pay_structure_type,
    COALESCE(u.salary, 25000) as base_salary,
    ROUND(COALESCE(u.salary, 25000) / 208, 2) as hourly_rate, -- 26 days * 8 hours
    ROUND(COALESCE(u.salary, 25000) / 26, 2) as daily_rate,   -- 26 working days
    1.5 as overtime_rate_multiplier,
    10.0 as provident_fund_percentage,
    COALESCE(u.hire_date, CURRENT_DATE) as effective_from,
    TRUE as is_active,
    (SELECT id FROM users WHERE role = 'admin' LIMIT 1) as created_by
FROM users u
WHERE u.is_active = TRUE
ON CONFLICT (user_id, effective_from) DO NOTHING;

-- Step 5: Create indexes for performance
CREATE INDEX IF NOT EXISTS idx_nepali_calendar_bs_year_month ON nepali_calendar_config(bs_year, bs_month);
CREATE INDEX IF NOT EXISTS idx_nepali_calendar_holiday_type ON nepali_calendar_config(holiday_type, is_holiday);
CREATE INDEX IF NOT EXISTS idx_payroll_periods_fiscal_year_type ON payroll_periods(fiscal_year, period_type);
CREATE INDEX IF NOT EXISTS idx_payroll_periods_current ON payroll_periods(is_current_period) WHERE is_current_period = TRUE;
CREATE INDEX IF NOT EXISTS idx_employee_pay_structure_effective ON employee_pay_structure(user_id, effective_from, effective_to);

-- Step 6: Verification queries
-- Check inserted calendar data
SELECT 
    bs_year, 
    bs_month, 
    COUNT(*) as holiday_count,
    COUNT(CASE WHEN holiday_type = 'public' THEN 1 END) as public_holidays,
    COUNT(CASE WHEN holiday_type = 'festival' THEN 1 END) as festivals
FROM nepali_calendar_config 
WHERE is_holiday = TRUE
GROUP BY bs_year, bs_month
ORDER BY bs_year, bs_month;

-- Check payroll periods
SELECT 
    fiscal_year,
    period_type,
    COUNT(*) as period_count,
    SUM(working_days) as total_working_days,
    SUM(public_holidays) as total_holidays
FROM payroll_periods
GROUP BY fiscal_year, period_type
ORDER BY fiscal_year, period_type;

-- Check payroll settings
SELECT 
    setting_key,
    setting_value,
    setting_type,
    description
FROM payroll_settings
WHERE is_system_setting = TRUE
ORDER BY setting_key;

-- Check employee pay structures
SELECT 
    COUNT(*) as total_employees,
    AVG(base_salary) as avg_salary,
    MIN(base_salary) as min_salary,
    MAX(base_salary) as max_salary,
    COUNT(CASE WHEN pay_structure_type = 'monthly' THEN 1 END) as monthly_employees,
    COUNT(CASE WHEN pay_structure_type = 'hourly' THEN 1 END) as hourly_employees
FROM employee_pay_structure
WHERE is_active = TRUE;

-- Step 7: Insert default payroll components (deductions and allowances)
INSERT INTO payroll_components_master (
    name, code, type, category, calculation_type, fixed_amount, percentage, percentage_base,
    is_taxable, is_statutory, affects_provident_fund, affects_gratuity,
    applicable_to_pay_structures, applicable_to_employee_categories, applicable_to_departments,
    description, is_active, effective_from, created_by
) VALUES

-- Statutory Deductions
('Provident Fund (Employee)', 'PF_EMP', 'deduction', 'statutory', 'percentage', NULL, 10.00, 'base_salary',
 FALSE, TRUE, FALSE, TRUE, '["monthly", "hourly", "daily"]', '["regular", "contract"]', '[]',
 'Employee contribution to Provident Fund (10% of basic salary)', TRUE, '2024-01-01',
 (SELECT id FROM users WHERE role = 'admin' LIMIT 1)),

('Social Security Fund', 'SSF', 'deduction', 'statutory', 'percentage', NULL, 11.00, 'base_salary',
 FALSE, TRUE, FALSE, TRUE, '["monthly", "hourly", "daily"]', '["regular", "contract"]', '[]',
 'Social Security Fund contribution (11% of basic salary)', TRUE, '2024-01-01',
 (SELECT id FROM users WHERE role = 'admin' LIMIT 1)),

('Income Tax', 'IT', 'deduction', 'statutory', 'formula', NULL, NULL, NULL,
 FALSE, TRUE, FALSE, FALSE, '["monthly", "hourly", "daily", "project_based"]', '["regular", "contract"]', '[]',
 'Income tax as per Nepal tax slabs', TRUE, '2024-01-01',
 (SELECT id FROM users WHERE role = 'admin' LIMIT 1)),

-- Company Policy Deductions
('Late Penalty', 'LATE_PEN', 'deduction', 'company_policy', 'formula', NULL, NULL, NULL,
 FALSE, FALSE, FALSE, FALSE, '["monthly", "hourly", "daily"]', '["regular", "contract", "probation"]', '[]',
 'Penalty for late attendance (NPR 10 per minute)', TRUE, '2024-01-01',
 (SELECT id FROM users WHERE role = 'admin' LIMIT 1)),

('Advance Salary Deduction', 'ADV_SAL', 'deduction', 'voluntary', 'fixed', 0, NULL, NULL,
 FALSE, FALSE, TRUE, TRUE, '["monthly", "hourly", "daily"]', '["regular", "contract"]', '[]',
 'Deduction for advance salary taken', TRUE, '2024-01-01',
 (SELECT id FROM users WHERE role = 'admin' LIMIT 1)),

-- Allowances
('House Rent Allowance', 'HRA', 'allowance', 'company_policy', 'percentage', NULL, 15.00, 'base_salary',
 TRUE, FALSE, TRUE, TRUE, '["monthly"]', '["regular", "contract"]', '[]',
 'House Rent Allowance (15% of basic salary)', TRUE, '2024-01-01',
 (SELECT id FROM users WHERE role = 'admin' LIMIT 1)),

('Transport Allowance', 'TA', 'allowance', 'company_policy', 'fixed', 3000.00, NULL, NULL,
 TRUE, FALSE, TRUE, TRUE, '["monthly", "daily"]', '["regular", "contract"]', '[]',
 'Monthly transport allowance', TRUE, '2024-01-01',
 (SELECT id FROM users WHERE role = 'admin' LIMIT 1)),

('Meal Allowance', 'MEAL', 'allowance', 'company_policy', 'fixed', 2000.00, NULL, NULL,
 TRUE, FALSE, TRUE, TRUE, '["monthly"]', '["regular", "contract"]', '[]',
 'Monthly meal allowance', TRUE, '2024-01-01',
 (SELECT id FROM users WHERE role = 'admin' LIMIT 1)),

('Attendance Bonus', 'ATT_BONUS', 'allowance', 'company_policy', 'conditional', 2000.00, NULL, NULL,
 TRUE, FALSE, TRUE, TRUE, '["monthly", "hourly", "daily"]', '["regular", "contract"]', '[]',
 'Bonus for perfect attendance (95% or above)', TRUE, '2024-01-01',
 (SELECT id FROM users WHERE role = 'admin' LIMIT 1)),

('Festival Bonus', 'FEST_BONUS', 'allowance', 'company_policy', 'percentage', NULL, 100.00, 'base_salary',
 TRUE, FALSE, TRUE, TRUE, '["monthly", "hourly", "daily"]', '["regular", "contract"]', '[]',
 'Festival bonus during Dashain/Tihar (100% of basic salary)', TRUE, '2024-01-01',
 (SELECT id FROM users WHERE role = 'admin' LIMIT 1)),

('Overtime Allowance', 'OT_ALLOW', 'allowance', 'statutory', 'formula', NULL, NULL, NULL,
 TRUE, FALSE, TRUE, TRUE, '["hourly", "daily", "monthly"]', '["regular", "contract"]', '[]',
 'Overtime payment as per labor law (1.5x regular rate)', TRUE, '2024-01-01',
 (SELECT id FROM users WHERE role = 'admin' LIMIT 1)),

('Performance Incentive', 'PERF_INC', 'allowance', 'company_policy', 'percentage', NULL, 10.00, 'base_salary',
 TRUE, FALSE, TRUE, TRUE, '["monthly"]', '["regular"]', '[]',
 'Performance-based incentive (up to 10% of basic salary)', TRUE, '2024-01-01',
 (SELECT id FROM users WHERE role = 'admin' LIMIT 1))

ON CONFLICT (code) DO NOTHING;

-- ============================================================================
-- SUCCESS CRITERIA
-- ============================================================================
-- After successful completion:
-- ✅ Nepali calendar data populated for 2081-2083 BS
-- ✅ Payroll periods created for fiscal year 2081-82
-- ✅ Comprehensive payroll settings configured
-- ✅ Employee pay structures created for existing users
-- ✅ Default payroll components (deductions/allowances) created
-- ✅ Performance indexes created
-- ✅ Nepal-specific holidays and festivals configured
-- ✅ Labor law parameters set according to Nepal regulations
