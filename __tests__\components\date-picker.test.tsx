/**
 * Test suite for date picker components
 * Tests both NepaliDatePicker and ModalAwareNepaliDatePicker
 */

import React from 'react'
import { render, screen } from '@testing-library/react'
import '@testing-library/jest-dom'
import { NepaliDatePicker } from '@/components/ui/nepali-date-picker'
import { ModalAwareNepaliDatePicker } from '@/components/ui/modal-aware-nepali-date-picker'
import { DualCalendarInput } from '@/components/ui/dual-calendar-input'

// Mock the nepali-calendar functions
jest.mock('@/lib/nepali-calendar', () => ({
  NepaliCalendar: {
    adToBS: jest.fn(() => ({ year: 2081, month: 4, day: 15 })),
    bsToAD: jest.fn(() => new Date('2024-07-30')),
    getBSMonthName: jest.fn(() => 'Shrawan'),
  },
  getCurrentNepaliDate: jest.fn(() => ({ year: 2081, month: 4, day: 15 })),
  BSDate: {},
}))

// Mock the nepal-config
jest.mock('@/lib/nepal-config', () => ({
  nepalConfig: {
    getConfig: jest.fn(() => ({
      calendar: {
        currentBSYear: 2081,
        currentBSMonth: 4,
        currentBSDay: 15,
      },
    })),
  },
}))

// Mock the calendar component
jest.mock('@/components/ui/nepali-calendar', () => ({
  NepaliCalendarComponent: ({ onDateSelect }: { onDateSelect: (date: Date) => void }) => (
    <div data-testid="nepali-calendar">
      <button onClick={() => onDateSelect(new Date('2024-07-30'))}>
        Select Date
      </button>
    </div>
  ),
}))

describe('Date Picker Components', () => {
  describe('NepaliDatePicker', () => {
    it('renders without crashing', () => {
      render(
        <NepaliDatePicker
          value={undefined}
          onChange={() => {}}
          placeholder="Select date"
        />
      )
      
      expect(screen.getByText('Select date')).toBeInTheDocument()
    })

    it('displays both calendars when showBothCalendars is true', () => {
      render(
        <NepaliDatePicker
          value={new Date('2024-07-30')}
          onChange={() => {}}
          showBothCalendars={true}
        />
      )
      
      // Should display formatted date with both AD and BS
      expect(screen.getByRole('button')).toBeInTheDocument()
    })
  })

  describe('ModalAwareNepaliDatePicker', () => {
    it('renders without crashing', () => {
      render(
        <ModalAwareNepaliDatePicker
          value={undefined}
          onChange={() => {}}
          placeholder="Select date"
        />
      )
      
      expect(screen.getByText('Select date')).toBeInTheDocument()
    })

    it('displays current BS year when showBothCalendars is true', () => {
      render(
        <ModalAwareNepaliDatePicker
          value={undefined}
          onChange={() => {}}
          showBothCalendars={true}
        />
      )
      
      // Should display current BS year without crashing
      expect(screen.getByRole('button')).toBeInTheDocument()
    })

    it('handles getCurrentNepaliDate errors gracefully', () => {
      // Mock getCurrentNepaliDate to throw an error
      const { getCurrentNepaliDate } = require('@/lib/nepali-calendar')
      getCurrentNepaliDate.mockImplementationOnce(() => {
        throw new Error('Test error')
      })

      render(
        <ModalAwareNepaliDatePicker
          value={undefined}
          onChange={() => {}}
          showBothCalendars={true}
        />
      )
      
      // Should still render without crashing
      expect(screen.getByRole('button')).toBeInTheDocument()
    })

    it('handles config errors gracefully', () => {
      // Mock both getCurrentNepaliDate and nepalConfig to fail
      const { getCurrentNepaliDate } = require('@/lib/nepali-calendar')
      const { nepalConfig } = require('@/lib/nepal-config')
      
      getCurrentNepaliDate.mockImplementationOnce(() => {
        throw new Error('Test error')
      })
      
      nepalConfig.getConfig.mockImplementationOnce(() => {
        throw new Error('Config error')
      })

      render(
        <ModalAwareNepaliDatePicker
          value={undefined}
          onChange={() => {}}
          showBothCalendars={true}
        />
      )
      
      // Should still render with fallback year
      expect(screen.getByRole('button')).toBeInTheDocument()
    })
  })

  describe('DualCalendarInput', () => {
    it('renders without crashing', () => {
      render(
        <DualCalendarInput
          value=""
          onChange={() => {}}
          label="Test Date"
        />
      )
      
      expect(screen.getByText('Test Date')).toBeInTheDocument()
    })

    it('uses modal-aware picker when enableSmartPositioning is true', () => {
      render(
        <DualCalendarInput
          value=""
          onChange={() => {}}
          label="Test Date"
          enableSmartPositioning={true}
        />
      )
      
      expect(screen.getByText('Test Date')).toBeInTheDocument()
    })

    it('uses regular picker when enableSmartPositioning is false', () => {
      render(
        <DualCalendarInput
          value=""
          onChange={() => {}}
          label="Test Date"
          enableSmartPositioning={false}
        />
      )
      
      expect(screen.getByText('Test Date')).toBeInTheDocument()
    })
  })
})
