import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query"
import { toast } from "@/hooks/use-toast"

// Types
interface Attachment {
  id: string
  task_id: string
  filename: string
  original_filename: string
  file_path: string
  file_size: number
  mime_type: string
  uploaded_by: string
  uploaded_by_name: string
  uploaded_by_email: string
  created_at: string
}

// API functions
const attachmentApi = {
  async getTaskAttachments(taskId: string) {
    const response = await fetch(`/api/tasks/${taskId}/attachments`, {
      credentials: "include",
    })

    if (!response.ok) {
      throw new Error("Failed to fetch attachments")
    }

    return response.json()
  },

  async uploadAttachment(taskId: string, file: File) {
    const formData = new FormData()
    formData.append("file", file)

    const response = await fetch(`/api/tasks/${taskId}/attachments`, {
      method: "POST",
      credentials: "include",
      body: formData,
    })

    if (!response.ok) {
      const error = await response.json()
      throw new Error(error.error || "Failed to upload file")
    }

    return response.json()
  },

  async deleteAttachment(taskId: string, attachmentId: string) {
    const response = await fetch(`/api/tasks/${taskId}/attachments/${attachmentId}`, {
      method: "DELETE",
      credentials: "include",
    })

    if (!response.ok) {
      const error = await response.json()
      throw new Error(error.error || "Failed to delete attachment")
    }

    return response.json()
  },
}

// React Query hooks
export function useTaskAttachments(taskId: string) {
  return useQuery({
    queryKey: ["attachments", taskId],
    queryFn: () => attachmentApi.getTaskAttachments(taskId),
    enabled: !!taskId,
    staleTime: 60 * 1000, // 1 minute
  })
}

export function useUploadAttachment(taskId: string) {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: (file: File) => attachmentApi.uploadAttachment(taskId, file),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["attachments", taskId] })
      queryClient.invalidateQueries({ queryKey: ["tasks"] }) // Update attachment count
      toast({
        title: "Success",
        description: "File uploaded successfully",
      })
    },
    onError: (error: Error) => {
      toast({
        title: "Upload Failed",
        description: error.message,
        variant: "destructive",
      })
    },
  })
}

export function useDeleteAttachment(taskId: string) {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: (attachmentId: string) => attachmentApi.deleteAttachment(taskId, attachmentId),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["attachments", taskId] })
      queryClient.invalidateQueries({ queryKey: ["tasks"] }) // Update attachment count
      toast({
        title: "Success",
        description: "File deleted successfully",
      })
    },
    onError: (error: Error) => {
      toast({
        title: "Error",
        description: error.message,
        variant: "destructive",
      })
    },
  })
}

// Utility functions
export function formatFileSize(bytes: number): string {
  if (bytes === 0) return "0 Bytes"
  
  const k = 1024
  const sizes = ["Bytes", "KB", "MB", "GB"]
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + " " + sizes[i]
}

export function getFileIcon(mimeType: string | null | undefined): string {
  // Handle null, undefined, or empty mimeType
  if (!mimeType || typeof mimeType !== 'string') {
    return "📎" // Default file icon
  }

  if (mimeType.startsWith("image/")) return "🖼️"
  if (mimeType === "application/pdf") return "📄"
  if (mimeType.includes("word")) return "📝"
  if (mimeType.includes("excel") || mimeType.includes("spreadsheet")) return "📊"
  if (mimeType.includes("zip")) return "🗜️"
  if (mimeType.startsWith("text/")) return "📄"
  return "📎"
}

export function isImageFile(mimeType: string | null | undefined): boolean {
  // Handle null, undefined, or empty mimeType
  if (!mimeType || typeof mimeType !== 'string') {
    return false
  }

  return mimeType.startsWith("image/")
}
