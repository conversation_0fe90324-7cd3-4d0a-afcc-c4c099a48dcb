# Payroll Management System User Guide

## Table of Contents

1. [Getting Started](#getting-started)
2. [Admin Dashboard](#admin-dashboard)
3. [Employee Self-Service](#employee-self-service)
4. [Payroll Processing](#payroll-processing)
5. [Nepal Localization Features](#nepal-localization-features)
6. [Compliance Management](#compliance-management)
7. [Reports and Analytics](#reports-and-analytics)
8. [Troubleshooting](#troubleshooting)

## Getting Started

### System Requirements

- Modern web browser (Chrome, Firefox, Safari, Edge)
- Internet connection
- Valid user account with appropriate permissions

### Logging In

1. Navigate to the payroll system URL
2. Enter your username and password
3. Click "Sign In"
4. You'll be redirected to your dashboard based on your role

### User Roles

- **Admin**: Full system access, payroll processing, settings management
- **HR Manager**: Employee management, payroll review, compliance monitoring
- **Payroll Officer**: Payroll calculation, processing, and reporting
- **Employee**: Self-service access to personal payroll information

## Admin Dashboard

### Overview

The admin dashboard provides a comprehensive view of payroll operations:

- **Total Payroll**: Current month's total payroll amount in NPR
- **Employee Count**: Total active employees
- **Pending Approvals**: Payrolls awaiting approval
- **Processing Status**: Current payroll processing progress

### Key Features

#### Payroll Summary Cards
- Display amounts in NPR with lakhs/crores notation
- Show trends compared to previous periods
- Include fiscal year context (Bikram Sambat)

#### Compliance Alerts
- Real-time labor law compliance monitoring
- Critical violations highlighted in red
- Recommendations for resolution

#### Monthly Trends
- Visual representation of payroll trends
- Comparison across Nepali months (Shrawan, Bhadra, etc.)
- Average pay calculations

### Navigation

Use the main navigation menu to access:
- **Overview**: Dashboard home
- **Processing**: Payroll calculation and processing
- **Compliance**: Labor law compliance monitoring
- **Reports**: Payroll reports and analytics
- **Settings**: System configuration

## Employee Self-Service

### Accessing Your Payroll Information

1. Log in with your employee credentials
2. Navigate to "My Payroll" section
3. View current and historical payroll data

### Features Available

#### Current Month Overview
- **Gross Pay**: Total earnings before deductions
- **Deductions**: Taxes, PF, insurance, etc.
- **Net Pay**: Final take-home amount
- **Attendance Summary**: Working days, overtime hours

#### Payroll History
- View past payroll records
- Filter by date range or fiscal year
- Download payslips in PDF format

#### Annual Summary
- Year-to-date earnings and deductions
- Performance trends and growth
- Tax information for filing

### Downloading Payslips

1. Go to "Payroll History" tab
2. Find the desired pay period
3. Click the "Download" button
4. PDF will be generated with:
   - Employee details
   - Pay period (both AD and BS dates)
   - Detailed breakdown of earnings and deductions
   - Company letterhead and signatures

## Payroll Processing

### Setting Up Payroll Periods

1. Navigate to **Admin > Payroll > Processing**
2. Select fiscal year and period type
3. Choose payroll period (monthly recommended)
4. Configure working days and holidays

### Processing Workflow

#### Step 1: Preparation
- Ensure attendance data is complete
- Verify employee information is up-to-date
- Check for any pending leave applications

#### Step 2: Calculation
1. Click "Calculate Payroll" button
2. Select employees to process (or process all)
3. Review calculation parameters:
   - Base salary/hourly rates
   - Overtime calculations
   - Allowances and deductions
   - Tax calculations

#### Step 3: Review and Approval
- Review calculated amounts
- Check compliance warnings
- Approve individual payrolls or bulk approve
- Address any errors or violations

#### Step 4: Processing
- Start payroll processing
- Monitor progress in real-time
- Handle any processing errors
- Generate final reports

### Bulk Processing

For large organizations:
1. Use "Bulk Process" feature
2. Select employee groups or departments
3. Set processing parameters
4. Monitor batch processing status
5. Review completion reports

### Error Handling

Common errors and solutions:
- **Missing Attendance**: Update attendance records
- **Compliance Violations**: Adjust working hours or overtime
- **Invalid Employee Data**: Update employee information
- **Calculation Errors**: Review pay structure settings

## Nepal Localization Features

### Bikram Sambat Calendar Integration

#### Date Display
- All dates shown in both AD and BS formats
- Fiscal year follows Nepal's Shrawan-Ashadh cycle
- Month names in both English and Nepali

#### Fiscal Year Management
- Automatic fiscal year detection
- Quarter and month breakdowns
- Working day calculations excluding Nepal holidays

### NPR Currency Formatting

#### Indian Numbering System
- Amounts displayed with lakhs and crores notation
- Example: रू 15,00,000 (15 lakhs)
- Compact notation: 1.5L, 2.3Cr

#### Currency Features
- Automatic currency conversion to words
- Breakdown into crores, lakhs, thousands
- Support for both English and Nepali number words

### Holiday Calendar

#### Nepal Public Holidays
- Comprehensive list of Nepal festivals and holidays
- Automatic working day calculations
- Holiday impact on payroll processing

#### Major Festivals Included
- Dashain (multiple days)
- Tihar/Deepawali
- Holi
- Buddha Jayanti
- Constitution Day
- And many more...

### Labor Law Compliance

#### Nepal Labor Act 2074 Compliance
- Maximum 8 hours regular work per day
- Maximum 4 hours overtime per day
- Minimum wage enforcement (NPR 17,300)
- Saturday weekly off

#### Overtime Calculations
- First 4 hours: 1.5x rate
- Next 4 hours: 2.0x rate
- Beyond 8 hours: 2.5x rate

## Compliance Management

### Real-time Monitoring

The system continuously monitors:
- Daily working hours
- Weekly working hours
- Overtime limits
- Minimum wage compliance
- Leave entitlements

### Compliance Dashboard

#### Overview Metrics
- Overall compliance percentage
- Number of compliant employees
- Active violations and warnings
- Compliance trends over time

#### Violation Categories
- **Critical**: Immediate action required
- **High**: Address within 24 hours
- **Medium**: Address within week
- **Low**: Monitor and improve

### Generating Compliance Reports

1. Navigate to **Compliance > Reports**
2. Select date range and employee groups
3. Choose report type:
   - Individual employee compliance
   - Department-wise compliance
   - Violation summary
   - Trend analysis
4. Generate and download reports

### Remediation Actions

For compliance violations:
1. Review violation details
2. Implement recommended actions
3. Update employee schedules if needed
4. Monitor for improvement
5. Document corrective measures

## Reports and Analytics

### Available Reports

#### Payroll Reports
- **Monthly Payroll Summary**: Total payroll by department
- **Employee Payroll Details**: Individual payroll breakdowns
- **Overtime Analysis**: Overtime trends and costs
- **Deduction Summary**: Tax and benefit deductions

#### Compliance Reports
- **Labor Law Compliance**: Violation tracking and trends
- **Working Hours Analysis**: Hours worked vs. limits
- **Minimum Wage Compliance**: Wage level analysis

#### Financial Reports
- **Cost Center Analysis**: Payroll costs by department
- **Budget vs. Actual**: Payroll budget tracking
- **Year-over-Year Comparison**: Annual payroll trends

### Generating Reports

1. Go to **Reports** section
2. Select report type
3. Configure parameters:
   - Date range (AD or BS dates)
   - Employee filters
   - Department selection
   - Output format (PDF, Excel, CSV)
4. Click "Generate Report"
5. Download when ready

### Scheduled Reports

Set up automatic report generation:
1. Configure report parameters
2. Set schedule (daily, weekly, monthly)
3. Add email recipients
4. Reports will be automatically generated and emailed

## Troubleshooting

### Common Issues

#### Login Problems
- **Forgot Password**: Use "Forgot Password" link
- **Account Locked**: Contact system administrator
- **Browser Issues**: Clear cache and cookies

#### Payroll Calculation Issues
- **Incorrect Amounts**: Check employee pay structure
- **Missing Overtime**: Verify attendance data
- **Wrong Deductions**: Review deduction settings

#### Performance Issues
- **Slow Loading**: Check internet connection
- **Timeout Errors**: Try refreshing the page
- **Browser Compatibility**: Use supported browsers

### Getting Help

#### Support Channels
- **Help Desk**: Submit support tickets
- **User Manual**: Comprehensive documentation
- **Training Videos**: Step-by-step tutorials
- **FAQ Section**: Common questions and answers

#### Contact Information
- **Email**: <EMAIL>
- **Phone**: +977-1-XXXXXXX
- **Business Hours**: Sunday-Friday, 10 AM - 6 PM

### System Maintenance

#### Scheduled Maintenance
- Usually performed during off-hours
- Advance notification provided
- Temporary service interruptions possible

#### Data Backup
- Automatic daily backups
- Data retention for 7 years
- Disaster recovery procedures in place

### Best Practices

#### For Administrators
- Regular data backups
- Monitor system performance
- Keep software updated
- Train users properly

#### For Users
- Log out when finished
- Keep passwords secure
- Report issues promptly
- Follow company policies

#### For Payroll Processing
- Verify data before processing
- Review calculations carefully
- Maintain audit trails
- Document any manual adjustments

## Security and Privacy

### Data Protection
- All data encrypted in transit and at rest
- Regular security audits
- Compliance with data protection laws
- Access controls and user permissions

### User Responsibilities
- Keep login credentials secure
- Report suspicious activities
- Follow company IT policies
- Protect sensitive information

### System Security Features
- Multi-factor authentication
- Session timeouts
- Audit logging
- Role-based access control

---

For additional support or questions not covered in this guide, please contact the system administrator or help desk.
