import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query"
import { toast } from "@/hooks/use-toast"

// Types
interface Project {
  id: string
  name: string
  description?: string
  color: string
  created_by: string
  created_by_name: string
  is_active: boolean
  created_at: string
  updated_at: string
  task_count: number
  completed_tasks: number
  active_tasks: number
}

interface CreateProjectData {
  name: string
  description?: string
  color?: string
}

interface UpdateProjectData extends Partial<CreateProjectData> {
  is_active?: boolean
}

// API functions
const projectApi = {
  async getProjects() {
    const response = await fetch("/api/projects", {
      credentials: "include",
    })

    if (!response.ok) {
      throw new Error("Failed to fetch projects")
    }

    return response.json()
  },

  async getProject(id: string) {
    const response = await fetch(`/api/projects/${id}`, {
      credentials: "include",
    })

    if (!response.ok) {
      throw new Error("Failed to fetch project")
    }

    return response.json()
  },

  async createProject(data: CreateProjectData) {
    const response = await fetch("/api/projects", {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      credentials: "include",
      body: JSON.stringify(data),
    })

    if (!response.ok) {
      const error = await response.json()
      throw new Error(error.error || "Failed to create project")
    }

    return response.json()
  },

  async updateProject(id: string, data: UpdateProjectData) {
    const response = await fetch(`/api/projects/${id}`, {
      method: "PUT",
      headers: {
        "Content-Type": "application/json",
      },
      credentials: "include",
      body: JSON.stringify(data),
    })

    if (!response.ok) {
      const error = await response.json()
      throw new Error(error.error || "Failed to update project")
    }

    return response.json()
  },

  async deleteProject(id: string) {
    const response = await fetch(`/api/projects/${id}`, {
      method: "DELETE",
      credentials: "include",
    })

    if (!response.ok) {
      const error = await response.json()
      throw new Error(error.error || "Failed to delete project")
    }

    return response.json()
  },
}

// React Query hooks
export function useProjects() {
  return useQuery({
    queryKey: ["projects"],
    queryFn: () => projectApi.getProjects(),
    staleTime: 5 * 60 * 1000, // 5 minutes
  })
}

export function useProject(id: string) {
  return useQuery({
    queryKey: ["project", id],
    queryFn: () => projectApi.getProject(id),
    enabled: !!id,
  })
}

export function useCreateProject() {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: projectApi.createProject,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["projects"] })
      queryClient.invalidateQueries({ queryKey: ["tasks"] }) // Refresh tasks to show new project
      toast({
        title: "Success",
        description: "Project created successfully",
      })
    },
    onError: (error: Error) => {
      toast({
        title: "Error",
        description: error.message,
        variant: "destructive",
      })
    },
  })
}

export function useUpdateProject() {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: ({ id, data }: { id: string; data: UpdateProjectData }) =>
      projectApi.updateProject(id, data),
    onSuccess: (response, { id }) => {
      queryClient.invalidateQueries({ queryKey: ["projects"] })
      queryClient.invalidateQueries({ queryKey: ["project", id] })
      queryClient.invalidateQueries({ queryKey: ["tasks"] }) // Refresh tasks to show updated project
      toast({
        title: "Success",
        description: "Project updated successfully",
      })
    },
    onError: (error: Error) => {
      toast({
        title: "Error",
        description: error.message,
        variant: "destructive",
      })
    },
  })
}

export function useDeleteProject() {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: projectApi.deleteProject,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["projects"] })
      queryClient.invalidateQueries({ queryKey: ["tasks"] }) // Refresh tasks to remove deleted project
      toast({
        title: "Success",
        description: "Project deleted successfully",
      })
    },
    onError: (error: Error) => {
      toast({
        title: "Error",
        description: error.message,
        variant: "destructive",
      })
    },
  })
}

// Utility hooks
export function useProjectOptions() {
  const { data: projectsResponse } = useProjects()
  
  const projects = projectsResponse?.data || []
  
  return projects.map((project: Project) => ({
    value: project.id,
    label: project.name,
    color: project.color,
  }))
}
