#!/usr/bin/env node

// Quick script to create missing task management tables
require('dotenv').config({ path: '.env.local' });
const { neon } = require('@neondatabase/serverless');

async function createTables() {
  console.log('Creating task management tables...');
  
  const sql = neon(process.env.DATABASE_URL);
  
  try {
    // Test connection
    await sql`SELECT 1`;
    console.log('✅ Connected to database');
    
    // Create task_projects table
    await sql`
      CREATE TABLE IF NOT EXISTS task_projects (
        id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
        name VARCHAR(255) NOT NULL,
        description TEXT,
        color VARCHAR(7) DEFAULT '#3B82F6',
        created_by UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
        is_active BOOLEAN DEFAULT true,
        created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
        updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
      )
    `;
    console.log('✅ Created task_projects table');
    
    // Create tasks table
    await sql`
      CREATE TABLE IF NOT EXISTS tasks (
        id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
        title VARCHAR(255) NOT NULL,
        description TEXT,
        assigned_to UUID REFERENCES users(id) ON DELETE SET NULL,
        assigned_by UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
        status VARCHAR(20) NOT NULL DEFAULT 'todo' CHECK (status IN ('todo', 'in_progress', 'completed', 'cancelled')),
        priority VARCHAR(10) NOT NULL DEFAULT 'medium' CHECK (priority IN ('low', 'medium', 'high', 'urgent')),
        due_date TIMESTAMP WITH TIME ZONE,
        project_id UUID REFERENCES task_projects(id) ON DELETE SET NULL,
        estimated_hours DECIMAL(5,2),
        position INTEGER DEFAULT 0,
        completed_at TIMESTAMP WITH TIME ZONE,
        created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
        updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
      )
    `;
    console.log('✅ Created tasks table');
    
    // Create task_comments table
    await sql`
      CREATE TABLE IF NOT EXISTS task_comments (
        id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
        task_id UUID NOT NULL REFERENCES tasks(id) ON DELETE CASCADE,
        user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
        content TEXT NOT NULL,
        created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
        updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
      )
    `;
    console.log('✅ Created task_comments table');
    
    // Create task_attachments table
    await sql`
      CREATE TABLE IF NOT EXISTS task_attachments (
        id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
        task_id UUID NOT NULL REFERENCES tasks(id) ON DELETE CASCADE,
        user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
        file_name VARCHAR(255) NOT NULL,
        file_size BIGINT NOT NULL,
        file_type VARCHAR(100) NOT NULL,
        file_path TEXT NOT NULL,
        created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
      )
    `;
    console.log('✅ Created task_attachments table');
    
    // Create task_time_logs table
    await sql`
      CREATE TABLE IF NOT EXISTS task_time_logs (
        id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
        task_id UUID NOT NULL REFERENCES tasks(id) ON DELETE CASCADE,
        user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
        start_time TIMESTAMP WITH TIME ZONE NOT NULL,
        end_time TIMESTAMP WITH TIME ZONE,
        duration_minutes INTEGER,
        description TEXT,
        created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
      )
    `;
    console.log('✅ Created task_time_logs table');
    
    // Create indexes
    await sql`CREATE INDEX IF NOT EXISTS idx_tasks_assigned_to ON tasks(assigned_to)`;
    await sql`CREATE INDEX IF NOT EXISTS idx_tasks_status ON tasks(status)`;
    await sql`CREATE INDEX IF NOT EXISTS idx_tasks_project_id ON tasks(project_id)`;
    await sql`CREATE INDEX IF NOT EXISTS idx_task_comments_task_id ON task_comments(task_id)`;
    await sql`CREATE INDEX IF NOT EXISTS idx_task_attachments_task_id ON task_attachments(task_id)`;
    console.log('✅ Created indexes');
    
    // Insert sample project
    const adminUser = await sql`SELECT id FROM users WHERE role = 'admin' LIMIT 1`;
    if (adminUser.length > 0) {
      await sql`
        INSERT INTO task_projects (name, description, color, created_by) 
        VALUES ('Default Project', 'Default project for general tasks', '#6366F1', ${adminUser[0].id})
        ON CONFLICT DO NOTHING
      `;
      console.log('✅ Created sample project');
    }
    
    // Verify tables
    const tables = await sql`
      SELECT table_name 
      FROM information_schema.tables 
      WHERE table_schema = 'public' 
      AND table_name LIKE 'task%'
      ORDER BY table_name
    `;
    
    console.log('\n📋 Task management tables:');
    tables.forEach(t => console.log(`   - ${t.table_name}`));
    
    console.log('\n🎉 All tables created successfully!');
    
  } catch (error) {
    console.error('❌ Error:', error.message);
    process.exit(1);
  }
}

createTables();
