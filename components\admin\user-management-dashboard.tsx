"use client"

import React, { useState, useEffect } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { Switch } from "@/components/ui/switch"
import { useAuth, type User } from "@/components/auth-provider"
import { Search, Plus, Edit, Trash2, Users, Building, UserCheck, UserX, RefreshCw, Eye, Loader2 } from "lucide-react"
import { FileUpload } from "@/components/ui/file-upload"
import { useRouter } from "next/navigation"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"

interface Department {
  id: string
  name: string
  description?: string
  manager_name?: string
  budget?: number
  location?: string
  is_active: boolean
}

export function UserManagementDashboard() {
  const router = useRouter()
  const { user: currentUser } = useAuth()
  const [users, setUsers] = useState<User[]>([])
  const [departments, setDepartments] = useState<Department[]>([])
  const [loading, setLoading] = useState(true)
  const [searchTerm, setSearchTerm] = useState("")
  const [selectedDepartment, setSelectedDepartment] = useState<string>("all")
  const [selectedRole, setSelectedRole] = useState<string>("all")
  const [showCreateUser, setShowCreateUser] = useState(false)
  const [showCreateDepartment, setShowCreateDepartment] = useState(false)
  const [editingUser, setEditingUser] = useState<User | null>(null)

  // Loading states for different operations
  const [creatingUser, setCreatingUser] = useState(false)
  const [updatingUser, setUpdatingUser] = useState(false)
  const [deletingUser, setDeletingUser] = useState<string | null>(null)
  const [togglingUserStatus, setTogglingUserStatus] = useState<string | null>(null)
  const [creatingDepartment, setCreatingDepartment] = useState(false)
  const [uploadingFiles, setUploadingFiles] = useState<Record<string, boolean>>({})
  const [userProfileImages, setUserProfileImages] = useState<Record<string, string>>({})

  // Form states
  const [newUser, setNewUser] = useState({
    // Basic Information
    email: "",
    full_name: "",
    role: "staff",
    employee_id: "",

    // Employment Details
    department: "",
    position: "",
    employment_type: "full_time",
    employment_status: "active",
    hire_date: "",
    termination_date: "",

    // Contact Information
    phone: "",
    emergency_contact_name: "",
    emergency_contact_phone: "",

    // Personal Information
    date_of_birth: "",
    gender: "",
    marital_status: "",
    nationality: "",

    // Financial Information
    salary: "",
    salary_currency: "NPR",
    bank_name: "",
    bank_branch: "",
    bank_account_number: "",

    // Government IDs
    citizenship_number: "",
    pan_number: "",
  })

  const [editUser, setEditUser] = useState({
    email: "",
    full_name: "",
    role: "staff",
    employee_id: "",
    department: "",
    position: "",
    phone: "",
    salary: "",
    employment_type: "full_time",
  })

  // File upload states
  const [uploadFiles, setUploadFiles] = useState<{
    profile_picture?: File
    document?: File
    contract?: File
    signature?: File
  }>({})
  const [userFiles, setUserFiles] = useState<Record<string, any[]>>({})
  const [loadingFiles, setLoadingFiles] = useState(false)

  const [newDepartment, setNewDepartment] = useState({
    name: "",
    description: "",
    location: "",
    budget: "",
  })

  useEffect(() => {
    fetchUsers()
    fetchDepartments()
  }, [])

  const fetchUsers = async () => {
    try {
      const params = new URLSearchParams()
      if (searchTerm) params.append("search", searchTerm)
      if (selectedDepartment !== "all") params.append("department", selectedDepartment)
      if (selectedRole !== "all") params.append("role", selectedRole)

      const response = await fetch(`/api/admin/users?${params}`)
      const data = await response.json()
      
      if (data.success) {
        setUsers(data.users)
        // Fetch profile images after users are loaded
        setTimeout(() => fetchUserProfileImages(data.users), 100)
      }
    } catch (error) {
      console.error("Error fetching users:", error)
    } finally {
      setLoading(false)
    }
  }

  const fetchDepartments = async () => {
    try {
      const response = await fetch("/api/admin/departments")
      const data = await response.json()
      
      if (data.success) {
        setDepartments(data.departments)
      }
    } catch (error) {
      console.error("Error fetching departments:", error)
    }
  }

  const handleCreateUser = async (e: React.FormEvent) => {
    e.preventDefault()

    if (creatingUser) return // Prevent multiple submissions

    setCreatingUser(true)
    try {
      const response = await fetch("/api/admin/users", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({
          ...newUser,
          salary: newUser.salary ? parseFloat(newUser.salary) : null,
        }),
      })

      const data = await response.json()

      if (data.success) {
        const createdUser = data.user

        // Upload files if any were selected
        const fileUploadPromises = []
        for (const [fileType, file] of Object.entries(uploadFiles)) {
          if (file) {
            fileUploadPromises.push(handleFileUpload(createdUser.id, fileType, file))
          }
        }

        if (fileUploadPromises.length > 0) {
          await Promise.all(fileUploadPromises)
        }

        setShowCreateUser(false)
        setNewUser({
          // Basic Information
          email: "",
          full_name: "",
          role: "staff",
          employee_id: "",

          // Employment Details
          department: "",
          position: "",
          employment_type: "full_time",
          employment_status: "active",
          hire_date: "",
          termination_date: "",

          // Contact Information
          phone: "",
          emergency_contact_name: "",
          emergency_contact_phone: "",

          // Personal Information
          date_of_birth: "",
          gender: "",
          marital_status: "",
          nationality: "",

          // Financial Information
          salary: "",
          salary_currency: "NPR",
          bank_name: "",
          bank_branch: "",
          bank_account_number: "",

          // Government IDs
          citizenship_number: "",
          pan_number: "",
        })
        setUploadFiles({}) // Clear file uploads
        fetchUsers()
      } else {
        alert(data.error || "Failed to create user")
      }
    } catch (error) {
      console.error("Error creating user:", error)
      alert("Failed to create user")
    } finally {
      setCreatingUser(false)
    }
  }

  const handleEditUser = async (e: React.FormEvent) => {
    e.preventDefault()
    if (!editingUser || updatingUser) return

    setUpdatingUser(true)
    try {
      const response = await fetch(`/api/admin/users/${editingUser.id}`, {
        method: "PUT",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({
          ...editUser,
          salary: editUser.salary ? parseFloat(editUser.salary) : null,
        }),
      })

      if (response.ok) {
        setEditingUser(null)
        setEditUser({
          email: "",
          full_name: "",
          role: "staff",
          employee_id: "",
          department: "",
          position: "",
          phone: "",
          salary: "",
          employment_type: "full_time",
        })
        fetchUsers()
        alert("User updated successfully!")
      } else {
        const data = await response.json()
        alert(data.error || "Failed to update user")
      }
    } catch (error) {
      console.error("Error updating user:", error)
      alert("Failed to update user")
    } finally {
      setUpdatingUser(false)
    }
  }

  const handleCreateDepartment = async (e: React.FormEvent) => {
    e.preventDefault()

    if (creatingDepartment) return

    setCreatingDepartment(true)
    try {
      const response = await fetch("/api/admin/departments", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({
          ...newDepartment,
          budget: newDepartment.budget ? parseFloat(newDepartment.budget) : null,
        }),
      })

      const data = await response.json()
      
      if (data.success) {
        setShowCreateDepartment(false)
        setNewDepartment({
          name: "",
          description: "",
          location: "",
          budget: "",
        })
        fetchDepartments()
      } else {
        alert(data.error || "Failed to create department")
      }
    } catch (error) {
      console.error("Error creating department:", error)
      alert("Failed to create department")
    } finally {
      setCreatingDepartment(false)
    }
  }

  const handleToggleUserStatus = async (userId: string, currentStatus: boolean) => {
    const action = currentStatus ? "deactivate" : "activate"
    if (!confirm(`Are you sure you want to ${action} this user?`)) return

    if (togglingUserStatus === userId) return

    setTogglingUserStatus(userId)
    try {
      const response = await fetch(`/api/admin/users/${userId}`, {
        method: "PATCH",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({ is_active: !currentStatus }),
      })

      if (response.ok) {
        fetchUsers()
        const data = await response.json()
        alert(data.message || `User ${action}d successfully`)
      } else {
        const data = await response.json()
        alert(data.error || `Failed to ${action} user`)
      }
    } catch (error) {
      console.error(`Error ${action}ing user:`, error)
      alert(`Failed to ${action} user`)
    } finally {
      setTogglingUserStatus(null)
    }
  }

  const handleReactivateUser = async (userId: string) => {
    if (!confirm("Are you sure you want to reactivate this user? This will restore their access and set their employment status to active.")) return

    try {
      const response = await fetch(`/api/admin/users/${userId}`, {
        method: "PATCH",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({
          is_active: true,
          employment_status: 'active',
          termination_date: null
        }),
      })

      if (response.ok) {
        fetchUsers()
        const data = await response.json()
        alert(data.message || "User reactivated successfully")
      } else {
        const data = await response.json()
        alert(data.error || "Failed to reactivate user")
      }
    } catch (error) {
      console.error("Error reactivating user:", error)
      alert("Failed to reactivate user")
    }
  }

  const handleHardDeleteUser = async (userId: string, userName: string) => {
    if (deletingUser === userId) return

    const confirmMessage = `⚠️ PERMANENT DELETE WARNING ⚠️

This will PERMANENTLY DELETE the user "${userName}" and ALL associated data including:
• User profile and employment information
• All uploaded files (profile pictures, documents, contracts, signatures)
• Attendance records and payroll history
• System access logs and activity history

This action CANNOT be undone!

Type "DELETE" to confirm permanent deletion:`

    const confirmation = prompt(confirmMessage)

    if (confirmation !== "DELETE") {
      alert("Deletion cancelled. User was not deleted.")
      return
    }

    setDeletingUser(userId)
    try {
      const response = await fetch(`/api/admin/users/${userId}/hard-delete`, {
        method: "DELETE",
      })

      if (response.ok) {
        fetchUsers()
        const data = await response.json()
        alert(data.message || "User permanently deleted")
      } else {
        const data = await response.json()
        alert(data.error || "Failed to delete user")
      }
    } catch (error) {
      console.error("Error deleting user:", error)
      alert("Failed to delete user")
    } finally {
      setDeletingUser(null)
    }
  }

  const handleViewUserDetails = (userId: string) => {
    router.push(`/admin/users/${userId}`)
  }

  const getInitials = (name: string) => {
    return name
      .split(' ')
      .map(word => word.charAt(0))
      .join('')
      .toUpperCase()
      .slice(0, 2)
  }

  const fetchUserProfileImages = async (userList: User[] = users) => {
    try {
      const imagePromises = userList.map(async (user) => {
        try {
          const response = await fetch(`/api/admin/users/${user.id}/files`)
          if (response.ok) {
            const data = await response.json()
            const profilePicture = data.files.find((file: any) => file.file_type === 'profile_picture')
            return {
              userId: user.id,
              imageUrl: profilePicture ? `/api/admin/files/${profilePicture.id}` : null
            }
          }
        } catch (error) {
          console.error(`Error fetching profile image for user ${user.id}:`, error)
        }
        return { userId: user.id, imageUrl: null }
      })

      const results = await Promise.all(imagePromises)
      const imageMap: Record<string, string> = {}
      results.forEach(result => {
        if (result.imageUrl) {
          imageMap[result.userId] = result.imageUrl
        }
      })
      setUserProfileImages(imageMap)
    } catch (error) {
      console.error("Error fetching user profile images:", error)
    }
  }

  // File management functions
  const fetchUserFiles = async (userId: string) => {
    try {
      setLoadingFiles(true)
      const response = await fetch(`/api/admin/users/${userId}/files`)
      if (response.ok) {
        const data = await response.json()
        setUserFiles(prev => ({ ...prev, [userId]: data.files }))
      }
    } catch (error) {
      console.error("Error fetching user files:", error)
    } finally {
      setLoadingFiles(false)
    }
  }

  const handleFileUpload = async (userId: string, fileType: string, file: File) => {
    try {
      const formData = new FormData()
      formData.append('file', file)
      formData.append('fileType', fileType)

      const response = await fetch(`/api/admin/users/${userId}/files`, {
        method: 'POST',
        body: formData
      })

      if (response.ok) {
        const data = await response.json()
        alert(data.message || "File uploaded successfully")
        fetchUserFiles(userId) // Refresh files list
        // Clear the uploaded file from state
        setUploadFiles(prev => ({ ...prev, [fileType]: undefined }))
      } else {
        const data = await response.json()
        alert(data.error || "Failed to upload file")
      }
    } catch (error) {
      console.error("Error uploading file:", error)
      alert("Failed to upload file")
    }
  }

  const handleFileDelete = async (fileId: string, userId: string) => {
    if (!confirm("Are you sure you want to delete this file?")) return

    try {
      const response = await fetch(`/api/admin/files/${fileId}`, {
        method: 'DELETE'
      })

      if (response.ok) {
        const data = await response.json()
        alert(data.message || "File deleted successfully")
        fetchUserFiles(userId) // Refresh files list
      } else {
        const data = await response.json()
        alert(data.error || "Failed to delete file")
      }
    } catch (error) {
      console.error("Error deleting file:", error)
      alert("Failed to delete file")
    }
  }

  useEffect(() => {
    const debounceTimer = setTimeout(() => {
      fetchUsers()
    }, 300)

    return () => clearTimeout(debounceTimer)
  }, [searchTerm, selectedDepartment, selectedRole])

  const getRoleBadgeColor = (role: string) => {
    switch (role) {
      case "admin": return "bg-red-100 text-red-800"
      case "hr_manager": return "bg-blue-100 text-blue-800"
      case "manager": return "bg-green-100 text-green-800"
      default: return "bg-gray-100 text-gray-800"
    }
  }

  const getStatusBadgeColor = (isActive: boolean) => {
    return isActive ? "bg-green-100 text-green-800" : "bg-red-100 text-red-800"
  }

  if (!currentUser || !["admin", "hr_manager"].includes(currentUser.role)) {
    return <div>Access denied. Admin or HR Manager role required.</div>
  }

  return (
    <div className="space-y-6">
      {/* Header Section */}
      <div className="flex flex-col space-y-4 lg:flex-row lg:items-center lg:justify-between lg:space-y-0">
        <div>
          <h1 className="text-2xl lg:text-3xl font-bold">User Management</h1>
          <p className="text-muted-foreground">Manage employees, departments, and user accounts</p>
        </div>
      </div>

      {/* Secondary Actions - Below main content on mobile */}
      <div className="flex flex-wrap items-center gap-2 justify-end border-b pb-4">
        <Dialog open={showCreateDepartment} onOpenChange={setShowCreateDepartment}>
          <DialogTrigger asChild>
            <Button variant="outline" size="sm" className="text-xs sm:text-sm">
              <Building className="mr-1 sm:mr-2 h-3 w-3 sm:h-4 sm:w-4" />
              <span className="hidden sm:inline">Add Department</span>
              <span className="sm:hidden">Dept</span>
            </Button>
          </DialogTrigger>
            <DialogContent>
              <DialogHeader>
                <DialogTitle>Create New Department</DialogTitle>
                <DialogDescription>Add a new department to the organization</DialogDescription>
              </DialogHeader>
              <form onSubmit={handleCreateDepartment} className="space-y-4">
                <div>
                  <Label htmlFor="dept-name">Department Name *</Label>
                  <Input
                    id="dept-name"
                    value={newDepartment.name}
                    onChange={(e) => setNewDepartment({ ...newDepartment, name: e.target.value })}
                    required
                  />
                </div>
                <div>
                  <Label htmlFor="dept-description">Description</Label>
                  <Textarea
                    id="dept-description"
                    value={newDepartment.description}
                    onChange={(e) => setNewDepartment({ ...newDepartment, description: e.target.value })}
                  />
                </div>
                <div>
                  <Label htmlFor="dept-location">Location</Label>
                  <Input
                    id="dept-location"
                    value={newDepartment.location}
                    onChange={(e) => setNewDepartment({ ...newDepartment, location: e.target.value })}
                  />
                </div>
                <div>
                  <Label htmlFor="dept-budget">Budget (NPR)</Label>
                  <Input
                    id="dept-budget"
                    type="number"
                    value={newDepartment.budget}
                    onChange={(e) => setNewDepartment({ ...newDepartment, budget: e.target.value })}
                  />
                </div>
                <Button type="submit" className="w-full" disabled={creatingDepartment}>
                  {creatingDepartment ? (
                    <>
                      <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                      Creating Department...
                    </>
                  ) : (
                    "Create Department"
                  )}
                </Button>
              </form>
            </DialogContent>
          </Dialog>

          <Dialog open={showCreateUser} onOpenChange={setShowCreateUser}>
          <DialogTrigger asChild>
            <Button size="sm" className="text-xs sm:text-sm">
              <Plus className="mr-1 sm:mr-2 h-3 w-3 sm:h-4 sm:w-4" />
              <span className="hidden sm:inline">Add User</span>
              <span className="sm:hidden">Add</span>
            </Button>
          </DialogTrigger>
            <DialogContent className="max-w-4xl max-h-[90vh] overflow-hidden flex flex-col">
              <DialogHeader className="flex-shrink-0">
                <DialogTitle>Create New User</DialogTitle>
                <DialogDescription>Add a new employee to the system. Employee ID will be auto-generated.</DialogDescription>
              </DialogHeader>
              <div className="flex-1 overflow-y-auto pr-2">
                <form onSubmit={handleCreateUser} className="space-y-6 pb-4">
                  {/* Basic Information Section */}
                  <div className="space-y-4">
                    <h3 className="text-lg font-medium border-b pb-2">Basic Information</h3>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div>
                        <Label htmlFor="email">Email *</Label>
                        <Input
                          id="email"
                          type="email"
                          value={newUser.email}
                          onChange={(e) => setNewUser({ ...newUser, email: e.target.value })}
                          required
                        />
                      </div>
                      <div>
                        <Label htmlFor="full_name">Full Name *</Label>
                        <Input
                          id="full_name"
                          value={newUser.full_name}
                          onChange={(e) => setNewUser({ ...newUser, full_name: e.target.value })}
                          required
                        />
                      </div>
                      <div>
                        <Label htmlFor="role">Role *</Label>
                        <Select value={newUser.role} onValueChange={(value) => setNewUser({ ...newUser, role: value })}>
                          <SelectTrigger>
                            <SelectValue />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="staff">Staff</SelectItem>
                            <SelectItem value="manager">Manager</SelectItem>
                            <SelectItem value="hr_manager">HR Manager</SelectItem>
                            {currentUser.role === "admin" && <SelectItem value="admin">Admin</SelectItem>}
                          </SelectContent>
                        </Select>
                      </div>
                      <div>
                        <Label htmlFor="phone">Phone</Label>
                        <Input
                          id="phone"
                          value={newUser.phone}
                          onChange={(e) => setNewUser({ ...newUser, phone: e.target.value })}
                        />
                      </div>
                    </div>
                  </div>

                  {/* Employment Details Section */}
                  <div className="space-y-4">
                    <h3 className="text-lg font-medium border-b pb-2">Employment Details</h3>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div>
                        <Label htmlFor="department">Department</Label>
                        <Select value={newUser.department} onValueChange={(value) => setNewUser({ ...newUser, department: value })}>
                          <SelectTrigger>
                            <SelectValue placeholder="Select department" />
                          </SelectTrigger>
                          <SelectContent>
                            {departments.map((dept) => (
                              <SelectItem key={dept.id} value={dept.name}>{dept.name}</SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                      </div>
                      <div>
                        <Label htmlFor="position">Position</Label>
                        <Input
                          id="position"
                          value={newUser.position}
                          onChange={(e) => setNewUser({ ...newUser, position: e.target.value })}
                        />
                      </div>
                      <div>
                        <Label htmlFor="employment_type">Employment Type</Label>
                        <Select value={newUser.employment_type} onValueChange={(value) => setNewUser({ ...newUser, employment_type: value })}>
                          <SelectTrigger>
                            <SelectValue />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="full_time">Full Time</SelectItem>
                            <SelectItem value="part_time">Part Time</SelectItem>
                            <SelectItem value="contract">Contract</SelectItem>
                            <SelectItem value="intern">Intern</SelectItem>
                          </SelectContent>
                        </Select>
                      </div>
                      <div>
                        <Label htmlFor="employment_status">Employment Status</Label>
                        <Select value={newUser.employment_status} onValueChange={(value) => setNewUser({ ...newUser, employment_status: value })}>
                          <SelectTrigger>
                            <SelectValue />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="active">Active</SelectItem>
                            <SelectItem value="probation">Probation</SelectItem>
                            <SelectItem value="terminated">Terminated</SelectItem>
                            <SelectItem value="resigned">Resigned</SelectItem>
                          </SelectContent>
                        </Select>
                      </div>
                      <div>
                        <Label htmlFor="hire_date">Hire Date</Label>
                        <Input
                          id="hire_date"
                          type="date"
                          value={newUser.hire_date}
                          onChange={(e) => setNewUser({ ...newUser, hire_date: e.target.value })}
                        />
                      </div>
                      <div>
                        <Label htmlFor="termination_date">Termination Date (if applicable)</Label>
                        <Input
                          id="termination_date"
                          type="date"
                          value={newUser.termination_date}
                          onChange={(e) => setNewUser({ ...newUser, termination_date: e.target.value })}
                        />
                      </div>
                    </div>
                  </div>

                  {/* Personal Information Section */}
                  <div className="space-y-4">
                    <h3 className="text-lg font-medium border-b pb-2">Personal Information</h3>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div>
                        <Label htmlFor="date_of_birth">Date of Birth</Label>
                        <Input
                          id="date_of_birth"
                          type="date"
                          value={newUser.date_of_birth}
                          onChange={(e) => setNewUser({ ...newUser, date_of_birth: e.target.value })}
                        />
                      </div>
                      <div>
                        <Label htmlFor="gender">Gender</Label>
                        <Select value={newUser.gender} onValueChange={(value) => setNewUser({ ...newUser, gender: value })}>
                          <SelectTrigger>
                            <SelectValue placeholder="Select gender" />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="male">Male</SelectItem>
                            <SelectItem value="female">Female</SelectItem>
                            <SelectItem value="other">Other</SelectItem>
                            <SelectItem value="prefer_not_to_say">Prefer not to say</SelectItem>
                          </SelectContent>
                        </Select>
                      </div>
                      <div>
                        <Label htmlFor="marital_status">Marital Status</Label>
                        <Select value={newUser.marital_status} onValueChange={(value) => setNewUser({ ...newUser, marital_status: value })}>
                          <SelectTrigger>
                            <SelectValue placeholder="Select marital status" />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="single">Single</SelectItem>
                            <SelectItem value="married">Married</SelectItem>
                            <SelectItem value="divorced">Divorced</SelectItem>
                            <SelectItem value="widowed">Widowed</SelectItem>
                          </SelectContent>
                        </Select>
                      </div>
                      <div>
                        <Label htmlFor="nationality">Nationality</Label>
                        <Input
                          id="nationality"
                          value={newUser.nationality}
                          onChange={(e) => setNewUser({ ...newUser, nationality: e.target.value })}
                          placeholder="e.g., Nepali"
                        />
                      </div>
                    </div>
                  </div>

                  {/* Contact Information Section */}
                  <div className="space-y-4">
                    <h3 className="text-lg font-medium border-b pb-2">Emergency Contact</h3>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div>
                        <Label htmlFor="emergency_contact_name">Emergency Contact Name</Label>
                        <Input
                          id="emergency_contact_name"
                          value={newUser.emergency_contact_name}
                          onChange={(e) => setNewUser({ ...newUser, emergency_contact_name: e.target.value })}
                        />
                      </div>
                      <div>
                        <Label htmlFor="emergency_contact_phone">Emergency Contact Phone</Label>
                        <Input
                          id="emergency_contact_phone"
                          value={newUser.emergency_contact_phone}
                          onChange={(e) => setNewUser({ ...newUser, emergency_contact_phone: e.target.value })}
                        />
                      </div>
                    </div>
                  </div>

                  {/* Financial Information Section */}
                  <div className="space-y-4">
                    <h3 className="text-lg font-medium border-b pb-2">Financial Information</h3>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div>
                        <Label htmlFor="salary">Salary</Label>
                        <Input
                          id="salary"
                          type="number"
                          value={newUser.salary}
                          onChange={(e) => setNewUser({ ...newUser, salary: e.target.value })}
                          placeholder="Enter salary amount"
                        />
                      </div>
                      <div>
                        <Label htmlFor="salary_currency">Salary Currency</Label>
                        <Select value={newUser.salary_currency} onValueChange={(value) => setNewUser({ ...newUser, salary_currency: value })}>
                          <SelectTrigger>
                            <SelectValue />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="NPR">NPR (Nepali Rupee)</SelectItem>
                            <SelectItem value="USD">USD (US Dollar)</SelectItem>
                            <SelectItem value="EUR">EUR (Euro)</SelectItem>
                            <SelectItem value="INR">INR (Indian Rupee)</SelectItem>
                          </SelectContent>
                        </Select>
                      </div>
                      <div>
                        <Label htmlFor="bank_name">Bank Name</Label>
                        <Input
                          id="bank_name"
                          value={newUser.bank_name}
                          onChange={(e) => setNewUser({ ...newUser, bank_name: e.target.value })}
                          placeholder="e.g., Nepal Bank Limited"
                        />
                      </div>
                      <div>
                        <Label htmlFor="bank_branch">Bank Branch</Label>
                        <Input
                          id="bank_branch"
                          value={newUser.bank_branch}
                          onChange={(e) => setNewUser({ ...newUser, bank_branch: e.target.value })}
                          placeholder="e.g., Kathmandu Branch"
                        />
                      </div>
                      <div>
                        <Label htmlFor="bank_account_number">Bank Account Number</Label>
                        <Input
                          id="bank_account_number"
                          value={newUser.bank_account_number}
                          onChange={(e) => setNewUser({ ...newUser, bank_account_number: e.target.value })}
                          placeholder="Enter account number"
                        />
                      </div>
                    </div>
                  </div>

                  {/* Government IDs Section */}
                  <div className="space-y-4">
                    <h3 className="text-lg font-medium border-b pb-2">Government IDs</h3>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div>
                        <Label htmlFor="citizenship_number">Citizenship Number</Label>
                        <Input
                          id="citizenship_number"
                          value={newUser.citizenship_number}
                          onChange={(e) => setNewUser({ ...newUser, citizenship_number: e.target.value })}
                          placeholder="e.g., 12-34-56-78901"
                        />
                      </div>
                      <div>
                        <Label htmlFor="pan_number">PAN Number</Label>
                        <Input
                          id="pan_number"
                          value={newUser.pan_number}
                          onChange={(e) => setNewUser({ ...newUser, pan_number: e.target.value })}
                          placeholder="e.g., 123456789"
                        />
                      </div>
                    </div>
                  </div>

                {/* File Upload Section */}
                <div className="space-y-4">
                  <h3 className="text-lg font-medium border-b pb-2">File Uploads (Optional)</h3>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <FileUpload
                      fileType="profile_picture"
                      label="Profile Picture"
                      description="Upload user's profile photo"
                      accept="image/*"
                      maxSizeMB={5}
                      onFileSelect={(file) => setUploadFiles(prev => ({ ...prev, profile_picture: file || undefined }))}
                    />
                    <FileUpload
                      fileType="signature"
                      label="Digital Signature"
                      description="Upload user's signature"
                      accept="image/*"
                      maxSizeMB={2}
                      onFileSelect={(file) => setUploadFiles(prev => ({ ...prev, signature: file || undefined }))}
                    />
                    <FileUpload
                      fileType="document"
                      label="Documents"
                      description="Upload ID, certificates, etc."
                      accept=".pdf,.doc,.docx,.jpg,.jpeg,.png"
                      maxSizeMB={10}
                      onFileSelect={(file) => setUploadFiles(prev => ({ ...prev, document: file || undefined }))}
                    />
                    <FileUpload
                      fileType="contract"
                      label="Employment Contract"
                      description="Upload employment agreement"
                      accept=".pdf,.doc,.docx,.jpg,.jpeg,.png"
                      maxSizeMB={10}
                      onFileSelect={(file) => setUploadFiles(prev => ({ ...prev, contract: file || undefined }))}
                    />
                  </div>
                </div>

                  <Button type="submit" className="w-full" disabled={creatingUser}>
                    {creatingUser ? (
                      <>
                        <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                        Creating User...
                      </>
                    ) : (
                      "Create User"
                    )}
                  </Button>
                </form>
              </div>
            </DialogContent>
          </Dialog>
      </div>

      {/* Statistics Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Users</CardTitle>
            <Users className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{users.length}</div>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Active Users</CardTitle>
            <UserCheck className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{users.filter(u => u.is_active).length}</div>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Departments</CardTitle>
            <Building className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{departments.length}</div>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Inactive Users</CardTitle>
            <UserX className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{users.filter(u => !u.is_active).length}</div>
          </CardContent>
        </Card>
      </div>

      {/* Filters */}
      <Card>
        <CardHeader>
          <CardTitle>Filter Users</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex flex-col space-y-4 sm:flex-row sm:items-center sm:space-y-0 sm:gap-4">
            <div className="flex-1">
              <div className="relative">
                <Search className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
                <Input
                  placeholder="Search by name, email, or employee ID..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10"
                />
              </div>
            </div>
            <Select value={selectedDepartment} onValueChange={setSelectedDepartment}>
              <SelectTrigger className="w-full sm:w-48">
                <SelectValue placeholder="All Departments" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Departments</SelectItem>
                {departments.map((dept) => (
                  <SelectItem key={dept.id} value={dept.name}>{dept.name}</SelectItem>
                ))}
              </SelectContent>
            </Select>
            <Select value={selectedRole} onValueChange={setSelectedRole}>
              <SelectTrigger className="w-full sm:w-32">
                <SelectValue placeholder="All Roles" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Roles</SelectItem>
                <SelectItem value="admin">Admin</SelectItem>
                <SelectItem value="hr_manager">HR Manager</SelectItem>
                <SelectItem value="manager">Manager</SelectItem>
                <SelectItem value="staff">Staff</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </CardContent>
      </Card>

      {/* Users Table */}
      <Card>
        <CardHeader>
          <CardTitle>Users</CardTitle>
          <CardDescription>Manage all users in the system</CardDescription>
        </CardHeader>
        <CardContent>
          {loading ? (
            <div className="text-center py-8">Loading users...</div>
          ) : (
            <div className="overflow-x-auto border rounded-lg">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead className="w-12"></TableHead>
                    <TableHead className="min-w-[200px]">Employee</TableHead>
                    <TableHead className="hidden sm:table-cell">Employee ID</TableHead>
                    <TableHead className="hidden lg:table-cell">Department</TableHead>
                    <TableHead className="min-w-[120px]">Actions</TableHead>
                    <TableHead className="min-w-[100px]">Role</TableHead>
                    <TableHead className="hidden md:table-cell">Position</TableHead>
                    <TableHead className="hidden xl:table-cell">Status</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {users.map((user) => (
                    <TableRow
                      key={user.id}
                      className={`cursor-pointer transition-colors ${!user.is_active ? 'bg-red-50 border-l-4 border-l-red-200 hover:bg-red-100' : 'hover:bg-muted/50'}`}
                      onClick={(e) => {
                        // Don't navigate if clicking on action buttons
                        if ((e.target as HTMLElement).closest('button')) {
                          return
                        }
                        handleViewUserDetails(user.id)
                      }}
                      role="button"
                      tabIndex={0}
                      aria-label={`View details for ${user.full_name}`}
                      onKeyDown={(e) => {
                        if (e.key === 'Enter' || e.key === ' ') {
                          e.preventDefault()
                          handleViewUserDetails(user.id)
                        }
                      }}
                    >
                      {/* Avatar - Always visible */}
                      <TableCell className="w-12">
                        <Avatar className="h-8 w-8 flex-shrink-0">
                          <AvatarImage src={userProfileImages[user.id] || undefined} />
                          <AvatarFallback className="text-xs font-semibold bg-gradient-to-br from-blue-500 to-purple-600 text-white">
                            {getInitials(user.full_name)}
                          </AvatarFallback>
                        </Avatar>
                      </TableCell>

                      {/* Employee Info - Always visible */}
                      <TableCell className="min-w-[200px]">
                        <div className="min-w-0">
                          <div className="font-medium truncate flex items-center gap-2">
                            {user.full_name}
                            {!user.is_active && (
                              <Badge variant="secondary" className="text-xs">Inactive</Badge>
                            )}
                          </div>
                          <div className="text-sm text-muted-foreground truncate">{user.email}</div>
                          {/* Show additional info on mobile */}
                          <div className="sm:hidden text-xs text-muted-foreground mt-1 space-y-1">
                            <div>ID: {user.employee_id || "N/A"}</div>
                            <div className="lg:hidden">{user.department || "N/A"}</div>
                            <div className="md:hidden">{user.position || "N/A"}</div>
                            <div className="xl:hidden">
                              <Badge className={`${getStatusBadgeColor(user.is_active)} text-xs`}>
                                {user.is_active ? "Active" : "Inactive"}
                              </Badge>
                            </div>
                          </div>
                        </div>
                      </TableCell>

                      {/* Employee ID - Hidden on mobile */}
                      <TableCell className="hidden sm:table-cell">
                        <Badge variant="outline" className="text-xs">{user.employee_id || "N/A"}</Badge>
                      </TableCell>

                      {/* Department - Hidden on mobile and tablet */}
                      <TableCell className="hidden lg:table-cell">
                        <span className="text-sm">{user.department || "N/A"}</span>
                      </TableCell>

                      {/* Actions - Moved before role, always visible */}
                      <TableCell className="min-w-[120px]">
                        <div className="flex flex-col space-y-1">
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => {
                              setEditingUser(user)
                              setEditUser({
                                email: user.email || "",
                                full_name: user.full_name || "",
                                role: user.role || "staff",
                                employee_id: user.employee_id || "",
                                department: user.department || "",
                                position: user.position || "",
                                phone: user.phone || "",
                                salary: user.salary?.toString() || "",
                                employment_type: user.employment_type || "full_time",
                              })
                              // Fetch user files when opening edit modal
                              fetchUserFiles(user.id)
                            }}
                            className="text-xs h-8 min-h-[44px] sm:min-h-[32px]"
                          >
                            <Edit className="h-3 w-3 sm:h-4 sm:w-4 mr-1" />
                            <span className="hidden sm:inline">Edit</span>
                            <span className="sm:hidden">Edit</span>
                          </Button>
                          {currentUser.role === "admin" && user.id !== currentUser.id && (
                            <Button
                              variant="outline"
                              size="sm"
                              onClick={() => handleHardDeleteUser(user.id, user.full_name)}
                              className="text-red-600 hover:text-red-700 text-xs h-8 min-h-[44px] sm:min-h-[32px]"
                              disabled={deletingUser === user.id}
                            >
                              {deletingUser === user.id ? (
                                <Loader2 className="h-3 w-3 sm:h-4 sm:w-4 mr-1 animate-spin" />
                              ) : (
                                <Trash2 className="h-3 w-3 sm:h-4 sm:w-4 mr-1" />
                              )}
                              <span className="hidden sm:inline">
                                {deletingUser === user.id ? "Deleting..." : "Delete"}
                              </span>
                              <span className="sm:hidden">
                                {deletingUser === user.id ? "Del..." : "Del"}
                              </span>
                            </Button>
                          )}
                        </div>
                      </TableCell>

                      {/* Role - Always visible */}
                      <TableCell className="min-w-[100px]">
                        <Badge className={`${getRoleBadgeColor(user.role)} text-xs`}>
                          <span className="hidden sm:inline">{user.role.replace("_", " ").toUpperCase()}</span>
                          <span className="sm:hidden">
                            {user.role === "hr_manager" ? "HR" :
                             user.role === "admin" ? "ADM" :
                             user.role === "manager" ? "MGR" : "STF"}
                          </span>
                        </Badge>
                      </TableCell>

                      {/* Position - Hidden on mobile */}
                      <TableCell className="hidden md:table-cell">
                        <span className="text-sm">{user.position || "N/A"}</span>
                      </TableCell>

                      {/* Status - Hidden on mobile and tablet */}
                      <TableCell className="hidden xl:table-cell">
                        <div className="flex items-center space-x-2">
                          <Badge className={getStatusBadgeColor(user.is_active)}>
                            {user.is_active ? "Active" : "Inactive"}
                          </Badge>
                          {currentUser.role === "admin" && user.id !== currentUser.id && (
                            <div className="flex items-center">
                              {togglingUserStatus === user.id ? (
                                <Loader2 className="h-4 w-4 animate-spin" />
                              ) : (
                                <Switch
                                  checked={user.is_active}
                                  onCheckedChange={() => handleToggleUserStatus(user.id, user.is_active)}
                                  size="sm"
                                  disabled={togglingUserStatus === user.id}
                                />
                              )}
                            </div>
                          )}
                        </div>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
              {users.length === 0 && (
                <div className="text-center py-8 text-gray-500">
                  No users found matching your criteria
                </div>
              )}
            </div>
          )}
        </CardContent>
      </Card>

      {/* Edit User Modal */}
      <Dialog open={!!editingUser} onOpenChange={(open) => !open && setEditingUser(null)}>
        <DialogContent className="max-w-4xl max-h-[90vh] overflow-hidden flex flex-col">
          <DialogHeader className="flex-shrink-0">
            <DialogTitle>Edit User</DialogTitle>
            <DialogDescription>Update user information and employment details</DialogDescription>
          </DialogHeader>
          <div className="flex-1 overflow-y-auto pr-2">
            <form onSubmit={handleEditUser} className="space-y-6 pb-4">
            <div className="grid grid-cols-2 gap-4">
              <div>
                <Label htmlFor="edit-email">Email *</Label>
                <Input
                  id="edit-email"
                  type="email"
                  value={editUser.email}
                  onChange={(e) => setEditUser({ ...editUser, email: e.target.value })}
                  required
                />
              </div>
              <div>
                <Label htmlFor="edit-full_name">Full Name *</Label>
                <Input
                  id="edit-full_name"
                  value={editUser.full_name}
                  onChange={(e) => setEditUser({ ...editUser, full_name: e.target.value })}
                  required
                />
              </div>
              <div>
                <Label htmlFor="edit-employee_id">Employee ID</Label>
                <Input
                  id="edit-employee_id"
                  value={editUser.employee_id}
                  onChange={(e) => setEditUser({ ...editUser, employee_id: e.target.value })}
                />
              </div>
              <div>
                <Label htmlFor="edit-role">Role *</Label>
                <Select value={editUser.role} onValueChange={(value) => setEditUser({ ...editUser, role: value })}>
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="staff">Staff</SelectItem>
                    <SelectItem value="manager">Manager</SelectItem>
                    <SelectItem value="hr_manager">HR Manager</SelectItem>
                    <SelectItem value="admin">Admin</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              <div>
                <Label htmlFor="edit-department">Department</Label>
                <Select value={editUser.department} onValueChange={(value) => setEditUser({ ...editUser, department: value })}>
                  <SelectTrigger>
                    <SelectValue placeholder="Select department" />
                  </SelectTrigger>
                  <SelectContent>
                    {departments.map((dept) => (
                      <SelectItem key={dept.id} value={dept.name}>
                        {dept.name}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
              <div>
                <Label htmlFor="edit-position">Position</Label>
                <Input
                  id="edit-position"
                  value={editUser.position}
                  onChange={(e) => setEditUser({ ...editUser, position: e.target.value })}
                />
              </div>
              <div>
                <Label htmlFor="edit-phone">Phone</Label>
                <Input
                  id="edit-phone"
                  value={editUser.phone}
                  onChange={(e) => setEditUser({ ...editUser, phone: e.target.value })}
                />
              </div>
              <div>
                <Label htmlFor="edit-salary">Salary</Label>
                <Input
                  id="edit-salary"
                  type="number"
                  value={editUser.salary}
                  onChange={(e) => setEditUser({ ...editUser, salary: e.target.value })}
                />
              </div>
              <div>
                <Label htmlFor="edit-employment_type">Employment Type</Label>
                <Select value={editUser.employment_type} onValueChange={(value) => setEditUser({ ...editUser, employment_type: value })}>
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="full_time">Full Time</SelectItem>
                    <SelectItem value="part_time">Part Time</SelectItem>
                    <SelectItem value="contract">Contract</SelectItem>
                    <SelectItem value="intern">Intern</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>

            {/* File Management Section */}
            {editingUser && (
              <div className="space-y-4">
                <h3 className="text-lg font-medium">File Management</h3>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <FileUpload
                    fileType="profile_picture"
                    label="Profile Picture"
                    description="Upload user's profile photo"
                    accept="image/*"
                    maxSizeMB={5}
                    existingFiles={userFiles[editingUser.id]?.filter(f => f.file_type === 'profile_picture') || []}
                    onFileSelect={(file) => {
                      if (file) {
                        handleFileUpload(editingUser.id, 'profile_picture', file)
                      }
                    }}
                    onFileDelete={(fileId) => handleFileDelete(fileId, editingUser.id)}
                  />
                  <FileUpload
                    fileType="signature"
                    label="Digital Signature"
                    description="Upload user's signature"
                    accept="image/*"
                    maxSizeMB={2}
                    existingFiles={userFiles[editingUser.id]?.filter(f => f.file_type === 'signature') || []}
                    onFileSelect={(file) => {
                      if (file) {
                        handleFileUpload(editingUser.id, 'signature', file)
                      }
                    }}
                    onFileDelete={(fileId) => handleFileDelete(fileId, editingUser.id)}
                  />
                  <FileUpload
                    fileType="document"
                    label="Documents"
                    description="Upload ID, certificates, etc."
                    accept=".pdf,.doc,.docx,.jpg,.jpeg,.png"
                    maxSizeMB={10}
                    existingFiles={userFiles[editingUser.id]?.filter(f => f.file_type === 'document') || []}
                    onFileSelect={(file) => {
                      if (file) {
                        handleFileUpload(editingUser.id, 'document', file)
                      }
                    }}
                    onFileDelete={(fileId) => handleFileDelete(fileId, editingUser.id)}
                  />
                  <FileUpload
                    fileType="contract"
                    label="Employment Contract"
                    description="Upload employment agreement"
                    accept=".pdf,.doc,.docx,.jpg,.jpeg,.png"
                    maxSizeMB={10}
                    existingFiles={userFiles[editingUser.id]?.filter(f => f.file_type === 'contract') || []}
                    onFileSelect={(file) => {
                      if (file) {
                        handleFileUpload(editingUser.id, 'contract', file)
                      }
                    }}
                    onFileDelete={(fileId) => handleFileDelete(fileId, editingUser.id)}
                  />
                </div>
              </div>
            )}

            <div className="flex justify-end space-x-2">
              <Button type="button" variant="outline" onClick={() => setEditingUser(null)}>
                Cancel
              </Button>
              <Button type="submit" disabled={updatingUser}>
                {updatingUser ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    Updating User...
                  </>
                ) : (
                  "Update User"
                )}
              </Button>
            </div>
            </form>
          </div>
        </DialogContent>
      </Dialog>
    </div>
  )
}
