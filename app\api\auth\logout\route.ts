import { type NextRequest, NextResponse } from "next/server"
import { AuthService } from "@/lib/auth-utils"

export async function POST(request: NextRequest) {
  try {
    const sessionToken = request.cookies.get("session-token")?.value

    if (sessionToken) {
      await AuthService.logout(sessionToken)
    }

    const response = NextResponse.json({ success: true })

    // Clear session cookie
    response.cookies.set("session-token", "", {
      httpOnly: true,
      secure: process.env.NODE_ENV === "production",
      sameSite: "lax",
      maxAge: 0,
      path: "/",
    })

    return response
  } catch (error) {
    console.error("Logout API error:", error)
    return NextResponse.json({ success: false, error: "Internal server error" }, { status: 500 })
  }
}
