const { neon } = require('@neondatabase/serverless');
const fs = require('fs');
const path = require('path');

// Load environment variables
require('dotenv').config({ path: '.env.local' });

const sql = neon(process.env.DATABASE_URL);

async function testAttachmentSystem() {
  try {
    console.log('🧪 Testing File Attachment System...\n');

    // 1. Get test data
    console.log('1. Getting test data...');
    const users = await sql`
      SELECT id, full_name, email, role 
      FROM users 
      WHERE is_active = true 
      ORDER BY created_at 
      LIMIT 3
    `;
    
    const tasks = await sql`
      SELECT id, title, assigned_to, assigned_by 
      FROM tasks 
      ORDER BY created_at DESC 
      LIMIT 2
    `;
    
    if (users.length < 1 || tasks.length < 1) {
      console.log('❌ Need at least 1 user and 1 task to test attachments');
      return;
    }
    
    console.log(`✅ Found ${users.length} users and ${tasks.length} tasks`);
    const testTask = tasks[0];
    console.log(`📝 Using task: "${testTask.title}" for testing`);

    // 2. Clear existing attachments for clean testing
    console.log('\n2. Clearing existing attachments...');
    await sql`DELETE FROM task_attachments WHERE task_id = ${testTask.id}`;
    console.log('✅ Cleared existing attachments');

    // 3. Test creating mock attachments (simulating file uploads)
    console.log('\n3. Testing attachment creation...');
    
    const mockAttachments = [
      {
        file_name: 'Project Requirements.pdf',
        file_path: `/uploads/tasks/${testTask.id}/test-document.pdf`,
        file_size: 1024000, // 1MB
        file_type: 'application/pdf',
        user_id: users[0].id,
        description: 'Project requirements document'
      },
      {
        file_name: 'UI Design Mockup.png',
        file_path: `/uploads/tasks/${testTask.id}/design-mockup.png`,
        file_size: 512000, // 512KB
        file_type: 'image/png',
        user_id: users[1] ? users[1].id : users[0].id,
        description: 'UI design mockup'
      },
      {
        file_name: 'Data Export.xlsx',
        file_path: `/uploads/tasks/${testTask.id}/data-export.xlsx`,
        file_size: 256000, // 256KB
        file_type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
        user_id: users[0].id,
        description: 'Data export spreadsheet'
      }
    ];

    const createdAttachments = [];
    
    for (const attachmentData of mockAttachments) {
      const result = await sql`
        INSERT INTO task_attachments (
          task_id, user_id, file_name, file_path,
          file_size, file_type, description
        )
        VALUES (
          ${testTask.id},
          ${attachmentData.user_id},
          ${attachmentData.file_name},
          ${attachmentData.file_path},
          ${attachmentData.file_size},
          ${attachmentData.file_type},
          ${attachmentData.description}
        )
        RETURNING *
      `;

      createdAttachments.push(result[0]);
      console.log(`   ✅ Created: "${attachmentData.file_name}" (${(attachmentData.file_size / 1024).toFixed(1)}KB)`);
    }

    // 4. Test retrieving attachments with user details
    console.log('\n4. Testing attachment retrieval...');
    
    const attachmentsWithDetails = await sql`
      SELECT
        a.*,
        u.full_name as uploaded_by_name,
        u.email as uploaded_by_email
      FROM task_attachments a
      LEFT JOIN users u ON a.user_id = u.id
      WHERE a.task_id = ${testTask.id}
      ORDER BY a.created_at ASC
    `;

    console.log(`✅ Retrieved ${attachmentsWithDetails.length} attachments:`);
    attachmentsWithDetails.forEach((attachment, index) => {
      console.log(`   ${index + 1}. ${attachment.file_name}`);
      console.log(`      Size: ${(attachment.file_size / 1024).toFixed(1)}KB`);
      console.log(`      Type: ${attachment.file_type}`);
      console.log(`      Uploaded by: ${attachment.uploaded_by_name} (${attachment.uploaded_by_email})`);
      console.log(`      Path: ${attachment.file_path}`);
      console.log(`      Description: ${attachment.description}`);
      console.log(`      Created: ${new Date(attachment.created_at).toLocaleString()}`);
    });

    // 5. Test task statistics with attachment count
    console.log('\n5. Testing task statistics with attachments...');
    
    const taskWithAttachments = await sql`
      SELECT
        t.*,
        (
          SELECT COUNT(*)
          FROM task_attachments ta
          WHERE ta.task_id = t.id
        ) as attachment_count,
        (
          SELECT COALESCE(SUM(ta.file_size), 0)
          FROM task_attachments ta
          WHERE ta.task_id = t.id
        ) as total_attachment_size
      FROM tasks t
      WHERE t.id = ${testTask.id}
    `;

    const taskStats = taskWithAttachments[0];
    console.log(`✅ Task statistics:`);
    console.log(`   Title: ${taskStats.title}`);
    console.log(`   Attachment count: ${taskStats.attachment_count}`);
    console.log(`   Total attachment size: ${(taskStats.total_attachment_size / 1024).toFixed(1)}KB`);

    // 6. Test file type validation simulation
    console.log('\n6. Testing file type validation...');
    
    const allowedTypes = [
      'image/jpeg', 'image/png', 'image/gif', 'image/webp',
      'application/pdf', 'text/plain', 'text/csv',
      'application/msword', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
      'application/vnd.ms-excel', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
      'application/zip', 'application/x-zip-compressed'
    ];

    const testFiles = [
      { name: 'document.pdf', type: 'application/pdf', size: 1024000 },
      { name: 'image.jpg', type: 'image/jpeg', size: 512000 },
      { name: 'script.exe', type: 'application/x-executable', size: 256000 }, // Should fail
      { name: 'large-file.zip', type: 'application/zip', size: 15 * 1024 * 1024 }, // Should fail (too large)
    ];

    console.log(`✅ File validation tests:`);
    testFiles.forEach(file => {
      const typeAllowed = allowedTypes.includes(file.type);
      const sizeAllowed = file.size <= (10 * 1024 * 1024); // 10MB limit
      const valid = typeAllowed && sizeAllowed;
      
      console.log(`   ${file.name}: ${valid ? '✅ Valid' : '❌ Invalid'}`);
      if (!typeAllowed) console.log(`     - File type not allowed: ${file.type}`);
      if (!sizeAllowed) console.log(`     - File too large: ${(file.size / 1024 / 1024).toFixed(1)}MB (max 10MB)`);
    });

    // 7. Test attachment deletion
    console.log('\n7. Testing attachment deletion...');
    
    if (createdAttachments.length > 0) {
      const attachmentToDelete = createdAttachments[createdAttachments.length - 1];
      
      await sql`
        DELETE FROM task_attachments WHERE id = ${attachmentToDelete.id}
      `;
      
      console.log(`   ✅ Deleted "${attachmentToDelete.file_name}"`);

      // Verify deletion
      const remainingAttachments = await sql`
        SELECT COUNT(*) as count
        FROM task_attachments
        WHERE task_id = ${testTask.id}
      `;
      console.log(`   Remaining attachments: ${remainingAttachments[0].count}`);
    }

    // 8. Test access control simulation
    console.log('\n8. Testing access control logic...');
    
    const testUserId = users[1] ? users[1].id : users[0].id;
    const testAttachment = createdAttachments[0];
    
    // Check if user has access to the attachment
    const attachmentAccess = await sql`
      SELECT 
        a.*,
        t.assigned_to as task_assigned_to,
        t.assigned_by as task_assigned_by,
        (
          SELECT EXISTS (
            SELECT 1 FROM task_assignments 
            WHERE task_id = t.id AND user_id = ${testUserId}
          )
        ) as is_assigned_to_task
      FROM task_attachments a
      LEFT JOIN tasks t ON a.task_id = t.id
      WHERE a.id = ${testAttachment.id}
    `;

    const attachment = attachmentAccess[0];
    const isUploader = attachment.user_id === testUserId;
    const hasTaskAccess = attachment.task_assigned_to === testUserId || attachment.task_assigned_by === testUserId;
    const hasAssignmentAccess = attachment.is_assigned_to_task;
    const totalAccess = isUploader || hasTaskAccess || hasAssignmentAccess;
    
    console.log(`✅ Access control for ${users[1] ? users[1].full_name : users[0].full_name}:`);
    console.log(`   Is uploader: ${isUploader}`);
    console.log(`   Has task access: ${hasTaskAccess}`);
    console.log(`   Has assignment access: ${hasAssignmentAccess}`);
    console.log(`   Total access: ${totalAccess}`);

    // 9. Test attachment metadata and file info
    console.log('\n9. Testing attachment metadata...');
    
    const attachmentMetadata = await sql`
      SELECT
        COUNT(*) as total_attachments,
        SUM(file_size) as total_size,
        AVG(file_size) as avg_size,
        COUNT(DISTINCT file_type) as unique_types,
        COUNT(DISTINCT user_id) as unique_uploaders
      FROM task_attachments
      WHERE task_id = ${testTask.id}
    `;

    const metadata = attachmentMetadata[0];
    console.log(`✅ Attachment metadata:`);
    console.log(`   Total attachments: ${metadata.total_attachments}`);
    console.log(`   Total size: ${(metadata.total_size / 1024).toFixed(1)}KB`);
    console.log(`   Average size: ${(metadata.avg_size / 1024).toFixed(1)}KB`);
    console.log(`   Unique file types: ${metadata.unique_types}`);
    console.log(`   Unique uploaders: ${metadata.unique_uploaders}`);

    console.log('\n🎉 File Attachment System test completed successfully!');
    console.log('\n📋 Summary:');
    console.log(`   - Attachment creation: ✅ Working`);
    console.log(`   - Attachment retrieval with user details: ✅ Working`);
    console.log(`   - Task statistics with attachment counts: ✅ Working`);
    console.log(`   - File type and size validation: ✅ Working`);
    console.log(`   - Attachment deletion: ✅ Working`);
    console.log(`   - Access control logic: ✅ Working`);
    console.log(`   - Attachment metadata calculation: ✅ Working`);

  } catch (error) {
    console.error('❌ Test failed:', error.message);
    console.error('Full error:', error);
  }
}

testAttachmentSystem();
