// Nepal Public Holiday Management System
// Phase 3: Nepal Localization Implementation - Holiday Calendar

import { NepaliCalendar, BSDate } from './nepali-calendar'
import { db } from './neon'

export interface Holiday {
  id: string
  name: string
  nameNepali: string
  date: string // AD date (YYYY-MM-DD)
  bsDate: BSDate
  bsDateString: string
  type: 'public' | 'festival' | 'observance' | 'bank' | 'government'
  category: 'national' | 'religious' | 'cultural' | 'international'
  isFixed: boolean // true for fixed dates, false for lunar/calculated dates
  description?: string
  isOptional: boolean
  affectsPayroll: boolean
  compensatoryOff: boolean
  createdBy?: string
  isActive: boolean
}

export interface HolidayCalendar {
  year: number
  bsYear: number
  holidays: Holiday[]
  totalHolidays: number
  publicHolidays: number
  festivals: number
  observances: number
  longWeekends: LongWeekend[]
}

export interface LongWeekend {
  startDate: string
  endDate: string
  totalDays: number
  holidays: Holiday[]
  description: string
}

export interface HolidayStats {
  totalHolidays: number
  byType: { [key: string]: number }
  byCategory: { [key: string]: number }
  byMonth: { [key: string]: number }
  upcomingHolidays: Holiday[]
  recentHolidays: Holiday[]
}

export class HolidayManager {
  
  /**
   * Get all holidays for a specific year
   */
  async getHolidaysForYear(year: number): Promise<Holiday[]> {
    try {
      const result = await db.sql`
        SELECT * FROM nepali_calendar_config
        WHERE EXTRACT(YEAR FROM ad_date) = ${year}
          AND is_holiday = TRUE
        ORDER BY ad_date ASC
      `

      return result.map(row => this.mapDatabaseToHoliday(row))
    } catch (error) {
      console.error('Error fetching holidays for year:', error)
      return []
    }
  }

  /**
   * Get holidays for a specific BS year
   */
  async getHolidaysForBSYear(bsYear: number): Promise<Holiday[]> {
    try {
      const result = await db.sql`
        SELECT * FROM nepali_calendar_config
        WHERE bs_year = ${bsYear}
          AND is_holiday = TRUE
        ORDER BY bs_month, bs_day ASC
      `

      return result.map(row => this.mapDatabaseToHoliday(row))
    } catch (error) {
      console.error('Error fetching holidays for BS year:', error)
      return []
    }
  }

  /**
   * Get holidays in a date range
   */
  async getHolidaysInRange(startDate: string, endDate: string): Promise<Holiday[]> {
    try {
      const result = await db.sql`
        SELECT * FROM nepali_calendar_config
        WHERE ad_date BETWEEN ${startDate} AND ${endDate}
          AND is_holiday = TRUE
        ORDER BY ad_date ASC
      `

      return result.map(row => this.mapDatabaseToHoliday(row))
    } catch (error) {
      console.error('Error fetching holidays in range:', error)
      return []
    }
  }

  /**
   * Check if a specific date is a holiday
   */
  async isHoliday(date: string): Promise<boolean> {
    try {
      const result = await db.sql`
        SELECT COUNT(*) as count FROM nepali_calendar_config
        WHERE ad_date = ${date}
          AND is_holiday = TRUE
          AND is_active = TRUE
      `

      return parseInt(result[0].count) > 0
    } catch (error) {
      console.error('Error checking if date is holiday:', error)
      return false
    }
  }

  /**
   * Get holiday information for a specific date
   */
  async getHolidayForDate(date: string): Promise<Holiday | null> {
    try {
      const result = await db.sql`
        SELECT * FROM nepali_calendar_config
        WHERE ad_date = ${date}
          AND is_holiday = TRUE
          AND is_active = TRUE
        LIMIT 1
      `

      return result.length > 0 ? this.mapDatabaseToHoliday(result[0]) : null
    } catch (error) {
      console.error('Error fetching holiday for date:', error)
      return null
    }
  }

  /**
   * Create a new holiday
   */
  async createHoliday(holiday: Omit<Holiday, 'id'>): Promise<Holiday> {
    try {
      const bsDate = NepaliCalendar.adToBS(holiday.date)
      
      const result = await db.sql`
        INSERT INTO nepali_calendar_config (
          bs_year, bs_month, bs_day, ad_date, is_holiday, holiday_name, holiday_type,
          holiday_name_nepali, holiday_category, is_fixed, description, is_optional,
          affects_payroll, compensatory_off, is_active, created_by
        )
        VALUES (
          ${bsDate.year}, ${bsDate.month}, ${bsDate.day}, ${holiday.date}, TRUE,
          ${holiday.name}, ${holiday.type}, ${holiday.nameNepali}, ${holiday.category},
          ${holiday.isFixed}, ${holiday.description || null}, ${holiday.isOptional},
          ${holiday.affectsPayroll}, ${holiday.compensatoryOff}, ${holiday.isActive},
          ${holiday.createdBy || null}
        )
        RETURNING *
      `

      return this.mapDatabaseToHoliday(result[0])
    } catch (error) {
      console.error('Error creating holiday:', error)
      throw new Error('Failed to create holiday')
    }
  }

  /**
   * Update an existing holiday
   */
  async updateHoliday(id: string, updates: Partial<Holiday>): Promise<Holiday> {
    try {
      const setClause = Object.entries(updates)
        .filter(([key, value]) => value !== undefined && key !== 'id')
        .map(([key, value]) => {
          const dbKey = this.mapFieldToDatabase(key)
          return `${dbKey} = '${value}'`
        })
        .join(', ')

      if (!setClause) {
        throw new Error('No valid updates provided')
      }

      const result = await db.sql`
        UPDATE nepali_calendar_config
        SET ${db.sql.raw(setClause)}, updated_at = NOW()
        WHERE id = ${id}
        RETURNING *
      `

      if (result.length === 0) {
        throw new Error('Holiday not found')
      }

      return this.mapDatabaseToHoliday(result[0])
    } catch (error) {
      console.error('Error updating holiday:', error)
      throw new Error('Failed to update holiday')
    }
  }

  /**
   * Delete a holiday
   */
  async deleteHoliday(id: string): Promise<void> {
    try {
      await db.sql`
        DELETE FROM nepali_calendar_config
        WHERE id = ${id}
      `
    } catch (error) {
      console.error('Error deleting holiday:', error)
      throw new Error('Failed to delete holiday')
    }
  }

  /**
   * Generate holiday calendar for a year
   */
  async generateHolidayCalendar(year: number): Promise<HolidayCalendar> {
    const holidays = await this.getHolidaysForYear(year)
    const bsYear = NepaliCalendar.adToBS(new Date(year, 0, 1)).year

    const totalHolidays = holidays.length
    const publicHolidays = holidays.filter(h => h.type === 'public').length
    const festivals = holidays.filter(h => h.type === 'festival').length
    const observances = holidays.filter(h => h.type === 'observance').length

    const longWeekends = this.findLongWeekends(holidays, year)

    return {
      year,
      bsYear,
      holidays,
      totalHolidays,
      publicHolidays,
      festivals,
      observances,
      longWeekends
    }
  }

  /**
   * Get holiday statistics
   */
  async getHolidayStats(year?: number): Promise<HolidayStats> {
    const targetYear = year || new Date().getFullYear()
    const holidays = await this.getHolidaysForYear(targetYear)
    
    const byType: { [key: string]: number } = {}
    const byCategory: { [key: string]: number } = {}
    const byMonth: { [key: string]: number } = {}

    holidays.forEach(holiday => {
      byType[holiday.type] = (byType[holiday.type] || 0) + 1
      byCategory[holiday.category] = (byCategory[holiday.category] || 0) + 1
      
      const month = new Date(holiday.date).toLocaleDateString('en-US', { month: 'long' })
      byMonth[month] = (byMonth[month] || 0) + 1
    })

    const today = new Date()
    const upcomingHolidays = holidays
      .filter(h => new Date(h.date) > today)
      .slice(0, 5)

    const recentHolidays = holidays
      .filter(h => new Date(h.date) <= today)
      .slice(-5)
      .reverse()

    return {
      totalHolidays: holidays.length,
      byType,
      byCategory,
      byMonth,
      upcomingHolidays,
      recentHolidays
    }
  }

  /**
   * Find long weekends (3+ consecutive non-working days)
   */
  private findLongWeekends(holidays: Holiday[], year: number): LongWeekend[] {
    const longWeekends: LongWeekend[] = []
    
    // Sort holidays by date
    const sortedHolidays = holidays.sort((a, b) => new Date(a.date).getTime() - new Date(b.date).getTime())
    
    for (const holiday of sortedHolidays) {
      const holidayDate = new Date(holiday.date)
      const dayOfWeek = holidayDate.getDay()
      
      // Check for long weekends around this holiday
      let startDate = new Date(holidayDate)
      let endDate = new Date(holidayDate)
      let consecutiveDays = 1
      let weekendHolidays = [holiday]
      
      // Check backwards for consecutive non-working days
      let checkDate = new Date(holidayDate)
      checkDate.setDate(checkDate.getDate() - 1)
      
      while (this.isNonWorkingDay(checkDate, sortedHolidays)) {
        startDate = new Date(checkDate)
        consecutiveDays++
        
        const dayHoliday = sortedHolidays.find(h => h.date === checkDate.toISOString().split('T')[0])
        if (dayHoliday) {
          weekendHolidays.unshift(dayHoliday)
        }
        
        checkDate.setDate(checkDate.getDate() - 1)
      }
      
      // Check forwards for consecutive non-working days
      checkDate = new Date(holidayDate)
      checkDate.setDate(checkDate.getDate() + 1)
      
      while (this.isNonWorkingDay(checkDate, sortedHolidays)) {
        endDate = new Date(checkDate)
        consecutiveDays++
        
        const dayHoliday = sortedHolidays.find(h => h.date === checkDate.toISOString().split('T')[0])
        if (dayHoliday) {
          weekendHolidays.push(dayHoliday)
        }
        
        checkDate.setDate(checkDate.getDate() + 1)
      }
      
      // If 3+ consecutive days, it's a long weekend
      if (consecutiveDays >= 3) {
        const existing = longWeekends.find(lw => 
          lw.startDate === startDate.toISOString().split('T')[0] &&
          lw.endDate === endDate.toISOString().split('T')[0]
        )
        
        if (!existing) {
          longWeekends.push({
            startDate: startDate.toISOString().split('T')[0],
            endDate: endDate.toISOString().split('T')[0],
            totalDays: consecutiveDays,
            holidays: weekendHolidays,
            description: `${consecutiveDays}-day long weekend including ${weekendHolidays.map(h => h.name).join(', ')}`
          })
        }
      }
    }
    
    return longWeekends
  }

  /**
   * Check if a date is a non-working day (weekend or holiday)
   */
  private isNonWorkingDay(date: Date, holidays: Holiday[]): boolean {
    const dayOfWeek = date.getDay()
    const dateString = date.toISOString().split('T')[0]
    
    // Saturday is weekly off in Nepal
    if (dayOfWeek === 6) return true
    
    // Check if it's a holiday
    return holidays.some(h => h.date === dateString)
  }

  /**
   * Get upcoming holidays (next 30 days)
   */
  async getUpcomingHolidays(days: number = 30): Promise<Holiday[]> {
    const today = new Date()
    const endDate = new Date(today)
    endDate.setDate(today.getDate() + days)
    
    return this.getHolidaysInRange(
      today.toISOString().split('T')[0],
      endDate.toISOString().split('T')[0]
    )
  }

  /**
   * Import holidays from external source or bulk data
   */
  async importHolidays(holidays: Omit<Holiday, 'id'>[]): Promise<{ success: number; failed: number; errors: string[] }> {
    let success = 0
    let failed = 0
    const errors: string[] = []

    for (const holiday of holidays) {
      try {
        await this.createHoliday(holiday)
        success++
      } catch (error) {
        failed++
        errors.push(`Failed to import ${holiday.name}: ${error.message}`)
      }
    }

    return { success, failed, errors }
  }

  /**
   * Map database row to Holiday object
   */
  private mapDatabaseToHoliday(row: any): Holiday {
    const bsDate: BSDate = {
      year: row.bs_year,
      month: row.bs_month,
      day: row.bs_day
    }

    return {
      id: row.id,
      name: row.holiday_name,
      nameNepali: row.holiday_name_nepali || row.holiday_name,
      date: row.ad_date,
      bsDate,
      bsDateString: NepaliCalendar.formatBSDate(bsDate),
      type: row.holiday_type || 'public',
      category: row.holiday_category || 'national',
      isFixed: row.is_fixed || false,
      description: row.description,
      isOptional: row.is_optional || false,
      affectsPayroll: row.affects_payroll !== false,
      compensatoryOff: row.compensatory_off || false,
      createdBy: row.created_by,
      isActive: row.is_active !== false
    }
  }

  /**
   * Map field names to database column names
   */
  private mapFieldToDatabase(field: string): string {
    const mapping: { [key: string]: string } = {
      name: 'holiday_name',
      nameNepali: 'holiday_name_nepali',
      date: 'ad_date',
      type: 'holiday_type',
      category: 'holiday_category',
      isFixed: 'is_fixed',
      isOptional: 'is_optional',
      affectsPayroll: 'affects_payroll',
      compensatoryOff: 'compensatory_off',
      isActive: 'is_active',
      createdBy: 'created_by'
    }
    
    return mapping[field] || field
  }
}

// Export singleton instance
export const holidayManager = new HolidayManager()
