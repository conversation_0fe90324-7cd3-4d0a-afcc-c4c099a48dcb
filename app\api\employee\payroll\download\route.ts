// Employee Payroll Download API
// Handles payslip and document downloads for employees

import { NextRequest, NextResponse } from 'next/server'
import { AuthService } from '@/lib/auth-utils'
import { db } from '@/lib/neon'

export async function GET(request: NextRequest) {
  try {
    const sessionToken = request.cookies.get("session-token")?.value;

    if (!sessionToken) {
      return NextResponse.json({
        success: false,
        error: 'Unauthorized'
      }, { status: 401 });
    }

    const user = await AuthService.verifySession(sessionToken);

    if (!user) {
      return NextResponse.json({
        success: false,
        error: 'Invalid session'
      }, { status: 401 });
    }

    const { searchParams } = new URL(request.url);
    const payrollId = searchParams.get('payrollId');
    const format = searchParams.get('format') || 'pdf';
    const documentType = searchParams.get('type') || 'payslip';

    if (!payrollId) {
      return NextResponse.json({
        success: false,
        error: 'Payroll ID is required'
      }, { status: 400 });
    }

    // Verify the payroll belongs to the requesting user
    const payroll = await db.sql`
      SELECT 
        p.*,
        u.full_name,
        u.employee_id,
        u.department,
        u.position
      FROM payroll p
      JOIN users u ON p.user_id = u.id
      WHERE p.id = ${payrollId} AND p.user_id = ${user.id}
    `;

    if (payroll.length === 0) {
      return NextResponse.json({
        success: false,
        error: 'Payroll record not found or access denied'
      }, { status: 404 });
    }

    const payrollRecord = payroll[0];

    if (documentType === 'payslip') {
      // Generate payslip content
      const payslipData = {
        employee: {
          name: payrollRecord.full_name,
          employeeId: payrollRecord.employee_id,
          department: payrollRecord.department,
          position: payrollRecord.position
        },
        payPeriod: {
          start: payrollRecord.pay_period_start,
          end: payrollRecord.pay_period_end,
          nepaliPeriod: payrollRecord.nepali_pay_period
        },
        earnings: {
          baseSalary: payrollRecord.base_salary || 0,
          overtimeHours: payrollRecord.overtime_hours || 0,
          overtimeRate: payrollRecord.overtime_rate || 0,
          bonuses: payrollRecord.bonuses || 0,
          allowances: payrollRecord.allowances || 0,
          grossPay: payrollRecord.gross_pay || 0
        },
        deductions: {
          taxDeductions: payrollRecord.tax_deductions || 0,
          providentFund: payrollRecord.provident_fund || 0,
          otherDeductions: payrollRecord.deductions || 0,
          totalDeductions: (payrollRecord.tax_deductions || 0) + (payrollRecord.provident_fund || 0) + (payrollRecord.deductions || 0)
        },
        netPay: payrollRecord.net_pay || 0,
        processedAt: payrollRecord.processed_at,
        status: payrollRecord.status
      };

      if (format === 'pdf') {
        // In a real implementation, you would generate a PDF using a library like puppeteer or jsPDF
        // For now, we'll return a mock PDF response
        const pdfContent = generateMockPDF(payslipData);
        
        return new NextResponse(pdfContent, {
          headers: {
            'Content-Type': 'application/pdf',
            'Content-Disposition': `attachment; filename="payslip_${payrollRecord.employee_id}_${payrollId}.pdf"`
          }
        });
      } else if (format === 'json') {
        return NextResponse.json({
          success: true,
          data: payslipData,
          message: 'Payslip data retrieved successfully'
        });
      } else {
        return NextResponse.json({
          success: false,
          error: 'Unsupported format. Supported formats: pdf, json'
        }, { status: 400 });
      }
    } else if (documentType === 'tax_statement') {
      // Generate annual tax statement
      const taxStatement = await generateTaxStatement(user.id, payrollRecord);
      
      if (format === 'pdf') {
        const pdfContent = generateMockTaxStatementPDF(taxStatement);
        
        return new NextResponse(pdfContent, {
          headers: {
            'Content-Type': 'application/pdf',
            'Content-Disposition': `attachment; filename="tax_statement_${payrollRecord.employee_id}_2024.pdf"`
          }
        });
      } else {
        return NextResponse.json({
          success: true,
          data: taxStatement,
          message: 'Tax statement retrieved successfully'
        });
      }
    } else {
      return NextResponse.json({
        success: false,
        error: 'Unsupported document type. Supported types: payslip, tax_statement'
      }, { status: 400 });
    }

  } catch (error) {
    console.error('Error in employee payroll download:', error);
    return NextResponse.json({
      success: false,
      error: error.message || 'Internal server error'
    }, { status: 500 });
  }
}

// Helper function to generate mock PDF content
function generateMockPDF(payslipData: any): Buffer {
  // In a real implementation, this would use a PDF generation library
  // For now, we'll create a simple text-based "PDF"
  const content = `
PAYSLIP
=======

Employee: ${payslipData.employee.name}
Employee ID: ${payslipData.employee.employeeId}
Department: ${payslipData.employee.department}
Position: ${payslipData.employee.position}

Pay Period: ${new Date(payslipData.payPeriod.start).toLocaleDateString()} - ${new Date(payslipData.payPeriod.end).toLocaleDateString()}
Nepali Period: ${payslipData.payPeriod.nepaliPeriod || 'N/A'}

EARNINGS:
---------
Base Salary: NPR ${payslipData.earnings.baseSalary.toLocaleString()}
Overtime: ${payslipData.earnings.overtimeHours} hrs @ NPR ${payslipData.earnings.overtimeRate}/hr
Bonuses: NPR ${payslipData.earnings.bonuses.toLocaleString()}
Allowances: NPR ${payslipData.earnings.allowances.toLocaleString()}
Gross Pay: NPR ${payslipData.earnings.grossPay.toLocaleString()}

DEDUCTIONS:
-----------
Tax Deductions: NPR ${payslipData.deductions.taxDeductions.toLocaleString()}
Provident Fund: NPR ${payslipData.deductions.providentFund.toLocaleString()}
Other Deductions: NPR ${payslipData.deductions.otherDeductions.toLocaleString()}
Total Deductions: NPR ${payslipData.deductions.totalDeductions.toLocaleString()}

NET PAY: NPR ${payslipData.netPay.toLocaleString()}

Processed: ${payslipData.processedAt ? new Date(payslipData.processedAt).toLocaleString() : 'Pending'}
Status: ${payslipData.status}

---
This is a computer-generated document.
  `;

  return Buffer.from(content, 'utf-8');
}

// Helper function to generate tax statement
async function generateTaxStatement(userId: string, payrollRecord: any) {
  // Get annual tax data
  const year = new Date(payrollRecord.pay_period_start).getFullYear();
  
  const annualData = await db.sql`
    SELECT 
      SUM(gross_pay) as total_gross_pay,
      SUM(tax_deductions) as total_tax_deducted,
      SUM(provident_fund) as total_pf_contribution,
      COUNT(*) as pay_periods
    FROM payroll
    WHERE user_id = ${userId}
    AND EXTRACT(YEAR FROM pay_period_start) = ${year}
    AND status IN ('approved', 'processed', 'paid')
  `;

  const data = annualData[0] || {
    total_gross_pay: 0,
    total_tax_deducted: 0,
    total_pf_contribution: 0,
    pay_periods: 0
  };

  return {
    year: year,
    employee: {
      name: payrollRecord.full_name,
      employeeId: payrollRecord.employee_id,
      department: payrollRecord.department
    },
    summary: {
      totalGrossPay: data.total_gross_pay || 0,
      totalTaxDeducted: data.total_tax_deducted || 0,
      totalPFContribution: data.total_pf_contribution || 0,
      payPeriods: data.pay_periods || 0
    },
    generatedAt: new Date().toISOString()
  };
}

// Helper function to generate mock tax statement PDF
function generateMockTaxStatementPDF(taxStatement: any): Buffer {
  const content = `
ANNUAL TAX STATEMENT - ${taxStatement.year}
==========================================

Employee: ${taxStatement.employee.name}
Employee ID: ${taxStatement.employee.employeeId}
Department: ${taxStatement.employee.department}

ANNUAL SUMMARY:
---------------
Total Gross Pay: NPR ${taxStatement.summary.totalGrossPay.toLocaleString()}
Total Tax Deducted: NPR ${taxStatement.summary.totalTaxDeducted.toLocaleString()}
Total PF Contribution: NPR ${taxStatement.summary.totalPFContribution.toLocaleString()}
Pay Periods: ${taxStatement.summary.payPeriods}

Generated: ${new Date(taxStatement.generatedAt).toLocaleString()}

---
This is a computer-generated document for tax filing purposes.
  `;

  return Buffer.from(content, 'utf-8');
}
