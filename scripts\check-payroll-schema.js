require('dotenv').config({ path: '.env.local' });
const { neon } = require('@neondatabase/serverless');

async function checkSchema() {
  try {
    const sql = neon(process.env.DATABASE_URL);
    
    console.log('🔍 Checking existing payroll schema...');
    
    // Check if payroll tables exist
    const tables = await sql`
      SELECT table_name 
      FROM information_schema.tables 
      WHERE table_name IN ('payroll', 'payroll_components_master', 'employee_pay_structure', 'payroll_periods')
      ORDER BY table_name
    `;
    
    console.log('Existing payroll tables:', tables.map(t => t.table_name));
    
    if (tables.length === 0) {
      console.log('⚠️  No payroll tables found. Need to run base schema first.');
      return false;
    } else {
      console.log('✅ Base payroll schema exists. Ready for enhancements.');
      
      // Check users table structure
      const userColumns = await sql`
        SELECT column_name 
        FROM information_schema.columns 
        WHERE table_name = 'users'
        ORDER BY column_name
      `;
      
      console.log('Current user table columns:', userColumns.map(c => c.column_name));
      return true;
    }
    
  } catch (error) {
    console.error('❌ Schema check failed:', error.message);
    return false;
  }
}

checkSchema();
