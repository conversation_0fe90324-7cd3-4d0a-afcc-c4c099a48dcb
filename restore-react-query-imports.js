#!/usr/bin/env node

/**
 * <PERSON>ript to restore original React Query imports after installation
 * Run this after successfully installing @tanstack/react-query packages
 */

const fs = require('fs')
const path = require('path')

console.log('🔄 Restoring React Query imports...')

// File restoration mappings
const restorations = [
  {
    file: 'lib/react-query-provider.tsx',
    content: `"use client"

import { QueryClient, QueryClientProvider } from "@tanstack/react-query"
import { ReactQueryDevtools } from "@tanstack/react-query-devtools"
import { useState } from "react"

export function ReactQueryProvider({ children }: { children: React.ReactNode }) {
  const [queryClient] = useState(
    () =>
      new QueryClient({
        defaultOptions: {
          queries: {
            staleTime: 60 * 1000, // 1 minute
            gcTime: 10 * 60 * 1000, // 10 minutes
            retry: (failureCount, error: any) => {
              // Don't retry on 4xx errors
              if (error?.status >= 400 && error?.status < 500) {
                return false
              }
              return failureCount < 3
            },
          },
          mutations: {
            retry: false,
          },
        },
      })
  )

  return (
    <QueryClientProvider client={queryClient}>
      {children}
      <ReactQueryDevtools initialIsOpen={false} />
    </QueryClientProvider>
  )
}
`
  },
  {
    file: 'hooks/use-tasks.ts',
    startsWith: 'import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query"\nimport { toast } from "@/hooks/use-toast"'
  },
  {
    file: 'hooks/use-projects.ts',
    startsWith: 'import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query"\nimport { toast } from "@/hooks/use-toast"'
  },
  {
    file: 'hooks/use-comments.ts',
    startsWith: 'import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query"\nimport { toast } from "@/hooks/use-toast"'
  },
  {
    file: 'hooks/use-attachments.ts',
    startsWith: 'import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query"\nimport { toast } from "@/hooks/use-toast"'
  }
]

function restoreFile(filePath, content, startsWith) {
  try {
    if (!fs.existsSync(filePath)) {
      console.log(`❌ File not found: ${filePath}`)
      return false
    }

    const currentContent = fs.readFileSync(filePath, 'utf8')
    
    if (content) {
      // Full content replacement
      fs.writeFileSync(filePath, content)
      console.log(`✅ Restored: ${filePath}`)
      return true
    } else if (startsWith) {
      // Replace the fallback imports with original imports
      const lines = currentContent.split('\n')
      const fallbackStartIndex = lines.findIndex(line => line.includes('// Temporary fallback until React Query is installed'))
      
      if (fallbackStartIndex === -1) {
        console.log(`ℹ️  No fallback found in: ${filePath} (already restored?)`)
        return true
      }

      // Find the end of the fallback block
      let fallbackEndIndex = fallbackStartIndex
      for (let i = fallbackStartIndex; i < lines.length; i++) {
        if (lines[i].includes('}') && lines[i + 1] && !lines[i + 1].trim().startsWith('//') && !lines[i + 1].trim().startsWith('let') && !lines[i + 1].trim().startsWith('try') && !lines[i + 1].trim().startsWith('const') && !lines[i + 1].trim().startsWith('}')) {
          fallbackEndIndex = i
          break
        }
      }

      // Replace the fallback block with original imports
      const beforeFallback = lines.slice(0, fallbackStartIndex - 1)
      const afterFallback = lines.slice(fallbackEndIndex + 1)
      
      const restoredContent = [
        startsWith,
        '',
        ...afterFallback
      ].join('\n')

      fs.writeFileSync(filePath, restoredContent)
      console.log(`✅ Restored imports in: ${filePath}`)
      return true
    }
  } catch (error) {
    console.log(`❌ Error restoring ${filePath}:`, error.message)
    return false
  }
}

// Check if React Query is installed
try {
  require.resolve('@tanstack/react-query')
  require.resolve('@tanstack/react-query-devtools')
  console.log('✅ React Query packages found!')
} catch (error) {
  console.log('❌ React Query packages not found. Please install them first:')
  console.log('   npm install @tanstack/react-query @tanstack/react-query-devtools')
  process.exit(1)
}

// Restore files
let successCount = 0
let totalCount = restorations.length

restorations.forEach(({ file, content, startsWith }) => {
  if (restoreFile(file, content, startsWith)) {
    successCount++
  }
})

console.log(`\n📊 Restoration Summary:`)
console.log(`   ✅ Successfully restored: ${successCount}/${totalCount} files`)

if (successCount === totalCount) {
  console.log('\n🎉 All files restored successfully!')
  console.log('   You can now restart your development server:')
  console.log('   npm run dev')
} else {
  console.log('\n⚠️  Some files could not be restored. Please check manually.')
}
