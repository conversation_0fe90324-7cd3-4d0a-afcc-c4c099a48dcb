import { NextRequest, NextResponse } from "next/server"
import { AuthService } from "@/lib/auth-utils"
import { serverDb } from "@/lib/server-db"
import { z } from "zod"

// Validation schema for stage updates
const updateStageSchema = z.object({
  new_stage: z.enum(["early", "assertive", "escalation", "legal_recovery", "complete"]),
  notes: z.string().optional(),
  new_order: z.number().positive().optional(),
})

export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const sessionToken = request.cookies.get("session-token")?.value

    if (!sessionToken) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 })
    }

    const user = await AuthService.verifySession(sessionToken)

    if (!user || !["admin", "hr_manager"].includes(user.role)) {
      return NextResponse.json({ error: "Forbidden" }, { status: 403 })
    }

    const loanId = params.id
    const body = await request.json()
    const validatedData = updateStageSchema.parse(body)

    // Get current loan details
    const currentLoan = await serverDb.sql`
      SELECT * FROM loan_records WHERE id = ${loanId}
    `

    if (currentLoan.length === 0) {
      return NextResponse.json({ error: "Loan not found" }, { status: 404 })
    }

    const loan = currentLoan[0]

    // If stage is not changing, just update order if provided
    if (loan.current_stage === validatedData.new_stage) {
      if (validatedData.new_order) {
        await serverDb.sql`
          UPDATE loan_records 
          SET stage_order = ${validatedData.new_order}, updated_by = ${user.id}
          WHERE id = ${loanId}
        `
      }

      return NextResponse.json({
        success: true,
        message: "Loan order updated",
      })
    }

    // Get the next available order for the new stage
    let newOrder = validatedData.new_order
    if (!newOrder) {
      const maxOrderResult = await serverDb.sql`
        SELECT COALESCE(MAX(stage_order), 0) as max_order
        FROM loan_records 
        WHERE current_stage = ${validatedData.new_stage}
      `
      newOrder = maxOrderResult[0].max_order + 1
    }

    // Start transaction for stage update
    await serverDb.sql`BEGIN`

    try {
      // Update the loan stage and order
      await serverDb.sql`
        UPDATE loan_records 
        SET 
          current_stage = ${validatedData.new_stage},
          stage_order = ${newOrder},
          updated_by = ${user.id},
          updated_at = NOW()
        WHERE id = ${loanId}
      `

      // Log the stage transition
      await serverDb.sql`
        INSERT INTO loan_stage_transitions (
          loan_id, from_stage, to_stage, transitioned_by, notes
        )
        VALUES (
          ${loanId},
          ${loan.current_stage},
          ${validatedData.new_stage},
          ${user.id},
          ${validatedData.notes || null}
        )
      `

      // Reorder remaining loans in the old stage
      await serverDb.sql`
        UPDATE loan_records 
        SET stage_order = stage_order - 1
        WHERE current_stage = ${loan.current_stage} 
        AND stage_order > ${loan.stage_order}
      `

      // If inserting in the middle of new stage, shift others down
      if (validatedData.new_order) {
        await serverDb.sql`
          UPDATE loan_records 
          SET stage_order = stage_order + 1
          WHERE current_stage = ${validatedData.new_stage} 
          AND stage_order >= ${validatedData.new_order}
          AND id != ${loanId}
        `
      }

      await serverDb.sql`COMMIT`

      // Get updated loan with customer details
      const updatedLoan = await serverDb.sql`
        SELECT 
          lr.*,
          c.name as customer_name,
          c.phone as customer_phone,
          c.email as customer_email,
          (lr.loan_amount - lr.amount_paid) as outstanding_amount
        FROM loan_records lr
        LEFT JOIN loan_recovery_customers c ON lr.customer_id = c.id
        WHERE lr.id = ${loanId}
      `

      return NextResponse.json({
        success: true,
        loan: updatedLoan[0],
        message: `Loan moved to ${validatedData.new_stage} stage`,
      })

    } catch (error) {
      await serverDb.sql`ROLLBACK`
      throw error
    }

  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: "Validation error", details: error.errors },
        { status: 400 }
      )
    }

    console.error("Update loan stage API error:", error)
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    )
  }
}

// Get stage transition history for a loan
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const sessionToken = request.cookies.get("session-token")?.value

    if (!sessionToken) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 })
    }

    const user = await AuthService.verifySession(sessionToken)

    if (!user || !["admin", "hr_manager"].includes(user.role)) {
      return NextResponse.json({ error: "Forbidden" }, { status: 403 })
    }

    const loanId = params.id

    const transitions = await serverDb.sql`
      SELECT 
        lst.*,
        u.full_name as transitioned_by_name
      FROM loan_stage_transitions lst
      LEFT JOIN users u ON lst.transitioned_by = u.id
      WHERE lst.loan_id = ${loanId}
      ORDER BY lst.transition_date DESC
    `

    return NextResponse.json({
      success: true,
      transitions,
    })
  } catch (error) {
    console.error("Get stage transitions API error:", error)
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    )
  }
}
