#!/usr/bin/env node

// Migration script to fix attendance table column data types
const fs = require('fs');
const path = require('path');

let neon, dotenv;

try {
  ({ neon } = require('@neondatabase/serverless'));
  dotenv = require('dotenv');
  dotenv.config({ path: '.env.local' });
} catch (error) {
  console.log('⚠️  Required packages not installed yet. Please run: npm install @neondatabase/serverless dotenv');
  console.log('Error:', error.message);
  process.exit(1);
}

async function runMigration() {
  console.log('🔄 Running attendance table migration...\n');
  
  // Check if DATABASE_URL is set
  if (!process.env.DATABASE_URL) {
    console.error('❌ ERROR: DATABASE_URL environment variable is not set');
    console.log('📝 Please update your .env.local file with your Neon connection string');
    process.exit(1);
  }
  
  console.log('✅ DATABASE_URL found in environment');
  
  try {
    const sql = neon(process.env.DATABASE_URL);
    
    // Test connection first
    console.log('🔄 Testing database connection...');
    await sql`SELECT 1`;
    console.log('✅ Database connection successful!\n');
    
    // Check current table structure
    console.log('🔍 Checking current attendance table structure...');
    const tableInfo = await sql`
      SELECT column_name, data_type, is_nullable
      FROM information_schema.columns 
      WHERE table_name = 'attendance' 
      AND column_name IN ('check_in_time', 'check_out_time')
      ORDER BY column_name;
    `;
    
    if (tableInfo.length === 0) {
      console.log('⚠️  Attendance table not found or columns missing');
      console.log('📝 Please run the main database setup script first');
      process.exit(1);
    }
    
    console.log('📊 Current column structure:');
    tableInfo.forEach(col => {
      console.log(`  - ${col.column_name}: ${col.data_type} (nullable: ${col.is_nullable})`);
    });
    
    // Check if migration is needed
    const needsMigration = tableInfo.some(col => col.data_type === 'time without time zone');
    
    if (!needsMigration) {
      console.log('\n✅ Columns are already TIMESTAMP WITH TIME ZONE, no migration needed!');
      return;
    }
    
    console.log('\n🔄 Migration needed, proceeding...');
    
    // Read and execute migration script
    const migrationPath = path.join(__dirname, 'migrate-attendance-columns.sql');
    
    if (!fs.existsSync(migrationPath)) {
      console.error('❌ Migration script not found:', migrationPath);
      process.exit(1);
    }
    
    const migrationSQL = fs.readFileSync(migrationPath, 'utf8');
    console.log('📄 Executing migration script...');
    
    // Execute the migration
    await sql.unsafe(migrationSQL);
    
    console.log('✅ Migration script executed successfully!');
    
    // Verify the migration
    console.log('\n🔍 Verifying migration results...');
    const updatedTableInfo = await sql`
      SELECT column_name, data_type, is_nullable
      FROM information_schema.columns 
      WHERE table_name = 'attendance' 
      AND column_name IN ('check_in_time', 'check_out_time')
      ORDER BY column_name;
    `;
    
    console.log('📊 Updated column structure:');
    updatedTableInfo.forEach(col => {
      const status = col.data_type === 'timestamp with time zone' ? '✅' : '❌';
      console.log(`  ${status} ${col.column_name}: ${col.data_type} (nullable: ${col.is_nullable})`);
    });
    
    // Check if all columns are now correct
    const allCorrect = updatedTableInfo.every(col => col.data_type === 'timestamp with time zone');
    
    if (allCorrect) {
      console.log('\n🎉 Migration completed successfully!');
      console.log('✅ All attendance time columns are now TIMESTAMP WITH TIME ZONE');
      console.log('\n📝 Next steps:');
      console.log('  1. Test the clock-in functionality from /employee/attendance');
      console.log('  2. Test the admin attendance management from /admin/attendance');
      console.log('  3. Verify that timestamps are stored correctly');
    } else {
      console.log('\n❌ Migration verification failed');
      console.log('⚠️  Some columns may not have been updated correctly');
      process.exit(1);
    }
    
  } catch (error) {
    console.error('❌ Migration failed:', error);
    console.log('\n🔧 Troubleshooting:');
    console.log('  1. Check your DATABASE_URL is correct');
    console.log('  2. Ensure you have proper database permissions');
    console.log('  3. Verify the attendance table exists');
    console.log('  4. Check the migration script syntax');
    process.exit(1);
  }
}

// Run the migration
runMigration().catch(error => {
  console.error('❌ Unexpected error:', error);
  process.exit(1);
});
