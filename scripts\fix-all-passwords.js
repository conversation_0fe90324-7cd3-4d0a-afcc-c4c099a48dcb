#!/usr/bin/env node

const { neon } = require('@neondatabase/serverless');
const bcrypt = require('bcryptjs');
require('dotenv').config({ path: '.env.local' });

async function fixAllPasswords() {
  console.log('Fixing all user passwords...');
  
  try {
    const sql = neon(process.env.DATABASE_URL);
    
    // Generate correct hash for admin123
    const hash = await bcrypt.hash('admin123', 12);
    console.log('Generated hash for admin123');
    
    // Update all users
    const result = await sql`
      UPDATE users 
      SET password_hash = ${hash}
      WHERE is_active = true
    `;
    
    console.log('Updated all active users');
    
    // Get all users to verify
    const users = await sql`
      SELECT email, full_name FROM users WHERE is_active = true ORDER BY email
    `;
    
    console.log(`\nFixed passwords for ${users.length} users:`);
    users.forEach(user => {
      console.log(`- ${user.email} (${user.full_name})`);
    });
    
    console.log('\nAll users now have password: admin123');
    
  } catch (error) {
    console.error('Error:', error.message);
  }
}

fixAllPasswords();
