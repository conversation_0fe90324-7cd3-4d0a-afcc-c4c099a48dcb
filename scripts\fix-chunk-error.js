#!/usr/bin/env node

/**
 * <PERSON>ript to fix Next.js ChunkLoadError issues
 * This script addresses common causes of chunk loading failures
 */

const fs = require('fs');
const path = require('path');

console.log('🔧 Fixing Next.js ChunkLoadError Issues');
console.log('======================================\n');

async function fixChunkError() {
  try {
    // Step 1: Check if .next directory exists and remove it
    console.log('1. Checking and cleaning build cache...');
    const nextDir = path.join(process.cwd(), '.next');
    
    if (fs.existsSync(nextDir)) {
      console.log('   Found .next directory, removing...');
      try {
        fs.rmSync(nextDir, { recursive: true, force: true });
        console.log('   ✅ Removed .next directory');
      } catch (error) {
        console.log('   ⚠️  Could not remove .next directory:', error.message);
      }
    } else {
      console.log('   ✅ .next directory already clean');
    }

    // Step 2: Check next.config.js for potential issues
    console.log('\n2. Checking Next.js configuration...');
    const nextConfigPath = path.join(process.cwd(), 'next.config.js');
    const nextConfigMjsPath = path.join(process.cwd(), 'next.config.mjs');
    
    if (fs.existsSync(nextConfigPath)) {
      console.log('   Found next.config.js');
      const config = fs.readFileSync(nextConfigPath, 'utf8');
      console.log('   ✅ Configuration file exists');
    } else if (fs.existsSync(nextConfigMjsPath)) {
      console.log('   Found next.config.mjs');
      console.log('   ✅ Configuration file exists');
    } else {
      console.log('   No next.config.js found (using defaults)');
    }

    // Step 3: Check package.json for Next.js version
    console.log('\n3. Checking Next.js version...');
    const packageJsonPath = path.join(process.cwd(), 'package.json');
    
    if (fs.existsSync(packageJsonPath)) {
      const packageJson = JSON.parse(fs.readFileSync(packageJsonPath, 'utf8'));
      const nextVersion = packageJson.dependencies?.next || packageJson.devDependencies?.next;
      
      if (nextVersion) {
        console.log(`   Next.js version: ${nextVersion}`);
        
        // Check if version is outdated
        if (nextVersion.includes('14.2.16')) {
          console.log('   ⚠️  Next.js version 14.2.16 is outdated and may have chunk loading issues');
          console.log('   💡 Consider updating to Next.js 15.x for better stability');
        } else {
          console.log('   ✅ Next.js version looks good');
        }
      } else {
        console.log('   ❌ Next.js not found in dependencies');
      }
    }

    // Step 4: Check for common problematic files
    console.log('\n4. Checking for problematic files...');
    
    const problematicPaths = [
      'app/layout.tsx',
      'app/page.tsx',
      'app/globals.css',
      'components/auth-provider.tsx',
      'components/app-header.tsx',
      'lib/react-query-provider.tsx'
    ];
    
    for (const filePath of problematicPaths) {
      const fullPath = path.join(process.cwd(), filePath);
      if (fs.existsSync(fullPath)) {
        console.log(`   ✅ ${filePath} exists`);
      } else {
        console.log(`   ❌ ${filePath} missing`);
      }
    }

    // Step 5: Create a simple next.config.js if it doesn't exist
    console.log('\n5. Ensuring proper Next.js configuration...');
    
    if (!fs.existsSync(nextConfigPath) && !fs.existsSync(nextConfigMjsPath)) {
      console.log('   Creating basic next.config.js...');
      
      const basicConfig = `/** @type {import('next').NextConfig} */
const nextConfig = {
  experimental: {
    // Disable SWC minification if causing issues
    swcMinify: false,
  },
  // Ensure proper chunk loading
  webpack: (config, { isServer }) => {
    if (!isServer) {
      config.resolve.fallback = {
        ...config.resolve.fallback,
        fs: false,
      };
    }
    return config;
  },
  // Optimize chunk loading
  async headers() {
    return [
      {
        source: '/_next/static/(.*)',
        headers: [
          {
            key: 'Cache-Control',
            value: 'public, max-age=31536000, immutable',
          },
        ],
      },
    ];
  },
};

module.exports = nextConfig;
`;
      
      fs.writeFileSync(nextConfigPath, basicConfig);
      console.log('   ✅ Created basic next.config.js');
    } else {
      console.log('   ✅ Next.js configuration already exists');
    }

    // Step 6: Provide recommendations
    console.log('\n6. Recommendations to fix ChunkLoadError:');
    console.log('   📋 Manual steps to try:');
    console.log('   1. Run: npm run dev');
    console.log('   2. If error persists, try: npm run build && npm run start');
    console.log('   3. Check browser console for specific error details');
    console.log('   4. Try accessing http://localhost:3000 in incognito mode');
    console.log('   5. Clear browser cache and cookies');
    console.log('   6. If using VS Code, restart the terminal');
    
    console.log('\n   🔧 Advanced fixes if needed:');
    console.log('   1. Update Next.js: npm install next@latest');
    console.log('   2. Clear npm cache: npm cache clean --force');
    console.log('   3. Delete node_modules and reinstall: rm -rf node_modules && npm install');
    console.log('   4. Check for TypeScript errors: npm run type-check');
    
    console.log('\n🎉 Chunk error fix preparation completed!');
    console.log('Try running the development server now.');
    
  } catch (error) {
    console.error('❌ Error during chunk fix:', error);
    process.exit(1);
  }
}

// Run the fix
if (require.main === module) {
  fixChunkError();
}

module.exports = { fixChunkError };
