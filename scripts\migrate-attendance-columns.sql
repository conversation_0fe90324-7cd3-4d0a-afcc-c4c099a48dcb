-- Migration script to fix attendance table column data types
-- This script updates check_in_time and check_out_time from TIME to TIMESTAMP WITH TIME ZONE

-- First, check if the table exists and what the current column types are
DO $$
BEGIN
    -- Check if attendance table exists
    IF EXISTS (SELECT FROM information_schema.tables WHERE table_name = 'attendance') THEN
        RAISE NOTICE 'Attendance table exists, proceeding with migration...';
        
        -- Check current column types
        IF EXISTS (
            SELECT FROM information_schema.columns 
            WHERE table_name = 'attendance' 
            AND column_name = 'check_in_time' 
            AND data_type = 'time without time zone'
        ) THEN
            RAISE NOTICE 'Found TIME columns, converting to TIMESTAMP WITH TIME ZONE...';
            
            -- Step 1: Add temporary columns with the correct data type
            ALTER TABLE attendance ADD COLUMN IF NOT EXISTS check_in_time_temp TIMESTAMP WITH TIME ZONE;
            ALTER TABLE attendance ADD COLUMN IF NOT EXISTS check_out_time_temp TIMESTAMP WITH TIME ZONE;
            
            -- Step 2: Migrate existing data
            -- For TIME values, we'll combine them with the date column to create proper timestamps
            UPDATE attendance 
            SET check_in_time_temp = CASE 
                WHEN check_in_time IS NOT NULL THEN 
                    (date + check_in_time)::TIMESTAMP WITH TIME ZONE
                ELSE NULL 
            END,
            check_out_time_temp = CASE 
                WHEN check_out_time IS NOT NULL THEN 
                    (date + check_out_time)::TIMESTAMP WITH TIME ZONE
                ELSE NULL 
            END;
            
            -- Step 3: Drop old columns
            ALTER TABLE attendance DROP COLUMN IF EXISTS check_in_time;
            ALTER TABLE attendance DROP COLUMN IF EXISTS check_out_time;
            
            -- Step 4: Rename temporary columns
            ALTER TABLE attendance RENAME COLUMN check_in_time_temp TO check_in_time;
            ALTER TABLE attendance RENAME COLUMN check_out_time_temp TO check_out_time;
            
            RAISE NOTICE 'Migration completed successfully!';
        ELSE
            RAISE NOTICE 'Columns are already TIMESTAMP WITH TIME ZONE, no migration needed.';
        END IF;
    ELSE
        RAISE NOTICE 'Attendance table does not exist, please run the main table creation script first.';
    END IF;
END $$;

-- Verify the migration
DO $$
BEGIN
    IF EXISTS (
        SELECT FROM information_schema.columns 
        WHERE table_name = 'attendance' 
        AND column_name = 'check_in_time' 
        AND data_type = 'timestamp with time zone'
    ) THEN
        RAISE NOTICE 'Verification: check_in_time is now TIMESTAMP WITH TIME ZONE ✓';
    ELSE
        RAISE WARNING 'Verification failed: check_in_time is not TIMESTAMP WITH TIME ZONE';
    END IF;
    
    IF EXISTS (
        SELECT FROM information_schema.columns 
        WHERE table_name = 'attendance' 
        AND column_name = 'check_out_time' 
        AND data_type = 'timestamp with time zone'
    ) THEN
        RAISE NOTICE 'Verification: check_out_time is now TIMESTAMP WITH TIME ZONE ✓';
    ELSE
        RAISE WARNING 'Verification failed: check_out_time is not TIMESTAMP WITH TIME ZONE';
    END IF;
END $$;
