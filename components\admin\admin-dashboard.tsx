"use client"

import React, { useState, useEffect } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Button } from "@/components/ui/button"
import { Progress } from "@/components/ui/progress"
import { 
  Users, 
  Building, 
  UserCheck, 
  UserX, 
  TrendingUp, 
  Calendar,
  DollarSign,
  Clock,
  Activity,
  AlertTriangle,
  CheckCircle,
  XCircle
} from "lucide-react"
import { useAuth } from "@/components/auth-provider"
import Link from "next/link"

interface DashboardStats {
  totalUsers: number
  activeUsers: number
  inactiveUsers: number
  totalDepartments: number
  newUsersThisMonth: number
  pendingApprovals: number
  recentActivity: ActivityItem[]
  departmentStats: DepartmentStat[]
  roleDistribution: RoleDistribution[]
}

interface ActivityItem {
  id: string
  type: string
  description: string
  timestamp: string
  user?: string
}

interface DepartmentStat {
  name: string
  userCount: number
  activeCount: number
}

interface RoleDistribution {
  role: string
  count: number
  percentage: number
}

export function AdminDashboard() {
  const { user } = useAuth()
  const [stats, setStats] = useState<DashboardStats | null>(null)
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    fetchDashboardStats()
  }, [])

  const fetchDashboardStats = async () => {
    try {
      setLoading(true)
      
      // Fetch users
      const usersResponse = await fetch("/api/admin/users")
      const usersData = await usersResponse.json()
      
      // Fetch departments
      const deptResponse = await fetch("/api/admin/departments")
      const deptData = await deptResponse.json()
      
      if (usersData.success && deptData.success) {
        const users = usersData.users
        const departments = deptData.departments
        
        // Calculate stats
        const totalUsers = users.length
        const activeUsers = users.filter((u: any) => u.is_active).length
        const inactiveUsers = totalUsers - activeUsers
        
        // Calculate new users this month
        const thisMonth = new Date()
        thisMonth.setDate(1)
        const newUsersThisMonth = users.filter((u: any) => 
          new Date(u.created_at) >= thisMonth
        ).length
        
        // Department stats
        const departmentStats = departments.map((dept: any) => {
          const deptUsers = users.filter((u: any) => u.department === dept.name)
          return {
            name: dept.name,
            userCount: deptUsers.length,
            activeCount: deptUsers.filter((u: any) => u.is_active).length
          }
        })
        
        // Role distribution
        const roleCount = users.reduce((acc: any, user: any) => {
          acc[user.role] = (acc[user.role] || 0) + 1
          return acc
        }, {})
        
        const roleDistribution = Object.entries(roleCount).map(([role, count]: [string, any]) => ({
          role,
          count,
          percentage: Math.round((count / totalUsers) * 100)
        }))
        
        // Mock recent activity (in a real app, this would come from audit logs)
        const recentActivity = [
          {
            id: "1",
            type: "user_created",
            description: "New user account created",
            timestamp: new Date().toISOString(),
            user: "Admin"
          },
          {
            id: "2",
            type: "role_changed",
            description: "User role updated to Manager",
            timestamp: new Date(Date.now() - 3600000).toISOString(),
            user: "HR Manager"
          },
          {
            id: "3",
            type: "department_created",
            description: "New department added",
            timestamp: new Date(Date.now() - 7200000).toISOString(),
            user: "Admin"
          }
        ]
        
        setStats({
          totalUsers,
          activeUsers,
          inactiveUsers,
          totalDepartments: departments.length,
          newUsersThisMonth,
          pendingApprovals: 0, // Mock data
          recentActivity,
          departmentStats,
          roleDistribution
        })
      }
    } catch (error) {
      console.error("Error fetching dashboard stats:", error)
    } finally {
      setLoading(false)
    }
  }

  const getActivityIcon = (type: string) => {
    switch (type) {
      case "user_created": return UserCheck
      case "user_deactivated": return UserX
      case "role_changed": return Users
      case "department_created": return Building
      default: return Activity
    }
  }

  const getActivityColor = (type: string) => {
    switch (type) {
      case "user_created": return "text-green-600"
      case "user_deactivated": return "text-red-600"
      case "role_changed": return "text-blue-600"
      case "department_created": return "text-purple-600"
      default: return "text-gray-600"
    }
  }

  if (loading) {
    return (
      <div className="space-y-6">
        <div className="text-center py-8">Loading dashboard...</div>
      </div>
    )
  }

  if (!stats) {
    return (
      <div className="space-y-6">
        <div className="text-center py-8">Failed to load dashboard data</div>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div>
        <h1 className="text-3xl font-bold">Admin Dashboard</h1>
        <p className="text-gray-600">Welcome back, {user?.full_name}. Here's what's happening in your organization.</p>
      </div>

      {/* Key Metrics */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Users</CardTitle>
            <Users className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.totalUsers}</div>
            <p className="text-xs text-muted-foreground">
              +{stats.newUsersThisMonth} this month
            </p>
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Active Users</CardTitle>
            <UserCheck className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.activeUsers}</div>
            <p className="text-xs text-muted-foreground">
              {Math.round((stats.activeUsers / stats.totalUsers) * 100)}% of total
            </p>
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Departments</CardTitle>
            <Building className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.totalDepartments}</div>
            <p className="text-xs text-muted-foreground">
              Across organization
            </p>
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Inactive Users</CardTitle>
            <UserX className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.inactiveUsers}</div>
            <p className="text-xs text-muted-foreground">
              Need attention
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Quick Actions */}
      <Card>
        <CardHeader>
          <CardTitle>Quick Actions</CardTitle>
          <CardDescription>Common administrative tasks</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="flex flex-wrap gap-2">
            <Link href="/admin/users">
              <Button variant="outline">
                <Users className="w-4 h-4 mr-2" />
                Manage Users
              </Button>
            </Link>
            <Link href="/admin/departments">
              <Button variant="outline">
                <Building className="w-4 h-4 mr-2" />
                Manage Departments
              </Button>
            </Link>
            <Link href="/admin/reports">
              <Button variant="outline">
                <TrendingUp className="w-4 h-4 mr-2" />
                View Reports
              </Button>
            </Link>
            <Link href="/admin/settings">
              <Button variant="outline">
                <Activity className="w-4 h-4 mr-2" />
                System Settings
              </Button>
            </Link>
          </div>
        </CardContent>
      </Card>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Role Distribution */}
        <Card>
          <CardHeader>
            <CardTitle>Role Distribution</CardTitle>
            <CardDescription>User roles across the organization</CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            {stats.roleDistribution.map((role) => (
              <div key={role.role} className="space-y-2">
                <div className="flex justify-between text-sm">
                  <span className="capitalize">{role.role.replace("_", " ")}</span>
                  <span>{role.count} users ({role.percentage}%)</span>
                </div>
                <Progress value={role.percentage} className="h-2" />
              </div>
            ))}
          </CardContent>
        </Card>

        {/* Department Overview */}
        <Card>
          <CardHeader>
            <CardTitle>Department Overview</CardTitle>
            <CardDescription>User distribution by department</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {stats.departmentStats.map((dept) => (
                <div key={dept.name} className="flex items-center justify-between">
                  <div>
                    <div className="font-medium">{dept.name}</div>
                    <div className="text-sm text-gray-500">
                      {dept.activeCount} active of {dept.userCount} total
                    </div>
                  </div>
                  <Badge variant="outline">
                    {dept.userCount} users
                  </Badge>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Recent Activity */}
      <Card>
        <CardHeader>
          <CardTitle>Recent Activity</CardTitle>
          <CardDescription>Latest system activities and changes</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {stats.recentActivity.map((activity) => {
              const Icon = getActivityIcon(activity.type)
              const colorClass = getActivityColor(activity.type)
              
              return (
                <div key={activity.id} className="flex items-start gap-3">
                  <Icon className={`w-4 h-4 mt-1 ${colorClass}`} />
                  <div className="flex-1">
                    <div className="text-sm">{activity.description}</div>
                    <div className="text-xs text-gray-500">
                      {new Date(activity.timestamp).toLocaleString()} • by {activity.user}
                    </div>
                  </div>
                </div>
              )
            })}
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
