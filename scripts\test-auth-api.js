#!/usr/bin/env node

require('dotenv').config({ path: '.env.local' });

async function testAuthAPI() {
  console.log('🔍 TESTING AUTHENTICATION API ENDPOINTS');
  console.log('=======================================');
  
  // Test if the development server is running
  const baseUrl = 'http://localhost:3000';
  
  try {
    // Test 1: Check if server is running
    console.log('🔄 Checking if development server is running...');
    
    try {
      const healthResponse = await fetch(`${baseUrl}/api/health`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        },
      });
      
      if (healthResponse.ok) {
        console.log('✅ Development server is running');
      } else {
        console.log('⚠️  Development server responded but with error status');
      }
    } catch (error) {
      console.log('❌ Development server is not running or not accessible');
      console.log('💡 Please start the server with: npm run dev');
      return;
    }
    
    // Test 2: Test login API endpoint
    console.log('\n🔄 Testing login API endpoint...');
    
    const loginData = {
      email: '<EMAIL>',
      password: 'admin123'
    };
    
    try {
      const loginResponse = await fetch(`${baseUrl}/api/auth/login`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(loginData),
        credentials: 'include',
      });
      
      console.log(`Login response status: ${loginResponse.status}`);
      
      if (loginResponse.ok) {
        const loginResult = await loginResponse.json();
        console.log('✅ Login API successful');
        console.log(`User: ${loginResult.user?.full_name} (${loginResult.user?.email})`);
        
        // Extract session cookie
        const setCookieHeader = loginResponse.headers.get('set-cookie');
        let sessionToken = null;
        
        if (setCookieHeader) {
          const sessionMatch = setCookieHeader.match(/session-token=([^;]+)/);
          if (sessionMatch) {
            sessionToken = sessionMatch[1];
            console.log('✅ Session token received');
          }
        }
        
        // Test 3: Test session verification with /api/auth/me
        if (sessionToken) {
          console.log('\n🔄 Testing session verification...');
          
          const meResponse = await fetch(`${baseUrl}/api/auth/me`, {
            method: 'GET',
            headers: {
              'Content-Type': 'application/json',
              'Cookie': `session-token=${sessionToken}`,
            },
            credentials: 'include',
          });
          
          console.log(`Me API response status: ${meResponse.status}`);
          
          if (meResponse.ok) {
            const userData = await meResponse.json();
            console.log('✅ Session verification successful');
            console.log(`Verified user: ${userData.full_name} (${userData.email})`);
          } else {
            console.log('❌ Session verification failed');
            const errorText = await meResponse.text();
            console.log('Error response:', errorText);
          }
          
          // Test 4: Test logout
          console.log('\n🔄 Testing logout...');
          
          const logoutResponse = await fetch(`${baseUrl}/api/auth/logout`, {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
              'Cookie': `session-token=${sessionToken}`,
            },
            credentials: 'include',
          });
          
          if (logoutResponse.ok) {
            console.log('✅ Logout successful');
          } else {
            console.log('❌ Logout failed');
          }
        }
        
      } else {
        console.log('❌ Login API failed');
        const errorText = await loginResponse.text();
        console.log('Error response:', errorText);
        
        // Try to parse as JSON for better error details
        try {
          const errorJson = JSON.parse(errorText);
          console.log('Error details:', errorJson);
        } catch (e) {
          console.log('Raw error:', errorText);
        }
      }
      
    } catch (error) {
      console.log('❌ Login API request failed');
      console.log('Error:', error.message);
      
      if (error.message.includes('fetch failed')) {
        console.log('💡 This suggests a network connectivity issue');
        console.log('💡 Make sure the development server is running on port 3000');
      }
      
      if (error.message.includes('timeout')) {
        console.log('💡 This suggests a database timeout issue');
        console.log('💡 Check the database connection and query performance');
      }
    }
    
    console.log('\n📊 API TEST SUMMARY:');
    console.log('====================');
    console.log('If the login API failed, possible causes:');
    console.log('1. Development server not running (npm run dev)');
    console.log('2. Database connection timeout in API routes');
    console.log('3. Environment variables not loaded properly');
    console.log('4. Authentication logic errors');
    
    console.log('\n🔧 TROUBLESHOOTING STEPS:');
    console.log('1. Ensure development server is running: npm run dev');
    console.log('2. Check server console for error messages');
    console.log('3. Verify DATABASE_URL is loaded in API routes');
    console.log('4. Check browser network tab for detailed error info');
    
  } catch (error) {
    console.error('❌ API test failed:', error.message);
  }
}

// Helper function to test if server is running
async function isServerRunning() {
  try {
    const response = await fetch('http://localhost:3000', { 
      method: 'HEAD',
      signal: AbortSignal.timeout(5000) // 5 second timeout
    });
    return true;
  } catch (error) {
    return false;
  }
}

// Run the test
testAuthAPI();
