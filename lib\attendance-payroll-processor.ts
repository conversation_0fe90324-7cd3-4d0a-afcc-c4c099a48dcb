// Attendance-to-Payroll Data Pipeline
// Phase 2: Core Payroll Engine Development - Attendance Integration

import { db } from './neon';
import { nepalConfig } from './nepal-config';
import { NepaliCalendar } from './nepali-calendar';
import { calculateWorkingHours, calculateTotalDailyHours } from './attendance-utils';

export interface AttendancePayrollData {
  userId: string;
  periodStart: string;
  periodEnd: string;
  dailyAttendance: DailyAttendanceData[];
  periodSummary: AttendancePeriodSummary;
  payrollAdjustments: PayrollAdjustments;
}

export interface DailyAttendanceData {
  date: string;
  bsDate: string;
  dayOfWeek: number;
  isWorkingDay: boolean;
  isHoliday: boolean;
  holidayName?: string;
  
  // Session data
  sessions: AttendanceSession[];
  sessionCount: number;
  
  // Time calculations
  totalHours: number;
  regularHours: number;
  overtimeHours: number;
  breakHours: number;
  
  // Status and penalties
  status: 'present' | 'absent' | 'late' | 'half_day' | 'on_leave';
  lateMinutes: number;
  earlyDeparture: boolean;
  
  // Payroll impact
  payableHours: number;
  overtimePayableHours: number;
  latePenalty: number;
  attendanceBonus: number;
}

export interface AttendanceSession {
  sequence: number;
  entryType: 'regular' | 'break' | 'meeting' | 'overtime';
  checkInTime: string;
  checkOutTime?: string;
  duration: number;
  isActive: boolean;
  notes?: string;
}

export interface AttendancePeriodSummary {
  totalDays: number;
  workingDays: number;
  presentDays: number;
  absentDays: number;
  lateDays: number;
  halfDays: number;
  leaveDays: number;
  holidayDays: number;
  
  totalHours: number;
  regularHours: number;
  overtimeHours: number;
  payableHours: number;
  
  attendanceRate: number;
  punctualityRate: number;
  averageHoursPerDay: number;
  
  // Payroll calculations
  totalLatePenalty: number;
  totalAttendanceBonus: number;
  eligibleForPerfectAttendanceBonus: boolean;
}

export interface PayrollAdjustments {
  attendanceBonus: number;
  latePenalty: number;
  overtimeEligible: boolean;
  perfectAttendanceBonus: number;
  holidayWorkBonus: number;
  earlyDeparturePenalty: number;
}

export class AttendancePayrollProcessor {
  private nepalConfig = nepalConfig;

  /**
   * Main function to process attendance data for payroll period
   */
  async processAttendanceForPayroll(
    userId: string,
    periodStart: string,
    periodEnd: string
  ): Promise<AttendancePayrollData> {
    try {
      // Validate inputs
      this.validateInputs(userId, periodStart, periodEnd);

      // Get raw attendance data from database
      const rawAttendanceData = await db.getAttendanceForPayrollPeriod(userId, periodStart, periodEnd);

      // Process daily attendance data
      const dailyAttendance = await this.processDailyAttendanceData(rawAttendanceData, periodStart, periodEnd);

      // Calculate period summary
      const periodSummary = this.calculatePeriodSummary(dailyAttendance, periodStart, periodEnd);

      // Calculate payroll adjustments
      const payrollAdjustments = this.calculatePayrollAdjustments(dailyAttendance, periodSummary);

      return {
        userId,
        periodStart,
        periodEnd,
        dailyAttendance,
        periodSummary,
        payrollAdjustments
      };
    } catch (error) {
      console.error('Error processing attendance for payroll:', error);
      throw new Error(`Failed to process attendance data: ${error.message}`);
    }
  }

  /**
   * Process raw attendance data into structured daily data
   */
  private async processDailyAttendanceData(
    rawData: any[],
    periodStart: string,
    periodEnd: string
  ): Promise<DailyAttendanceData[]> {
    const dailyData: DailyAttendanceData[] = [];
    
    // Create date range for the period
    const startDate = new Date(periodStart);
    const endDate = new Date(periodEnd);
    const currentDate = new Date(startDate);

    while (currentDate <= endDate) {
      const dateString = currentDate.toISOString().split('T')[0];
      const attendanceForDate = rawData.find(data => data.date === dateString);
      
      const dailyAttendance = await this.processSingleDayAttendance(dateString, attendanceForDate);
      dailyData.push(dailyAttendance);
      
      currentDate.setDate(currentDate.getDate() + 1);
    }

    return dailyData;
  }

  /**
   * Process attendance data for a single day
   */
  private async processSingleDayAttendance(
    date: string,
    attendanceData?: any
  ): Promise<DailyAttendanceData> {
    const dateObj = new Date(date);
    const bsDate = NepaliCalendar.formatBSDate(NepaliCalendar.adToBS(dateObj));
    const dayOfWeek = dateObj.getDay();
    
    // Check if it's a working day and holiday
    const isWorkingDay = this.nepalConfig.isWorkingDay(dateObj);
    const isHoliday = this.nepalConfig.isHoliday(date);
    const holidayInfo = this.nepalConfig.getHolidaysInRange(date, date)[0];

    if (!attendanceData) {
      // No attendance data for this day
      return {
        date,
        bsDate,
        dayOfWeek,
        isWorkingDay,
        isHoliday,
        holidayName: holidayInfo?.name,
        sessions: [],
        sessionCount: 0,
        totalHours: 0,
        regularHours: 0,
        overtimeHours: 0,
        breakHours: 0,
        status: isWorkingDay && !isHoliday ? 'absent' : 'on_leave',
        lateMinutes: 0,
        earlyDeparture: false,
        payableHours: 0,
        overtimePayableHours: 0,
        latePenalty: 0,
        attendanceBonus: 0
      };
    }

    // Process attendance data
    const sessions = await this.getSessionsForDate(attendanceData.userId, date);
    const timeCalculations = this.calculateDayTimeBreakdown(sessions);
    const statusInfo = this.determineDayStatus(attendanceData, timeCalculations);
    const payrollImpact = this.calculateDayPayrollImpact(timeCalculations, statusInfo, isHoliday);

    return {
      date,
      bsDate,
      dayOfWeek,
      isWorkingDay,
      isHoliday,
      holidayName: holidayInfo?.name,
      sessions,
      sessionCount: sessions.length,
      totalHours: timeCalculations.totalHours,
      regularHours: timeCalculations.regularHours,
      overtimeHours: timeCalculations.overtimeHours,
      breakHours: timeCalculations.breakHours,
      status: statusInfo.status,
      lateMinutes: statusInfo.lateMinutes,
      earlyDeparture: statusInfo.earlyDeparture,
      payableHours: payrollImpact.payableHours,
      overtimePayableHours: payrollImpact.overtimePayableHours,
      latePenalty: payrollImpact.latePenalty,
      attendanceBonus: payrollImpact.attendanceBonus
    };
  }

  /**
   * Get detailed session data for a specific date
   */
  private async getSessionsForDate(userId: string, date: string): Promise<AttendanceSession[]> {
    try {
      // This would get detailed session data from the enhanced attendance table
      const sessions = await db.sql`
        SELECT 
          daily_sequence as sequence,
          entry_type,
          check_in_time,
          check_out_time,
          hours_worked as duration,
          is_active,
          notes
        FROM attendance
        WHERE user_id = ${userId} AND date = ${date}
        ORDER BY daily_sequence ASC
      `;

      return sessions.map(session => ({
        sequence: session.sequence,
        entryType: session.entry_type || 'regular',
        checkInTime: session.check_in_time,
        checkOutTime: session.check_out_time,
        duration: Number(session.duration || 0),
        isActive: Boolean(session.is_active),
        notes: session.notes
      }));
    } catch (error) {
      console.error('Error fetching sessions for date:', error);
      return [];
    }
  }

  /**
   * Calculate time breakdown for a day
   */
  private calculateDayTimeBreakdown(sessions: AttendanceSession[]): {
    totalHours: number;
    regularHours: number;
    overtimeHours: number;
    breakHours: number;
  } {
    let totalHours = 0;
    let breakHours = 0;

    sessions.forEach(session => {
      if (session.entryType === 'break') {
        breakHours += session.duration;
      } else {
        totalHours += session.duration;
      }
    });

    // Calculate regular vs overtime hours
    const laborLaw = this.nepalConfig.getLaborLawConfig();
    const regularHours = Math.min(totalHours, laborLaw.standardWorkingHours);
    const overtimeHours = Math.max(0, totalHours - laborLaw.standardWorkingHours);

    return {
      totalHours,
      regularHours,
      overtimeHours,
      breakHours
    };
  }

  /**
   * Determine day status and penalties
   */
  private determineDayStatus(attendanceData: any, timeCalculations: any): {
    status: 'present' | 'absent' | 'late' | 'half_day' | 'on_leave';
    lateMinutes: number;
    earlyDeparture: boolean;
  } {
    const status = attendanceData.status || 'absent';
    const lateMinutes = attendanceData.lateMinutes || 0;
    const earlyDeparture = attendanceData.earlyDeparture || false;

    return { status, lateMinutes, earlyDeparture };
  }

  /**
   * Calculate payroll impact for a day
   */
  private calculateDayPayrollImpact(
    timeCalculations: any,
    statusInfo: any,
    isHoliday: boolean
  ): {
    payableHours: number;
    overtimePayableHours: number;
    latePenalty: number;
    attendanceBonus: number;
  } {
    let payableHours = timeCalculations.regularHours;
    let overtimePayableHours = timeCalculations.overtimeHours;
    let latePenalty = 0;
    let attendanceBonus = 0;

    // Apply late penalty
    if (statusInfo.lateMinutes > 0) {
      const settings = this.nepalConfig.getConfig();
      latePenalty = statusInfo.lateMinutes * 10; // NPR 10 per minute
    }

    // Holiday work bonus
    if (isHoliday && timeCalculations.totalHours > 0) {
      attendanceBonus = 500; // NPR 500 for working on holiday
    }

    // Half day adjustment
    if (statusInfo.status === 'half_day') {
      payableHours = Math.min(payableHours, 4);
      overtimePayableHours = 0;
    }

    return {
      payableHours,
      overtimePayableHours,
      latePenalty,
      attendanceBonus
    };
  }

  /**
   * Calculate period summary
   */
  private calculatePeriodSummary(
    dailyAttendance: DailyAttendanceData[],
    periodStart: string,
    periodEnd: string
  ): AttendancePeriodSummary {
    const totalDays = dailyAttendance.length;
    const workingDays = dailyAttendance.filter(d => d.isWorkingDay).length;
    const presentDays = dailyAttendance.filter(d => ['present', 'late'].includes(d.status)).length;
    const absentDays = dailyAttendance.filter(d => d.status === 'absent').length;
    const lateDays = dailyAttendance.filter(d => d.status === 'late').length;
    const halfDays = dailyAttendance.filter(d => d.status === 'half_day').length;
    const leaveDays = dailyAttendance.filter(d => d.status === 'on_leave').length;
    const holidayDays = dailyAttendance.filter(d => d.isHoliday).length;

    const totalHours = dailyAttendance.reduce((sum, d) => sum + d.totalHours, 0);
    const regularHours = dailyAttendance.reduce((sum, d) => sum + d.regularHours, 0);
    const overtimeHours = dailyAttendance.reduce((sum, d) => sum + d.overtimeHours, 0);
    const payableHours = dailyAttendance.reduce((sum, d) => sum + d.payableHours, 0);

    const attendanceRate = workingDays > 0 ? (presentDays / workingDays) * 100 : 0;
    const punctualityRate = presentDays > 0 ? ((presentDays - lateDays) / presentDays) * 100 : 0;
    const averageHoursPerDay = presentDays > 0 ? totalHours / presentDays : 0;

    const totalLatePenalty = dailyAttendance.reduce((sum, d) => sum + d.latePenalty, 0);
    const totalAttendanceBonus = dailyAttendance.reduce((sum, d) => sum + d.attendanceBonus, 0);
    const eligibleForPerfectAttendanceBonus = attendanceRate >= 95 && punctualityRate >= 90;

    return {
      totalDays,
      workingDays,
      presentDays,
      absentDays,
      lateDays,
      halfDays,
      leaveDays,
      holidayDays,
      totalHours,
      regularHours,
      overtimeHours,
      payableHours,
      attendanceRate: Math.round(attendanceRate * 100) / 100,
      punctualityRate: Math.round(punctualityRate * 100) / 100,
      averageHoursPerDay: Math.round(averageHoursPerDay * 100) / 100,
      totalLatePenalty,
      totalAttendanceBonus,
      eligibleForPerfectAttendanceBonus
    };
  }

  /**
   * Calculate payroll adjustments based on attendance
   */
  private calculatePayrollAdjustments(
    dailyAttendance: DailyAttendanceData[],
    periodSummary: AttendancePeriodSummary
  ): PayrollAdjustments {
    let attendanceBonus = 0;
    let perfectAttendanceBonus = 0;
    let holidayWorkBonus = 0;

    // Perfect attendance bonus
    if (periodSummary.eligibleForPerfectAttendanceBonus) {
      perfectAttendanceBonus = 2000; // NPR 2000
    }

    // Regular attendance bonus
    if (periodSummary.attendanceRate >= 95) {
      attendanceBonus = 1000; // NPR 1000
    }

    // Holiday work bonus
    holidayWorkBonus = dailyAttendance
      .filter(d => d.isHoliday && d.totalHours > 0)
      .reduce((sum, d) => sum + d.attendanceBonus, 0);

    return {
      attendanceBonus,
      latePenalty: periodSummary.totalLatePenalty,
      overtimeEligible: periodSummary.overtimeHours > 0,
      perfectAttendanceBonus,
      holidayWorkBonus,
      earlyDeparturePenalty: 0 // Would be calculated based on early departures
    };
  }

  /**
   * Validate inputs
   */
  private validateInputs(userId: string, periodStart: string, periodEnd: string): void {
    if (!userId) throw new Error('User ID is required');
    if (!periodStart || !periodEnd) throw new Error('Period start and end dates are required');
    if (new Date(periodStart) >= new Date(periodEnd)) {
      throw new Error('Period start date must be before end date');
    }
  }

  /**
   * Get attendance summary for multiple users (bulk processing)
   */
  async processBulkAttendanceForPayroll(
    userIds: string[],
    periodStart: string,
    periodEnd: string
  ): Promise<{ [userId: string]: AttendancePayrollData }> {
    const results: { [userId: string]: AttendancePayrollData } = {};

    for (const userId of userIds) {
      try {
        results[userId] = await this.processAttendanceForPayroll(userId, periodStart, periodEnd);
      } catch (error) {
        console.error(`Error processing attendance for user ${userId}:`, error);
        // Continue with other users even if one fails
      }
    }

    return results;
  }
}

// Export singleton instance
export const attendancePayrollProcessor = new AttendancePayrollProcessor();
