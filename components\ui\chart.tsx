"use client"

import type * as React from "react"
import { <PERSON><PERSON><PERSON> as <PERSON><PERSON><PERSON>Component, <PERSON><PERSON><PERSON> as Line<PERSON>hartComponent } from "recharts"
import {
  Line,
  Bar,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  ResponsiveContainer,
  Legend,
  Pie,
  Cell,
  <PERSON><PERSON><PERSON> as <PERSON><PERSON><PERSON>Wrapper,
  type TooltipProps,
} from "recharts"
import { cn } from "@/lib/utils"

interface ChartProps extends React.HTMLAttributes<HTMLDivElement> {
  data: any[]
  index: string
  categories: string[]
  colors?: string[]
  valueFormatter?: (value: number) => string
  yAxisWidth?: number
  showLegend?: boolean
  showXAxis?: boolean
  showYAxis?: boolean
  showGrid?: boolean
}

interface PieChartProps extends React.HTMLAttributes<HTMLDivElement> {
  data: any[]
  index: string
  category: string
  colors?: string[]
  valueFormatter?: (value: number) => string
  showLegend?: boolean
}

const ChartTooltip = ({
  active,
  payload,
  label,
  valueFormatter,
}: <PERSON>ltipP<PERSON><any, any> & {
  valueFormatter?: (value: number) => string
}) => {
  if (!active || !payload) {
    return null
  }

  return (
    <div className="rounded-lg border bg-background dark:bg-gray-800 dark:border-gray-700 p-2 shadow-sm">
      <div className="grid grid-cols-2 gap-2">
        <div className="flex flex-col">
          <span className="text-[0.70rem] uppercase text-muted-foreground dark:text-gray-400">{label}</span>
          <span className="font-bold text-muted-foreground dark:text-gray-300">
            {valueFormatter ? valueFormatter(payload[0]?.value) : payload[0]?.value}
          </span>
        </div>
      </div>
    </div>
  )
}

export function LineChart({
  data,
  index,
  categories,
  colors = ["#4ade80", "#f87171", "#60a5fa"],
  valueFormatter = (value: number) => `${value}`,
  yAxisWidth = 56,
  showLegend = true,
  showXAxis = true,
  showYAxis = true,
  showGrid = true,
  className,
  ...props
}: ChartProps) {
  return (
    <div className={cn("w-full h-[200px]", className)} {...props}>
      <ResponsiveContainer width="100%" height="100%">
        <LineChartComponent
          data={data}
          margin={{
            top: 16,
            right: 16,
            bottom: 16,
            left: 16,
          }}
        >
          {showGrid && <CartesianGrid strokeDasharray="3 3" className="stroke-muted dark:stroke-gray-700" />}
          {showXAxis && (
            <XAxis
              dataKey={index}
              className="text-sm text-muted-foreground dark:text-gray-400"
              tickLine={false}
              axisLine={false}
              padding={{ left: 16, right: 16 }}
              minTickGap={8}
            />
          )}
          {showYAxis && (
            <YAxis
              width={yAxisWidth}
              className="text-sm text-muted-foreground dark:text-gray-400"
              tickLine={false}
              axisLine={false}
              tickFormatter={valueFormatter}
            />
          )}
          <Tooltip content={(props) => <ChartTooltip {...props} valueFormatter={valueFormatter} />} />
          {showLegend && (
            <Legend
              content={({ payload }) => (
                <div className="flex flex-wrap gap-4 text-sm text-muted-foreground dark:text-gray-400">
                  {payload?.map((entry, index) => (
                    <div key={`item-${index}`} className="flex items-center gap-1">
                      <div className="h-2 w-2 rounded-full" style={{ backgroundColor: entry.color }} />
                      <span>{entry.value}</span>
                    </div>
                  ))}
                </div>
              )}
              className="text-xs text-muted-foreground dark:text-gray-400"
              verticalAlign="top"
              height={32}
            />
          )}
          {categories.map((category, index) => (
            <Line
              key={category}
              type="monotone"
              dataKey={category}
              stroke={colors[index % colors.length]}
              strokeWidth={2}
              activeDot={{ r: 6 }}
              className="stroke-primary dark:stroke-blue-400"
            />
          ))}
        </LineChartComponent>
      </ResponsiveContainer>
    </div>
  )
}

export function BarChart({
  data,
  index,
  categories,
  colors = ["#4ade80", "#f87171", "#60a5fa"],
  valueFormatter = (value: number) => `${value}`,
  yAxisWidth = 56,
  showLegend = true,
  showXAxis = true,
  showYAxis = true,
  showGrid = true,
  className,
  ...props
}: ChartProps) {
  return (
    <div className={cn("w-full h-[200px]", className)} {...props}>
      <ResponsiveContainer width="100%" height="100%">
        <BarChartComponent
          data={data}
          margin={{
            top: 16,
            right: 16,
            bottom: 16,
            left: 16,
          }}
        >
          {showGrid && <CartesianGrid strokeDasharray="3 3" className="stroke-muted dark:stroke-gray-700" />}
          {showXAxis && (
            <XAxis
              dataKey={index}
              className="text-sm text-muted-foreground dark:text-gray-400"
              tickLine={false}
              axisLine={false}
            />
          )}
          {showYAxis && (
            <YAxis
              width={yAxisWidth}
              className="text-sm text-muted-foreground dark:text-gray-400"
              tickLine={false}
              axisLine={false}
              tickFormatter={valueFormatter}
            />
          )}
          <Tooltip content={(props) => <ChartTooltip {...props} valueFormatter={valueFormatter} />} />
          {showLegend && (
            <Legend
              content={({ payload }) => (
                <div className="flex flex-wrap gap-4 text-sm text-muted-foreground dark:text-gray-400">
                  {payload?.map((entry, index) => (
                    <div key={`item-${index}`} className="flex items-center gap-1">
                      <div className="h-2 w-2 rounded-full" style={{ backgroundColor: entry.color }} />
                      <span>{entry.value}</span>
                    </div>
                  ))}
                </div>
              )}
              className="text-xs text-muted-foreground dark:text-gray-400"
              verticalAlign="top"
              height={32}
            />
          )}
          {categories.map((category, index) => (
            <Bar key={category} dataKey={category} fill={colors[index % colors.length]} radius={[4, 4, 0, 0]} />
          ))}
        </BarChartComponent>
      </ResponsiveContainer>
    </div>
  )
}

export function PieChart({
  data,
  index,
  category,
  colors = ["#4ade80", "#f87171", "#60a5fa", "#fbbf24", "#c084fc"],
  valueFormatter = (value: number) => `${value}`,
  showLegend = true,
  className,
  ...props
}: PieChartProps) {
  return (
    <div className={cn("w-full h-[200px]", className)} {...props}>
      <ResponsiveContainer width="100%" height="100%">
        <PieChartWrapper
          margin={{
            top: 16,
            right: 16,
            bottom: 16,
            left: 16,
          }}
        >
          <Pie
            data={data}
            dataKey={category}
            nameKey={index}
            cx="50%"
            cy="50%"
            outerRadius={80}
            innerRadius={40}
            fill="#8884d8"
            paddingAngle={2}
            label={({ name, percent }) => `${name}: ${(percent * 100).toFixed(0)}%`}
            labelLine={false}
          >
            {data.map((entry, index) => (
              <Cell key={`cell-${index}`} fill={colors[index % colors.length]} />
            ))}
          </Pie>
          <Tooltip content={(props) => <ChartTooltip {...props} valueFormatter={valueFormatter} />} />
          {showLegend && (
            <Legend
              content={({ payload }) => (
                <div className="flex flex-wrap justify-center gap-4 text-sm text-muted-foreground dark:text-gray-400">
                  {payload?.map((entry, index) => (
                    <div key={`item-${index}`} className="flex items-center gap-1">
                      <div className="h-2 w-2 rounded-full" style={{ backgroundColor: entry.color }} />
                      <span>{entry.value}</span>
                    </div>
                  ))}
                </div>
              )}
              className="text-xs text-muted-foreground dark:text-gray-400"
              verticalAlign="bottom"
              height={32}
            />
          )}
        </PieChartWrapper>
      </ResponsiveContainer>
    </div>
  )
}
