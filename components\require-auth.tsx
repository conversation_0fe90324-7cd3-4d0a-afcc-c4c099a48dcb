"use client"

import type React from "react"

import { useEffect } from "react"
import { useRouter } from "next/navigation"
import { useAuth } from "@/components/auth-provider"

interface RequireAuthProps {
  children: React.ReactNode
  requiredRole?: "admin" | "user"
}

export function RequireAuth({ children, requiredRole }: RequireAuthProps) {
  const { user, isAdmin } = useAuth()
  const router = useRouter()

  useEffect(() => {
    if (!user) {
      router.push("/auth/login")
      return
    }

    if (requiredRole === "admin" && !isAdmin) {
      router.push("/")
      return
    }
  }, [user, isAdmin, router, requiredRole])

  // If we're checking for authentication and there's no user, don't render children
  if (!user) {
    return null
  }

  // If we're checking for admin role and user is not admin, don't render children
  if (requiredRole === "admin" && !isAdmin) {
    return null
  }

  return <>{children}</>
}
