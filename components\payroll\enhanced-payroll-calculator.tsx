"use client"

import React, { useState, useEffect } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Badge } from '@/components/ui/badge'
import { <PERSON><PERSON>, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog'
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table'
import { Progress } from '@/components/ui/progress'
import { toast } from 'sonner'
import { Calculator, Play, Save, CheckCircle, Clock, DollarSign, <PERSON>, TrendingUp, Calendar, Eye, Download } from 'lucide-react'

interface PayrollCalculation {
  user_id: string
  employee_name: string
  employee_type: string
  base_salary: number
  hourly_rate: number
  attendance_summary: {
    total_working_days: number
    days_present: number
    days_absent: number
    days_late: number
    total_hours_worked: number
    regular_hours: number
    overtime_hours: number
  }
  regular_pay: number
  overtime_pay: number
  attendance_bonus: number
  late_penalty: number
  total_allowances: number
  total_deductions: number
  gross_pay: number
  income_tax: number
  provident_fund: number
  net_pay: number
  payroll_period: string
  status: string
}

export function EnhancedPayrollCalculator() {
  const [calculations, setCalculations] = useState<PayrollCalculation[]>([])
  const [existingPayrolls, setExistingPayrolls] = useState<any[]>([])
  const [loading, setLoading] = useState(false)
  const [calculating, setCalculating] = useState(false)
  const [selectedYear, setSelectedYear] = useState(new Date().getFullYear())
  const [selectedMonth, setSelectedMonth] = useState(new Date().getMonth() + 1)
  const [showPreviewDialog, setShowPreviewDialog] = useState(false)
  const [selectedCalculation, setSelectedCalculation] = useState<PayrollCalculation | null>(null)
  const [calculationProgress, setCalculationProgress] = useState(0)

  useEffect(() => {
    fetchExistingPayrolls()
  }, [selectedYear, selectedMonth])

  const fetchExistingPayrolls = async () => {
    try {
      setLoading(true)
      const response = await fetch(`/api/admin/payroll/enhanced-calculate?action=existing&year=${selectedYear}&month=${selectedMonth}`)
      const data = await response.json()

      if (data.success) {
        setExistingPayrolls(data.data)
      } else {
        toast.error(data.error || 'Failed to fetch existing payrolls')
      }
    } catch (error) {
      console.error('Error fetching existing payrolls:', error)
      toast.error('Failed to fetch existing payrolls')
    } finally {
      setLoading(false)
    }
  }

  const handleBulkPreview = async () => {
    try {
      setCalculating(true)
      setCalculationProgress(0)
      
      const response = await fetch(`/api/admin/payroll/enhanced-calculate?action=bulk_preview&year=${selectedYear}&month=${selectedMonth}`)
      const data = await response.json()

      if (data.success) {
        setCalculations(data.data)
        setCalculationProgress(100)
        toast.success(`Payroll calculated for ${data.data.length} employees`)
      } else {
        toast.error(data.error || 'Failed to calculate payroll')
      }
    } catch (error) {
      console.error('Error calculating bulk payroll:', error)
      toast.error('Failed to calculate payroll')
    } finally {
      setCalculating(false)
    }
  }

  const handleSaveCalculations = async () => {
    try {
      if (calculations.length === 0) {
        toast.error('No calculations to save')
        return
      }

      setLoading(true)
      const response = await fetch('/api/admin/payroll/enhanced-calculate', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          action: 'save_calculations',
          calculations: calculations
        })
      })

      const data = await response.json()

      if (data.success) {
        toast.success(data.message)
        setCalculations([])
        fetchExistingPayrolls()
      } else {
        toast.error(data.error || 'Failed to save calculations')
      }
    } catch (error) {
      console.error('Error saving calculations:', error)
      toast.error('Failed to save calculations')
    } finally {
      setLoading(false)
    }
  }

  const handleBulkCalculateAndSave = async () => {
    try {
      setCalculating(true)
      setCalculationProgress(0)
      
      const response = await fetch('/api/admin/payroll/enhanced-calculate', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          action: 'calculate_bulk',
          year: selectedYear,
          month: selectedMonth
        })
      })

      const data = await response.json()

      if (data.success) {
        setCalculationProgress(100)
        toast.success(data.message)
        fetchExistingPayrolls()
      } else {
        toast.error(data.error || 'Failed to calculate and save payroll')
      }
    } catch (error) {
      console.error('Error calculating and saving payroll:', error)
      toast.error('Failed to calculate and save payroll')
    } finally {
      setCalculating(false)
    }
  }

  const handleUpdateStatus = async (payrollIds: string[], status: string) => {
    try {
      setLoading(true)
      const response = await fetch('/api/admin/payroll/enhanced-calculate', {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          action: 'update_status',
          payrollIds: payrollIds,
          status: status
        })
      })

      const data = await response.json()

      if (data.success) {
        toast.success(data.message)
        fetchExistingPayrolls()
      } else {
        toast.error(data.error || 'Failed to update status')
      }
    } catch (error) {
      console.error('Error updating status:', error)
      toast.error('Failed to update status')
    } finally {
      setLoading(false)
    }
  }

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'draft':
        return <Badge variant="secondary">Draft</Badge>
      case 'calculated':
        return <Badge variant="outline">Calculated</Badge>
      case 'approved':
        return <Badge variant="default">Approved</Badge>
      case 'processed':
        return <Badge variant="destructive">Processed</Badge>
      default:
        return <Badge variant="secondary">{status}</Badge>
    }
  }

  const currentMonthYear = `${selectedYear}-${selectedMonth.toString().padStart(2, '0')}`

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-3xl font-bold">Enhanced Payroll Calculator</h2>
          <p className="text-muted-foreground">
            Automatic payroll calculation based on attendance data with allowances and deductions
          </p>
        </div>
        <div className="flex items-center space-x-2">
          <Select value={selectedMonth.toString()} onValueChange={(value) => setSelectedMonth(parseInt(value))}>
            <SelectTrigger className="w-32">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              {Array.from({ length: 12 }, (_, i) => (
                <SelectItem key={i + 1} value={(i + 1).toString()}>
                  {new Date(0, i).toLocaleString('default', { month: 'long' })}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
          <Select value={selectedYear.toString()} onValueChange={(value) => setSelectedYear(parseInt(value))}>
            <SelectTrigger className="w-24">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              {Array.from({ length: 5 }, (_, i) => (
                <SelectItem key={selectedYear - 2 + i} value={(selectedYear - 2 + i).toString()}>
                  {selectedYear - 2 + i}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>
      </div>

      {/* Action Cards */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <Calculator className="h-5 w-5 mr-2" />
              Preview Calculation
            </CardTitle>
            <CardDescription>Calculate payroll without saving to review results</CardDescription>
          </CardHeader>
          <CardContent>
            <Button 
              onClick={handleBulkPreview} 
              disabled={calculating || loading}
              className="w-full"
            >
              <Eye className="h-4 w-4 mr-2" />
              {calculating ? 'Calculating...' : 'Preview Payroll'}
            </Button>
            {calculating && (
              <div className="mt-4">
                <Progress value={calculationProgress} className="w-full" />
                <p className="text-sm text-muted-foreground mt-2">
                  Calculating payroll... {calculationProgress}%
                </p>
              </div>
            )}
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <Play className="h-5 w-5 mr-2" />
              Calculate & Save
            </CardTitle>
            <CardDescription>Calculate and save payroll directly to database</CardDescription>
          </CardHeader>
          <CardContent>
            <Button 
              onClick={handleBulkCalculateAndSave} 
              disabled={calculating || loading}
              className="w-full"
            >
              <Save className="h-4 w-4 mr-2" />
              {calculating ? 'Processing...' : 'Calculate & Save'}
            </Button>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <CheckCircle className="h-5 w-5 mr-2" />
              Save Preview
            </CardTitle>
            <CardDescription>Save the current preview calculations</CardDescription>
          </CardHeader>
          <CardContent>
            <Button 
              onClick={handleSaveCalculations} 
              disabled={calculations.length === 0 || loading}
              variant="outline"
              className="w-full"
            >
              <Save className="h-4 w-4 mr-2" />
              Save {calculations.length} Calculations
            </Button>
          </CardContent>
        </Card>
      </div>

      <Tabs defaultValue="preview" className="space-y-4">
        <TabsList>
          <TabsTrigger value="preview">
            Preview Results
            {calculations.length > 0 && (
              <Badge variant="secondary" className="ml-2">
                {calculations.length}
              </Badge>
            )}
          </TabsTrigger>
          <TabsTrigger value="existing">
            Existing Payrolls
            {existingPayrolls.length > 0 && (
              <Badge variant="default" className="ml-2">
                {existingPayrolls.length}
              </Badge>
            )}
          </TabsTrigger>
        </TabsList>

        <TabsContent value="preview" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Payroll Calculation Preview</CardTitle>
              <CardDescription>
                Preview payroll calculations for {new Date(selectedYear, selectedMonth - 1).toLocaleString('default', { month: 'long', year: 'numeric' })}
              </CardDescription>
            </CardHeader>
            <CardContent>
              {calculations.length === 0 ? (
                <div className="text-center py-8 text-muted-foreground">
                  <Calculator className="h-12 w-12 mx-auto mb-4 opacity-50" />
                  <p>No calculations available. Click "Preview Payroll" to calculate.</p>
                </div>
              ) : (
                <div className="space-y-4">
                  {/* Summary */}
                  <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
                    <Card>
                      <CardContent className="p-4">
                        <div className="flex items-center justify-between">
                          <div>
                            <p className="text-sm text-muted-foreground">Total Employees</p>
                            <p className="text-2xl font-bold">{calculations.length}</p>
                          </div>
                          <Users className="h-8 w-8 text-muted-foreground" />
                        </div>
                      </CardContent>
                    </Card>
                    
                    <Card>
                      <CardContent className="p-4">
                        <div className="flex items-center justify-between">
                          <div>
                            <p className="text-sm text-muted-foreground">Total Gross Pay</p>
                            <p className="text-2xl font-bold">
                              NPR {calculations.reduce((sum, calc) => sum + calc.gross_pay, 0).toLocaleString()}
                            </p>
                          </div>
                          <DollarSign className="h-8 w-8 text-muted-foreground" />
                        </div>
                      </CardContent>
                    </Card>
                    
                    <Card>
                      <CardContent className="p-4">
                        <div className="flex items-center justify-between">
                          <div>
                            <p className="text-sm text-muted-foreground">Total Net Pay</p>
                            <p className="text-2xl font-bold">
                              NPR {calculations.reduce((sum, calc) => sum + calc.net_pay, 0).toLocaleString()}
                            </p>
                          </div>
                          <TrendingUp className="h-8 w-8 text-muted-foreground" />
                        </div>
                      </CardContent>
                    </Card>
                    
                    <Card>
                      <CardContent className="p-4">
                        <div className="flex items-center justify-between">
                          <div>
                            <p className="text-sm text-muted-foreground">Avg Net Pay</p>
                            <p className="text-2xl font-bold">
                              NPR {(calculations.reduce((sum, calc) => sum + calc.net_pay, 0) / calculations.length).toLocaleString()}
                            </p>
                          </div>
                          <Calculator className="h-8 w-8 text-muted-foreground" />
                        </div>
                      </CardContent>
                    </Card>
                  </div>

                  {/* Calculations Table */}
                  <div className="rounded-md border">
                    <Table>
                      <TableHeader>
                        <TableRow>
                          <TableHead>Employee</TableHead>
                          <TableHead>Base Salary</TableHead>
                          <TableHead>Attendance</TableHead>
                          <TableHead>Gross Pay</TableHead>
                          <TableHead>Deductions</TableHead>
                          <TableHead>Net Pay</TableHead>
                          <TableHead>Actions</TableHead>
                        </TableRow>
                      </TableHeader>
                      <TableBody>
                        {calculations.map((calc) => (
                          <TableRow key={calc.user_id}>
                            <TableCell>
                              <div className="space-y-1">
                                <div className="font-medium">{calc.employee_name}</div>
                                <Badge variant="outline">{calc.employee_type.replace('_', ' ')}</Badge>
                              </div>
                            </TableCell>
                            <TableCell>
                              <div className="font-medium">NPR {calc.base_salary.toLocaleString()}</div>
                              <div className="text-sm text-muted-foreground">
                                NPR {calc.hourly_rate.toFixed(2)}/hr
                              </div>
                            </TableCell>
                            <TableCell>
                              <div className="space-y-1">
                                <div className="text-sm">
                                  {calc.attendance_summary.days_present}/{calc.attendance_summary.total_working_days} days
                                </div>
                                <div className="text-sm text-muted-foreground">
                                  {calc.attendance_summary.total_hours_worked.toFixed(1)}h total
                                </div>
                              </div>
                            </TableCell>
                            <TableCell>
                              <div className="font-medium">NPR {calc.gross_pay.toLocaleString()}</div>
                              <div className="text-sm text-muted-foreground">
                                +NPR {calc.total_allowances.toLocaleString()} allowances
                              </div>
                            </TableCell>
                            <TableCell>
                              <div className="text-sm">
                                Tax: NPR {calc.income_tax.toLocaleString()}
                              </div>
                              <div className="text-sm">
                                PF: NPR {calc.provident_fund.toLocaleString()}
                              </div>
                              <div className="text-sm">
                                Other: NPR {calc.total_deductions.toLocaleString()}
                              </div>
                            </TableCell>
                            <TableCell>
                              <div className="font-bold text-green-600">
                                NPR {calc.net_pay.toLocaleString()}
                              </div>
                            </TableCell>
                            <TableCell>
                              <Button
                                size="sm"
                                variant="outline"
                                onClick={() => {
                                  setSelectedCalculation(calc)
                                  setShowPreviewDialog(true)
                                }}
                              >
                                <Eye className="h-4 w-4" />
                              </Button>
                            </TableCell>
                          </TableRow>
                        ))}
                      </TableBody>
                    </Table>
                  </div>
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="existing" className="space-y-4">
          <Card>
            <CardHeader>
              <div className="flex items-center justify-between">
                <div>
                  <CardTitle>Existing Payroll Records</CardTitle>
                  <CardDescription>
                    Saved payroll calculations for {new Date(selectedYear, selectedMonth - 1).toLocaleString('default', { month: 'long', year: 'numeric' })}
                  </CardDescription>
                </div>
                {existingPayrolls.length > 0 && (
                  <div className="flex items-center space-x-2">
                    <Button
                      size="sm"
                      onClick={() => handleUpdateStatus(existingPayrolls.map(p => p.id), 'approved')}
                      disabled={loading}
                    >
                      <CheckCircle className="h-4 w-4 mr-2" />
                      Approve All
                    </Button>
                  </div>
                )}
              </div>
            </CardHeader>
            <CardContent>
              {existingPayrolls.length === 0 ? (
                <div className="text-center py-8 text-muted-foreground">
                  <Calendar className="h-12 w-12 mx-auto mb-4 opacity-50" />
                  <p>No payroll records found for this period</p>
                </div>
              ) : (
                <div className="rounded-md border">
                  <Table>
                    <TableHeader>
                      <TableRow>
                        <TableHead>Employee</TableHead>
                        <TableHead>Department</TableHead>
                        <TableHead>Gross Pay</TableHead>
                        <TableHead>Net Pay</TableHead>
                        <TableHead>Status</TableHead>
                        <TableHead>Actions</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {existingPayrolls.map((payroll) => (
                        <TableRow key={payroll.id}>
                          <TableCell>
                            <div className="space-y-1">
                              <div className="font-medium">{payroll.full_name}</div>
                              <div className="text-sm text-muted-foreground">{payroll.email}</div>
                            </div>
                          </TableCell>
                          <TableCell>
                            <Badge variant="outline">{payroll.department || 'Not assigned'}</Badge>
                          </TableCell>
                          <TableCell>
                            <div className="font-medium">NPR {payroll.gross_pay?.toLocaleString() || '0'}</div>
                          </TableCell>
                          <TableCell>
                            <div className="font-bold text-green-600">
                              NPR {payroll.net_pay?.toLocaleString() || '0'}
                            </div>
                          </TableCell>
                          <TableCell>
                            {getStatusBadge(payroll.status)}
                          </TableCell>
                          <TableCell>
                            <div className="flex items-center space-x-2">
                              {payroll.status === 'calculated' && (
                                <Button
                                  size="sm"
                                  onClick={() => handleUpdateStatus([payroll.id], 'approved')}
                                  disabled={loading}
                                >
                                  <CheckCircle className="h-4 w-4 mr-1" />
                                  Approve
                                </Button>
                              )}
                              <Button size="sm" variant="outline">
                                <Eye className="h-4 w-4" />
                              </Button>
                            </div>
                          </TableCell>
                        </TableRow>
                      ))}
                    </TableBody>
                  </Table>
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>

      {/* Preview Dialog */}
      <Dialog open={showPreviewDialog} onOpenChange={setShowPreviewDialog}>
        <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle>Payroll Calculation Details</DialogTitle>
            <DialogDescription>
              {selectedCalculation && `Detailed breakdown for ${selectedCalculation.employee_name}`}
            </DialogDescription>
          </DialogHeader>
          {selectedCalculation && (
            <div className="space-y-6">
              {/* Employee Info */}
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Label>Employee Name</Label>
                  <p className="font-medium">{selectedCalculation.employee_name}</p>
                </div>
                <div>
                  <Label>Employee Type</Label>
                  <p className="font-medium">{selectedCalculation.employee_type.replace('_', ' ')}</p>
                </div>
                <div>
                  <Label>Base Salary</Label>
                  <p className="font-medium">NPR {selectedCalculation.base_salary.toLocaleString()}</p>
                </div>
                <div>
                  <Label>Hourly Rate</Label>
                  <p className="font-medium">NPR {selectedCalculation.hourly_rate.toFixed(2)}</p>
                </div>
              </div>

              {/* Attendance Summary */}
              <div>
                <h4 className="font-semibold mb-3">Attendance Summary</h4>
                <div className="grid grid-cols-3 gap-4">
                  <div>
                    <Label>Working Days</Label>
                    <p className="font-medium">{selectedCalculation.attendance_summary.total_working_days}</p>
                  </div>
                  <div>
                    <Label>Days Present</Label>
                    <p className="font-medium">{selectedCalculation.attendance_summary.days_present}</p>
                  </div>
                  <div>
                    <Label>Days Absent</Label>
                    <p className="font-medium">{selectedCalculation.attendance_summary.days_absent}</p>
                  </div>
                  <div>
                    <Label>Regular Hours</Label>
                    <p className="font-medium">{selectedCalculation.attendance_summary.regular_hours.toFixed(1)}</p>
                  </div>
                  <div>
                    <Label>Overtime Hours</Label>
                    <p className="font-medium">{selectedCalculation.attendance_summary.overtime_hours.toFixed(1)}</p>
                  </div>
                  <div>
                    <Label>Total Hours</Label>
                    <p className="font-medium">{selectedCalculation.attendance_summary.total_hours_worked.toFixed(1)}</p>
                  </div>
                </div>
              </div>

              {/* Pay Breakdown */}
              <div>
                <h4 className="font-semibold mb-3">Pay Breakdown</h4>
                <div className="space-y-2">
                  <div className="flex justify-between">
                    <span>Regular Pay:</span>
                    <span className="font-medium">NPR {selectedCalculation.regular_pay.toLocaleString()}</span>
                  </div>
                  <div className="flex justify-between">
                    <span>Overtime Pay:</span>
                    <span className="font-medium">NPR {selectedCalculation.overtime_pay.toLocaleString()}</span>
                  </div>
                  <div className="flex justify-between">
                    <span>Attendance Bonus:</span>
                    <span className="font-medium">NPR {selectedCalculation.attendance_bonus.toLocaleString()}</span>
                  </div>
                  <div className="flex justify-between">
                    <span>Late Penalty:</span>
                    <span className="font-medium text-red-600">-NPR {selectedCalculation.late_penalty.toLocaleString()}</span>
                  </div>
                  <div className="flex justify-between">
                    <span>Total Allowances:</span>
                    <span className="font-medium">NPR {selectedCalculation.total_allowances.toLocaleString()}</span>
                  </div>
                  <div className="border-t pt-2">
                    <div className="flex justify-between font-semibold">
                      <span>Gross Pay:</span>
                      <span>NPR {selectedCalculation.gross_pay.toLocaleString()}</span>
                    </div>
                  </div>
                </div>
              </div>

              {/* Deductions */}
              <div>
                <h4 className="font-semibold mb-3">Deductions</h4>
                <div className="space-y-2">
                  <div className="flex justify-between">
                    <span>Income Tax:</span>
                    <span className="font-medium">NPR {selectedCalculation.income_tax.toLocaleString()}</span>
                  </div>
                  <div className="flex justify-between">
                    <span>Provident Fund:</span>
                    <span className="font-medium">NPR {selectedCalculation.provident_fund.toLocaleString()}</span>
                  </div>
                  <div className="flex justify-between">
                    <span>Other Deductions:</span>
                    <span className="font-medium">NPR {selectedCalculation.total_deductions.toLocaleString()}</span>
                  </div>
                  <div className="border-t pt-2">
                    <div className="flex justify-between font-bold text-green-600">
                      <span>Net Pay:</span>
                      <span>NPR {selectedCalculation.net_pay.toLocaleString()}</span>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          )}
          <DialogFooter>
            <Button variant="outline" onClick={() => setShowPreviewDialog(false)}>
              Close
            </Button>
            <Button>
              <Download className="h-4 w-4 mr-2" />
              Download Payslip
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  )
}
