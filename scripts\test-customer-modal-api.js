const { neon } = require('@neondatabase/serverless');
require('dotenv').config({ path: '.env.local' });

const sql = neon(process.env.DATABASE_URL);

async function testCustomerModalAPI() {
  try {
    console.log('🔍 Testing Customer Detail Modal API...\n');

    // First, let's check if we have any customers
    console.log('1. Checking available customers...');
    const customers = await sql`
      SELECT id, name, phone, email 
      FROM loan_recovery_customers 
      LIMIT 5
    `;
    
    console.log(`Found ${customers.length} customers:`);
    customers.forEach(customer => {
      console.log(`  - ${customer.name} (ID: ${customer.id})`);
    });

    if (customers.length === 0) {
      console.log('❌ No customers found. Creating test customer...');
      
      // Create a test customer
      const [newCustomer] = await sql`
        INSERT INTO loan_recovery_customers (name, phone, email, address)
        VALUES ('Test Customer', '+977-9841234567', '<EMAIL>', 'Test Address')
        RETURNING id, name, phone, email
      `;
      
      console.log('✅ Created test customer:', newCustomer);
      customers.push(newCustomer);
    }

    // Test the customer detail API logic for the first customer
    const testCustomerId = customers[0].id;
    console.log(`\n2. Testing customer detail API for customer: ${testCustomerId}`);

    // This is the exact query from the API endpoint
    const customerDetails = await sql`
      SELECT 
        c.*,
        COALESCE(
          json_agg(
            json_build_object(
              'id', lr.id,
              'loan_amount', lr.loan_amount,
              'amount_paid', lr.amount_paid,
              'outstanding_amount', lr.loan_amount - lr.amount_paid,
              'current_stage', lr.current_stage,
              'due_date', lr.due_date,
              'created_at', lr.created_at
            )
          ) FILTER (WHERE lr.id IS NOT NULL), 
          '[]'::json
        ) as loan_records
      FROM loan_recovery_customers c
      LEFT JOIN loan_records lr ON c.id = lr.customer_id
      WHERE c.id = ${testCustomerId}
      GROUP BY c.id, c.name, c.phone, c.email, c.address, c.created_at, c.updated_at
    `;

    if (customerDetails.length === 0) {
      console.log('❌ Customer not found');
      return;
    }

    const customer = customerDetails[0];
    console.log('✅ Customer details retrieved:');
    console.log({
      id: customer.id,
      name: customer.name,
      phone: customer.phone,
      email: customer.email,
      loanRecords: customer.loan_records.length
    });

    // Test conversation notes (using correct table and structure)
    console.log('\n3. Testing conversation notes...');
    const notes = await sql`
      SELECT
        lcn.*,
        u.full_name as created_by_name
      FROM loan_conversation_notes lcn
      LEFT JOIN users u ON lcn.created_by = u.id
      WHERE lcn.loan_id IN (
        SELECT id FROM loan_records WHERE customer_id = ${testCustomerId}
      )
      ORDER BY lcn.created_at DESC
    `;

    console.log(`Found ${notes.length} conversation notes`);

    // Test reminders (using correct table and structure)
    console.log('\n4. Testing reminders...');
    const reminders = await sql`
      SELECT
        lr.*,
        u.full_name as created_by_name
      FROM loan_reminders lr
      LEFT JOIN users u ON lr.created_by = u.id
      WHERE lr.loan_id IN (
        SELECT id FROM loan_records WHERE customer_id = ${testCustomerId}
      )
      ORDER BY lr.reminder_date ASC
    `;

    console.log(`Found ${reminders.length} reminders`);

    // Test the complete API response format
    console.log('\n5. Testing complete API response format...');
    const apiResponse = {
      success: true,
      customer: {
        ...customer,
        loans: customer.loan_records, // Rename for consistency
        notes: notes,
        reminders: reminders
      }
    };

    console.log('API Response structure:', {
      success: apiResponse.success,
      hasCustomer: !!apiResponse.customer,
      customerName: apiResponse.customer.name,
      loanRecordsCount: apiResponse.customer.loans.length,
      notesCount: apiResponse.customer.notes.length,
      remindersCount: apiResponse.customer.reminders.length
    });

    console.log('\n✅ Customer Detail Modal API test completed successfully!');
    console.log('\n📋 Summary:');
    console.log(`- Customer ID: ${testCustomerId}`);
    console.log(`- Customer Name: ${customer.name}`);
    console.log(`- Loan Records: ${customer.loan_records.length}`);
    console.log(`- Conversation Notes: ${notes.length}`);
    console.log(`- Reminders: ${reminders.length}`);

  } catch (error) {
    console.error('❌ Test failed:', error);
    console.error('Error details:', error.message);
    if (error.stack) {
      console.error('Stack trace:', error.stack);
    }
  }
}

testCustomerModalAPI();
