<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Admin Dashboard Demo - ExoBank</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background-color: #f8fafc;
            color: #1e293b;
        }

        /* Main Header */
        .main-header {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            z-index: 100;
            height: 64px;
            background: white;
            border-bottom: 1px solid #e2e8f0;
            display: flex;
            align-items: center;
            padding: 0 1rem;
        }

        .logo {
            font-size: 1.5rem;
            font-weight: bold;
            color: #059669;
            margin-right: 2rem;
        }

        .main-nav {
            display: flex;
            gap: 2rem;
            margin-right: auto;
        }

        .main-nav a {
            text-decoration: none;
            color: #64748b;
            font-weight: 500;
            transition: color 0.2s;
        }

        .main-nav a:hover {
            color: #059669;
        }

        .user-menu {
            display: flex;
            align-items: center;
            gap: 1rem;
        }

        .user-avatar {
            width: 32px;
            height: 32px;
            border-radius: 50%;
            background: #059669;
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
        }

        /* Admin Sidebar - FIXED POSITIONING */
        .admin-sidebar {
            position: fixed;
            top: 64px;
            left: 0;
            bottom: 0;
            width: 256px;
            background: white;
            border-right: 1px solid #e2e8f0;
            z-index: 50;
            display: flex;
            flex-direction: column;
            overflow-y: auto;
        }

        .sidebar-header {
            padding: 1rem;
            border-bottom: 1px solid #e2e8f0;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .sidebar-logo {
            width: 32px;
            height: 32px;
            background: #059669; /* Updated darker green */
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: bold;
            font-size: 0.875rem;
        }

        .sidebar-title {
            font-weight: 600;
            color: #1e293b;
        }

        .user-info {
            padding: 1rem;
            border-bottom: 1px solid #e2e8f0;
            display: flex;
            align-items: center;
            gap: 0.75rem;
        }

        .user-badge {
            background: rgba(5, 150, 105, 0.1); /* Updated with new green */
            color: #059669; /* Updated darker green */
            border: 1px solid rgba(5, 150, 105, 0.2);
            padding: 0.125rem 0.5rem;
            border-radius: 9999px;
            font-size: 0.75rem;
            font-weight: 500;
        }

        .sidebar-nav {
            flex: 1;
            padding: 1rem;
        }

        .nav-item {
            display: block;
            width: 100%;
            padding: 0.75rem;
            margin-bottom: 0.5rem;
            border: none;
            background: none;
            text-align: left;
            border-radius: 8px;
            color: #64748b;
            text-decoration: none;
            font-weight: 500;
            transition: all 0.2s;
            display: flex;
            align-items: center;
            gap: 0.75rem;
        }

        .nav-item:hover {
            background: #f1f5f9;
            color: #1e293b;
        }

        .nav-item.active {
            background: #059669; /* Updated darker green */
            color: white;
        }

        .nav-item.active:hover {
            background: rgba(5, 150, 105, 0.9); /* Updated darker green with opacity */
        }

        .nav-icon {
            width: 20px;
            height: 20px;
            fill: currentColor;
        }

        .sidebar-footer {
            padding: 1rem;
            border-top: 1px solid #e2e8f0;
        }

        .logout-btn {
            display: flex;
            align-items: center;
            gap: 0.75rem;
            width: 100%;
            padding: 0.75rem;
            border: none;
            background: none;
            color: #64748b;
            border-radius: 8px;
            transition: all 0.2s;
            cursor: pointer;
        }

        .logout-btn:hover {
            background: #f1f5f9;
            color: #1e293b;
        }

        /* Main Content - ADJUSTED FOR FIXED SIDEBAR */
        .main-content {
            margin-left: 256px;
            margin-top: 64px;
            padding: 2rem;
            min-height: calc(100vh - 64px);
        }

        .content-header {
            margin-bottom: 2rem;
        }

        .content-title {
            font-size: 2rem;
            font-weight: bold;
            color: #1e293b;
            margin-bottom: 0.5rem;
        }

        .content-description {
            color: #64748b;
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 1.5rem;
            margin-bottom: 2rem;
        }

        .stat-card {
            background: white;
            padding: 1.5rem;
            border-radius: 12px;
            border: 1px solid #e2e8f0;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
        }

        .stat-title {
            color: #64748b;
            font-size: 0.875rem;
            font-weight: 500;
            margin-bottom: 0.5rem;
        }

        .stat-value {
            font-size: 2rem;
            font-weight: bold;
            color: #1e293b;
        }

        .scrollable-content {
            height: 800px;
            background: white;
            border-radius: 12px;
            border: 1px solid #e2e8f0;
            padding: 1.5rem;
        }

        .scroll-item {
            padding: 1rem;
            border-bottom: 1px solid #e2e8f0;
            margin-bottom: 1rem;
        }

        /* Dark theme */
        @media (prefers-color-scheme: dark) {
            body {
                background-color: #0f172a;
                color: #e2e8f0;
            }

            .main-header {
                background: #1e293b;
                border-bottom-color: #334155;
            }

            .admin-sidebar {
                background: #1e293b;
                border-right-color: #334155;
            }

            .sidebar-header {
                border-bottom-color: #334155;
            }

            .sidebar-title {
                color: #e2e8f0;
            }

            .user-info {
                border-bottom-color: #334155;
            }

            .nav-item {
                color: #94a3b8;
            }

            .nav-item:hover {
                background: #334155;
                color: #e2e8f0;
            }

            .sidebar-footer {
                border-top-color: #334155;
            }

            .logout-btn {
                color: #94a3b8;
            }

            .logout-btn:hover {
                background: #334155;
                color: #e2e8f0;
            }

            .stat-card, .scrollable-content {
                background: #1e293b;
                border-color: #334155;
            }

            .content-title {
                color: #e2e8f0;
            }

            .stat-value {
                color: #e2e8f0;
            }
        }
    </style>
</head>
<body>
    <!-- Main Header (Two-tier navigation - Tier 1) -->
    <header class="main-header">
        <div class="logo">ExoBank</div>
        <nav class="main-nav">
            <a href="#dashboard">Dashboard</a>
            <a href="#leads">Leads</a>
            <a href="#training">Training</a>
            <a href="#calculator">Calculator</a>
            <a href="#analytics">Analytics</a>
        </nav>
        <div class="user-menu">
            <button>🔔</button>
            <button>🌙</button>
            <div class="user-avatar">S</div>
        </div>
    </header>

    <!-- Admin Sidebar (Two-tier navigation - Tier 2) -->
    <aside class="admin-sidebar">
        <div class="sidebar-header">
            <div class="sidebar-logo">EB</div>
            <div class="sidebar-title">ExoBank Admin</div>
        </div>

        <div class="user-info">
            <div class="user-avatar">SA</div>
            <div>
                <div style="font-weight: 500; font-size: 0.875rem;">System Administrator</div>
                <div class="user-badge">ADMIN</div>
            </div>
        </div>

        <nav class="sidebar-nav">
            <a href="#" class="nav-item active">
                <svg class="nav-icon" viewBox="0 0 24 24"><path d="M3 13h8V3H3v10zm0 8h8v-6H3v6zm10 0h8V11h-8v10zm0-18v6h8V3h-8z"/></svg>
                Dashboard
            </a>
            <a href="#" class="nav-item">
                <svg class="nav-icon" viewBox="0 0 24 24"><path d="M16 7c0-2.21-1.79-4-4-4S8 4.79 8 7s1.79 4 4 4 4-1.79 4-4zm-4 6c-2.67 0-8 1.34-8 4v3h16v-3c0-2.66-5.33-4-8-4z"/></svg>
                User Management
            </a>
            <a href="#" class="nav-item">
                <svg class="nav-icon" viewBox="0 0 24 24"><path d="M19 3h-1V1h-2v2H8V1H6v2H5c-1.11 0-1.99.9-1.99 2L3 19c0 1.1.89 2 2 2h14c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2zm0 16H5V8h14v11zM7 10h5v5H7z"/></svg>
                Attendance
            </a>
            <a href="#" class="nav-item">
                <svg class="nav-icon" viewBox="0 0 24 24"><path d="M11.8 10.9c-2.27-.59-3-1.2-3-2.15 0-1.09 1.01-1.85 2.7-1.85 1.78 0 2.44.85 2.5 2.1h2.21c-.07-1.72-1.12-3.3-3.21-3.81V3h-3v2.16c-1.94.42-3.5 1.68-3.5 3.61 0 2.31 1.91 3.46 4.7 4.13 2.5.6 3 1.48 3 2.41 0 .69-.49 1.79-2.7 1.79-2.06 0-2.87-.92-2.98-2.1h-2.2c.12 2.19 1.76 3.42 3.68 3.83V21h3v-2.15c1.95-.37 3.5-1.5 3.5-3.55 0-2.84-2.43-3.81-4.7-4.4z"/></svg>
                Payroll
            </a>
            <a href="#" class="nav-item">
                <svg class="nav-icon" viewBox="0 0 24 24"><path d="M14 2H6c-1.1 0-1.99.9-1.99 2L4 20c0 1.1.89 2 2 2h8c1.1 0 2-.9 2-2V8l-6-6zm2 16H8v-2h8v2zm0-4H8v-2h8v2zm-3-5V3.5L18.5 9H13z"/></svg>
                Tasks
            </a>
            <a href="#" class="nav-item">
                <svg class="nav-icon" viewBox="0 0 24 24"><path d="M3.5 18.49l6-6.01 4 4L22 6.92l-1.41-1.41-7.09 7.97-4-4L2 16.99z"/></svg>
                Reports
            </a>
            <a href="#" class="nav-item">
                <svg class="nav-icon" viewBox="0 0 24 24"><path d="M19.14,12.94c0.04-0.3,0.06-0.61,0.06-0.94c0-0.32-0.02-0.64-0.07-0.94l2.03-1.58c0.18-0.14,0.23-0.41,0.12-0.61 l-1.92-3.32c-0.12-0.22-0.37-0.29-0.59-0.22l-2.39,0.96c-0.5-0.38-1.03-0.7-1.62-0.94L14.4,2.81c-0.04-0.24-0.24-0.41-0.48-0.41 h-3.84c-0.24,0-0.43,0.17-0.47,0.41L9.25,5.35C8.66,5.59,8.12,5.92,7.63,6.29L5.24,5.33c-0.22-0.08-0.47,0-0.59,0.22L2.74,8.87 C2.62,9.08,2.66,9.34,2.86,9.48l2.03,1.58C4.84,11.36,4.8,11.69,4.8,12s0.02,0.64,0.07,0.94l-2.03,1.58 c-0.18,0.14-0.23,0.41-0.12,0.61l1.92,3.32c0.12,0.22,0.37,0.29,0.59,0.22l2.39-0.96c0.5,0.38,1.03,0.7,1.62,0.94l0.36,2.54 c0.05,0.24,0.24,0.41,0.48,0.41h3.84c0.24,0,0.44-0.17,0.47-0.41l0.36-2.54c0.59-0.24,1.13-0.56,1.62-0.94l2.39,0.96 c0.22,0.08,0.47,0,0.59-0.22l1.92-3.32c0.12-0.22,0.07-0.47-0.12-0.61L19.14,12.94z M12,15.6c-1.98,0-3.6-1.62-3.6-3.6 s1.62-3.6,3.6-3.6s3.6,1.62,3.6,3.6S13.98,15.6,12,15.6z"/></svg>
                Settings
            </a>
        </nav>

        <div class="sidebar-footer">
            <button class="logout-btn">
                <svg class="nav-icon" viewBox="0 0 24 24"><path d="M17 7l-1.41 1.41L18.17 11H8v2h10.17l-2.58 2.58L17 17l5-5zM4 5h8V3H4c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h8v-2H4V5z"/></svg>
                Logout
            </button>
        </div>
    </aside>

    <!-- Main Content Area -->
    <main class="main-content">
        <div class="content-header">
            <h1 class="content-title">Admin Dashboard</h1>
            <p class="content-description">System overview and management</p>
        </div>

        <div class="stats-grid">
            <div class="stat-card">
                <div class="stat-title">Total Users</div>
                <div class="stat-value">4</div>
            </div>
            <div class="stat-card">
                <div class="stat-title">Today's Attendance</div>
                <div class="stat-value">0</div>
            </div>
            <div class="stat-card">
                <div class="stat-title">Active Tasks</div>
                <div class="stat-value">0</div>
            </div>
            <div class="stat-card">
                <div class="stat-title">Pending Payroll</div>
                <div class="stat-value">0</div>
            </div>
        </div>

        <div class="scrollable-content">
            <h3 style="margin-bottom: 1rem; color: #1e293b;">Scrollable Content Area</h3>
            <p style="margin-bottom: 1rem; color: #64748b;">
                This demonstrates the fixed sidebar positioning. The sidebar remains in place while you scroll through this content.
            </p>
            
            <div class="scroll-item">
                <h4>Item 1</h4>
                <p>Notice how the sidebar stays fixed while scrolling through this content.</p>
            </div>
            <div class="scroll-item">
                <h4>Item 2</h4>
                <p>The new darker green color (#059669) provides better contrast and professionalism.</p>
            </div>
            <div class="scroll-item">
                <h4>Item 3</h4>
                <p>Both the main header and admin sidebar work together as a two-tier navigation system.</p>
            </div>
            <div class="scroll-item">
                <h4>Item 4</h4>
                <p>The active navigation state uses the updated ExoBank green color.</p>
            </div>
            <div class="scroll-item">
                <h4>Item 5</h4>
                <p>User badges and accent elements also use the new professional green shade.</p>
            </div>
            <div class="scroll-item">
                <h4>Item 6</h4>
                <p>The design works well in both light and dark themes.</p>
            </div>
            <div class="scroll-item">
                <h4>Item 7</h4>
                <p>Scroll down to see more content and test the fixed positioning...</p>
            </div>
            <div class="scroll-item">
                <h4>Item 8</h4>
                <p>The sidebar remains accessible at all times during scrolling.</p>
            </div>
            <div class="scroll-item">
                <h4>Item 9</h4>
                <p>This ensures excellent user experience for admin users.</p>
            </div>
            <div class="scroll-item">
                <h4>Item 10</h4>
                <p>End of scrollable content. The sidebar should have remained fixed throughout.</p>
            </div>
        </div>
    </main>
</body>
</html>
