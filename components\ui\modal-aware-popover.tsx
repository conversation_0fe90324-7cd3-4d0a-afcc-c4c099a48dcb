"use client"

import * as React from "react"
import * as PopoverPrimitive from "@radix-ui/react-popover"
import { cn } from "@/lib/utils"

const ModalAwarePopover = PopoverPrimitive.Root

const ModalAwarePopoverTrigger = PopoverPrimitive.Trigger

interface ModalAwarePopoverContentProps extends React.ComponentPropsWithoutRef<typeof PopoverPrimitive.Content> {
  /**
   * Whether to enable smart positioning within modals
   * When true, the popover will detect if it's inside a modal and adjust positioning accordingly
   */
  enableSmartPositioning?: boolean
  /**
   * Minimum distance from modal edges (in pixels)
   */
  modalPadding?: number
  /**
   * Preferred side when there's enough space
   */
  preferredSide?: "top" | "bottom" | "left" | "right"
}

const ModalAwarePopoverContent = React.forwardRef<
  React.ElementRef<typeof PopoverPrimitive.Content>,
  ModalAwarePopoverContentProps
>(({ 
  className, 
  align = "center", 
  sideOffset = 4, 
  enableSmartPositioning = true,
  modalPadding = 16,
  preferredSide = "bottom",
  ...props 
}, ref) => {
  const [computedSide, setComputedSide] = React.useState<"top" | "bottom" | "left" | "right">(preferredSide)
  const [computedSideOffset, setComputedSideOffset] = React.useState(sideOffset)
  const contentRef = React.useRef<HTMLDivElement>(null)

  // Function to detect if we're inside a modal
  const isInsideModal = React.useCallback(() => {
    if (!enableSmartPositioning) return false
    
    // Look for common modal selectors
    const modalSelectors = [
      '[role="dialog"]',
      '[data-state="open"]',
      '.fixed.inset-0',
      '.modal',
      '.dialog'
    ]
    
    let element = contentRef.current?.parentElement
    while (element && element !== document.body) {
      for (const selector of modalSelectors) {
        if (element.matches?.(selector)) {
          return element
        }
      }
      element = element.parentElement
    }
    return null
  }, [enableSmartPositioning])

  // Function to calculate optimal positioning
  const calculateOptimalPosition = React.useCallback(() => {
    if (!enableSmartPositioning || !contentRef.current) return

    const modal = isInsideModal()
    if (!modal) return

    // Find the trigger element more reliably
    let trigger = contentRef.current.parentElement?.querySelector('[data-radix-popover-trigger]')
    if (!trigger) {
      // Fallback: look for button with calendar icon or date picker
      trigger = contentRef.current.parentElement?.querySelector('button[id="date-picker"]')
    }
    if (!trigger) return

    const modalRect = modal.getBoundingClientRect()
    const triggerRect = trigger.getBoundingClientRect()
    const viewportHeight = window.innerHeight
    const viewportWidth = window.innerWidth

    // Calculate available space in each direction within the modal
    const spaceAbove = triggerRect.top - modalRect.top - modalPadding
    const spaceBelow = modalRect.bottom - triggerRect.bottom - modalPadding
    const spaceLeft = triggerRect.left - modalRect.left - modalPadding
    const spaceRight = modalRect.right - triggerRect.right - modalPadding

    // Dynamic size estimation optimized for horizontal positioning
    let estimatedHeight = 400 // Default for calendar popup
    let estimatedWidth = 320   // Slightly wider default for better calendar display

    // Adjust for smaller screens with horizontal positioning in mind
    if (viewportWidth < 768) { // Mobile
      estimatedHeight = Math.min(350, viewportHeight * 0.6)
      estimatedWidth = Math.min(300, viewportWidth - 40) // More conservative for mobile
    } else if (viewportWidth < 1024) { // Tablet
      estimatedHeight = Math.min(380, viewportHeight * 0.7)
      estimatedWidth = Math.min(340, viewportWidth * 0.4) // Optimize for horizontal split
    } else { // Desktop
      estimatedWidth = Math.min(360, viewportWidth * 0.3) // Larger calendar on desktop
    }

    let newSide = preferredSide
    let newSideOffset = sideOffset

    // Smart positioning logic with HORIZONTAL priority system
    // Prioritize left/right positioning to avoid top cutoff issues
    const horizontalPositions = [
      { side: "right", space: spaceRight, required: estimatedWidth },
      { side: "left", space: spaceLeft, required: estimatedWidth }
    ]

    const verticalPositions = [
      { side: "bottom", space: spaceBelow, required: estimatedHeight },
      { side: "top", space: spaceAbove, required: estimatedHeight }
    ]

    // Sort horizontal positions by available space (descending)
    horizontalPositions.sort((a, b) => b.space - a.space)

    // Sort vertical positions by available space (descending)
    verticalPositions.sort((a, b) => b.space - a.space)

    // First, try to find a horizontal position that fits
    const bestHorizontalPosition = horizontalPositions.find(pos => pos.space >= pos.required)

    if (bestHorizontalPosition) {
      // Use horizontal positioning if it fits
      newSide = bestHorizontalPosition.side as "top" | "bottom" | "left" | "right"
    } else {
      // If no horizontal position fits, try vertical positions
      const bestVerticalPosition = verticalPositions.find(pos => pos.space >= pos.required)

      if (bestVerticalPosition) {
        newSide = bestVerticalPosition.side as "top" | "bottom" | "left" | "right"
      } else {
        // If nothing fits perfectly, prefer horizontal with most space
        const allPositions = [...horizontalPositions, ...verticalPositions]
        allPositions.sort((a, b) => b.space - a.space)

        // Prefer horizontal even if it has slightly less space than vertical
        const bestHorizontal = horizontalPositions[0]
        const bestVertical = verticalPositions[0]

        // Use horizontal if it has at least 80% of the space of the best vertical option
        if (bestHorizontal.space >= bestVertical.space * 0.8) {
          newSide = bestHorizontal.side as "top" | "bottom" | "left" | "right"
        } else {
          newSide = allPositions[0].side as "top" | "bottom" | "left" | "right"
        }
      }
    }

    // Adjust side offset based on available space and screen size
    if (newSide === "bottom" || newSide === "top") {
      // Vertical positioning - adjust based on height constraints
      const availableSpace = newSide === "bottom" ? spaceBelow : spaceAbove
      const minOffset = 4
      const maxOffset = Math.max(minOffset, availableSpace - estimatedHeight - 10)
      newSideOffset = Math.min(sideOffset, maxOffset)
    } else {
      // Horizontal positioning - optimize for calendar width and modal boundaries
      const availableSpace = newSide === "right" ? spaceRight : spaceLeft
      const minOffset = 8 // Slightly larger offset for horizontal positioning

      // For horizontal positioning, ensure adequate spacing from modal edges
      const safetyMargin = 16
      const maxOffset = Math.max(minOffset, availableSpace - estimatedWidth - safetyMargin)
      newSideOffset = Math.min(sideOffset, maxOffset)

      // For mobile screens, reduce offset to maximize available space
      if (viewportWidth < 768) {
        newSideOffset = Math.max(4, Math.min(newSideOffset, 8))
      }
    }

    // Ensure minimum offset with preference for horizontal positioning
    newSideOffset = Math.max(newSide === "left" || newSide === "right" ? 8 : 4, newSideOffset)

    console.log('📍 Horizontal-priority positioning calculated:', {
      modal: { width: modalRect.width, height: modalRect.height },
      trigger: { top: triggerRect.top, left: triggerRect.left, width: triggerRect.width, height: triggerRect.height },
      spaces: { above: spaceAbove, below: spaceBelow, left: spaceLeft, right: spaceRight },
      estimated: { height: estimatedHeight, width: estimatedWidth },
      viewport: { width: viewportWidth, height: viewportHeight },
      result: { side: newSide, offset: newSideOffset, strategy: 'horizontal-priority' }
    })

    setComputedSide(newSide)
    setComputedSideOffset(newSideOffset)
  }, [enableSmartPositioning, modalPadding, preferredSide, sideOffset])

  // Recalculate position when popover opens
  React.useEffect(() => {
    const timer = setTimeout(calculateOptimalPosition, 0)
    return () => clearTimeout(timer)
  }, [calculateOptimalPosition])

  // Listen for window resize to recalculate position
  React.useEffect(() => {
    if (!enableSmartPositioning) return

    const handleResize = () => {
      calculateOptimalPosition()
    }

    window.addEventListener('resize', handleResize)
    return () => window.removeEventListener('resize', handleResize)
  }, [calculateOptimalPosition, enableSmartPositioning])

  return (
    <PopoverPrimitive.Portal>
      <PopoverPrimitive.Content
        ref={(node) => {
          if (typeof ref === 'function') {
            ref(node)
          } else if (ref) {
            ref.current = node
          }
          if (node) {
            contentRef.current = node
          }
        }}
        align={align}
        side={computedSide}
        sideOffset={computedSideOffset}
        className={cn(
          // Higher z-index to ensure it appears above modals
          "z-[60] w-72 rounded-md border bg-popover p-4 text-popover-foreground shadow-md outline-none",
          // Animation classes
          "data-[state=open]:animate-in data-[state=closed]:animate-out",
          "data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0",
          "data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95",
          // Side-specific animations
          "data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2",
          "data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2",
          // Ensure it stays within viewport bounds
          enableSmartPositioning && "max-h-[calc(100vh-2rem)] overflow-auto",
          className
        )}
        {...props}
      />
    </PopoverPrimitive.Portal>
  )
})
ModalAwarePopoverContent.displayName = "ModalAwarePopoverContent"

export { ModalAwarePopover, ModalAwarePopoverTrigger, ModalAwarePopoverContent }
