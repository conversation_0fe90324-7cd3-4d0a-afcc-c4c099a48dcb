# Comprehensive User Management System

This guide covers the comprehensive user management and employee management system implemented in the kanban board application.

## Overview

The system provides a complete solution for managing users, employees, departments, roles, and permissions with the following key features:

- **User Authentication & Authorization**: Secure login/logout with role-based access control
- **Employee Management**: Comprehensive employee profiles with personal, employment, and contact information
- **Department Management**: Organize users into departments with managers and budgets
- **Role-Based Permissions**: Granular permission system with predefined roles
- **Admin Dashboard**: Comprehensive administrative interface for user management
- **Database Integration**: Full integration with Neon PostgreSQL database

## Database Schema

### Core Tables

1. **users** - Enhanced user table with comprehensive employee information
2. **departments** - Department management with managers and budgets
3. **user_sessions** - Session management for authentication
4. **permissions** - System permissions for different resources
5. **role_permissions** - Mapping between roles and permissions
6. **employee_addresses** - Employee address information
7. **employee_documents** - Document management for employees
8. **attendance** - Employee attendance tracking
9. **payroll** - Payroll management
10. **audit_logs** - System audit trail

### User Roles

- **Admin**: Full system access with all permissions
- **HR Manager**: Human resources management with user and payroll access
- **Manager**: Department management with limited administrative access
- **Staff**: Basic access to personal dashboard and attendance

## Setup Instructions

### 1. Database Setup

Run the setup script to initialize the database:

```bash
node scripts/setup-user-management.js
```

This script will:
- Create all necessary database tables
- Insert default permissions and role mappings
- Create sample departments
- Insert sample users with different roles
- Set up proper indexes for performance

### 2. Environment Configuration

Ensure your `.env.local` file contains the correct Neon database URL:

```env
DATABASE_URL="your-neon-connection-string"
```

### 3. Sample Users

The setup script creates the following sample users (all with password: `admin123`):

- **<EMAIL>** - System Administrator (Admin)
- **<EMAIL>** - HR Manager (HR Manager)
- **<EMAIL>** - Operations Manager (Manager)
- **<EMAIL>** - Senior Accountant (Staff)
- **<EMAIL>** - Software Developer (Staff)
- **<EMAIL>** - Marketing Executive (Staff)
- **<EMAIL>** - Customer Support Representative (Staff)
- **<EMAIL>** - Sales Executive (Staff)

## Features

### Admin Dashboard (`/admin`)

The main administrative interface provides:
- User statistics and metrics
- Quick actions for common tasks
- Role distribution visualization
- Department overview
- Recent activity feed

### User Management (`/admin/users`)

Comprehensive user management with:
- User listing with search and filters
- Create new users with full employee information
- Edit user profiles and employment details
- Role assignment and permission management
- User deactivation (soft delete)

### User Profile Management (`/admin/users/[id]`)

Detailed user profile management with tabs for:
- **Basic Info**: Core user account information
- **Employment**: Job-related information and employment status
- **Personal**: Personal details and identification information
- **Contact & Bank**: Contact information and banking details

### Department Management (`/admin/departments`)

- Create and manage departments
- Assign department managers
- Set department budgets and locations
- View department statistics

### Role & Permission System

- Predefined roles with specific permissions
- Granular permission system for different resources
- Role comparison and assignment interface
- Permission preview before role changes

## API Endpoints

### User Management
- `GET /api/admin/users` - List users with filtering
- `POST /api/admin/users` - Create new user
- `GET /api/admin/users/[id]` - Get user details
- `PUT /api/admin/users/[id]` - Update user
- `DELETE /api/admin/users/[id]` - Deactivate user

### Department Management
- `GET /api/admin/departments` - List departments
- `POST /api/admin/departments` - Create department

### Permission Management
- `GET /api/admin/permissions` - List all permissions
- `GET /api/admin/permissions/role/[role]` - Get role permissions

### Authentication
- `POST /api/auth/login` - User login
- `POST /api/auth/logout` - User logout
- `GET /api/auth/me` - Get current user
- `POST /api/auth/register` - User registration

## Components

### Admin Components
- `AdminDashboard` - Main admin dashboard
- `UserManagementDashboard` - User management interface
- `UserProfileForm` - User profile editing form
- `RoleAssignment` - Role and permission management

### Auth Components
- `AuthProvider` - Authentication context provider
- `RoleGuard` - Role-based route protection
- `RequireAuth` - Authentication requirement wrapper
- `PermissionGuard` - Permission-based component protection

## Security Features

- **Session-based Authentication**: Secure session management with HTTP-only cookies
- **Role-based Access Control**: Granular permissions for different user roles
- **Route Protection**: Middleware-based route protection for admin areas
- **Input Validation**: Server-side validation for all user inputs
- **Audit Logging**: System audit trail for user actions
- **Password Hashing**: Secure password storage using bcrypt

## Database Operations

The system uses the enhanced `serverDb` utility with operations for:
- User CRUD operations with comprehensive employee data
- Department management
- Permission and role management
- Session management
- Audit logging

## Permissions System

### Resources
- `dashboard` - Main dashboard access
- `users` - User management
- `departments` - Department management
- `attendance` - Attendance management
- `payroll` - Payroll management
- `reports` - Report generation
- `settings` - System settings
- `audit` - Audit log access
- `permissions` - Permission management

### Actions
- `read` - View/read access
- `write` - Create/update/delete access

## Best Practices

1. **Always use role guards** for protecting admin routes
2. **Validate user permissions** before allowing actions
3. **Use the serverDb utility** for all database operations
4. **Implement proper error handling** in API endpoints
5. **Log important user actions** for audit purposes
6. **Use TypeScript interfaces** for type safety
7. **Follow the established patterns** for new features

## Troubleshooting

### Common Issues

1. **Database Connection Errors**
   - Verify DATABASE_URL in .env.local
   - Check Neon database status
   - Run the setup script again

2. **Permission Denied Errors**
   - Check user role and permissions
   - Verify role guards are properly implemented
   - Check session validity

3. **User Creation Failures**
   - Verify all required fields are provided
   - Check for duplicate email addresses
   - Validate employee ID uniqueness

### Debug Mode

Enable debug logging by setting:
```env
NODE_ENV=development
```

This will provide detailed error messages and database query logs.

## Future Enhancements

Potential improvements for the user management system:

1. **Advanced Reporting**: Detailed user and department analytics
2. **Bulk Operations**: Import/export users via CSV
3. **Advanced Permissions**: Custom permission sets per user
4. **User Groups**: Additional grouping beyond departments
5. **Integration APIs**: REST APIs for external system integration
6. **Mobile App Support**: Mobile-friendly interfaces
7. **Advanced Audit**: Detailed audit trails with search and filtering

## Support

For issues or questions regarding the user management system:

1. Check the troubleshooting section above
2. Review the API documentation
3. Examine the sample data and test users
4. Check the database schema and relationships
