import { NextRequest, NextResponse } from "next/server"
import { AuthService } from "@/lib/auth-utils"
import { serverDb } from "@/lib/server-db"
import { z } from "zod"

// Validation schema for loan updates
const updateLoanSchema = z.object({
  loan_amount: z.number().positive("Loan amount must be positive").optional(),
  amount_paid: z.number().min(0, "Amount paid cannot be negative").optional(),
  due_date: z.string().regex(/^\d{4}-\d{2}-\d{2}$/, "Invalid date format").optional(),
  due_date_bs: z.string().optional(),
  current_stage: z.enum(["early", "assertive", "escalation", "legal_recovery", "complete"]).optional(),
}).refine((data) => {
  // If both loan_amount and amount_paid are provided, amount_paid cannot exceed loan_amount
  if (data.loan_amount !== undefined && data.amount_paid !== undefined) {
    return data.amount_paid <= data.loan_amount
  }
  return true
}, {
  message: "Amount paid cannot exceed loan amount",
  path: ["amount_paid"]
})

export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const sessionToken = request.cookies.get("session-token")?.value

    if (!sessionToken) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 })
    }

    const user = await AuthService.verifySession(sessionToken)

    if (!user || !["admin", "hr_manager"].includes(user.role)) {
      return NextResponse.json({ error: "Forbidden" }, { status: 403 })
    }

    const loanId = params.id

    // Get loan details with customer information
    const loanResult = await serverDb.sql`
      SELECT 
        lr.*,
        c.name as customer_name,
        c.phone as customer_phone,
        c.email as customer_email,
        c.address as customer_address,
        u.full_name as created_by_name,
        (lr.loan_amount - lr.amount_paid) as outstanding_amount,
        CASE
          WHEN lr.due_date < CURRENT_DATE THEN
            CURRENT_DATE - lr.due_date
          ELSE 0
        END as days_overdue
      FROM loan_records lr
      LEFT JOIN loan_recovery_customers c ON lr.customer_id = c.id
      LEFT JOIN users u ON lr.created_by = u.id
      WHERE lr.id = ${loanId}
    `

    if (loanResult.length === 0) {
      return NextResponse.json({ error: "Loan not found" }, { status: 404 })
    }

    return NextResponse.json({
      success: true,
      loan: loanResult[0],
    })
  } catch (error) {
    console.error("Get loan API error:", error)
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    )
  }
}

export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const sessionToken = request.cookies.get("session-token")?.value

    if (!sessionToken) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 })
    }

    const user = await AuthService.verifySession(sessionToken)

    if (!user || !["admin", "hr_manager"].includes(user.role)) {
      return NextResponse.json({ error: "Forbidden" }, { status: 403 })
    }

    const loanId = params.id
    const body = await request.json()
    const validatedData = updateLoanSchema.parse(body)

    // Check if loan exists and get current values
    const existingLoan = await serverDb.sql`
      SELECT * FROM loan_records WHERE id = ${loanId}
    `

    if (existingLoan.length === 0) {
      return NextResponse.json({ error: "Loan not found" }, { status: 404 })
    }

    const currentLoan = existingLoan[0]

    // Additional validation: if only amount_paid is being updated, check against current loan_amount
    if (validatedData.amount_paid !== undefined && validatedData.loan_amount === undefined) {
      if (validatedData.amount_paid > currentLoan.loan_amount) {
        return NextResponse.json(
          { error: "Amount paid cannot exceed current loan amount" },
          { status: 400 }
        )
      }
    }

    // Update the loan record
    const updatedLoan = await serverDb.sql`
      UPDATE loan_records 
      SET 
        loan_amount = COALESCE(${validatedData.loan_amount}, loan_amount),
        amount_paid = COALESCE(${validatedData.amount_paid}, amount_paid),
        due_date = COALESCE(${validatedData.due_date}, due_date),
        due_date_bs = COALESCE(${validatedData.due_date_bs}, due_date_bs),
        current_stage = COALESCE(${validatedData.current_stage}, current_stage),
        updated_by = ${user.id},
        updated_at = NOW()
      WHERE id = ${loanId}
      RETURNING *
    `

    // Log stage transition if stage was changed
    if (validatedData.current_stage && validatedData.current_stage !== currentLoan.current_stage) {
      await serverDb.sql`
        INSERT INTO loan_stage_transitions (loan_id, from_stage, to_stage, transitioned_by, notes)
        VALUES (${loanId}, ${currentLoan.current_stage}, ${validatedData.current_stage}, ${user.id}, 'Updated via loan edit')
      `
    }

    return NextResponse.json({
      success: true,
      loan: updatedLoan[0],
      message: "Loan updated successfully",
    })
  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: "Validation error", details: error.errors },
        { status: 400 }
      )
    }

    console.error("Update loan API error:", error)
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    )
  }
}

export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const sessionToken = request.cookies.get("session-token")?.value

    if (!sessionToken) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 })
    }

    const user = await AuthService.verifySession(sessionToken)

    // Only admin can delete loans
    if (!user || user.role !== "admin") {
      return NextResponse.json({ error: "Forbidden" }, { status: 403 })
    }

    const loanId = params.id

    // Check if loan exists
    const existingLoan = await serverDb.sql`
      SELECT id FROM loan_records WHERE id = ${loanId}
    `

    if (existingLoan.length === 0) {
      return NextResponse.json({ error: "Loan not found" }, { status: 404 })
    }

    // Delete the loan (this will cascade to related records)
    await serverDb.sql`
      DELETE FROM loan_records WHERE id = ${loanId}
    `

    return NextResponse.json({
      success: true,
      message: "Loan deleted successfully",
    })
  } catch (error) {
    console.error("Delete loan API error:", error)
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    )
  }
}
