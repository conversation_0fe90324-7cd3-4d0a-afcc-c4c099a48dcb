const { neon } = require('@neondatabase/serverless');
require('dotenv').config({ path: '.env.local' });

const sql = neon(process.env.DATABASE_URL);

async function fixUserRole() {
  try {
    console.log('🔍 Checking current user role...');
    
    // Check current user
    const users = await sql`SELECT id, full_name, email, role FROM users WHERE email = '<EMAIL>'`;
    
    if (users.length === 0) {
      console.log('❌ User not found');
      return;
    }
    
    console.log('Current user:', users[0]);
    
    // Update user role to admin
    if (users[0].role !== 'admin') {
      console.log('🔧 Updating user role to admin...');
      await sql`UPDATE users SET role = 'admin' WHERE email = '<EMAIL>'`;
      console.log('✅ User role updated to admin');
    } else {
      console.log('✅ User already has admin role');
    }
    
    // Verify the update
    const updatedUsers = await sql`SELECT id, full_name, email, role FROM users WHERE email = '<EMAIL>'`;
    console.log('Updated user:', updatedUsers[0]);
    
  } catch (error) {
    console.error('❌ Error:', error);
  }
}

fixUserRole();
