/**
 * @jest-environment node
 */

import { NextRequest } from 'next/server'
import { GET, POST } from '@/app/api/tasks/route'
import { AuthService } from '@/lib/auth-utils'
import { serverDb } from '@/lib/server-db'

// Mock dependencies
jest.mock('@/lib/auth-utils')
jest.mock('@/lib/server-db')

const mockAuthService = AuthService as jest.Mocked<typeof AuthService>
const mockServerDb = serverDb as jest.Mocked<typeof serverDb>

describe('/api/tasks', () => {
  beforeEach(() => {
    jest.clearAllMocks()
  })

  describe('GET /api/tasks', () => {
    it('should return unauthorized when no session token', async () => {
      const request = new NextRequest('http://localhost:3000/api/tasks')
      
      const response = await GET(request)
      const data = await response.json()

      expect(response.status).toBe(401)
      expect(data.error).toBe('Unauthorized')
    })

    it('should return unauthorized when invalid session', async () => {
      const request = new NextRequest('http://localhost:3000/api/tasks', {
        headers: {
          Cookie: 'session-token=invalid-token'
        }
      })

      mockAuthService.verifySession.mockResolvedValue(null)

      const response = await GET(request)
      const data = await response.json()

      expect(response.status).toBe(401)
      expect(data.error).toBe('Unauthorized')
    })

    it('should return tasks for authenticated user', async () => {
      const mockUser = {
        id: 'user-1',
        email: '<EMAIL>',
        full_name: 'Test User',
        role: 'employee'
      }

      const mockTasks = [
        {
          id: 'task-1',
          title: 'Test Task',
          description: 'Test Description',
          status: 'todo',
          priority: 'medium',
          assigned_to: 'user-1',
          assigned_by: 'user-1',
          created_at: new Date().toISOString()
        }
      ]

      const request = new NextRequest('http://localhost:3000/api/tasks', {
        headers: {
          Cookie: 'session-token=valid-token'
        }
      })

      mockAuthService.verifySession.mockResolvedValue(mockUser)
      mockServerDb.sql.mockResolvedValueOnce([{ total: '1' }]) // Count query
      mockServerDb.sql.mockResolvedValueOnce(mockTasks) // Tasks query

      const response = await GET(request)
      const data = await response.json()

      expect(response.status).toBe(200)
      expect(data.success).toBe(true)
      expect(data.data.tasks).toEqual(mockTasks)
      expect(data.data.pagination.total).toBe(1)
    })

    it('should filter tasks by status', async () => {
      const mockUser = {
        id: 'user-1',
        email: '<EMAIL>',
        full_name: 'Test User',
        role: 'employee'
      }

      const request = new NextRequest('http://localhost:3000/api/tasks?status=completed', {
        headers: {
          Cookie: 'session-token=valid-token'
        }
      })

      mockAuthService.verifySession.mockResolvedValue(mockUser)
      mockServerDb.sql.mockResolvedValueOnce([{ total: '0' }])
      mockServerDb.sql.mockResolvedValueOnce([])

      const response = await GET(request)
      const data = await response.json()

      expect(response.status).toBe(200)
      expect(mockServerDb.sql).toHaveBeenCalledWith(
        expect.stringContaining('status = $'),
        expect.arrayContaining(['completed'])
      )
    })

    it('should handle search queries', async () => {
      const mockUser = {
        id: 'user-1',
        email: '<EMAIL>',
        full_name: 'Test User',
        role: 'admin'
      }

      const request = new NextRequest('http://localhost:3000/api/tasks?search=test', {
        headers: {
          Cookie: 'session-token=valid-token'
        }
      })

      mockAuthService.verifySession.mockResolvedValue(mockUser)
      mockServerDb.sql.mockResolvedValueOnce([{ total: '0' }])
      mockServerDb.sql.mockResolvedValueOnce([])

      const response = await GET(request)

      expect(response.status).toBe(200)
      expect(mockServerDb.sql).toHaveBeenCalledWith(
        expect.stringContaining('ILIKE'),
        expect.arrayContaining(['%test%'])
      )
    })
  })

  describe('POST /api/tasks', () => {
    it('should create task for authenticated user', async () => {
      const mockUser = {
        id: 'user-1',
        email: '<EMAIL>',
        full_name: 'Test User',
        role: 'employee'
      }

      const taskData = {
        title: 'New Task',
        description: 'New Description',
        priority: 'high'
      }

      const mockCreatedTask = {
        id: 'task-2',
        ...taskData,
        assigned_to: 'user-1',
        assigned_by: 'user-1',
        status: 'todo',
        created_at: new Date().toISOString()
      }

      const request = new NextRequest('http://localhost:3000/api/tasks', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          Cookie: 'session-token=valid-token'
        },
        body: JSON.stringify(taskData)
      })

      mockAuthService.verifySession.mockResolvedValue(mockUser)
      mockServerDb.sql.mockResolvedValueOnce([{ next_position: 1 }]) // Position query
      mockServerDb.sql.mockResolvedValueOnce([mockCreatedTask]) // Insert query
      mockServerDb.sql.mockResolvedValueOnce([mockCreatedTask]) // Complete task query

      const response = await POST(request)
      const data = await response.json()

      expect(response.status).toBe(201)
      expect(data.success).toBe(true)
      expect(data.data.title).toBe(taskData.title)
      expect(data.message).toBe('Task created successfully')
    })

    it('should validate required fields', async () => {
      const mockUser = {
        id: 'user-1',
        email: '<EMAIL>',
        full_name: 'Test User',
        role: 'employee'
      }

      const invalidTaskData = {
        description: 'Missing title'
      }

      const request = new NextRequest('http://localhost:3000/api/tasks', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          Cookie: 'session-token=valid-token'
        },
        body: JSON.stringify(invalidTaskData)
      })

      mockAuthService.verifySession.mockResolvedValue(mockUser)

      const response = await POST(request)
      const data = await response.json()

      expect(response.status).toBe(400)
      expect(data.error).toBe('Invalid task data')
      expect(data.details).toBeDefined()
    })

    it('should handle assignment restrictions for non-admin users', async () => {
      const mockUser = {
        id: 'user-1',
        email: '<EMAIL>',
        full_name: 'Test User',
        role: 'employee'
      }

      const taskData = {
        title: 'New Task',
        assigned_to: 'user-2' // Different user
      }

      const request = new NextRequest('http://localhost:3000/api/tasks', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          Cookie: 'session-token=valid-token'
        },
        body: JSON.stringify(taskData)
      })

      mockAuthService.verifySession.mockResolvedValue(mockUser)

      const response = await POST(request)
      const data = await response.json()

      expect(response.status).toBe(403)
      expect(data.error).toBe('You can only assign tasks to yourself')
    })

    it('should allow admin users to assign to anyone', async () => {
      const mockUser = {
        id: 'user-1',
        email: '<EMAIL>',
        full_name: 'Admin User',
        role: 'admin'
      }

      const taskData = {
        title: 'Admin Task',
        assigned_to: 'user-2'
      }

      const mockCreatedTask = {
        id: 'task-3',
        ...taskData,
        assigned_by: 'user-1',
        status: 'todo',
        created_at: new Date().toISOString()
      }

      const request = new NextRequest('http://localhost:3000/api/tasks', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          Cookie: 'session-token=valid-token'
        },
        body: JSON.stringify(taskData)
      })

      mockAuthService.verifySession.mockResolvedValue(mockUser)
      mockServerDb.sql.mockResolvedValueOnce([{ next_position: 1 }])
      mockServerDb.sql.mockResolvedValueOnce([mockCreatedTask])
      mockServerDb.sql.mockResolvedValueOnce([mockCreatedTask])

      const response = await POST(request)
      const data = await response.json()

      expect(response.status).toBe(201)
      expect(data.success).toBe(true)
      expect(data.data.assigned_to).toBe('user-2')
    })
  })
})
