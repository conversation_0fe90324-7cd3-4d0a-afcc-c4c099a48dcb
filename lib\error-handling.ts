// Comprehensive error handling utilities for the attendance system

export interface ApiError {
  message: string
  code?: string
  field?: string
  details?: any
}

export interface ApiResponse<T = any> {
  success: boolean
  data?: T
  error?: string
  errors?: ApiError[]
}

/**
 * Standard error messages for attendance operations
 */
export const ATTENDANCE_ERRORS = {
  ALREADY_CLOCKED_IN: "You have already clocked in today",
  NOT_CLOCKED_IN: "You must clock in before clocking out",
  ALREADY_CLOCKED_OUT: "You have already clocked out today",
  INVALID_TIME_FORMAT: "Invalid time format. Please use HH:MM format",
  INVALID_DATE: "Invalid date provided",
  FUTURE_DATE: "Cannot create attendance records for future dates",
  UNAUTHORIZED: "You are not authorized to perform this action",
  USER_NOT_FOUND: "User not found",
  ATTENDANCE_NOT_FOUND: "Attendance record not found",
  DUPLICATE_RECORD: "Attendance record already exists for this date",
  INVALID_TIME_RANGE: "Check-out time must be after check-in time",
  NETWORK_ERROR: "Network error. Please check your connection and try again",
  SERVER_ERROR: "Server error. Please try again later",
  VALIDATION_ERROR: "Please check your input and try again",
  PERMISSION_DENIED: "You don't have permission to perform this action",
  SESSION_EXPIRED: "Your session has expired. Please log in again",
} as const

/**
 * Validation functions
 */
export const validators = {
  /**
   * Validate time format (HH:MM)
   */
  isValidTime(time: string): boolean {
    const timeRegex = /^([0-1]?[0-9]|2[0-3]):[0-5][0-9]$/
    return timeRegex.test(time)
  },

  /**
   * Validate date format (YYYY-MM-DD)
   */
  isValidDate(date: string): boolean {
    const dateRegex = /^\d{4}-\d{2}-\d{2}$/
    if (!dateRegex.test(date)) return false
    
    const parsedDate = new Date(date)
    return parsedDate instanceof Date && !isNaN(parsedDate.getTime())
  },

  /**
   * Check if date is not in the future
   */
  isNotFutureDate(date: string): boolean {
    const inputDate = new Date(date)
    const today = new Date()
    today.setHours(23, 59, 59, 999) // End of today
    return inputDate <= today
  },

  /**
   * Validate time range (check-out after check-in)
   */
  isValidTimeRange(checkIn: string, checkOut: string): boolean {
    if (!checkIn || !checkOut) return true // Allow empty values
    
    const checkInTime = new Date(`2000-01-01T${checkIn}:00`)
    const checkOutTime = new Date(`2000-01-01T${checkOut}:00`)
    
    return checkOutTime > checkInTime
  },

  /**
   * Validate hours worked
   */
  isValidHours(hours: number): boolean {
    return hours >= 0 && hours <= 24
  },

  /**
   * Validate user ID format
   */
  isValidUserId(userId: string): boolean {
    // UUID format validation
    const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i
    return uuidRegex.test(userId)
  }
}

/**
 * Error handling utilities
 */
export class AttendanceError extends Error {
  public code: string
  public field?: string
  public details?: any

  constructor(message: string, code: string, field?: string, details?: any) {
    super(message)
    this.name = 'AttendanceError'
    this.code = code
    this.field = field
    this.details = details
  }
}

/**
 * Handle API responses and extract errors
 */
export function handleApiResponse<T>(response: ApiResponse<T>): T {
  if (!response.success) {
    const errorMessage = response.error || 'An unexpected error occurred'
    throw new AttendanceError(errorMessage, 'API_ERROR')
  }
  
  if (!response.data) {
    throw new AttendanceError('No data received from server', 'NO_DATA')
  }
  
  return response.data
}

/**
 * Handle network errors
 */
export function handleNetworkError(error: any): AttendanceError {
  if (error instanceof TypeError && error.message.includes('fetch')) {
    return new AttendanceError(ATTENDANCE_ERRORS.NETWORK_ERROR, 'NETWORK_ERROR')
  }
  
  if (error.name === 'AbortError') {
    return new AttendanceError('Request was cancelled', 'REQUEST_CANCELLED')
  }
  
  return new AttendanceError(ATTENDANCE_ERRORS.SERVER_ERROR, 'UNKNOWN_ERROR', undefined, error)
}

/**
 * Validate attendance form data
 */
export function validateAttendanceForm(data: {
  date?: string
  checkInTime?: string
  checkOutTime?: string
  hoursWorked?: number
  userId?: string
}): ApiError[] {
  const errors: ApiError[] = []

  if (data.date && !validators.isValidDate(data.date)) {
    errors.push({
      message: ATTENDANCE_ERRORS.INVALID_DATE,
      code: 'INVALID_DATE',
      field: 'date'
    })
  }

  if (data.date && !validators.isNotFutureDate(data.date)) {
    errors.push({
      message: ATTENDANCE_ERRORS.FUTURE_DATE,
      code: 'FUTURE_DATE',
      field: 'date'
    })
  }

  if (data.checkInTime && !validators.isValidTime(data.checkInTime)) {
    errors.push({
      message: ATTENDANCE_ERRORS.INVALID_TIME_FORMAT,
      code: 'INVALID_TIME_FORMAT',
      field: 'checkInTime'
    })
  }

  if (data.checkOutTime && !validators.isValidTime(data.checkOutTime)) {
    errors.push({
      message: ATTENDANCE_ERRORS.INVALID_TIME_FORMAT,
      code: 'INVALID_TIME_FORMAT',
      field: 'checkOutTime'
    })
  }

  if (data.checkInTime && data.checkOutTime && !validators.isValidTimeRange(data.checkInTime, data.checkOutTime)) {
    errors.push({
      message: ATTENDANCE_ERRORS.INVALID_TIME_RANGE,
      code: 'INVALID_TIME_RANGE',
      field: 'checkOutTime'
    })
  }

  if (data.hoursWorked !== undefined && !validators.isValidHours(data.hoursWorked)) {
    errors.push({
      message: 'Hours worked must be between 0 and 24',
      code: 'INVALID_HOURS',
      field: 'hoursWorked'
    })
  }

  if (data.userId && !validators.isValidUserId(data.userId)) {
    errors.push({
      message: 'Invalid user ID format',
      code: 'INVALID_USER_ID',
      field: 'userId'
    })
  }

  return errors
}

/**
 * Get user-friendly error message
 */
export function getUserFriendlyError(error: any): string {
  if (error instanceof AttendanceError) {
    return error.message
  }

  if (typeof error === 'string') {
    return error
  }

  if (error?.message) {
    return error.message
  }

  return ATTENDANCE_ERRORS.SERVER_ERROR
}

/**
 * Log errors for debugging
 */
export function logError(error: any, context?: string): void {
  const timestamp = new Date().toISOString()
  const logMessage = `[${timestamp}] ${context ? `[${context}] ` : ''}${error?.message || error}`
  
  console.error(logMessage, error)
  
  // In production, you might want to send this to a logging service
  if (process.env.NODE_ENV === 'production') {
    // Send to logging service
  }
}

/**
 * Retry mechanism for failed operations
 */
export async function withRetry<T>(
  operation: () => Promise<T>,
  maxRetries: number = 3,
  delay: number = 1000
): Promise<T> {
  let lastError: any

  for (let attempt = 1; attempt <= maxRetries; attempt++) {
    try {
      return await operation()
    } catch (error) {
      lastError = error
      
      if (attempt === maxRetries) {
        break
      }

      // Don't retry on certain errors
      if (error instanceof AttendanceError && 
          ['VALIDATION_ERROR', 'UNAUTHORIZED', 'PERMISSION_DENIED'].includes(error.code)) {
        break
      }

      // Wait before retrying
      await new Promise(resolve => setTimeout(resolve, delay * attempt))
    }
  }

  throw lastError
}
