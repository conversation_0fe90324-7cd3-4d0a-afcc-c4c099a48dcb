"use client"

import { useState } from "react"
import { <PERSON>pp<PERSON>eader } from "@/components/app-header"
import { Card, CardContent, CardDescription, Card<PERSON>ooter, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Tabs, <PERSON><PERSON><PERSON>ist, TabsTrigger } from "@/components/ui/tabs"
import { Badge } from "@/components/ui/badge"
import { Progress } from "@/components/ui/progress"
import {
  Search,
  Plus,
  Calendar,
  Users,
  BarChart,
  Mail,
  MessageSquare,
  Smartphone,
  Globe,
  ArrowRight,
  Filter,
  Clock,
  CheckCircle,
  AlertCircle,
} from "lucide-react"

const campaigns = [
  {
    id: "1",
    name: "Summer Savings Promotion",
    status: "active",
    startDate: "Jun 1, 2023",
    endDate: "Aug 31, 2023",
    audience: "All Clients",
    channels: ["Email", "SMS", "Social Media"],
    budget: 50000,
    spent: 32500,
    leads: 245,
    conversions: 42,
    roi: 2.8,
    progress: 65,
  },
  {
    id: "2",
    name: "Retirement Planning Webinar",
    status: "scheduled",
    startDate: "Jul 15, 2023",
    endDate: "Jul 15, 2023",
    audience: "Senior Clients",
    channels: ["Email", "Webinar"],
    budget: 15000,
    spent: 0,
    leads: 0,
    conversions: 0,
    roi: 0,
    progress: 0,
  },
  {
    id: "3",
    name: "Home Loan Interest Rate Special",
    status: "active",
    startDate: "May 1, 2023",
    endDate: "Jul 31, 2023",
    audience: "Potential Homebuyers",
    channels: ["Email", "SMS", "Web", "Branch"],
    budget: 75000,
    spent: 45000,
    leads: 180,
    conversions: 28,
    roi: 3.2,
    progress: 60,
  },
  {
    id: "4",
    name: "Investment Portfolio Diversification",
    status: "completed",
    startDate: "Jan 15, 2023",
    endDate: "Mar 15, 2023",
    audience: "High Net Worth Clients",
    channels: ["Email", "Direct Mail", "Personal Call"],
    budget: 100000,
    spent: 98500,
    leads: 120,
    conversions: 35,
    roi: 4.5,
    progress: 100,
  },
]

export default function CampaignsPage() {
  const [searchTerm, setSearchTerm] = useState("")

  const filteredCampaigns = campaigns.filter((campaign) =>
    campaign.name.toLowerCase().includes(searchTerm.toLowerCase()),
  )

  const getStatusColor = (status: string) => {
    switch (status) {
      case "active":
        return "bg-green-500 text-white"
      case "scheduled":
        return "bg-blue-500 text-white"
      case "completed":
        return "bg-gray-500 text-white"
      default:
        return "bg-gray-500 text-white"
    }
  }

  const getChannelIcon = (channel: string) => {
    switch (channel) {
      case "Email":
        return <Mail className="h-3 w-3" />
      case "SMS":
        return <MessageSquare className="h-3 w-3" />
      case "Social Media":
        return <Users className="h-3 w-3" />
      case "Web":
        return <Globe className="h-3 w-3" />
      case "Webinar":
        return <Users className="h-3 w-3" />
      case "Direct Mail":
        return <Mail className="h-3 w-3" />
      case "Personal Call":
        return <Smartphone className="h-3 w-3" />
      case "Branch":
        return <Globe className="h-3 w-3" />
      default:
        return <Globe className="h-3 w-3" />
    }
  }

  return (
    <div className="min-h-screen bg-gray-50 flex flex-col">
      <AppHeader title="Campaign Management" />

      <div className="p-4">
        <div className="flex items-center justify-between mb-4">
          <h2 className="text-xl font-semibold text-gray-800">Marketing Campaigns</h2>
          <Button className="bg-exobank-green hover:bg-exobank-green/90 text-white">
            <Plus className="h-4 w-4 mr-1" /> Create Campaign
          </Button>
        </div>

        <div className="mb-4">
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
            <Input
              placeholder="Search campaigns..."
              className="pl-10"
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
            />
          </div>
        </div>

        <Tabs defaultValue="all" className="mb-4">
          <TabsList className="grid w-full grid-cols-4">
            <TabsTrigger value="all">All</TabsTrigger>
            <TabsTrigger value="active">Active</TabsTrigger>
            <TabsTrigger value="scheduled">Scheduled</TabsTrigger>
            <TabsTrigger value="completed">Completed</TabsTrigger>
          </TabsList>
        </Tabs>

        <div className="flex items-center justify-between mb-4">
          <Button variant="outline" size="sm" className="text-xs">
            <Filter className="h-3 w-3 mr-1" /> Filter
          </Button>
          <span className="text-sm text-gray-500">{filteredCampaigns.length} campaigns</span>
        </div>

        <div className="space-y-4">
          {filteredCampaigns.map((campaign) => (
            <Card key={campaign.id} className="overflow-hidden">
              <CardHeader className="p-4 pb-2">
                <div className="flex justify-between items-start">
                  <div>
                    <div className="flex items-center gap-2">
                      <CardTitle className="text-base">{campaign.name}</CardTitle>
                      <Badge className={getStatusColor(campaign.status)}>{campaign.status}</Badge>
                    </div>
                    <CardDescription className="text-xs flex items-center gap-1">
                      <Calendar className="h-3 w-3 text-gray-400" />
                      <span>
                        {campaign.startDate} - {campaign.endDate}
                      </span>
                    </CardDescription>
                  </div>
                  <div className="text-right">
                    <div className="text-sm font-medium">Budget: ₹{campaign.budget.toLocaleString()}</div>
                    <div className="text-xs text-gray-500">
                      Spent: ₹{campaign.spent.toLocaleString()} ({Math.round((campaign.spent / campaign.budget) * 100)}
                      %)
                    </div>
                  </div>
                </div>
              </CardHeader>
              <CardContent className="p-4 pt-2 pb-2">
                <div className="grid grid-cols-2 gap-3 mb-3">
                  <div>
                    <div className="text-xs text-gray-500 mb-1">Target Audience</div>
                    <div className="text-sm">{campaign.audience}</div>
                  </div>
                  <div>
                    <div className="text-xs text-gray-500 mb-1">Channels</div>
                    <div className="flex flex-wrap gap-1">
                      {campaign.channels.map((channel, index) => (
                        <Badge key={index} variant="outline" className="text-xs flex items-center gap-1 bg-gray-50">
                          {getChannelIcon(channel)}
                          {channel}
                        </Badge>
                      ))}
                    </div>
                  </div>
                </div>

                <div className="grid grid-cols-3 gap-2 mb-3">
                  <div className="bg-gray-50 p-2 rounded-md">
                    <div className="text-xs text-gray-500">Leads</div>
                    <div className="text-sm font-medium">{campaign.leads}</div>
                  </div>
                  <div className="bg-gray-50 p-2 rounded-md">
                    <div className="text-xs text-gray-500">Conversions</div>
                    <div className="text-sm font-medium">{campaign.conversions}</div>
                  </div>
                  <div className="bg-gray-50 p-2 rounded-md">
                    <div className="text-xs text-gray-500">ROI</div>
                    <div className="text-sm font-medium">{campaign.roi}x</div>
                  </div>
                </div>

                <div>
                  <div className="flex items-center justify-between mb-1">
                    <span className="text-xs font-medium">Campaign Progress</span>
                    <span className="text-xs text-gray-500">{campaign.progress}%</span>
                  </div>
                  <Progress value={campaign.progress} className="h-1.5" />
                </div>
              </CardContent>
              <CardFooter className="p-4 pt-2 flex items-center justify-between bg-gray-50 border-t">
                <div className="flex items-center gap-1 text-xs">
                  {campaign.status === "active" ? (
                    <>
                      <Clock className="h-3 w-3 text-green-500" />
                      <span className="text-green-600">Campaign in progress</span>
                    </>
                  ) : campaign.status === "scheduled" ? (
                    <>
                      <AlertCircle className="h-3 w-3 text-blue-500" />
                      <span className="text-blue-600">Scheduled to start</span>
                    </>
                  ) : (
                    <>
                      <CheckCircle className="h-3 w-3 text-gray-500" />
                      <span className="text-gray-600">Campaign completed</span>
                    </>
                  )}
                </div>
                <div className="flex gap-1">
                  <Button variant="outline" size="sm" className="text-xs h-7">
                    <BarChart className="h-3 w-3 mr-1" /> Analytics
                  </Button>
                  <Button size="sm" className="text-xs h-7 bg-exobank-green hover:bg-exobank-green/90 text-white">
                    Manage <ArrowRight className="h-3 w-3 ml-1" />
                  </Button>
                </div>
              </CardFooter>
            </Card>
          ))}
        </div>
      </div>
    </div>
  )
}
