"use client"

import React, { useState, useEffect } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Badge } from '@/components/ui/badge'
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table'
import { Dialog, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog'
import { EmployeePayrollProfile } from '@/components/payroll/employee-payroll-profile'
import { toast } from 'sonner'
import { Search, User, CreditCard, DollarSign, Users, Eye, Edit } from 'lucide-react'

interface EmployeeSummary {
  id: string
  email: string
  full_name: string
  role: string
  department?: string
  position?: string
  employee_type: string
  employee_category: string
  employment_status: string
  salary?: number
  pay_grade?: string
  is_active: boolean
  hire_date?: string
}

export default function EmployeePayrollManagement() {
  const [employees, setEmployees] = useState<EmployeeSummary[]>([])
  const [loading, setLoading] = useState(true)
  const [searchTerm, setSearchTerm] = useState('')
  const [selectedEmployee, setSelectedEmployee] = useState<string | null>(null)
  const [showProfileDialog, setShowProfileDialog] = useState(false)

  useEffect(() => {
    fetchEmployees()
  }, [])

  const fetchEmployees = async () => {
    try {
      setLoading(true)
      const response = await fetch('/api/admin/employees/payroll-profile')
      const data = await response.json()

      if (data.success) {
        setEmployees(data.data)
      } else {
        toast.error(data.error || 'Failed to fetch employees')
      }
    } catch (error) {
      console.error('Error fetching employees:', error)
      toast.error('Failed to fetch employees')
    } finally {
      setLoading(false)
    }
  }

  const filteredEmployees = employees.filter(employee =>
    employee.full_name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    employee.email.toLowerCase().includes(searchTerm.toLowerCase()) ||
    employee.department?.toLowerCase().includes(searchTerm.toLowerCase()) ||
    employee.position?.toLowerCase().includes(searchTerm.toLowerCase())
  )

  const handleViewProfile = (employeeId: string) => {
    setSelectedEmployee(employeeId)
    setShowProfileDialog(true)
  }

  const handleCloseProfile = () => {
    setShowProfileDialog(false)
    setSelectedEmployee(null)
    fetchEmployees() // Refresh data after profile changes
  }

  // Calculate summary statistics
  const totalEmployees = employees.length
  const activeEmployees = employees.filter(emp => emp.employment_status === 'active').length
  const totalSalaryBudget = employees
    .filter(emp => emp.employment_status === 'active' && emp.salary)
    .reduce((sum, emp) => sum + (emp.salary || 0), 0)
  const averageSalary = activeEmployees > 0 ? totalSalaryBudget / activeEmployees : 0

  if (loading) {
    return (
      <div className="p-6">
        <div className="flex items-center justify-center h-64">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
        </div>
      </div>
    )
  }

  return (
    <div className="p-6 space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold">Employee Payroll Management</h1>
          <p className="text-muted-foreground">
            Manage employee payroll profiles, bank accounts, and compensation details
          </p>
        </div>
      </div>

      {/* Summary Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Employees</CardTitle>
            <Users className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{totalEmployees}</div>
            <p className="text-xs text-muted-foreground">
              {activeEmployees} active employees
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Active Employees</CardTitle>
            <User className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{activeEmployees}</div>
            <p className="text-xs text-muted-foreground">
              {((activeEmployees / totalEmployees) * 100).toFixed(1)}% of total
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Salary Budget</CardTitle>
            <DollarSign className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">NPR {totalSalaryBudget.toLocaleString()}</div>
            <p className="text-xs text-muted-foreground">
              Monthly budget for active employees
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Average Salary</CardTitle>
            <CreditCard className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">NPR {averageSalary.toLocaleString()}</div>
            <p className="text-xs text-muted-foreground">
              Per active employee
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Employee List */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle>Employee Payroll Profiles</CardTitle>
              <CardDescription>Manage employee payroll information and bank accounts</CardDescription>
            </div>
          </div>
        </CardHeader>
        <CardContent>
          {/* Search */}
          <div className="flex items-center space-x-4 mb-6">
            <div className="relative flex-1">
              <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
              <Input
                placeholder="Search employees..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-8"
              />
            </div>
          </div>

          {/* Employee Table */}
          <div className="rounded-md border">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Employee</TableHead>
                  <TableHead>Department</TableHead>
                  <TableHead>Employee Type</TableHead>
                  <TableHead>Employment Status</TableHead>
                  <TableHead>Salary</TableHead>
                  <TableHead>Pay Grade</TableHead>
                  <TableHead>Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {filteredEmployees.map((employee) => (
                  <TableRow key={employee.id}>
                    <TableCell>
                      <div className="space-y-1">
                        <div className="font-medium">{employee.full_name}</div>
                        <div className="text-sm text-muted-foreground">{employee.email}</div>
                        <div className="text-sm text-muted-foreground">{employee.position}</div>
                      </div>
                    </TableCell>
                    <TableCell>
                      <Badge variant="outline">{employee.department || 'Not assigned'}</Badge>
                    </TableCell>
                    <TableCell>
                      <Badge variant="secondary">
                        {employee.employee_type?.replace('_', ' ') || 'Not set'}
                      </Badge>
                    </TableCell>
                    <TableCell>
                      <Badge variant={
                        employee.employment_status === 'active' ? 'default' :
                        employee.employment_status === 'inactive' ? 'secondary' :
                        'destructive'
                      }>
                        {employee.employment_status || 'Not set'}
                      </Badge>
                    </TableCell>
                    <TableCell>
                      {employee.salary ? `NPR ${employee.salary.toLocaleString()}` : 'Not set'}
                    </TableCell>
                    <TableCell>
                      <Badge variant="outline">{employee.pay_grade || 'Not assigned'}</Badge>
                    </TableCell>
                    <TableCell>
                      <div className="flex items-center space-x-2">
                        <Button
                          size="sm"
                          variant="outline"
                          onClick={() => handleViewProfile(employee.id)}
                        >
                          <Eye className="h-4 w-4 mr-1" />
                          View
                        </Button>
                        <Button
                          size="sm"
                          onClick={() => handleViewProfile(employee.id)}
                        >
                          <Edit className="h-4 w-4 mr-1" />
                          Edit
                        </Button>
                      </div>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </div>

          {filteredEmployees.length === 0 && (
            <div className="text-center py-8 text-muted-foreground">
              <User className="h-12 w-12 mx-auto mb-4 opacity-50" />
              <p>No employees found matching your search</p>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Employee Profile Dialog */}
      <Dialog open={showProfileDialog} onOpenChange={setShowProfileDialog}>
        <DialogContent className="max-w-6xl max-h-[90vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle>Employee Payroll Profile</DialogTitle>
          </DialogHeader>
          {selectedEmployee && (
            <EmployeePayrollProfile
              employeeId={selectedEmployee}
              onClose={handleCloseProfile}
            />
          )}
        </DialogContent>
      </Dialog>
    </div>
  )
}
