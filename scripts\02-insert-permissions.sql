-- Insert default permissions
INSERT INTO public.permissions (name, description, resource, action) VALUES
-- User management
('view_users', 'View user list and profiles', 'users', 'read'),
('create_users', 'Create new users', 'users', 'create'),
('edit_users', 'Edit user information', 'users', 'update'),
('delete_users', 'Delete users', 'users', 'delete'),
('manage_user_roles', 'Manage user roles and permissions', 'users', 'manage_roles'),

-- Attendance management
('view_attendance', 'View attendance records', 'attendance', 'read'),
('manage_attendance', 'Manage attendance records', 'attendance', 'manage'),
('approve_leave', 'Approve leave requests', 'attendance', 'approve'),

-- Task management
('view_tasks', 'View tasks', 'tasks', 'read'),
('create_tasks', 'Create new tasks', 'tasks', 'create'),
('edit_tasks', 'Edit task details', 'tasks', 'update'),
('delete_tasks', 'Delete tasks', 'tasks', 'delete'),
('assign_tasks', 'Assign tasks to users', 'tasks', 'assign'),

-- Payroll management
('view_payroll', 'View payroll information', 'payroll', 'read'),
('manage_payroll', 'Manage payroll records', 'payroll', 'manage'),
('process_payroll', 'Process payroll payments', 'payroll', 'process'),

-- Reports and analytics
('view_reports', 'View reports and analytics', 'reports', 'read'),
('export_data', 'Export data and reports', 'reports', 'export'),

-- System settings
('view_settings', 'View system settings', 'settings', 'read'),
('manage_settings', 'Manage system settings', 'settings', 'manage');

-- Assign permissions to roles
-- Admin - Full access
INSERT INTO public.role_permissions (role, permission_id)
SELECT 'admin', id FROM public.permissions;

-- HR Manager - HR focused permissions
INSERT INTO public.role_permissions (role, permission_id)
SELECT 'hr_manager', id FROM public.permissions 
WHERE name IN (
  'view_users', 'create_users', 'edit_users',
  'view_attendance', 'manage_attendance', 'approve_leave',
  'view_payroll', 'manage_payroll', 'process_payroll',
  'view_reports', 'export_data'
);

-- Manager - Team management permissions
INSERT INTO public.role_permissions (role, permission_id)
SELECT 'manager', id FROM public.permissions 
WHERE name IN (
  'view_users', 'view_attendance', 'approve_leave',
  'view_tasks', 'create_tasks', 'edit_tasks', 'assign_tasks',
  'view_reports'
);

-- Staff - Basic permissions
INSERT INTO public.role_permissions (role, permission_id)
SELECT 'staff', id FROM public.permissions 
WHERE name IN (
  'view_tasks', 'edit_tasks'
);
