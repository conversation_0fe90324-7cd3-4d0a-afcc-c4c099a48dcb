import { NextRequest, NextResponse } from "next/server"
import { serverDb } from "@/lib/server-db"
import { AuthService } from "@/lib/auth-utils"

// GET /api/admin/files/[fileId] - Download file
export async function GET(request: NextRequest, { params }: { params: { fileId: string } }) {
  try {
    const sessionToken = request.cookies.get("session-token")?.value

    if (!sessionToken) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 })
    }

    const currentUser = await AuthService.verifySession(sessionToken)

    if (!currentUser) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 })
    }

    // Get client IP
    const clientIP = request.headers.get('x-forwarded-for') || request.headers.get('x-real-ip') || 'unknown'

    // Get file content
    const fileData = await serverDb.getFileContent(params.fileId, currentUser.id, clientIP)

    if (!fileData) {
      return NextResponse.json({ error: "File not found" }, { status: 404 })
    }

    const { file, content } = fileData

    // Check if user has permission to access this file
    if (file.user_id !== currentUser.id && !["admin", "hr_manager"].includes(currentUser.role)) {
      return NextResponse.json({ error: "Forbidden" }, { status: 403 })
    }

    // Return file content with appropriate headers
    return new NextResponse(content, {
      status: 200,
      headers: {
        'Content-Type': file.mime_type,
        'Content-Disposition': `attachment; filename="${file.original_filename}"`,
        'Content-Length': file.file_size.toString(),
        'Cache-Control': 'private, no-cache'
      }
    })
  } catch (error) {
    console.error("Download file API error:", error)
    return NextResponse.json({ error: "Internal server error" }, { status: 500 })
  }
}

// DELETE /api/admin/files/[fileId] - Delete file
export async function DELETE(request: NextRequest, { params }: { params: { fileId: string } }) {
  try {
    const sessionToken = request.cookies.get("session-token")?.value

    if (!sessionToken) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 })
    }

    const currentUser = await AuthService.verifySession(sessionToken)

    if (!currentUser || !["admin", "hr_manager"].includes(currentUser.role)) {
      return NextResponse.json({ error: "Forbidden" }, { status: 403 })
    }

    // Delete file
    const success = await serverDb.deleteFile(params.fileId, currentUser.id)

    if (!success) {
      return NextResponse.json({ error: "File not found" }, { status: 404 })
    }

    return NextResponse.json({ message: "File deleted successfully" })
  } catch (error) {
    console.error("Delete file API error:", error)
    return NextResponse.json({ error: "Internal server error" }, { status: 500 })
  }
}
