#!/usr/bin/env node

// <PERSON>ript to fix the missing working_days_configuration and attendance_calculation_settings tables
// This script executes the comprehensive schema creation script

const fs = require('fs');
const path = require('path');

let neon, dotenv;

try {
  ({ neon } = require('@neondatabase/serverless'));
  dotenv = require('dotenv');
  dotenv.config({ path: '.env.local' });
} catch (error) {
  console.log('⚠️  Required packages not installed yet. Please run: npm install @neondatabase/serverless dotenv');
  console.log('Error:', error.message);
  process.exit(1);
}

async function fixWorkingDaysTables() {
  console.log('🔧 Fixing Working Days Configuration Tables...\n');
  
  // Check if DATABASE_URL is set
  if (!process.env.DATABASE_URL) {
    console.error('❌ ERROR: DATABASE_URL environment variable is not set');
    console.log('📝 Please update your .env.local file with your Neon connection string');
    process.exit(1);
  }
  
  console.log('✅ DATABASE_URL found in environment');
  
  try {
    const sql = neon(process.env.DATABASE_URL);
    
    // Test connection first
    console.log('🔄 Testing database connection...');
    await sql`SELECT 1`;
    console.log('✅ Database connection successful!\n');
    
    // Read the comprehensive fix script
    const scriptPath = path.join(__dirname, 'diagnose-and-fix-working-days.sql');
    
    if (!fs.existsSync(scriptPath)) {
      console.error('❌ ERROR: diagnose-and-fix-working-days.sql not found');
      console.log('📁 Expected path:', scriptPath);
      process.exit(1);
    }
    
    console.log('📖 Reading schema script...');
    const schemaScript = fs.readFileSync(scriptPath, 'utf8');
    
    console.log('🚀 Executing comprehensive working days fix...');
    console.log('⏳ This may take a moment...\n');
    
    // Execute the entire script
    // Note: We need to split by statements and execute them individually
    // because some PostgreSQL features like DO blocks need separate execution
    
    const statements = schemaScript
      .split(/;\s*$/gm)
      .map(stmt => stmt.trim())
      .filter(stmt => stmt.length > 0 && !stmt.startsWith('--'));
    
    console.log(`📝 Found ${statements.length} SQL statements to execute`);
    
    let successCount = 0;
    let errorCount = 0;
    
    for (let i = 0; i < statements.length; i++) {
      const statement = statements[i];
      
      // Skip empty statements or comments
      if (!statement || statement.startsWith('--')) {
        continue;
      }
      
      try {
        console.log(`⏳ Executing statement ${i + 1}/${statements.length}...`);
        
        // Add semicolon back if it's not a DO block
        const finalStatement = statement.endsWith(';') ? statement : statement + ';';
        
        await sql([finalStatement]);
        successCount++;
        
      } catch (error) {
        console.log(`⚠️  Statement ${i + 1} had an issue: ${error.message}`);
        
        // Some errors are expected (like "table already exists")
        if (error.message.includes('already exists') || 
            error.message.includes('duplicate key') ||
            error.message.includes('does not exist')) {
          console.log('   (This is likely expected - continuing...)');
          successCount++;
        } else {
          console.error(`❌ Unexpected error in statement ${i + 1}:`, error.message);
          errorCount++;
        }
      }
    }
    
    console.log('\n📊 Execution Summary:');
    console.log(`✅ Successful statements: ${successCount}`);
    console.log(`❌ Failed statements: ${errorCount}`);
    
    // Verify the fix worked
    console.log('\n🔍 Verifying tables were created...');
    
    const tablesCheck = await sql`
      SELECT table_name 
      FROM information_schema.tables 
      WHERE table_schema = 'public' 
      AND table_name IN ('working_days_configuration', 'attendance_calculation_settings')
    `;
    
    console.log(`📋 Tables found: ${tablesCheck.map(t => t.table_name).join(', ')}`);
    
    if (tablesCheck.length === 2) {
      console.log('✅ SUCCESS: Both required tables exist!');
      
      // Check data counts
      const workingDaysCount = await sql`SELECT COUNT(*) as count FROM working_days_configuration`;
      const settingsCount = await sql`SELECT COUNT(*) as count FROM attendance_calculation_settings`;
      
      console.log(`📊 Working days configurations: ${workingDaysCount[0].count}`);
      console.log(`📊 Attendance settings: ${settingsCount[0].count}`);
      
      if (workingDaysCount[0].count >= 12 && settingsCount[0].count >= 10) {
        console.log('\n🎉 COMPLETE SUCCESS: All tables and data are properly configured!');
        console.log('🚀 The Payroll Settings page should now work correctly.');
        console.log('🔗 You can test it at: http://localhost:3000/admin/payroll/settings');
      } else {
        console.log('\n⚠️  Tables exist but may be missing some default data.');
        console.log('💡 You may need to add default configurations manually.');
      }
      
    } else {
      console.log('❌ FAILED: Some tables are still missing');
      console.log('💡 You may need to run the script manually in your Neon SQL Editor');
    }
    
  } catch (error) {
    console.error('💥 FATAL ERROR:', error);
    console.log('\n🔧 Troubleshooting:');
    console.log('1. Check your DATABASE_URL in .env.local');
    console.log('2. Ensure your Neon database is accessible');
    console.log('3. Try running the SQL script manually in Neon Console');
    process.exit(1);
  }
}

// Run the fix
fixWorkingDaysTables().catch(console.error);
