"use client"

import React, { useState, useEffect } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Badge } from '@/components/ui/badge'
import { <PERSON><PERSON>, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog'
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table'
import { Progress } from '@/components/ui/progress'
import { toast } from 'sonner'
import { 
  Calculator, Play, CheckCircle, FileText, Download, Calendar, 
  DollarSign, Users, Trending<PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>gle, Eye, Settings,
  Flag, Shield, Clock, BarChart3
} from 'lucide-react'

interface FiscalYear {
  fiscal_year: string
  start_date: string
  end_date: string
  ad_start_date: string
  ad_end_date: string
}

interface PayrollRecord {
  id: string
  user_id: string
  full_name: string
  department: string
  position: string
  base_salary: number
  gross_pay: number
  net_pay: number
  tax_deductions: number
  provident_fund: number
  status: string
  calculated_at: string
}

interface ComplianceReport {
  period: string
  fiscal_year: string
  total_employees: number
  total_gross_pay: number
  total_net_pay: number
  total_tax_deducted: number
  total_pf_deducted: number
  average_salary: number
  records: PayrollRecord[]
}

export function NepalCompliantPayroll() {
  const [fiscalYear, setFiscalYear] = useState<FiscalYear | null>(null)
  const [payrollRecords, setPayrollRecords] = useState<PayrollRecord[]>([])
  const [complianceReport, setComplianceReport] = useState<ComplianceReport | null>(null)
  const [loading, setLoading] = useState(false)
  const [processing, setProcessing] = useState(false)
  const [selectedYear, setSelectedYear] = useState(new Date().getFullYear())
  const [selectedMonth, setSelectedMonth] = useState(new Date().getMonth() + 1)
  const [processingProgress, setProcessingProgress] = useState(0)
  const [showComplianceDialog, setShowComplianceDialog] = useState(false)

  useEffect(() => {
    fetchFiscalYear()
    fetchPayrollRecords()
  }, [selectedYear, selectedMonth])

  const fetchFiscalYear = async () => {
    try {
      const response = await fetch('/api/admin/payroll/nepal-compliant?action=fiscal_year')
      const data = await response.json()

      if (data.success) {
        setFiscalYear(data.data)
      }
    } catch (error) {
      console.error('Error fetching fiscal year:', error)
    }
  }

  const fetchPayrollRecords = async () => {
    try {
      setLoading(true)
      const response = await fetch(`/api/admin/payroll/nepal-compliant?action=payroll_records&year=${selectedYear}&month=${selectedMonth}`)
      const data = await response.json()

      if (data.success) {
        setPayrollRecords(data.data)
      } else {
        toast.error(data.error || 'Failed to fetch payroll records')
      }
    } catch (error) {
      console.error('Error fetching payroll records:', error)
      toast.error('Failed to fetch payroll records')
    } finally {
      setLoading(false)
    }
  }

  const fetchComplianceReport = async () => {
    try {
      setLoading(true)
      const response = await fetch(`/api/admin/payroll/nepal-compliant?action=compliance_report&year=${selectedYear}&month=${selectedMonth}`)
      const data = await response.json()

      if (data.success) {
        setComplianceReport(data.data)
        setShowComplianceDialog(true)
      } else {
        toast.error(data.error || 'Failed to generate compliance report')
      }
    } catch (error) {
      console.error('Error fetching compliance report:', error)
      toast.error('Failed to generate compliance report')
    } finally {
      setLoading(false)
    }
  }

  const handleProcessMonthlyPayroll = async () => {
    try {
      setProcessing(true)
      setProcessingProgress(0)

      const response = await fetch('/api/admin/payroll/nepal-compliant', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          action: 'process_monthly',
          year: selectedYear,
          month: selectedMonth
        })
      })

      const data = await response.json()

      if (data.success) {
        setProcessingProgress(100)
        toast.success(data.message)
        fetchPayrollRecords()
      } else {
        toast.error(data.error || 'Failed to process monthly payroll')
      }
    } catch (error) {
      console.error('Error processing monthly payroll:', error)
      toast.error('Failed to process monthly payroll')
    } finally {
      setProcessing(false)
    }
  }

  const handleUpdateStatus = async (payrollIds: string[], status: string) => {
    try {
      setLoading(true)
      const response = await fetch('/api/admin/payroll/nepal-compliant', {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          action: 'update_status',
          payrollIds: payrollIds,
          status: status
        })
      })

      const data = await response.json()

      if (data.success) {
        toast.success(data.message)
        fetchPayrollRecords()
      } else {
        toast.error(data.error || 'Failed to update status')
      }
    } catch (error) {
      console.error('Error updating status:', error)
      toast.error('Failed to update status')
    } finally {
      setLoading(false)
    }
  }

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'calculated':
        return <Badge variant="outline">Calculated</Badge>
      case 'approved':
        return <Badge variant="default">Approved</Badge>
      case 'processed':
        return <Badge variant="secondary">Processed</Badge>
      case 'paid':
        return <Badge variant="destructive">Paid</Badge>
      default:
        return <Badge variant="secondary">{status}</Badge>
    }
  }

  const currentMonthYear = `${selectedYear}-${selectedMonth.toString().padStart(2, '0')}`

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-3xl font-bold flex items-center">
            <Flag className="h-8 w-8 mr-3 text-red-600" />
            Nepal-Compliant Payroll
          </h2>
          <p className="text-muted-foreground">
            Monthly payroll processing compliant with Nepal employment laws and tax regulations
          </p>
          {fiscalYear && (
            <p className="text-sm text-muted-foreground mt-1">
              Fiscal Year: {fiscalYear.fiscal_year} ({fiscalYear.start_date} to {fiscalYear.end_date})
            </p>
          )}
        </div>
        <div className="flex items-center space-x-2">
          <Select value={selectedMonth.toString()} onValueChange={(value) => setSelectedMonth(parseInt(value))}>
            <SelectTrigger className="w-32">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              {Array.from({ length: 12 }, (_, i) => (
                <SelectItem key={i + 1} value={(i + 1).toString()}>
                  {new Date(0, i).toLocaleString('default', { month: 'long' })}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
          <Select value={selectedYear.toString()} onValueChange={(value) => setSelectedYear(parseInt(value))}>
            <SelectTrigger className="w-24">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              {Array.from({ length: 5 }, (_, i) => (
                <SelectItem key={selectedYear - 2 + i} value={(selectedYear - 2 + i).toString()}>
                  {selectedYear - 2 + i}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>
      </div>

      {/* Action Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <Play className="h-5 w-5 mr-2" />
              Process Payroll
            </CardTitle>
            <CardDescription>Process Nepal-compliant monthly payroll</CardDescription>
          </CardHeader>
          <CardContent>
            <Button 
              onClick={handleProcessMonthlyPayroll} 
              disabled={processing || loading}
              className="w-full"
            >
              <Calculator className="h-4 w-4 mr-2" />
              {processing ? 'Processing...' : 'Process Monthly'}
            </Button>
            {processing && (
              <div className="mt-4">
                <Progress value={processingProgress} className="w-full" />
                <p className="text-sm text-muted-foreground mt-2">
                  Processing payroll... {processingProgress}%
                </p>
              </div>
            )}
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <Shield className="h-5 w-5 mr-2" />
              Compliance Report
            </CardTitle>
            <CardDescription>Generate Nepal compliance report</CardDescription>
          </CardHeader>
          <CardContent>
            <Button 
              onClick={fetchComplianceReport} 
              disabled={loading}
              variant="outline"
              className="w-full"
            >
              <FileText className="h-4 w-4 mr-2" />
              Generate Report
            </Button>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <CheckCircle className="h-5 w-5 mr-2" />
              Approve All
            </CardTitle>
            <CardDescription>Approve all calculated payrolls</CardDescription>
          </CardHeader>
          <CardContent>
            <Button 
              onClick={() => handleUpdateStatus(payrollRecords.filter(r => r.status === 'calculated').map(r => r.id), 'approved')} 
              disabled={loading || payrollRecords.filter(r => r.status === 'calculated').length === 0}
              className="w-full"
            >
              <CheckCircle className="h-4 w-4 mr-2" />
              Approve ({payrollRecords.filter(r => r.status === 'calculated').length})
            </Button>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <Download className="h-5 w-5 mr-2" />
              Export Data
            </CardTitle>
            <CardDescription>Export payroll data for reporting</CardDescription>
          </CardHeader>
          <CardContent>
            <Button 
              disabled={payrollRecords.length === 0}
              variant="outline"
              className="w-full"
            >
              <Download className="h-4 w-4 mr-2" />
              Export Excel
            </Button>
          </CardContent>
        </Card>
      </div>

      {/* Summary Cards */}
      {payrollRecords.length > 0 && (
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          <Card>
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm text-muted-foreground">Total Employees</p>
                  <p className="text-2xl font-bold">{payrollRecords.length}</p>
                </div>
                <Users className="h-8 w-8 text-muted-foreground" />
              </div>
            </CardContent>
          </Card>
          
          <Card>
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm text-muted-foreground">Total Gross Pay</p>
                  <p className="text-2xl font-bold">
                    NPR {payrollRecords.reduce((sum, record) => sum + (record.gross_pay || 0), 0).toLocaleString()}
                  </p>
                </div>
                <DollarSign className="h-8 w-8 text-muted-foreground" />
              </div>
            </CardContent>
          </Card>
          
          <Card>
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm text-muted-foreground">Total Net Pay</p>
                  <p className="text-2xl font-bold">
                    NPR {payrollRecords.reduce((sum, record) => sum + (record.net_pay || 0), 0).toLocaleString()}
                  </p>
                </div>
                <TrendingUp className="h-8 w-8 text-muted-foreground" />
              </div>
            </CardContent>
          </Card>
          
          <Card>
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm text-muted-foreground">Total Tax</p>
                  <p className="text-2xl font-bold">
                    NPR {payrollRecords.reduce((sum, record) => sum + (record.tax_deductions || 0), 0).toLocaleString()}
                  </p>
                </div>
                <BarChart3 className="h-8 w-8 text-muted-foreground" />
              </div>
            </CardContent>
          </Card>
        </div>
      )}

      {/* Payroll Records */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle>Monthly Payroll Records</CardTitle>
              <CardDescription>
                Nepal-compliant payroll for {new Date(selectedYear, selectedMonth - 1).toLocaleString('default', { month: 'long', year: 'numeric' })}
              </CardDescription>
            </div>
            {payrollRecords.length > 0 && (
              <div className="flex items-center space-x-2">
                <Badge variant="outline">
                  {payrollRecords.filter(r => r.status === 'calculated').length} Calculated
                </Badge>
                <Badge variant="default">
                  {payrollRecords.filter(r => r.status === 'approved').length} Approved
                </Badge>
                <Badge variant="secondary">
                  {payrollRecords.filter(r => r.status === 'processed').length} Processed
                </Badge>
              </div>
            )}
          </div>
        </CardHeader>
        <CardContent>
          {payrollRecords.length === 0 ? (
            <div className="text-center py-8 text-muted-foreground">
              <Calendar className="h-12 w-12 mx-auto mb-4 opacity-50" />
              <p>No payroll records found for this period</p>
              <p className="text-sm">Click "Process Monthly" to calculate payroll</p>
            </div>
          ) : (
            <div className="rounded-md border">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Employee</TableHead>
                    <TableHead>Department</TableHead>
                    <TableHead>Base Salary</TableHead>
                    <TableHead>Gross Pay</TableHead>
                    <TableHead>Tax</TableHead>
                    <TableHead>PF</TableHead>
                    <TableHead>Net Pay</TableHead>
                    <TableHead>Status</TableHead>
                    <TableHead>Actions</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {payrollRecords.map((record) => (
                    <TableRow key={record.id}>
                      <TableCell>
                        <div className="space-y-1">
                          <div className="font-medium">{record.full_name}</div>
                          <div className="text-sm text-muted-foreground">{record.position}</div>
                        </div>
                      </TableCell>
                      <TableCell>
                        <Badge variant="outline">{record.department}</Badge>
                      </TableCell>
                      <TableCell>
                        <div className="font-medium">NPR {record.base_salary?.toLocaleString() || '0'}</div>
                      </TableCell>
                      <TableCell>
                        <div className="font-medium">NPR {record.gross_pay?.toLocaleString() || '0'}</div>
                      </TableCell>
                      <TableCell>
                        <div className="text-sm">NPR {record.tax_deductions?.toLocaleString() || '0'}</div>
                      </TableCell>
                      <TableCell>
                        <div className="text-sm">NPR {record.provident_fund?.toLocaleString() || '0'}</div>
                      </TableCell>
                      <TableCell>
                        <div className="font-bold text-green-600">
                          NPR {record.net_pay?.toLocaleString() || '0'}
                        </div>
                      </TableCell>
                      <TableCell>
                        {getStatusBadge(record.status)}
                      </TableCell>
                      <TableCell>
                        <div className="flex items-center space-x-2">
                          {record.status === 'calculated' && (
                            <Button
                              size="sm"
                              onClick={() => handleUpdateStatus([record.id], 'approved')}
                              disabled={loading}
                            >
                              <CheckCircle className="h-4 w-4 mr-1" />
                              Approve
                            </Button>
                          )}
                          <Button size="sm" variant="outline">
                            <Eye className="h-4 w-4" />
                          </Button>
                        </div>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Compliance Report Dialog */}
      <Dialog open={showComplianceDialog} onOpenChange={setShowComplianceDialog}>
        <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle className="flex items-center">
              <Shield className="h-5 w-5 mr-2" />
              Nepal Compliance Report
            </DialogTitle>
            <DialogDescription>
              {complianceReport && `Compliance report for ${complianceReport.period} (FY ${complianceReport.fiscal_year})`}
            </DialogDescription>
          </DialogHeader>
          {complianceReport && (
            <div className="space-y-6">
              {/* Summary */}
              <div className="grid grid-cols-2 md:grid-cols-3 gap-4">
                <div className="space-y-2">
                  <Label>Total Employees</Label>
                  <p className="text-2xl font-bold">{complianceReport.total_employees}</p>
                </div>
                <div className="space-y-2">
                  <Label>Total Gross Pay</Label>
                  <p className="text-2xl font-bold">NPR {complianceReport.total_gross_pay.toLocaleString()}</p>
                </div>
                <div className="space-y-2">
                  <Label>Total Net Pay</Label>
                  <p className="text-2xl font-bold">NPR {complianceReport.total_net_pay.toLocaleString()}</p>
                </div>
                <div className="space-y-2">
                  <Label>Total Tax Deducted</Label>
                  <p className="text-2xl font-bold">NPR {complianceReport.total_tax_deducted.toLocaleString()}</p>
                </div>
                <div className="space-y-2">
                  <Label>Total PF Deducted</Label>
                  <p className="text-2xl font-bold">NPR {complianceReport.total_pf_deducted.toLocaleString()}</p>
                </div>
                <div className="space-y-2">
                  <Label>Average Salary</Label>
                  <p className="text-2xl font-bold">NPR {complianceReport.average_salary.toLocaleString()}</p>
                </div>
              </div>

              {/* Compliance Indicators */}
              <div className="space-y-4">
                <h4 className="font-semibold">Compliance Indicators</h4>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="flex items-center space-x-2">
                    <CheckCircle className="h-5 w-5 text-green-600" />
                    <span>Nepal Income Tax Act compliance</span>
                  </div>
                  <div className="flex items-center space-x-2">
                    <CheckCircle className="h-5 w-5 text-green-600" />
                    <span>Provident Fund Act compliance</span>
                  </div>
                  <div className="flex items-center space-x-2">
                    <CheckCircle className="h-5 w-5 text-green-600" />
                    <span>Labor Act 2074 compliance</span>
                  </div>
                  <div className="flex items-center space-x-2">
                    <CheckCircle className="h-5 w-5 text-green-600" />
                    <span>Social Security Act compliance</span>
                  </div>
                </div>
              </div>

              {/* Key Metrics */}
              <div className="space-y-4">
                <h4 className="font-semibold">Key Metrics</h4>
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <Label>Effective Tax Rate</Label>
                    <p className="text-lg font-medium">
                      {complianceReport.total_gross_pay > 0 
                        ? ((complianceReport.total_tax_deducted / complianceReport.total_gross_pay) * 100).toFixed(2)
                        : 0}%
                    </p>
                  </div>
                  <div>
                    <Label>PF Contribution Rate</Label>
                    <p className="text-lg font-medium">
                      {complianceReport.total_gross_pay > 0 
                        ? ((complianceReport.total_pf_deducted / complianceReport.total_gross_pay) * 100).toFixed(2)
                        : 0}%
                    </p>
                  </div>
                </div>
              </div>
            </div>
          )}
          <DialogFooter>
            <Button variant="outline" onClick={() => setShowComplianceDialog(false)}>
              Close
            </Button>
            <Button>
              <Download className="h-4 w-4 mr-2" />
              Export Report
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  )
}
