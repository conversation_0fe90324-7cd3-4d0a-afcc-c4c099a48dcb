"use client"

import { useState } from "react"
import { useRouter } from "next/navigation"
import { <PERSON>pp<PERSON>eader } from "@/components/app-header"
import { AdminSidebar } from "@/components/admin-sidebar"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group"
import { Label } from "@/components/ui/label"
import { useToast } from "@/components/ui/use-toast"
import { ArrowLeft, Save, Shield, Users, DollarSign, BarChart2, Settings } from "lucide-react"

export default function ManageUserRolesPage({ params }: { params: { id: string } }) {
  const router = useRouter()
  const { toast } = useToast()
  const [isLoading, setIsLoading] = useState(false)
  const [selectedRole, setSelectedRole] = useState("employee")

  // Mock user data - in a real app, you would fetch this based on the ID
  const user = {
    id: params.id,
    name: "<PERSON>",
    email: "<EMAIL>",
    currentRole: "Employee",
    department: "Marketing",
  }

  const handleSaveRole = async () => {
    setIsLoading(true)

    try {
      // Simulate API call
      await new Promise((resolve) => setTimeout(resolve, 1000))

      toast({
        title: "Role updated successfully",
        description: `${user.name}'s role has been updated.`,
      })

      router.push("/admin/users")
    } catch (error) {
      toast({
        variant: "destructive",
        title: "Error updating role",
        description: "There was a problem updating the user's role. Please try again.",
      })
    } finally {
      setIsLoading(false)
    }
  }

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900 flex flex-col">
      <AppHeader />

      <div className="flex flex-1">
        <AdminSidebar />

        <div className="flex-1 p-4 md:p-6">
          <div className="flex items-center justify-between mb-6">
            <div className="flex items-center">
              <Button variant="ghost" size="icon" onClick={() => router.push("/admin/users")} className="mr-2">
                <ArrowLeft className="h-5 w-5" />
              </Button>
              <div>
                <h1 className="text-xl font-semibold text-teal-800 dark:text-teal-300">Manage User Role</h1>
                <p className="text-sm text-gray-500 dark:text-gray-400">Update role and access level</p>
              </div>
            </div>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <div className="md:col-span-1">
              <Card className="dark:bg-gray-800 border-gray-200 dark:border-gray-700">
                <CardHeader>
                  <CardTitle className="text-teal-800 dark:text-teal-300">User Information</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    <div>
                      <p className="text-sm font-medium text-gray-500 dark:text-gray-400">Name</p>
                      <p className="text-base dark:text-gray-200">{user.name}</p>
                    </div>
                    <div>
                      <p className="text-sm font-medium text-gray-500 dark:text-gray-400">Email</p>
                      <p className="text-base dark:text-gray-200">{user.email}</p>
                    </div>
                    <div>
                      <p className="text-sm font-medium text-gray-500 dark:text-gray-400">Current Role</p>
                      <p className="text-base dark:text-gray-200">{user.currentRole}</p>
                    </div>
                    <div>
                      <p className="text-sm font-medium text-gray-500 dark:text-gray-400">Department</p>
                      <p className="text-base dark:text-gray-200">{user.department}</p>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>

            <div className="md:col-span-2">
              <Card className="dark:bg-gray-800 border-gray-200 dark:border-gray-700">
                <CardHeader>
                  <CardTitle className="text-teal-800 dark:text-teal-300">Assign Role</CardTitle>
                  <CardDescription>Select a role to define the user's access level and permissions</CardDescription>
                </CardHeader>
                <CardContent>
                  <RadioGroup value={selectedRole} onValueChange={setSelectedRole} className="space-y-4">
                    <div className="flex items-start space-x-3 p-3 rounded-md border border-gray-200 dark:border-gray-700 hover:bg-gray-50 dark:hover:bg-gray-700/50">
                      <RadioGroupItem value="admin" id="admin" className="mt-1" />
                      <div className="flex-1">
                        <Label htmlFor="admin" className="flex items-center text-base font-medium dark:text-gray-200">
                          <Shield className="h-5 w-5 mr-2 text-teal-600 dark:text-teal-400" />
                          Administrator
                        </Label>
                        <p className="text-sm text-gray-500 dark:text-gray-400 mt-1">
                          Full access to all features and settings. Can manage users, roles, and system configuration.
                        </p>
                      </div>
                    </div>

                    <div className="flex items-start space-x-3 p-3 rounded-md border border-gray-200 dark:border-gray-700 hover:bg-gray-50 dark:hover:bg-gray-700/50">
                      <RadioGroupItem value="manager" id="manager" className="mt-1" />
                      <div className="flex-1">
                        <Label htmlFor="manager" className="flex items-center text-base font-medium dark:text-gray-200">
                          <Users className="h-5 w-5 mr-2 text-teal-600 dark:text-teal-400" />
                          Manager
                        </Label>
                        <p className="text-sm text-gray-500 dark:text-gray-400 mt-1">
                          Can manage team members, approve requests, view reports, and access most features.
                        </p>
                      </div>
                    </div>

                    <div className="flex items-start space-x-3 p-3 rounded-md border border-gray-200 dark:border-gray-700 hover:bg-gray-50 dark:hover:bg-gray-700/50">
                      <RadioGroupItem value="employee" id="employee" className="mt-1" />
                      <div className="flex-1">
                        <Label
                          htmlFor="employee"
                          className="flex items-center text-base font-medium dark:text-gray-200"
                        >
                          <DollarSign className="h-5 w-5 mr-2 text-teal-600 dark:text-teal-400" />
                          Employee
                        </Label>
                        <p className="text-sm text-gray-500 dark:text-gray-400 mt-1">
                          Standard access to daily operations. Can manage own tasks, submit reports, and use basic
                          features.
                        </p>
                      </div>
                    </div>

                    <div className="flex items-start space-x-3 p-3 rounded-md border border-gray-200 dark:border-gray-700 hover:bg-gray-50 dark:hover:bg-gray-700/50">
                      <RadioGroupItem value="readonly" id="readonly" className="mt-1" />
                      <div className="flex-1">
                        <Label
                          htmlFor="readonly"
                          className="flex items-center text-base font-medium dark:text-gray-200"
                        >
                          <BarChart2 className="h-5 w-5 mr-2 text-teal-600 dark:text-teal-400" />
                          Read-Only
                        </Label>
                        <p className="text-sm text-gray-500 dark:text-gray-400 mt-1">
                          View-only access to data and reports. Cannot make changes or perform actions.
                        </p>
                      </div>
                    </div>

                    <div className="flex items-start space-x-3 p-3 rounded-md border border-gray-200 dark:border-gray-700 hover:bg-gray-50 dark:hover:bg-gray-700/50">
                      <RadioGroupItem value="custom" id="custom" className="mt-1" />
                      <div className="flex-1">
                        <Label htmlFor="custom" className="flex items-center text-base font-medium dark:text-gray-200">
                          <Settings className="h-5 w-5 mr-2 text-teal-600 dark:text-teal-400" />
                          Custom Role
                        </Label>
                        <p className="text-sm text-gray-500 dark:text-gray-400 mt-1">
                          Define custom permissions and access levels for this user.
                        </p>
                        <Button
                          variant="outline"
                          size="sm"
                          className="mt-2"
                          onClick={() => router.push(`/admin/users/permissions/${user.id}`)}
                        >
                          Configure Custom Permissions
                        </Button>
                      </div>
                    </div>
                  </RadioGroup>
                </CardContent>
                <CardFooter className="flex justify-between">
                  <Button variant="outline" onClick={() => router.push("/admin/users")}>
                    Cancel
                  </Button>
                  <Button
                    onClick={handleSaveRole}
                    className="bg-exobank-green hover:bg-exobank-green/90"
                    disabled={isLoading}
                  >
                    {isLoading ? (
                      "Saving..."
                    ) : (
                      <>
                        <Save className="mr-2 h-4 w-4" />
                        Save Changes
                      </>
                    )}
                  </Button>
                </CardFooter>
              </Card>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
