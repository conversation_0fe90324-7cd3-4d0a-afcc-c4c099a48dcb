"use client"

import React, { useState, useEffect } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Textarea } from '@/components/ui/textarea'
import { Badge } from '@/components/ui/badge'
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog'
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table'
import { toast } from 'sonner'
import { Plus, Check, X, Eye, Alert<PERSON><PERSON>gle, <PERSON>Sign, <PERSON>, TrendingDown, Clock, Search, FileText } from 'lucide-react'

interface DeductionType {
  id: string
  name: string
  code: string
  category: string
  calculation_type: string
  fixed_amount?: number
  percentage?: number
  percentage_base?: string
  is_taxable: boolean
  is_statutory: boolean
  description?: string
  is_active: boolean
}

interface DeductionApproval {
  id: string
  user_id: string
  component_id: string
  component_name?: string
  deduction_amount: number
  deduction_reason: string
  requested_by: string
  requested_by_name?: string
  approved_by?: string
  approved_by_name?: string
  status: 'pending' | 'approved' | 'rejected' | 'cancelled'
  request_date: string
  approval_date?: string
  effective_from: string
  effective_to?: string
  notes?: string
  rejection_reason?: string
}

interface EmployeeDeductionSummary {
  user_id: string
  full_name: string
  email: string
  department?: string
  position?: string
  base_salary?: number
  total_deductions: number
  pending_deductions: number
  approved_deductions: number
  deductions: DeductionApproval[]
}

interface DeductionStatistics {
  totalDeductionTypes: number
  totalPendingApprovals: number
  totalApprovedDeductions: number
  totalDeductionAmount: number
  mostUsedDeductionType: string
  averageDeductionPerEmployee: number
  approvalRate: number
}

export function DeductionsManagement() {
  const [deductionTypes, setDeductionTypes] = useState<DeductionType[]>([])
  const [pendingApprovals, setPendingApprovals] = useState<DeductionApproval[]>([])
  const [employeeSummary, setEmployeeSummary] = useState<EmployeeDeductionSummary[]>([])
  const [statistics, setStatistics] = useState<DeductionStatistics | null>(null)
  const [loading, setLoading] = useState(true)
  const [searchTerm, setSearchTerm] = useState('')
  const [showCreateTypeDialog, setShowCreateTypeDialog] = useState(false)
  const [showRequestDialog, setShowRequestDialog] = useState(false)
  const [showApprovalDialog, setShowApprovalDialog] = useState(false)
  const [selectedApproval, setSelectedApproval] = useState<DeductionApproval | null>(null)
  const [selectedEmployee, setSelectedEmployee] = useState<string>('')

  const [newDeductionType, setNewDeductionType] = useState({
    name: '',
    code: '',
    category: 'company_policy',
    calculation_type: 'fixed',
    fixed_amount: 0,
    percentage: 0,
    percentage_base: 'base_salary',
    is_taxable: true,
    is_statutory: false,
    description: ''
  })

  const [newRequest, setNewRequest] = useState({
    component_id: '',
    deduction_amount: 0,
    deduction_reason: '',
    effective_from: new Date().toISOString().split('T')[0],
    effective_to: '',
    notes: ''
  })

  const [approvalAction, setApprovalAction] = useState({
    action: 'approve',
    notes: '',
    rejectionReason: ''
  })

  useEffect(() => {
    fetchData()
  }, [])

  const fetchData = async () => {
    try {
      setLoading(true)
      
      // Fetch deduction types
      const typesResponse = await fetch('/api/admin/deductions?action=types')
      const typesData = await typesResponse.json()
      
      // Fetch pending approvals
      const pendingResponse = await fetch('/api/admin/deductions?action=pending')
      const pendingData = await pendingResponse.json()
      
      // Fetch employee summary
      const summaryResponse = await fetch('/api/admin/deductions?action=summary')
      const summaryData = await summaryResponse.json()
      
      // Fetch statistics
      const statsResponse = await fetch('/api/admin/deductions?action=statistics')
      const statsData = await statsResponse.json()

      if (typesData.success) {
        setDeductionTypes(typesData.data)
      }
      
      if (pendingData.success) {
        setPendingApprovals(pendingData.data)
      }
      
      if (summaryData.success) {
        setEmployeeSummary(summaryData.data)
      }
      
      if (statsData.success) {
        setStatistics(statsData.data)
      }

    } catch (error) {
      console.error('Error fetching deductions data:', error)
      toast.error('Failed to fetch deductions data')
    } finally {
      setLoading(false)
    }
  }

  const handleCreateDeductionType = async () => {
    try {
      const response = await fetch('/api/admin/deductions', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          action: 'create_type',
          deductionType: newDeductionType
        })
      })

      const data = await response.json()

      if (data.success) {
        toast.success('Deduction type created successfully')
        setShowCreateTypeDialog(false)
        setNewDeductionType({
          name: '',
          code: '',
          category: 'company_policy',
          calculation_type: 'fixed',
          fixed_amount: 0,
          percentage: 0,
          percentage_base: 'base_salary',
          is_taxable: true,
          is_statutory: false,
          description: ''
        })
        fetchData()
      } else {
        toast.error(data.error || 'Failed to create deduction type')
      }
    } catch (error) {
      console.error('Error creating deduction type:', error)
      toast.error('Failed to create deduction type')
    }
  }

  const handleRequestDeduction = async () => {
    try {
      if (!selectedEmployee) {
        toast.error('Please select an employee')
        return
      }

      if (newRequest.deduction_reason.trim().length < 10) {
        toast.error('Deduction reason must be at least 10 characters long')
        return
      }

      const response = await fetch('/api/admin/deductions', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          action: 'request',
          deductionRequest: {
            ...newRequest,
            user_id: selectedEmployee
          }
        })
      })

      const data = await response.json()

      if (data.success) {
        toast.success('Deduction request submitted successfully')
        setShowRequestDialog(false)
        setSelectedEmployee('')
        setNewRequest({
          component_id: '',
          deduction_amount: 0,
          deduction_reason: '',
          effective_from: new Date().toISOString().split('T')[0],
          effective_to: '',
          notes: ''
        })
        fetchData()
      } else {
        toast.error(data.error || 'Failed to submit deduction request')
      }
    } catch (error) {
      console.error('Error requesting deduction:', error)
      toast.error('Failed to submit deduction request')
    }
  }

  const handleApprovalAction = async () => {
    try {
      if (!selectedApproval) return

      if (approvalAction.action === 'reject' && approvalAction.rejectionReason.trim().length < 10) {
        toast.error('Rejection reason must be at least 10 characters long')
        return
      }

      const response = await fetch('/api/admin/deductions', {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          action: approvalAction.action,
          approvalId: selectedApproval.id,
          notes: approvalAction.notes,
          rejectionReason: approvalAction.rejectionReason
        })
      })

      const data = await response.json()

      if (data.success) {
        toast.success(`Deduction ${approvalAction.action}d successfully`)
        setShowApprovalDialog(false)
        setSelectedApproval(null)
        setApprovalAction({
          action: 'approve',
          notes: '',
          rejectionReason: ''
        })
        fetchData()
      } else {
        toast.error(data.error || `Failed to ${approvalAction.action} deduction`)
      }
    } catch (error) {
      console.error(`Error ${approvalAction.action}ing deduction:`, error)
      toast.error(`Failed to ${approvalAction.action} deduction`)
    }
  }

  const filteredEmployees = employeeSummary.filter(employee =>
    employee.full_name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    employee.email.toLowerCase().includes(searchTerm.toLowerCase()) ||
    employee.department?.toLowerCase().includes(searchTerm.toLowerCase())
  )

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-3xl font-bold">Deductions Management</h2>
          <p className="text-muted-foreground">
            Manage deduction types, requests, and approval workflow with audit trail
          </p>
        </div>
        <div className="flex items-center space-x-2">
          <Dialog open={showCreateTypeDialog} onOpenChange={setShowCreateTypeDialog}>
            <DialogTrigger asChild>
              <Button>
                <Plus className="h-4 w-4 mr-2" />
                Create Deduction Type
              </Button>
            </DialogTrigger>
            <DialogContent>
              <DialogHeader>
                <DialogTitle>Create New Deduction Type</DialogTitle>
                <DialogDescription>Define a new deduction type for the organization</DialogDescription>
              </DialogHeader>
              <div className="space-y-4">
                <div className="grid grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="name">Deduction Name</Label>
                    <Input 
                      id="name" 
                      value={newDeductionType.name}
                      onChange={(e) => setNewDeductionType({...newDeductionType, name: e.target.value})}
                      placeholder="e.g., Loan EMI"
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="code">Code</Label>
                    <Input 
                      id="code" 
                      value={newDeductionType.code}
                      onChange={(e) => setNewDeductionType({...newDeductionType, code: e.target.value.toUpperCase()})}
                      placeholder="e.g., LOAN_EMI"
                    />
                  </div>
                </div>
                <div className="grid grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="category">Category</Label>
                    <Select value={newDeductionType.category} onValueChange={(value) => setNewDeductionType({...newDeductionType, category: value})}>
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="statutory">Statutory</SelectItem>
                        <SelectItem value="voluntary">Voluntary</SelectItem>
                        <SelectItem value="company_policy">Company Policy</SelectItem>
                        <SelectItem value="custom">Custom</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="calculation_type">Calculation Type</Label>
                    <Select value={newDeductionType.calculation_type} onValueChange={(value) => setNewDeductionType({...newDeductionType, calculation_type: value})}>
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="fixed">Fixed Amount</SelectItem>
                        <SelectItem value="percentage">Percentage</SelectItem>
                        <SelectItem value="formula">Formula</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                </div>
                {newDeductionType.calculation_type === 'fixed' && (
                  <div className="space-y-2">
                    <Label htmlFor="fixed_amount">Fixed Amount (NPR)</Label>
                    <Input 
                      id="fixed_amount" 
                      type="number"
                      value={newDeductionType.fixed_amount}
                      onChange={(e) => setNewDeductionType({...newDeductionType, fixed_amount: parseFloat(e.target.value) || 0})}
                    />
                  </div>
                )}
                {newDeductionType.calculation_type === 'percentage' && (
                  <div className="grid grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <Label htmlFor="percentage">Percentage (%)</Label>
                      <Input 
                        id="percentage" 
                        type="number"
                        value={newDeductionType.percentage}
                        onChange={(e) => setNewDeductionType({...newDeductionType, percentage: parseFloat(e.target.value) || 0})}
                      />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="percentage_base">Percentage Base</Label>
                      <Select value={newDeductionType.percentage_base} onValueChange={(value) => setNewDeductionType({...newDeductionType, percentage_base: value})}>
                        <SelectTrigger>
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="base_salary">Base Salary</SelectItem>
                          <SelectItem value="gross_pay">Gross Pay</SelectItem>
                          <SelectItem value="net_pay">Net Pay</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                  </div>
                )}
                <div className="space-y-2">
                  <Label htmlFor="description">Description</Label>
                  <Textarea 
                    id="description" 
                    value={newDeductionType.description}
                    onChange={(e) => setNewDeductionType({...newDeductionType, description: e.target.value})}
                    placeholder="Description of the deduction type"
                  />
                </div>
                <div className="flex items-center space-x-4">
                  <div className="flex items-center space-x-2">
                    <input 
                      type="checkbox" 
                      id="is_taxable"
                      checked={newDeductionType.is_taxable}
                      onChange={(e) => setNewDeductionType({...newDeductionType, is_taxable: e.target.checked})}
                    />
                    <Label htmlFor="is_taxable">Affects Taxable Income</Label>
                  </div>
                  <div className="flex items-center space-x-2">
                    <input 
                      type="checkbox" 
                      id="is_statutory"
                      checked={newDeductionType.is_statutory}
                      onChange={(e) => setNewDeductionType({...newDeductionType, is_statutory: e.target.checked})}
                    />
                    <Label htmlFor="is_statutory">Statutory</Label>
                  </div>
                </div>
              </div>
              <DialogFooter>
                <Button variant="outline" onClick={() => setShowCreateTypeDialog(false)}>
                  Cancel
                </Button>
                <Button onClick={handleCreateDeductionType}>
                  Create Deduction Type
                </Button>
              </DialogFooter>
            </DialogContent>
          </Dialog>
          
          <Dialog open={showRequestDialog} onOpenChange={setShowRequestDialog}>
            <DialogTrigger asChild>
              <Button variant="outline">
                <AlertTriangle className="h-4 w-4 mr-2" />
                Request Deduction
              </Button>
            </DialogTrigger>
            <DialogContent>
              <DialogHeader>
                <DialogTitle>Request Employee Deduction</DialogTitle>
                <DialogDescription>Submit a deduction request for an employee (requires approval)</DialogDescription>
              </DialogHeader>
              <div className="space-y-4">
                <div className="space-y-2">
                  <Label htmlFor="employee">Select Employee</Label>
                  <Select value={selectedEmployee} onValueChange={setSelectedEmployee}>
                    <SelectTrigger>
                      <SelectValue placeholder="Choose an employee" />
                    </SelectTrigger>
                    <SelectContent>
                      {employeeSummary.map((employee) => (
                        <SelectItem key={employee.user_id} value={employee.user_id}>
                          {employee.full_name} - {employee.department}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
                <div className="space-y-2">
                  <Label htmlFor="component">Deduction Type</Label>
                  <Select value={newRequest.component_id} onValueChange={(value) => setNewRequest({...newRequest, component_id: value})}>
                    <SelectTrigger>
                      <SelectValue placeholder="Choose deduction type" />
                    </SelectTrigger>
                    <SelectContent>
                      {deductionTypes.map((type) => (
                        <SelectItem key={type.id} value={type.id}>
                          {type.name} ({type.code})
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
                <div className="space-y-2">
                  <Label htmlFor="amount">Deduction Amount (NPR)</Label>
                  <Input 
                    id="amount" 
                    type="number"
                    value={newRequest.deduction_amount}
                    onChange={(e) => setNewRequest({...newRequest, deduction_amount: parseFloat(e.target.value) || 0})}
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="reason">Deduction Reason *</Label>
                  <Textarea 
                    id="reason" 
                    value={newRequest.deduction_reason}
                    onChange={(e) => setNewRequest({...newRequest, deduction_reason: e.target.value})}
                    placeholder="Mandatory reason for deduction (minimum 10 characters)"
                    className={newRequest.deduction_reason.length < 10 ? 'border-red-500' : ''}
                  />
                  <p className="text-sm text-muted-foreground">
                    {newRequest.deduction_reason.length}/10 characters minimum
                  </p>
                </div>
                <div className="grid grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="effective_from">Effective From</Label>
                    <Input 
                      id="effective_from" 
                      type="date"
                      value={newRequest.effective_from}
                      onChange={(e) => setNewRequest({...newRequest, effective_from: e.target.value})}
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="effective_to">Effective To (Optional)</Label>
                    <Input 
                      id="effective_to" 
                      type="date"
                      value={newRequest.effective_to}
                      onChange={(e) => setNewRequest({...newRequest, effective_to: e.target.value})}
                    />
                  </div>
                </div>
                <div className="space-y-2">
                  <Label htmlFor="notes">Additional Notes</Label>
                  <Textarea 
                    id="notes" 
                    value={newRequest.notes}
                    onChange={(e) => setNewRequest({...newRequest, notes: e.target.value})}
                    placeholder="Any additional information"
                  />
                </div>
              </div>
              <DialogFooter>
                <Button variant="outline" onClick={() => setShowRequestDialog(false)}>
                  Cancel
                </Button>
                <Button onClick={handleRequestDeduction}>
                  Submit Request
                </Button>
              </DialogFooter>
            </DialogContent>
          </Dialog>
        </div>
      </div>

      {/* Statistics Cards */}
      {statistics && (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Pending Approvals</CardTitle>
              <Clock className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-orange-600">{statistics.totalPendingApprovals}</div>
              <p className="text-xs text-muted-foreground">
                Require immediate attention
              </p>
            </CardContent>
          </Card>
          
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Approved Deductions</CardTitle>
              <Check className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{statistics.totalApprovedDeductions}</div>
              <p className="text-xs text-muted-foreground">
                {statistics.approvalRate.toFixed(1)}% approval rate
              </p>
            </CardContent>
          </Card>
          
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Total Amount</CardTitle>
              <TrendingDown className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">NPR {statistics.totalDeductionAmount.toLocaleString()}</div>
              <p className="text-xs text-muted-foreground">
                Monthly deductions
              </p>
            </CardContent>
          </Card>
          
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Average per Employee</CardTitle>
              <Users className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">NPR {statistics.averageDeductionPerEmployee.toLocaleString()}</div>
              <p className="text-xs text-muted-foreground">
                Most used: {statistics.mostUsedDeductionType}
              </p>
            </CardContent>
          </Card>
        </div>
      )}

      <Tabs defaultValue="pending" className="space-y-4">
        <TabsList>
          <TabsTrigger value="pending">
            Pending Approvals
            {pendingApprovals.length > 0 && (
              <Badge variant="destructive" className="ml-2">
                {pendingApprovals.length}
              </Badge>
            )}
          </TabsTrigger>
          <TabsTrigger value="employees">Employee Deductions</TabsTrigger>
          <TabsTrigger value="types">Deduction Types</TabsTrigger>
        </TabsList>

        <TabsContent value="pending" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Pending Deduction Approvals</CardTitle>
              <CardDescription>Review and approve/reject deduction requests</CardDescription>
            </CardHeader>
            <CardContent>
              {pendingApprovals.length === 0 ? (
                <div className="text-center py-8 text-muted-foreground">
                  <Check className="h-12 w-12 mx-auto mb-4 opacity-50" />
                  <p>No pending approvals</p>
                </div>
              ) : (
                <div className="rounded-md border">
                  <Table>
                    <TableHeader>
                      <TableRow>
                        <TableHead>Employee</TableHead>
                        <TableHead>Deduction Type</TableHead>
                        <TableHead>Amount</TableHead>
                        <TableHead>Reason</TableHead>
                        <TableHead>Requested By</TableHead>
                        <TableHead>Request Date</TableHead>
                        <TableHead>Actions</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {pendingApprovals.map((approval) => (
                        <TableRow key={approval.id}>
                          <TableCell>
                            <div className="space-y-1">
                              <div className="font-medium">Employee ID: {approval.user_id}</div>
                            </div>
                          </TableCell>
                          <TableCell>
                            <Badge variant="outline">{approval.component_name}</Badge>
                          </TableCell>
                          <TableCell>
                            <div className="font-medium">NPR {approval.deduction_amount.toLocaleString()}</div>
                          </TableCell>
                          <TableCell>
                            <div className="max-w-xs truncate" title={approval.deduction_reason}>
                              {approval.deduction_reason}
                            </div>
                          </TableCell>
                          <TableCell>
                            <div className="text-sm">{approval.requested_by_name}</div>
                          </TableCell>
                          <TableCell>
                            <div className="text-sm">{new Date(approval.request_date).toLocaleDateString()}</div>
                          </TableCell>
                          <TableCell>
                            <div className="flex items-center space-x-2">
                              <Button
                                size="sm"
                                onClick={() => {
                                  setSelectedApproval(approval)
                                  setApprovalAction({ action: 'approve', notes: '', rejectionReason: '' })
                                  setShowApprovalDialog(true)
                                }}
                              >
                                <Check className="h-4 w-4 mr-1" />
                                Approve
                              </Button>
                              <Button
                                size="sm"
                                variant="destructive"
                                onClick={() => {
                                  setSelectedApproval(approval)
                                  setApprovalAction({ action: 'reject', notes: '', rejectionReason: '' })
                                  setShowApprovalDialog(true)
                                }}
                              >
                                <X className="h-4 w-4 mr-1" />
                                Reject
                              </Button>
                              <Button
                                size="sm"
                                variant="outline"
                                onClick={() => {
                                  setSelectedApproval(approval)
                                  setShowApprovalDialog(true)
                                }}
                              >
                                <Eye className="h-4 w-4" />
                              </Button>
                            </div>
                          </TableCell>
                        </TableRow>
                      ))}
                    </TableBody>
                  </Table>
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="employees" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Employee Deduction Summary</CardTitle>
              <CardDescription>View and manage deductions for all employees</CardDescription>
            </CardHeader>
            <CardContent>
              {/* Search */}
              <div className="flex items-center space-x-4 mb-6">
                <div className="relative flex-1">
                  <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
                  <Input
                    placeholder="Search employees..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="pl-8"
                  />
                </div>
              </div>

              <div className="rounded-md border">
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Employee</TableHead>
                      <TableHead>Department</TableHead>
                      <TableHead>Base Salary</TableHead>
                      <TableHead>Total Deductions</TableHead>
                      <TableHead>Pending</TableHead>
                      <TableHead>Approved</TableHead>
                      <TableHead>Actions</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {filteredEmployees.map((employee) => (
                      <TableRow key={employee.user_id}>
                        <TableCell>
                          <div className="space-y-1">
                            <div className="font-medium">{employee.full_name}</div>
                            <div className="text-sm text-muted-foreground">{employee.email}</div>
                          </div>
                        </TableCell>
                        <TableCell>
                          <Badge variant="outline">{employee.department || 'Not assigned'}</Badge>
                        </TableCell>
                        <TableCell>
                          {employee.base_salary ? `NPR ${employee.base_salary.toLocaleString()}` : 'Not set'}
                        </TableCell>
                        <TableCell>
                          <div className="font-medium">NPR {employee.total_deductions.toLocaleString()}</div>
                        </TableCell>
                        <TableCell>
                          <Badge variant="secondary">NPR {employee.pending_deductions.toLocaleString()}</Badge>
                        </TableCell>
                        <TableCell>
                          <Badge>NPR {employee.approved_deductions.toLocaleString()}</Badge>
                        </TableCell>
                        <TableCell>
                          <div className="flex items-center space-x-2">
                            <Button
                              size="sm"
                              variant="outline"
                              onClick={() => {
                                setSelectedEmployee(employee.user_id)
                                setShowRequestDialog(true)
                              }}
                            >
                              <Plus className="h-4 w-4 mr-1" />
                              Request
                            </Button>
                          </div>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="types" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Deduction Types</CardTitle>
              <CardDescription>Manage available deduction types for the organization</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="rounded-md border">
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Name</TableHead>
                      <TableHead>Code</TableHead>
                      <TableHead>Category</TableHead>
                      <TableHead>Calculation</TableHead>
                      <TableHead>Tax Impact</TableHead>
                      <TableHead>Status</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {deductionTypes.map((type) => (
                      <TableRow key={type.id}>
                        <TableCell>
                          <div className="space-y-1">
                            <div className="font-medium">{type.name}</div>
                            <div className="text-sm text-muted-foreground">{type.description}</div>
                          </div>
                        </TableCell>
                        <TableCell>
                          <Badge variant="outline">{type.code}</Badge>
                        </TableCell>
                        <TableCell>
                          <Badge variant="secondary">{type.category.replace('_', ' ')}</Badge>
                        </TableCell>
                        <TableCell>
                          <div className="space-y-1">
                            <div className="text-sm">{type.calculation_type}</div>
                            {type.calculation_type === 'fixed' && type.fixed_amount && (
                              <div className="text-xs text-muted-foreground">NPR {type.fixed_amount}</div>
                            )}
                            {type.calculation_type === 'percentage' && type.percentage && (
                              <div className="text-xs text-muted-foreground">{type.percentage}% of {type.percentage_base}</div>
                            )}
                          </div>
                        </TableCell>
                        <TableCell>
                          <Badge variant={type.is_taxable ? 'default' : 'secondary'}>
                            {type.is_taxable ? 'Affects Tax' : 'No Tax Impact'}
                          </Badge>
                        </TableCell>
                        <TableCell>
                          <Badge variant={type.is_active ? 'default' : 'destructive'}>
                            {type.is_active ? 'Active' : 'Inactive'}
                          </Badge>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>

      {/* Approval Dialog */}
      <Dialog open={showApprovalDialog} onOpenChange={setShowApprovalDialog}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>
              {approvalAction.action === 'approve' ? 'Approve' : 'Reject'} Deduction Request
            </DialogTitle>
            <DialogDescription>
              {selectedApproval && (
                <div className="space-y-2 mt-4">
                  <p><strong>Employee:</strong> {selectedApproval.user_id}</p>
                  <p><strong>Deduction:</strong> {selectedApproval.component_name}</p>
                  <p><strong>Amount:</strong> NPR {selectedApproval.deduction_amount.toLocaleString()}</p>
                  <p><strong>Reason:</strong> {selectedApproval.deduction_reason}</p>
                </div>
              )}
            </DialogDescription>
          </DialogHeader>
          <div className="space-y-4">
            {approvalAction.action === 'approve' ? (
              <div className="space-y-2">
                <Label htmlFor="approval_notes">Approval Notes (Optional)</Label>
                <Textarea 
                  id="approval_notes" 
                  value={approvalAction.notes}
                  onChange={(e) => setApprovalAction({...approvalAction, notes: e.target.value})}
                  placeholder="Any additional notes for approval"
                />
              </div>
            ) : (
              <div className="space-y-2">
                <Label htmlFor="rejection_reason">Rejection Reason *</Label>
                <Textarea 
                  id="rejection_reason" 
                  value={approvalAction.rejectionReason}
                  onChange={(e) => setApprovalAction({...approvalAction, rejectionReason: e.target.value})}
                  placeholder="Mandatory reason for rejection (minimum 10 characters)"
                  className={approvalAction.rejectionReason.length < 10 ? 'border-red-500' : ''}
                />
                <p className="text-sm text-muted-foreground">
                  {approvalAction.rejectionReason.length}/10 characters minimum
                </p>
              </div>
            )}
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setShowApprovalDialog(false)}>
              Cancel
            </Button>
            <Button 
              onClick={handleApprovalAction}
              variant={approvalAction.action === 'approve' ? 'default' : 'destructive'}
            >
              {approvalAction.action === 'approve' ? 'Approve' : 'Reject'}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  )
}
