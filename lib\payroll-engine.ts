// Payroll Calculation Engine Foundation
// Phase 1: Payroll Calculation Engine Foundation

import { nepalConfig, NepalLaborLawConfig } from './nepal-config';
import { NepaliCalendar, BSDate } from './nepali-calendar';

// Core interfaces for payroll calculations
export interface PayrollInput {
  userId: string;
  periodStart: string;
  periodEnd: string;
  attendanceData: AttendanceData[];
  employeeData: EmployeeData;
  payStructure: PayStructure;
  deductions: Deduction[];
  allowances: Allowance[];
}

export interface AttendanceData {
  date: string;
  totalHours: number;
  regularHours: number;
  overtimeHours: number;
  status: 'present' | 'absent' | 'late' | 'half_day' | 'on_leave';
  lateMinutes: number;
  earlyDeparture: boolean;
  sessionCount: number;
}

export interface EmployeeData {
  id: string;
  fullName: string;
  email: string;
  department?: string;
  position?: string;
  hireDate: string;
  isActive: boolean;
}

export interface PayStructure {
  type: 'hourly' | 'daily' | 'monthly' | 'project_based';
  baseSalary: number;
  hourlyRate: number;
  dailyRate: number;
  overtimeMultiplier: number;
  providentFundPercentage: number;
  effectiveFrom: string;
  effectiveTo?: string;
}

export interface Deduction {
  id: string;
  name: string;
  type: 'fixed' | 'percentage';
  amount: number;
  isPercentage: boolean;
  percentageBase: 'base_salary' | 'gross_pay' | 'net_pay';
  isTaxable: boolean;
  isStatutory: boolean;
}

export interface Allowance {
  id: string;
  name: string;
  type: 'fixed' | 'percentage';
  amount: number;
  isPercentage: boolean;
  percentageBase: 'base_salary' | 'gross_pay';
  isTaxable: boolean;
}

export interface PayrollCalculationResult {
  userId: string;
  periodStart: string;
  periodEnd: string;
  fiscalYear: string;
  bsPeriodStart: string;
  bsPeriodEnd: string;
  
  // Attendance summary
  workingDays: number;
  totalHoursWorked: number;
  regularHours: number;
  overtimeHours: number;
  absentDays: number;
  lateDays: number;
  attendanceRate: number;
  
  // Salary calculations
  baseSalary: number;
  regularPay: number;
  overtimePay: number;
  allowancesTotal: number;
  grossPay: number;
  
  // Deductions
  providentFund: number;
  incomeTax: number;
  socialSecurityFund: number;
  otherDeductions: number;
  totalDeductions: number;
  
  // Final amounts
  netPay: number;
  
  // Bonuses and penalties
  attendanceBonus: number;
  festivalBonus: number;
  latePenalty: number;
  
  // Detailed breakdowns
  allowanceBreakdown: AllowanceBreakdown[];
  deductionBreakdown: DeductionBreakdown[];
  attendanceBreakdown: AttendanceData[];
  
  // Calculation metadata
  calculatedAt: string;
  calculatedBy: string;
  calculationMethod: string;
}

export interface AllowanceBreakdown {
  name: string;
  amount: number;
  isTaxable: boolean;
}

export interface DeductionBreakdown {
  name: string;
  amount: number;
  isStatutory: boolean;
}

// Base payroll calculator class
export abstract class BasePayrollCalculator {
  protected nepalConfig = nepalConfig;
  protected laborLaw: NepalLaborLawConfig;

  constructor() {
    this.laborLaw = this.nepalConfig.getLaborLawConfig();
  }

  // Abstract methods to be implemented by specific calculators
  abstract calculateBasePay(input: PayrollInput): number;
  abstract calculateOvertimePay(input: PayrollInput): number;
  abstract calculateAllowances(input: PayrollInput): { total: number; breakdown: AllowanceBreakdown[] };
  abstract calculateDeductions(input: PayrollInput, grossPay: number): { total: number; breakdown: DeductionBreakdown[] };

  // Common calculation methods
  public async calculatePayroll(input: PayrollInput): Promise<PayrollCalculationResult> {
    // Validate input
    this.validateInput(input);

    // Calculate attendance summary
    const attendanceSummary = this.calculateAttendanceSummary(input.attendanceData);

    // Calculate base components
    const basePay = this.calculateBasePay(input);
    const overtimePay = this.calculateOvertimePay(input);
    const allowances = this.calculateAllowances(input);
    
    // Calculate gross pay
    const grossPay = basePay + overtimePay + allowances.total;
    
    // Calculate deductions
    const deductions = this.calculateDeductions(input, grossPay);
    
    // Calculate bonuses and penalties
    const bonusesAndPenalties = this.calculateBonusesAndPenalties(input, attendanceSummary);
    
    // Calculate final net pay
    const netPay = grossPay - deductions.total + bonusesAndPenalties.attendanceBonus + bonusesAndPenalties.festivalBonus - bonusesAndPenalties.latePenalty;

    // Get calendar information
    const bsDateRange = NepaliCalendar.getBSDateRange(input.periodStart, input.periodEnd);
    const fiscalYear = NepaliCalendar.getBSFiscalYear(bsDateRange.start);

    return {
      userId: input.userId,
      periodStart: input.periodStart,
      periodEnd: input.periodEnd,
      fiscalYear,
      bsPeriodStart: NepaliCalendar.formatBSDate(bsDateRange.start),
      bsPeriodEnd: NepaliCalendar.formatBSDate(bsDateRange.end),
      
      // Attendance summary
      workingDays: attendanceSummary.workingDays,
      totalHoursWorked: attendanceSummary.totalHours,
      regularHours: attendanceSummary.regularHours,
      overtimeHours: attendanceSummary.overtimeHours,
      absentDays: attendanceSummary.absentDays,
      lateDays: attendanceSummary.lateDays,
      attendanceRate: attendanceSummary.attendanceRate,
      
      // Salary calculations
      baseSalary: input.payStructure.baseSalary,
      regularPay: basePay,
      overtimePay,
      allowancesTotal: allowances.total,
      grossPay,
      
      // Deductions
      providentFund: this.calculateProvidentFund(grossPay, input.payStructure.providentFundPercentage),
      incomeTax: this.calculateIncomeTax(grossPay),
      socialSecurityFund: this.calculateSocialSecurityFund(grossPay),
      otherDeductions: deductions.total,
      totalDeductions: deductions.total,
      
      // Final amounts
      netPay,
      
      // Bonuses and penalties
      attendanceBonus: bonusesAndPenalties.attendanceBonus,
      festivalBonus: bonusesAndPenalties.festivalBonus,
      latePenalty: bonusesAndPenalties.latePenalty,
      
      // Detailed breakdowns
      allowanceBreakdown: allowances.breakdown,
      deductionBreakdown: deductions.breakdown,
      attendanceBreakdown: input.attendanceData,
      
      // Calculation metadata
      calculatedAt: new Date().toISOString(),
      calculatedBy: 'system',
      calculationMethod: this.constructor.name
    };
  }

  // Validate input data
  protected validateInput(input: PayrollInput): void {
    if (!input.userId) throw new Error('User ID is required');
    if (!input.periodStart || !input.periodEnd) throw new Error('Period dates are required');
    if (!input.employeeData) throw new Error('Employee data is required');
    if (!input.payStructure) throw new Error('Pay structure is required');
    if (new Date(input.periodStart) >= new Date(input.periodEnd)) {
      throw new Error('Period start date must be before end date');
    }
  }

  // Calculate attendance summary
  protected calculateAttendanceSummary(attendanceData: AttendanceData[]): {
    workingDays: number;
    totalHours: number;
    regularHours: number;
    overtimeHours: number;
    absentDays: number;
    lateDays: number;
    attendanceRate: number;
  } {
    const totalDays = attendanceData.length;
    const presentDays = attendanceData.filter(d => d.status === 'present' || d.status === 'late').length;
    const absentDays = attendanceData.filter(d => d.status === 'absent').length;
    const lateDays = attendanceData.filter(d => d.status === 'late').length;
    
    const totalHours = attendanceData.reduce((sum, d) => sum + d.totalHours, 0);
    const regularHours = attendanceData.reduce((sum, d) => sum + d.regularHours, 0);
    const overtimeHours = attendanceData.reduce((sum, d) => sum + d.overtimeHours, 0);
    
    const attendanceRate = totalDays > 0 ? (presentDays / totalDays) * 100 : 0;

    return {
      workingDays: presentDays,
      totalHours,
      regularHours,
      overtimeHours,
      absentDays,
      lateDays,
      attendanceRate
    };
  }

  // Calculate provident fund
  protected calculateProvidentFund(grossPay: number, percentage: number): number {
    return Math.round((grossPay * percentage / 100) * 100) / 100;
  }

  // Calculate income tax (simplified Nepal tax calculation)
  protected calculateIncomeTax(grossPay: number): number {
    // Simplified tax calculation - in production, use actual Nepal tax slabs
    const annualIncome = grossPay * 12;
    const taxThreshold = 500000; // NPR 5 lakhs
    
    if (annualIncome <= taxThreshold) return 0;
    
    const taxableIncome = annualIncome - taxThreshold;
    const annualTax = taxableIncome * 0.1; // 10% simplified rate
    
    return Math.round((annualTax / 12) * 100) / 100;
  }

  // Calculate social security fund
  protected calculateSocialSecurityFund(grossPay: number): number {
    // Nepal Social Security Fund contribution
    const ssfRate = 0.11; // 11% total (employee + employer)
    const employeeContribution = grossPay * (ssfRate / 2); // 5.5% employee share
    
    return Math.round(employeeContribution * 100) / 100;
  }

  // Calculate bonuses and penalties
  protected calculateBonusesAndPenalties(input: PayrollInput, attendanceSummary: any): {
    attendanceBonus: number;
    festivalBonus: number;
    latePenalty: number;
  } {
    let attendanceBonus = 0;
    let festivalBonus = 0;
    let latePenalty = 0;

    // Attendance bonus (if attendance rate > 95%)
    if (attendanceSummary.attendanceRate >= 95) {
      attendanceBonus = 2000; // NPR 2000 bonus
    }

    // Festival bonus (during Dashain/Tihar months)
    const periodStart = new Date(input.periodStart);
    const bsDate = NepaliCalendar.adToBS(periodStart);
    if (bsDate.month === 7 || bsDate.month === 8) { // Kartik/Mangsir (Dashain/Tihar)
      festivalBonus = input.payStructure.baseSalary; // 100% of basic salary
    }

    // Late penalty
    const totalLateMinutes = input.attendanceData.reduce((sum, d) => sum + d.lateMinutes, 0);
    latePenalty = totalLateMinutes * 10; // NPR 10 per minute

    return { attendanceBonus, festivalBonus, latePenalty };
  }
}

// Monthly salary calculator
export class MonthlySalaryCalculator extends BasePayrollCalculator {
  calculateBasePay(input: PayrollInput): number {
    return input.payStructure.baseSalary;
  }

  calculateOvertimePay(input: PayrollInput): number {
    const totalOvertimeHours = input.attendanceData.reduce((sum, d) => sum + d.overtimeHours, 0);
    return totalOvertimeHours * input.payStructure.hourlyRate * input.payStructure.overtimeMultiplier;
  }

  calculateAllowances(input: PayrollInput): { total: number; breakdown: AllowanceBreakdown[] } {
    const breakdown: AllowanceBreakdown[] = [];
    let total = 0;

    input.allowances.forEach(allowance => {
      let amount = 0;
      if (allowance.isPercentage) {
        const base = allowance.percentageBase === 'base_salary' ? input.payStructure.baseSalary : 0;
        amount = (base * allowance.amount) / 100;
      } else {
        amount = allowance.amount;
      }

      breakdown.push({
        name: allowance.name,
        amount,
        isTaxable: allowance.isTaxable
      });

      total += amount;
    });

    return { total, breakdown };
  }

  calculateDeductions(input: PayrollInput, grossPay: number): { total: number; breakdown: DeductionBreakdown[] } {
    const breakdown: DeductionBreakdown[] = [];
    let total = 0;

    input.deductions.forEach(deduction => {
      let amount = 0;
      if (deduction.isPercentage) {
        let base = 0;
        switch (deduction.percentageBase) {
          case 'base_salary':
            base = input.payStructure.baseSalary;
            break;
          case 'gross_pay':
            base = grossPay;
            break;
          default:
            base = grossPay;
        }
        amount = (base * deduction.amount) / 100;
      } else {
        amount = deduction.amount;
      }

      breakdown.push({
        name: deduction.name,
        amount,
        isStatutory: deduction.isStatutory
      });

      total += amount;
    });

    return { total, breakdown };
  }
}

// Hourly rate calculator
export class HourlyRateCalculator extends BasePayrollCalculator {
  calculateBasePay(input: PayrollInput): number {
    const totalRegularHours = input.attendanceData.reduce((sum, d) => sum + d.regularHours, 0);
    return totalRegularHours * input.payStructure.hourlyRate;
  }

  calculateOvertimePay(input: PayrollInput): number {
    const totalOvertimeHours = input.attendanceData.reduce((sum, d) => sum + d.overtimeHours, 0);
    return totalOvertimeHours * input.payStructure.hourlyRate * input.payStructure.overtimeMultiplier;
  }

  calculateAllowances(input: PayrollInput): { total: number; breakdown: AllowanceBreakdown[] } {
    const breakdown: AllowanceBreakdown[] = [];
    let total = 0;

    input.allowances.forEach(allowance => {
      let amount = 0;
      if (allowance.isPercentage) {
        // For hourly workers, base calculations on total hours worked
        const totalHours = input.attendanceData.reduce((sum, d) => sum + d.totalHours, 0);
        const estimatedMonthlySalary = totalHours * input.payStructure.hourlyRate;
        const base = allowance.percentageBase === 'base_salary' ? estimatedMonthlySalary : 0;
        amount = (base * allowance.amount) / 100;
      } else {
        amount = allowance.amount;
      }

      breakdown.push({
        name: allowance.name,
        amount,
        isTaxable: allowance.isTaxable
      });

      total += amount;
    });

    return { total, breakdown };
  }

  calculateDeductions(input: PayrollInput, grossPay: number): { total: number; breakdown: DeductionBreakdown[] } {
    const breakdown: DeductionBreakdown[] = [];
    let total = 0;

    input.deductions.forEach(deduction => {
      let amount = 0;
      if (deduction.isPercentage) {
        let base = 0;
        switch (deduction.percentageBase) {
          case 'base_salary':
            // For hourly workers, calculate based on hours worked
            const totalHours = input.attendanceData.reduce((sum, d) => sum + d.totalHours, 0);
            base = totalHours * input.payStructure.hourlyRate;
            break;
          case 'gross_pay':
            base = grossPay;
            break;
          default:
            base = grossPay;
        }
        amount = (base * deduction.amount) / 100;
      } else {
        amount = deduction.amount;
      }

      breakdown.push({
        name: deduction.name,
        amount,
        isStatutory: deduction.isStatutory
      });

      total += amount;
    });

    return { total, breakdown };
  }
}

// Daily rate calculator
export class DailyRateCalculator extends BasePayrollCalculator {
  calculateBasePay(input: PayrollInput): number {
    const workingDays = input.attendanceData.filter(d =>
      d.status === 'present' || d.status === 'late' || d.status === 'half_day'
    ).length;

    // Calculate half days
    const halfDays = input.attendanceData.filter(d => d.status === 'half_day').length;
    const fullDays = workingDays - halfDays;

    return (fullDays * input.payStructure.dailyRate) + (halfDays * input.payStructure.dailyRate * 0.5);
  }

  calculateOvertimePay(input: PayrollInput): number {
    const totalOvertimeHours = input.attendanceData.reduce((sum, d) => sum + d.overtimeHours, 0);
    // Calculate hourly rate from daily rate (assuming 8 hours per day)
    const hourlyRate = input.payStructure.dailyRate / 8;
    return totalOvertimeHours * hourlyRate * input.payStructure.overtimeMultiplier;
  }

  calculateAllowances(input: PayrollInput): { total: number; breakdown: AllowanceBreakdown[] } {
    const breakdown: AllowanceBreakdown[] = [];
    let total = 0;

    input.allowances.forEach(allowance => {
      let amount = 0;
      if (allowance.isPercentage) {
        const workingDays = input.attendanceData.filter(d =>
          d.status === 'present' || d.status === 'late' || d.status === 'half_day'
        ).length;
        const estimatedMonthlySalary = workingDays * input.payStructure.dailyRate;
        const base = allowance.percentageBase === 'base_salary' ? estimatedMonthlySalary : 0;
        amount = (base * allowance.amount) / 100;
      } else {
        amount = allowance.amount;
      }

      breakdown.push({
        name: allowance.name,
        amount,
        isTaxable: allowance.isTaxable
      });

      total += amount;
    });

    return { total, breakdown };
  }

  calculateDeductions(input: PayrollInput, grossPay: number): { total: number; breakdown: DeductionBreakdown[] } {
    const breakdown: DeductionBreakdown[] = [];
    let total = 0;

    input.deductions.forEach(deduction => {
      let amount = 0;
      if (deduction.isPercentage) {
        let base = 0;
        switch (deduction.percentageBase) {
          case 'base_salary':
            const workingDays = input.attendanceData.filter(d =>
              d.status === 'present' || d.status === 'late' || d.status === 'half_day'
            ).length;
            base = workingDays * input.payStructure.dailyRate;
            break;
          case 'gross_pay':
            base = grossPay;
            break;
          default:
            base = grossPay;
        }
        amount = (base * deduction.amount) / 100;
      } else {
        amount = deduction.amount;
      }

      breakdown.push({
        name: deduction.name,
        amount,
        isStatutory: deduction.isStatutory
      });

      total += amount;
    });

    return { total, breakdown };
  }
}

// Project-based calculator
export class ProjectBasedCalculator extends BasePayrollCalculator {
  calculateBasePay(input: PayrollInput): number {
    // For project-based pay, base salary is typically a fixed amount per project/milestone
    // This could be enhanced to support milestone-based payments
    return input.payStructure.baseSalary;
  }

  calculateOvertimePay(input: PayrollInput): number {
    // Project-based workers might not be eligible for overtime
    // or might have different overtime calculation rules
    const settings = this.nepalConfig.getConfig();
    const totalOvertimeHours = input.attendanceData.reduce((sum, d) => sum + d.overtimeHours, 0);

    // Use hourly rate if available, otherwise calculate from base salary
    const hourlyRate = input.payStructure.hourlyRate || (input.payStructure.baseSalary / 208); // 26 days * 8 hours
    return totalOvertimeHours * hourlyRate * input.payStructure.overtimeMultiplier;
  }

  calculateAllowances(input: PayrollInput): { total: number; breakdown: AllowanceBreakdown[] } {
    const breakdown: AllowanceBreakdown[] = [];
    let total = 0;

    input.allowances.forEach(allowance => {
      let amount = 0;
      if (allowance.isPercentage) {
        const base = allowance.percentageBase === 'base_salary' ? input.payStructure.baseSalary : 0;
        amount = (base * allowance.amount) / 100;
      } else {
        amount = allowance.amount;
      }

      breakdown.push({
        name: allowance.name,
        amount,
        isTaxable: allowance.isTaxable
      });

      total += amount;
    });

    return { total, breakdown };
  }

  calculateDeductions(input: PayrollInput, grossPay: number): { total: number; breakdown: DeductionBreakdown[] } {
    const breakdown: DeductionBreakdown[] = [];
    let total = 0;

    input.deductions.forEach(deduction => {
      let amount = 0;
      if (deduction.isPercentage) {
        let base = 0;
        switch (deduction.percentageBase) {
          case 'base_salary':
            base = input.payStructure.baseSalary;
            break;
          case 'gross_pay':
            base = grossPay;
            break;
          default:
            base = grossPay;
        }
        amount = (base * deduction.amount) / 100;
      } else {
        amount = deduction.amount;
      }

      breakdown.push({
        name: deduction.name,
        amount,
        isStatutory: deduction.isStatutory
      });

      total += amount;
    });

    return { total, breakdown };
  }
}

// Factory for creating appropriate calculator
export class PayrollCalculatorFactory {
  static createCalculator(payStructureType: string): BasePayrollCalculator {
    switch (payStructureType) {
      case 'monthly':
        return new MonthlySalaryCalculator();
      case 'hourly':
        return new HourlyRateCalculator();
      case 'daily':
        return new DailyRateCalculator();
      case 'project_based':
        return new ProjectBasedCalculator();
      default:
        return new MonthlySalaryCalculator();
    }
  }

  /**
   * Get available pay structure types
   */
  static getAvailablePayStructures(): { value: string; label: string; description: string }[] {
    return [
      {
        value: 'monthly',
        label: 'Monthly Salary',
        description: 'Fixed monthly salary with overtime calculations'
      },
      {
        value: 'hourly',
        label: 'Hourly Rate',
        description: 'Pay based on hours worked with hourly rate'
      },
      {
        value: 'daily',
        label: 'Daily Rate',
        description: 'Pay based on days worked with daily rate'
      },
      {
        value: 'project_based',
        label: 'Project Based',
        description: 'Fixed payment per project or milestone'
      }
    ];
  }

  /**
   * Validate pay structure configuration
   */
  static validatePayStructure(payStructure: PayStructure): { isValid: boolean; errors: string[] } {
    const errors: string[] = [];

    if (!payStructure.type) {
      errors.push('Pay structure type is required');
    }

    switch (payStructure.type) {
      case 'monthly':
        if (!payStructure.baseSalary || payStructure.baseSalary <= 0) {
          errors.push('Base salary must be greater than 0 for monthly pay structure');
        }
        break;
      case 'hourly':
        if (!payStructure.hourlyRate || payStructure.hourlyRate <= 0) {
          errors.push('Hourly rate must be greater than 0 for hourly pay structure');
        }
        break;
      case 'daily':
        if (!payStructure.dailyRate || payStructure.dailyRate <= 0) {
          errors.push('Daily rate must be greater than 0 for daily pay structure');
        }
        break;
      case 'project_based':
        if (!payStructure.baseSalary || payStructure.baseSalary <= 0) {
          errors.push('Project amount must be greater than 0 for project-based pay structure');
        }
        break;
      default:
        errors.push('Invalid pay structure type');
    }

    if (payStructure.overtimeMultiplier && payStructure.overtimeMultiplier < 1) {
      errors.push('Overtime multiplier must be at least 1.0');
    }

    if (payStructure.providentFundPercentage && (payStructure.providentFundPercentage < 0 || payStructure.providentFundPercentage > 100)) {
      errors.push('Provident fund percentage must be between 0 and 100');
    }

    return {
      isValid: errors.length === 0,
      errors
    };
  }
}

// Export main calculator interface
export const PayrollEngine = {
  calculatePayroll: async (input: PayrollInput): Promise<PayrollCalculationResult> => {
    const calculator = PayrollCalculatorFactory.createCalculator(input.payStructure.type);
    return calculator.calculatePayroll(input);
  }
};
