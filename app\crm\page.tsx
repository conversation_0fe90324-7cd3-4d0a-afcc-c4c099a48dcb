"use client"

import { useState } from "react"
import { <PERSON><PERSON><PERSON><PERSON><PERSON> } from "@/components/app-header"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Tabs, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs"
import { Avatar, AvatarFallback } from "@/components/ui/avatar"
import { Badge } from "@/components/ui/badge"
import { Progress } from "@/components/ui/progress"
import {
  Search,
  Plus,
  Phone,
  Mail,
  MapPin,
  Calendar,
  Clock,
  ArrowRight,
  Filter,
  Star,
  MoreHorizontal,
  MessageSquare,
} from "lucide-react"

const clients = [
  {
    id: "1",
    name: "<PERSON>",
    email: "<EMAIL>",
    phone: "+****************",
    location: "New York, NY",
    status: "active",
    lastContact: "2 days ago",
    products: ["Savings Account", "Fixed Deposit"],
    value: 125000,
    nextAction: "Follow up on investment options",
    nextActionDate: "Tomorrow",
    risk: "low",
  },
  {
    id: "2",
    name: "<PERSON>",
    email: "<EMAIL>",
    phone: "+****************",
    location: "Chicago, IL",
    status: "active",
    lastContact: "1 week ago",
    products: ["Home Loan", "Insurance"],
    value: 450000,
    nextAction: "Send loan approval documents",
    nextActionDate: "Today",
    risk: "medium",
  },
  {
    id: "3",
    name: "Michael Brown",
    email: "<EMAIL>",
    phone: "+****************",
    location: "Los Angeles, CA",
    status: "inactive",
    lastContact: "1 month ago",
    products: ["Savings Account"],
    value: 35000,
    nextAction: "Reactivation call",
    nextActionDate: "Next week",
    risk: "high",
  },
  {
    id: "4",
    name: "Sarah Miller",
    email: "<EMAIL>",
    phone: "+****************",
    location: "Boston, MA",
    status: "active",
    lastContact: "Yesterday",
    products: ["Investment Plan", "Insurance", "Credit Card"],
    value: 780000,
    nextAction: "Quarterly portfolio review",
    nextActionDate: "Next Monday",
    risk: "low",
  },
]

export default function CRMPage() {
  const [searchTerm, setSearchTerm] = useState("")

  const filteredClients = clients.filter(
    (client) =>
      client.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      client.email.toLowerCase().includes(searchTerm.toLowerCase()),
  )

  return (
    <div className="min-h-screen bg-gray-50 flex flex-col">
      <AppHeader title="Client Relationship Management" />

      <div className="p-4">
        <div className="flex items-center justify-between mb-4">
          <h2 className="text-xl font-semibold text-gray-800">Clients</h2>
          <Button className="bg-exobank-green hover:bg-exobank-green/90 text-white">
            <Plus className="h-4 w-4 mr-1" /> Add Client
          </Button>
        </div>

        <div className="mb-4">
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
            <Input
              placeholder="Search clients..."
              className="pl-10"
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
            />
          </div>
        </div>

        <Tabs defaultValue="all" className="mb-4">
          <TabsList className="grid w-full grid-cols-4">
            <TabsTrigger value="all">All Clients</TabsTrigger>
            <TabsTrigger value="active">Active</TabsTrigger>
            <TabsTrigger value="inactive">Inactive</TabsTrigger>
            <TabsTrigger value="high-value">High Value</TabsTrigger>
          </TabsList>
        </Tabs>

        <div className="flex items-center justify-between mb-4">
          <Button variant="outline" size="sm" className="text-xs">
            <Filter className="h-3 w-3 mr-1" /> Filter
          </Button>
          <span className="text-sm text-gray-500">{filteredClients.length} clients</span>
        </div>

        <div className="space-y-4">
          {filteredClients.map((client) => (
            <Card key={client.id} className="overflow-hidden">
              <CardHeader className="p-4 pb-2">
                <div className="flex justify-between items-start">
                  <div className="flex items-center gap-3">
                    <Avatar className="h-12 w-12 border border-gray-200">
                      <AvatarFallback>{client.name.charAt(0)}</AvatarFallback>
                    </Avatar>
                    <div>
                      <div className="flex items-center gap-2">
                        <CardTitle className="text-base">{client.name}</CardTitle>
                        <Badge
                          className={client.status === "active" ? "bg-green-500 text-white" : "bg-gray-500 text-white"}
                        >
                          {client.status}
                        </Badge>
                      </div>
                      <CardDescription className="text-xs flex items-center gap-1">
                        <Star
                          className={`h-3 w-3 ${
                            client.risk === "low"
                              ? "text-green-500 fill-green-500"
                              : client.risk === "medium"
                                ? "text-amber-500 fill-amber-500"
                                : "text-red-500 fill-red-500"
                          }`}
                        />
                        <span>
                          {client.risk === "low" ? "Low Risk" : client.risk === "medium" ? "Medium Risk" : "High Risk"}
                        </span>
                      </CardDescription>
                    </div>
                  </div>
                  <Button variant="ghost" size="icon">
                    <MoreHorizontal className="h-4 w-4" />
                  </Button>
                </div>
              </CardHeader>
              <CardContent className="p-4 pt-2 pb-2">
                <div className="grid grid-cols-2 gap-3 text-sm">
                  <div className="flex items-center gap-2">
                    <Phone className="h-3 w-3 text-gray-400" />
                    <span className="text-gray-600">{client.phone}</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <Mail className="h-3 w-3 text-gray-400" />
                    <span className="text-gray-600">{client.email}</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <MapPin className="h-3 w-3 text-gray-400" />
                    <span className="text-gray-600">{client.location}</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <Clock className="h-3 w-3 text-gray-400" />
                    <span className="text-gray-600">Last contact: {client.lastContact}</span>
                  </div>
                </div>

                <div className="mt-3">
                  <div className="flex items-center justify-between mb-1">
                    <span className="text-xs font-medium">Products</span>
                    <span className="text-xs text-gray-500">{client.products.length} active</span>
                  </div>
                  <div className="flex flex-wrap gap-1">
                    {client.products.map((product, index) => (
                      <Badge key={index} variant="outline" className="text-xs bg-gray-50">
                        {product}
                      </Badge>
                    ))}
                  </div>
                </div>

                <div className="mt-3">
                  <div className="flex items-center justify-between mb-1">
                    <span className="text-xs font-medium">Client Value</span>
                    <span className="text-xs font-medium text-exobank-green">₹{client.value.toLocaleString()}</span>
                  </div>
                  <Progress value={(client.value / 1000000) * 100} className="h-1.5" />
                </div>
              </CardContent>
              <CardFooter className="p-4 pt-2 flex items-center justify-between bg-gray-50 border-t">
                <div className="flex items-center gap-1 text-xs">
                  <Calendar className="h-3 w-3 text-exobank-green" />
                  <span className="font-medium">Next: {client.nextAction}</span>
                  <span className="text-gray-500">({client.nextActionDate})</span>
                </div>
                <div className="flex gap-1">
                  <Button variant="outline" size="sm" className="text-xs h-7">
                    <Phone className="h-3 w-3 mr-1" /> Call
                  </Button>
                  <Button variant="outline" size="sm" className="text-xs h-7">
                    <MessageSquare className="h-3 w-3 mr-1" /> Message
                  </Button>
                  <Button size="sm" className="text-xs h-7 bg-exobank-green hover:bg-exobank-green/90 text-white">
                    View <ArrowRight className="h-3 w-3 ml-1" />
                  </Button>
                </div>
              </CardFooter>
            </Card>
          ))}
        </div>
      </div>
    </div>
  )
}
