#!/usr/bin/env node

require('dotenv').config({ path: '.env.local' });
const { neon } = require('@neondatabase/serverless');

async function testConsolidation() {
  console.log('🎯 TESTING DATABASE CONSOLIDATION');
  console.log('=================================');
  
  if (!process.env.DATABASE_URL) {
    console.error('❌ DATABASE_URL environment variable is not set');
    process.exit(1);
  }
  
  try {
    const sql = neon(process.env.DATABASE_URL);
    
    // Test connection
    console.log('🔄 Testing database connection...');
    await sql`SELECT 1`;
    console.log('✅ Database connection successful');
    
    // Count total users
    console.log('\n👥 USER STATISTICS:');
    const totalUsers = await sql`SELECT COUNT(*) as count FROM users`;
    const activeUsers = await sql`SELECT COUNT(*) as count FROM users WHERE is_active = true`;
    const usersByRole = await sql`
      SELECT role, COUNT(*) as count 
      FROM users 
      WHERE is_active = true 
      GROUP BY role 
      ORDER BY count DESC
    `;
    
    console.log(`   Total users: ${totalUsers[0].count}`);
    console.log(`   Active users: ${activeUsers[0].count}`);
    console.log('   Users by role:');
    usersByRole.forEach(role => {
      console.log(`     ${role.role}: ${role.count}`);
    });
    
    // Count attendance records
    console.log('\n📅 ATTENDANCE STATISTICS:');
    const totalAttendance = await sql`SELECT COUNT(*) as count FROM attendance`;
    const recentAttendance = await sql`
      SELECT COUNT(*) as count 
      FROM attendance 
      WHERE check_in_time >= CURRENT_DATE - INTERVAL '7 days'
    `;
    const uniqueUsersWithAttendance = await sql`
      SELECT COUNT(DISTINCT user_id) as count 
      FROM attendance
    `;
    
    console.log(`   Total attendance records: ${totalAttendance[0].count}`);
    console.log(`   Recent attendance (7 days): ${recentAttendance[0].count}`);
    console.log(`   Users with attendance records: ${uniqueUsersWithAttendance[0].count}`);
    
    // Test API-like queries (what the application would use)
    console.log('\n🔍 APPLICATION-STYLE QUERIES:');
    
    // Query 1: Get all employees for admin dashboard
    const employeesForDashboard = await sql`
      SELECT id, full_name, email, role, department, is_active
      FROM users 
      WHERE is_active = true
      ORDER BY full_name
    `;
    console.log(`   Employees for dashboard: ${employeesForDashboard.length}`);
    
    // Query 2: Get today's attendance
    const todayAttendance = await sql`
      SELECT COUNT(*) as count
      FROM attendance 
      WHERE DATE(check_in_time) = CURRENT_DATE
    `;
    console.log(`   Today's attendance entries: ${todayAttendance[0].count}`);
    
    // Query 3: Get users for attendance system
    const usersForAttendance = await sql`
      SELECT id, full_name, email, role
      FROM users 
      WHERE is_active = true AND role IN ('staff', 'manager', 'hr_manager')
      ORDER BY full_name
    `;
    console.log(`   Users for attendance system: ${usersForAttendance.length}`);
    
    // Show sample users
    console.log('\n📋 SAMPLE USERS:');
    const sampleUsers = await sql`
      SELECT full_name, email, role, department
      FROM users 
      WHERE is_active = true
      ORDER BY created_at
      LIMIT 10
    `;
    sampleUsers.forEach(user => {
      console.log(`   • ${user.full_name} (${user.email}) - ${user.role} - ${user.department}`);
    });
    
    // Verify no duplicate emails
    console.log('\n🔍 DATA INTEGRITY CHECK:');
    const duplicateEmails = await sql`
      SELECT email, COUNT(*) as count
      FROM users
      GROUP BY email
      HAVING COUNT(*) > 1
    `;
    
    if (duplicateEmails.length === 0) {
      console.log('   ✅ No duplicate emails found');
    } else {
      console.log('   ⚠️  Duplicate emails found:');
      duplicateEmails.forEach(dup => {
        console.log(`     ${dup.email}: ${dup.count} occurrences`);
      });
    }
    
    console.log('\n🎉 CONSOLIDATION TEST RESULTS:');
    console.log('==============================');
    console.log(`✅ Database connection: Working`);
    console.log(`✅ Total active users: ${activeUsers[0].count}`);
    console.log(`✅ Attendance records: ${totalAttendance[0].count}`);
    console.log(`✅ Data integrity: ${duplicateEmails.length === 0 ? 'Good' : 'Issues found'}`);
    
    console.log('\n📋 EXPECTED RESULTS:');
    console.log('- All parts of the application should now show consistent employee counts');
    console.log('- The attendance system should work with all users');
    console.log('- No more discrepancies between different database connections');
    
  } catch (error) {
    console.error('❌ Test failed:', error.message);
    process.exit(1);
  }
}

testConsolidation();
