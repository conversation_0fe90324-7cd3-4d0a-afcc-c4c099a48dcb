import { NextResponse } from "next/server"
import type { NextRequest } from "next/server"
import { AuthService } from "@/lib/auth-utils"

export async function middleware(request: NextRequest) {
  const { pathname } = request.nextUrl

  // Skip middleware for API routes, static files, and auth pages
  if (
    pathname.startsWith("/api/") ||
    pathname.startsWith("/_next/") ||
    pathname.startsWith("/images/") ||
    pathname.startsWith("/auth/") ||
    pathname === "/favicon.ico" ||
    pathname.endsWith(".png") ||
    pathname.endsWith(".jpg") ||
    pathname.endsWith(".jpeg") ||
    pathname.endsWith(".gif") ||
    pathname.endsWith(".svg")
  ) {
    return NextResponse.next()
  }

  // Get session token from cookies
  const sessionToken = request.cookies.get("session-token")?.value

  // Check if user is authenticated
  if (!sessionToken) {
    return NextResponse.redirect(new URL("/auth/login", request.url))
  }

  try {
    // Verify session
    const user = await AuthService.verifySession(sessionToken)

    if (!user) {
      const response = NextResponse.redirect(new URL("/auth/login", request.url))
      response.cookies.delete("session-token")
      return response
    }

    // Role-based route protection
    if (pathname.startsWith("/admin/")) {
      if (!["admin", "hr_manager"].includes(user.role)) {
        return NextResponse.redirect(new URL("/dashboard", request.url))
      }
    }

    return NextResponse.next()
  } catch (error) {
    console.error("Middleware error:", error)
    const response = NextResponse.redirect(new URL("/auth/login", request.url))
    response.cookies.delete("session-token")
    return response
  }
}

export const config = {
  matcher: [
    /*
     * Match all request paths except for the ones starting with:
     * - api (API routes)
     * - _next/static (static files)
     * - _next/image (image optimization files)
     * - favicon.ico (favicon file)
     * - public files
     */
    "/((?!api|_next/static|_next/image|favicon.ico|images).*)",
  ],
}
