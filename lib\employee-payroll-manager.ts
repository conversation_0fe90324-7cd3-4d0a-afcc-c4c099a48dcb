// Employee Payroll Profile Management System
// Comprehensive employee payroll data management

import { db } from './neon';

export interface EmployeePayrollProfile {
  id: string;
  email: string;
  full_name: string;
  role: string;
  department?: string;
  position?: string;
  phone?: string;
  hire_date?: string;
  salary?: number;
  
  // Enhanced payroll fields
  employee_type: 'full_time' | 'part_time' | 'contract' | 'intern' | 'consultant';
  employee_category: 'regular' | 'probation' | 'temporary' | 'seasonal' | 'project_based';
  tax_identification_number?: string;
  citizenship_number?: string;
  pan_number?: string;
  employment_status: 'active' | 'inactive' | 'terminated' | 'resigned' | 'retired';
  pay_grade?: string;
  joining_bonus?: number;
  
  // Bank account information
  bank_accounts: BankAccount[];
  
  // Allowances and deductions
  allowances: AllowanceAssignment[];
  deductions: DeductionApproval[];
  
  is_active: boolean;
  created_at: string;
  updated_at: string;
}

export interface BankAccount {
  id: string;
  bank_name: string;
  bank_branch?: string;
  account_number: string;
  account_holder_name: string;
  account_type: 'savings' | 'current' | 'salary';
  is_primary: boolean;
  is_active: boolean;
  created_at: string;
}

export interface AllowanceAssignment {
  id: string;
  allowance_type: 'travelling' | 'phone' | 'meal' | 'transport' | 'medical' | 'education' | 'custom';
  allowance_name: string;
  calculation_type: 'fixed' | 'percentage';
  amount: number;
  percentage?: number;
  percentage_base?: 'base_salary' | 'gross_pay';
  is_taxable: boolean;
  effective_from: string;
  effective_to?: string;
  is_active: boolean;
  notes?: string;
}

export interface DeductionApproval {
  id: string;
  component_name: string;
  deduction_amount: number;
  deduction_reason: string;
  status: 'pending' | 'approved' | 'rejected' | 'cancelled';
  request_date: string;
  approval_date?: string;
  effective_from: string;
  effective_to?: string;
  notes?: string;
}

export interface PayrollComponent {
  id: string;
  name: string;
  code: string;
  type: 'deduction' | 'allowance';
  category: 'statutory' | 'voluntary' | 'company_policy' | 'custom';
  calculation_type: 'fixed' | 'percentage' | 'formula';
  fixed_amount?: number;
  percentage?: number;
  percentage_base?: string;
  is_taxable: boolean;
  is_statutory: boolean;
  description?: string;
  is_active: boolean;
}

export class EmployeePayrollManager {
  
  // Get comprehensive employee payroll profile
  async getEmployeePayrollProfile(userId: string): Promise<EmployeePayrollProfile | null> {
    try {
      // Get employee basic info
      const employee = await db.sql`
        SELECT * FROM users WHERE id = ${userId}
      `;
      
      if (employee.length === 0) {
        return null;
      }
      
      const emp = employee[0];
      
      // Get bank accounts
      const bankAccounts = await db.sql`
        SELECT * FROM employee_bank_accounts 
        WHERE user_id = ${userId} AND is_active = TRUE
        ORDER BY is_primary DESC, created_at DESC
      `;
      
      // Get allowances
      const allowances = await db.sql`
        SELECT * FROM allowance_assignments 
        WHERE user_id = ${userId} AND is_active = TRUE
        ORDER BY created_at DESC
      `;
      
      // Get deductions
      const deductions = await db.sql`
        SELECT 
          da.*,
          pcm.name as component_name
        FROM deduction_approvals da
        JOIN payroll_components_master pcm ON da.component_id = pcm.id
        WHERE da.user_id = ${userId}
        ORDER BY da.created_at DESC
      `;
      
      return {
        id: emp.id,
        email: emp.email,
        full_name: emp.full_name,
        role: emp.role,
        department: emp.department,
        position: emp.position,
        phone: emp.phone,
        hire_date: emp.hire_date,
        salary: emp.salary,
        employee_type: emp.employee_type || 'full_time',
        employee_category: emp.employee_category || 'regular',
        tax_identification_number: emp.tax_identification_number,
        citizenship_number: emp.citizenship_number,
        pan_number: emp.pan_number,
        employment_status: emp.employment_status || 'active',
        pay_grade: emp.pay_grade,
        joining_bonus: emp.joining_bonus || 0,
        bank_accounts: bankAccounts,
        allowances: allowances,
        deductions: deductions,
        is_active: emp.is_active,
        created_at: emp.created_at,
        updated_at: emp.updated_at
      };
      
    } catch (error) {
      console.error('Error getting employee payroll profile:', error);
      throw new Error('Failed to get employee payroll profile');
    }
  }
  
  // Update employee payroll information
  async updateEmployeePayrollInfo(userId: string, updates: Partial<EmployeePayrollProfile>): Promise<boolean> {
    try {
      const updateFields = [];
      const values = [];
      let paramIndex = 1;
      
      // Build dynamic update query
      const allowedFields = [
        'employee_type', 'employee_category', 'tax_identification_number',
        'citizenship_number', 'pan_number', 'employment_status', 'pay_grade',
        'joining_bonus', 'salary', 'department', 'position', 'phone'
      ];
      
      for (const field of allowedFields) {
        if (updates[field] !== undefined) {
          updateFields.push(`${field} = $${paramIndex}`);
          values.push(updates[field]);
          paramIndex++;
        }
      }
      
      if (updateFields.length === 0) {
        return true; // No updates needed
      }
      
      updateFields.push(`updated_at = NOW()`);
      values.push(userId);
      
      const query = `
        UPDATE users 
        SET ${updateFields.join(', ')}
        WHERE id = $${paramIndex}
      `;
      
      await db.sql([query, ...values]);
      return true;
      
    } catch (error) {
      console.error('Error updating employee payroll info:', error);
      throw new Error('Failed to update employee payroll information');
    }
  }
  
  // Add bank account
  async addBankAccount(userId: string, bankAccount: Omit<BankAccount, 'id' | 'created_at'>): Promise<string> {
    try {
      // If this is set as primary, make others non-primary
      if (bankAccount.is_primary) {
        await db.sql`
          UPDATE employee_bank_accounts 
          SET is_primary = FALSE 
          WHERE user_id = ${userId}
        `;
      }
      
      const result = await db.sql`
        INSERT INTO employee_bank_accounts (
          user_id, bank_name, bank_branch, account_number, account_holder_name,
          account_type, is_primary, is_active
        ) VALUES (
          ${userId}, ${bankAccount.bank_name}, ${bankAccount.bank_branch || null},
          ${bankAccount.account_number}, ${bankAccount.account_holder_name},
          ${bankAccount.account_type}, ${bankAccount.is_primary}, ${bankAccount.is_active}
        ) RETURNING id
      `;
      
      return result[0].id;
      
    } catch (error) {
      console.error('Error adding bank account:', error);
      throw new Error('Failed to add bank account');
    }
  }
  
  // Update bank account
  async updateBankAccount(accountId: string, updates: Partial<BankAccount>): Promise<boolean> {
    try {
      const updateFields = [];
      const values = [];
      let paramIndex = 1;
      
      const allowedFields = [
        'bank_name', 'bank_branch', 'account_number', 'account_holder_name',
        'account_type', 'is_primary', 'is_active'
      ];
      
      for (const field of allowedFields) {
        if (updates[field] !== undefined) {
          updateFields.push(`${field} = $${paramIndex}`);
          values.push(updates[field]);
          paramIndex++;
        }
      }
      
      if (updateFields.length === 0) {
        return true;
      }
      
      updateFields.push(`updated_at = NOW()`);
      values.push(accountId);
      
      const query = `
        UPDATE employee_bank_accounts 
        SET ${updateFields.join(', ')}
        WHERE id = $${paramIndex}
      `;
      
      await db.sql([query, ...values]);
      return true;
      
    } catch (error) {
      console.error('Error updating bank account:', error);
      throw new Error('Failed to update bank account');
    }
  }
  
  // Get all payroll components (allowances and deductions)
  async getPayrollComponents(): Promise<PayrollComponent[]> {
    try {
      const components = await db.sql`
        SELECT * FROM payroll_components_master 
        WHERE is_active = TRUE
        ORDER BY type, name
      `;
      
      return components;
      
    } catch (error) {
      console.error('Error getting payroll components:', error);
      throw new Error('Failed to get payroll components');
    }
  }
  
  // Get all employees with basic payroll info
  async getAllEmployeesPayrollSummary(): Promise<Partial<EmployeePayrollProfile>[]> {
    try {
      const employees = await db.sql`
        SELECT 
          id, email, full_name, role, department, position, 
          employee_type, employee_category, employment_status,
          salary, pay_grade, is_active, hire_date
        FROM users 
        WHERE role != 'admin'
        ORDER BY full_name
      `;
      
      return employees;
      
    } catch (error) {
      console.error('Error getting employees payroll summary:', error);
      throw new Error('Failed to get employees payroll summary');
    }
  }
}

export const employeePayrollManager = new EmployeePayrollManager();
