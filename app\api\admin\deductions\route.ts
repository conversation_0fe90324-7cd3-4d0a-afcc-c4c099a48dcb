// Enhanced Deductions Management API Endpoints
// Admin endpoints for managing deductions with mandatory notes and approval workflow

import { NextRequest, NextResponse } from 'next/server';
import { deductionsManager } from '@/lib/deductions-manager';
import { AuthService } from '@/lib/auth-utils';

// GET - Get deduction types, pending approvals, employee deductions, or statistics
export async function GET(request: NextRequest) {
  try {
    const sessionToken = request.cookies.get("session-token")?.value;

    if (!sessionToken) {
      return NextResponse.json({
        success: false,
        error: 'Unauthorized'
      }, { status: 401 });
    }

    const user = await AuthService.verifySession(sessionToken);

    if (!user || (user.role !== 'admin' && user.role !== 'hr_manager')) {
      return NextResponse.json({
        success: false,
        error: 'Insufficient permissions'
      }, { status: 403 });
    }

    const { searchParams } = new URL(request.url);
    const action = searchParams.get('action');
    const userId = searchParams.get('userId');
    const recordId = searchParams.get('recordId');

    if (action === 'types') {
      // Get all deduction types
      const deductionTypes = await deductionsManager.getDeductionTypes();
      return NextResponse.json({
        success: true,
        data: deductionTypes
      });
    } else if (action === 'pending') {
      // Get pending deduction approvals
      const pendingApprovals = await deductionsManager.getPendingApprovals();
      return NextResponse.json({
        success: true,
        data: pendingApprovals
      });
    } else if (action === 'employee' && userId) {
      // Get specific employee deductions
      const deductions = await deductionsManager.getEmployeeDeductions(userId);
      return NextResponse.json({
        success: true,
        data: deductions
      });
    } else if (action === 'summary') {
      // Get all employees deduction summary
      const summary = await deductionsManager.getAllEmployeesDeductionSummary();
      return NextResponse.json({
        success: true,
        data: summary
      });
    } else if (action === 'statistics') {
      // Get deduction statistics
      const statistics = await deductionsManager.getDeductionStatistics();
      return NextResponse.json({
        success: true,
        data: statistics
      });
    } else if (action === 'audit') {
      // Get deduction audit trail
      const auditTrail = await deductionsManager.getDeductionAuditTrail(recordId);
      return NextResponse.json({
        success: true,
        data: auditTrail
      });
    } else {
      return NextResponse.json({
        success: false,
        error: 'Invalid action parameter'
      }, { status: 400 });
    }

  } catch (error) {
    console.error('Error in deductions GET:', error);
    return NextResponse.json({
      success: false,
      error: 'Internal server error'
    }, { status: 500 });
  }
}

// POST - Create new deduction type or request deduction
export async function POST(request: NextRequest) {
  try {
    const sessionToken = request.cookies.get("session-token")?.value;

    if (!sessionToken) {
      return NextResponse.json({
        success: false,
        error: 'Unauthorized'
      }, { status: 401 });
    }

    const user = await AuthService.verifySession(sessionToken);

    if (!user || (user.role !== 'admin' && user.role !== 'hr_manager')) {
      return NextResponse.json({
        success: false,
        error: 'Insufficient permissions'
      }, { status: 403 });
    }

    const body = await request.json();
    const { action } = body;

    if (action === 'create_type') {
      // Create new deduction type
      const { deductionType } = body;

      if (!deductionType || !deductionType.name || !deductionType.code) {
        return NextResponse.json({
          success: false,
          error: 'Deduction name and code are required'
        }, { status: 400 });
      }

      // Validate calculation_type
      if (!['fixed', 'percentage', 'formula'].includes(deductionType.calculation_type)) {
        return NextResponse.json({
          success: false,
          error: 'Invalid calculation type'
        }, { status: 400 });
      }

      // Validate category
      if (!['statutory', 'voluntary', 'company_policy', 'custom'].includes(deductionType.category)) {
        return NextResponse.json({
          success: false,
          error: 'Invalid category'
        }, { status: 400 });
      }

      const deductionId = await deductionsManager.createDeductionType({
        name: deductionType.name,
        code: deductionType.code.toUpperCase(),
        type: 'deduction',
        category: deductionType.category,
        calculation_type: deductionType.calculation_type,
        fixed_amount: deductionType.fixed_amount || null,
        percentage: deductionType.percentage || null,
        percentage_base: deductionType.percentage_base || null,
        is_taxable: deductionType.is_taxable !== false,
        is_statutory: deductionType.is_statutory === true,
        description: deductionType.description || null,
        is_active: true,
        effective_from: deductionType.effective_from || new Date().toISOString().split('T')[0],
        effective_to: deductionType.effective_to || null
      });

      return NextResponse.json({
        success: true,
        data: { deductionId },
        message: 'Deduction type created successfully'
      });

    } else if (action === 'request') {
      // Request deduction for employee
      const { deductionRequest } = body;

      if (!deductionRequest || !deductionRequest.user_id || !deductionRequest.component_id) {
        return NextResponse.json({
          success: false,
          error: 'User ID and component ID are required'
        }, { status: 400 });
      }

      // Validate mandatory reason field
      if (!deductionRequest.deduction_reason || deductionRequest.deduction_reason.trim().length < 10) {
        return NextResponse.json({
          success: false,
          error: 'Deduction reason is mandatory and must be at least 10 characters long'
        }, { status: 400 });
      }

      // Validate deduction amount
      if (!deductionRequest.deduction_amount || deductionRequest.deduction_amount <= 0) {
        return NextResponse.json({
          success: false,
          error: 'Deduction amount must be greater than 0'
        }, { status: 400 });
      }

      const requestId = await deductionsManager.requestDeduction(
        deductionRequest,
        user.id
      );

      return NextResponse.json({
        success: true,
        data: { requestId },
        message: 'Deduction request submitted successfully'
      });

    } else {
      return NextResponse.json({
        success: false,
        error: 'Invalid action'
      }, { status: 400 });
    }

  } catch (error) {
    console.error('Error in deductions POST:', error);
    return NextResponse.json({
      success: false,
      error: error.message || 'Internal server error'
    }, { status: 500 });
  }
}

// PUT - Approve, reject, or update deduction
export async function PUT(request: NextRequest) {
  try {
    const sessionToken = request.cookies.get("session-token")?.value;

    if (!sessionToken) {
      return NextResponse.json({
        success: false,
        error: 'Unauthorized'
      }, { status: 401 });
    }

    const user = await AuthService.verifySession(sessionToken);

    if (!user || (user.role !== 'admin' && user.role !== 'hr_manager')) {
      return NextResponse.json({
        success: false,
        error: 'Insufficient permissions'
      }, { status: 403 });
    }

    const body = await request.json();
    const { action } = body;

    if (action === 'approve') {
      // Approve deduction request
      const { approvalId, notes } = body;

      if (!approvalId) {
        return NextResponse.json({
          success: false,
          error: 'Approval ID is required'
        }, { status: 400 });
      }

      const success = await deductionsManager.approveDeduction(approvalId, user.id, notes);

      if (success) {
        return NextResponse.json({
          success: true,
          message: 'Deduction approved successfully'
        });
      } else {
        return NextResponse.json({
          success: false,
          error: 'Failed to approve deduction'
        }, { status: 500 });
      }

    } else if (action === 'reject') {
      // Reject deduction request
      const { approvalId, rejectionReason } = body;

      if (!approvalId || !rejectionReason) {
        return NextResponse.json({
          success: false,
          error: 'Approval ID and rejection reason are required'
        }, { status: 400 });
      }

      if (rejectionReason.trim().length < 10) {
        return NextResponse.json({
          success: false,
          error: 'Rejection reason must be at least 10 characters long'
        }, { status: 400 });
      }

      const success = await deductionsManager.rejectDeduction(approvalId, user.id, rejectionReason);

      if (success) {
        return NextResponse.json({
          success: true,
          message: 'Deduction rejected successfully'
        });
      } else {
        return NextResponse.json({
          success: false,
          error: 'Failed to reject deduction'
        }, { status: 500 });
      }

    } else if (action === 'update_type') {
      // Update deduction type
      const { deductionId, updates } = body;

      if (!deductionId || !updates) {
        return NextResponse.json({
          success: false,
          error: 'Deduction ID and updates are required'
        }, { status: 400 });
      }

      const success = await deductionsManager.updateDeductionType(deductionId, updates);

      if (success) {
        return NextResponse.json({
          success: true,
          message: 'Deduction type updated successfully'
        });
      } else {
        return NextResponse.json({
          success: false,
          error: 'Failed to update deduction type'
        }, { status: 500 });
      }

    } else {
      return NextResponse.json({
        success: false,
        error: 'Invalid action'
      }, { status: 400 });
    }

  } catch (error) {
    console.error('Error in deductions PUT:', error);
    return NextResponse.json({
      success: false,
      error: error.message || 'Internal server error'
    }, { status: 500 });
  }
}

// DELETE - Cancel deduction request
export async function DELETE(request: NextRequest) {
  try {
    const sessionToken = request.cookies.get("session-token")?.value;

    if (!sessionToken) {
      return NextResponse.json({
        success: false,
        error: 'Unauthorized'
      }, { status: 401 });
    }

    const user = await AuthService.verifySession(sessionToken);

    if (!user || (user.role !== 'admin' && user.role !== 'hr_manager')) {
      return NextResponse.json({
        success: false,
        error: 'Insufficient permissions'
      }, { status: 403 });
    }

    const { searchParams } = new URL(request.url);
    const approvalId = searchParams.get('approvalId');

    if (!approvalId) {
      return NextResponse.json({
        success: false,
        error: 'Approval ID is required'
      }, { status: 400 });
    }

    const success = await deductionsManager.cancelDeduction(approvalId);

    if (success) {
      return NextResponse.json({
        success: true,
        message: 'Deduction request cancelled successfully'
      });
    } else {
      return NextResponse.json({
        success: false,
        error: 'Failed to cancel deduction request'
      }, { status: 500 });
    }

  } catch (error) {
    console.error('Error in deductions DELETE:', error);
    return NextResponse.json({
      success: false,
      error: 'Internal server error'
    }, { status: 500 });
  }
}
