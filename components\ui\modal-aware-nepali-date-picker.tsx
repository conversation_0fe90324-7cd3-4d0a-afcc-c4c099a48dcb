// Modal-Aware Nepali Date Picker Component
// Enhanced version with smart positioning for use within modals

"use client"

import React, { useState, useRef, useEffect } from 'react'
import { Calendar, CalendarDays, X } from 'lucide-react'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { ModalAwarePopover, ModalAwarePopoverContent, ModalAwarePopoverTrigger } from '@/components/ui/modal-aware-popover'
import { Badge } from '@/components/ui/badge'
import { NepaliCalendarComponent } from './nepali-calendar'
import { NepaliCalendar, BSDate, getCurrentNepaliDate } from '@/lib/nepali-calendar'
import { nepalConfig } from '@/lib/nepal-config'
import { cn } from '@/lib/utils'

interface ModalAwareNepaliDatePickerProps {
  value?: Date
  onChange?: (date: Date | undefined) => void
  placeholder?: string
  label?: string
  showBothCalendars?: boolean
  allowManualInput?: boolean
  minDate?: Date
  maxDate?: Date
  highlightHolidays?: boolean
  highlightWorkingDays?: boolean
  required?: boolean
  disabled?: boolean
  className?: string
  error?: string
  /**
   * Enable smart positioning for modals (default: true)
   */
  enableSmartPositioning?: boolean
  /**
   * Preferred positioning side (default: "right" for horizontal-priority positioning)
   */
  preferredSide?: "top" | "bottom" | "left" | "right"
}

export function ModalAwareNepaliDatePicker({
  value,
  onChange,
  placeholder = "Select date",
  label,
  showBothCalendars = true,
  allowManualInput = true,
  minDate,
  maxDate,
  highlightHolidays = true,
  highlightWorkingDays = true,
  required = false,
  disabled = false,
  className,
  error,
  enableSmartPositioning = true,
  preferredSide = "right"
}: ModalAwareNepaliDatePickerProps) {
  const [isOpen, setIsOpen] = useState(false)
  const [inputValue, setInputValue] = useState('')
  const [inputMode, setInputMode] = useState<'ad' | 'bs'>('ad')
  const [validationError, setValidationError] = useState<string>('')
  const inputRef = useRef<HTMLInputElement>(null)

  // Update input value when value changes
  useEffect(() => {
    if (value) {
      updateInputValue(value)
    } else {
      setInputValue('')
    }
  }, [value, inputMode])

  const updateInputValue = (date: Date) => {
    try {
      if (inputMode === 'ad') {
        setInputValue(date.toISOString().split('T')[0])
      } else {
        const bsDate = NepaliCalendar.adToBS(date)
        setInputValue(NepaliCalendar.formatBSDate(bsDate))
      }
    } catch (error) {
      console.warn('Failed to update input value:', error)
      setInputValue('')
    }
  }

  const formatDisplayValue = () => {
    if (!value) return placeholder
    
    try {
      const adDate = value.toISOString().split('T')[0]
      const bsDate = NepaliCalendar.adToBS(value)
      const bsFormatted = NepaliCalendar.formatBSDate(bsDate)
      
      if (showBothCalendars) {
        return `${adDate} (${bsFormatted})`
      } else {
        return inputMode === 'ad' ? adDate : bsFormatted
      }
    } catch (error) {
      return value.toISOString().split('T')[0]
    }
  }

  const handleDateSelect = (date: Date) => {
    console.log("📅 Date selected from calendar:", date)
    onChange?.(date)
    setIsOpen(false)
    setValidationError('')
  }

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const newValue = e.target.value
    setInputValue(newValue)
    setValidationError('')
  }

  const handleInputBlur = () => {
    if (!inputValue.trim()) {
      onChange?.(undefined)
      return
    }

    try {
      let parsedDate: Date

      if (inputMode === 'ad') {
        // Parse AD date (YYYY-MM-DD format)
        const dateRegex = /^(\d{4})-(\d{2})-(\d{2})$/
        const match = inputValue.match(dateRegex)
        
        if (!match) {
          throw new Error('Invalid AD date format. Use YYYY-MM-DD')
        }

        parsedDate = new Date(inputValue + 'T00:00:00')
        
        if (isNaN(parsedDate.getTime())) {
          throw new Error('Invalid AD date')
        }
      } else {
        // Parse BS date (YYYY-MM-DD format)
        const dateRegex = /^(\d{4})-(\d{2})-(\d{2})$/
        const match = inputValue.match(dateRegex)
        
        if (!match) {
          throw new Error('Invalid BS date format. Use YYYY-MM-DD')
        }

        const [, year, month, day] = match
        const bsDate: BSDate = {
          year: parseInt(year),
          month: parseInt(month),
          day: parseInt(day)
        }

        parsedDate = NepaliCalendar.bsToAD(bsDate)
      }

      // Validate date range
      if (minDate && parsedDate < minDate) {
        throw new Error('Date is before minimum allowed date')
      }
      if (maxDate && parsedDate > maxDate) {
        throw new Error('Date is after maximum allowed date')
      }

      onChange?.(parsedDate)
      setValidationError('')
    } catch (error) {
      setValidationError(error instanceof Error ? error.message : 'Invalid date')
    }
  }

  const handleInputKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      handleInputBlur()
    } else if (e.key === 'Escape') {
      setInputValue('')
      onChange?.(undefined)
      inputRef.current?.blur()
    }
  }

  const toggleInputMode = () => {
    setInputMode(prev => prev === 'ad' ? 'bs' : 'ad')
    if (value) {
      updateInputValue(value)
    }
  }

  const clearDate = () => {
    onChange?.(undefined)
    setInputValue('')
    setValidationError('')
  }

  return (
    <div className={cn("space-y-2", className)}>
      {label && (
        <Label className="text-sm font-medium">
          {label}
          {required && <span className="text-red-500 ml-1">*</span>}
        </Label>
      )}
      
      <div className="relative">
        <ModalAwarePopover open={isOpen} onOpenChange={setIsOpen}>
          <ModalAwarePopoverTrigger asChild>
            <Button
              id="date-picker"
              variant="outline"
              className={cn(
                "w-full justify-start text-left font-normal",
                !value && "text-muted-foreground",
                error || validationError ? "border-red-500" : ""
              )}
              disabled={disabled}
            >
              <Calendar className="mr-2 h-4 w-4" />
              {formatDisplayValue()}
            </Button>
          </ModalAwarePopoverTrigger>
          
          <ModalAwarePopoverContent
            className="w-auto p-0 max-w-sm"
            align="start"
            enableSmartPositioning={enableSmartPositioning}
            preferredSide={preferredSide}
            modalPadding={16}
          >
            <div className="p-4 space-y-4">
              {/* Calendar type toggle and manual input */}
              {(allowManualInput || showBothCalendars) && (
                <div className="space-y-3">
                  <div className="flex items-center justify-between">
                    <div className="flex gap-2">
                      <Badge 
                        variant={inputMode === 'ad' ? 'default' : 'outline'}
                        className="cursor-pointer"
                        onClick={toggleInputMode}
                      >
                        AD
                      </Badge>
                      <Badge 
                        variant={inputMode === 'bs' ? 'default' : 'outline'}
                        className="cursor-pointer"
                        onClick={toggleInputMode}
                      >
                        BS
                      </Badge>
                    </div>
                    
                    {showBothCalendars && (
                      <div className="text-xs text-gray-500">
                        {(() => {
                          try {
                            // Get current BS year using the proper method
                            const currentBSDate = getCurrentNepaliDate()
                            return currentBSDate?.year ? `${currentBSDate.year} BS` : '2081 BS'
                          } catch (error) {
                            console.warn('Failed to get current Nepali date:', error)
                            // Fallback to config if available, otherwise use a default
                            try {
                              const configYear = nepalConfig?.getConfig()?.calendar?.currentBSYear
                              return configYear ? `${configYear} BS` : '2081 BS'
                            } catch (configError) {
                              console.warn('Failed to get config year:', configError)
                              return '2081 BS'
                            }
                          }
                        })()}
                      </div>
                    )}
                  </div>
                  
                  {allowManualInput && (
                    <div className="flex gap-2">
                      <Input
                        ref={inputRef}
                        value={inputValue}
                        onChange={handleInputChange}
                        onBlur={handleInputBlur}
                        onKeyDown={handleInputKeyDown}
                        placeholder={inputMode === 'ad' ? 'YYYY-MM-DD' : 'YYYY-MM-DD (BS)'}
                        className="flex-1"
                      />
                      {value && (
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={clearDate}
                        >
                          <X className="h-4 w-4" />
                        </Button>
                      )}
                    </div>
                  )}
                  
                  {validationError && (
                    <p className="text-sm text-red-500">{validationError}</p>
                  )}
                </div>
              )}
              
              {/* Calendar component */}
              <NepaliCalendarComponent
                selectedDate={value}
                onDateSelect={handleDateSelect}
                showDualCalendar={showBothCalendars}
                highlightHolidays={highlightHolidays}
                highlightWorkingDays={highlightWorkingDays}
                minDate={minDate}
                maxDate={maxDate}
              />
            </div>
          </ModalAwarePopoverContent>
        </ModalAwarePopover>
      </div>
      
      {(error || validationError) && (
        <p className="text-sm text-red-500">{error || validationError}</p>
      )}
    </div>
  )
}

export default ModalAwareNepaliDatePicker
