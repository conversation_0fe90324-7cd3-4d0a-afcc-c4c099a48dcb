// Test the employee payroll API directly
const { neon } = require('@neondatabase/serverless');
require('dotenv').config({ path: '.env.local' });

async function testPayrollAPIDirect() {
  try {
    console.log('Testing employee payroll API directly...\n');

    const sql = neon(process.env.DATABASE_URL);

    // Get a user with salary data
    const users = await sql`
      SELECT id, full_name, salary, department, position, employee_type, employee_category
      FROM users 
      WHERE salary IS NOT NULL 
      LIMIT 1
    `;

    if (users.length === 0) {
      console.log('❌ No users with salary data found');
      return;
    }

    const user = users[0];
    console.log(`Testing with user: ${user.full_name} (ID: ${user.id})`);
    console.log(`Salary: ${user.salary}, Department: ${user.department}, Position: ${user.position}`);

    // Test the employee payroll manager directly
    const { employeePayrollManager } = require('../lib/employee-payroll-manager.ts');
    
    console.log('\n1. Testing employeePayrollManager.getEmployeePayrollProfile...');
    try {
      const profile = await employeePayrollManager.getEmployeePayrollProfile(user.id);
      
      if (profile) {
        console.log('✅ Profile retrieved successfully');
        console.log('Profile data:');
        console.log('- ID:', profile.id);
        console.log('- Full Name:', profile.full_name);
        console.log('- Position:', profile.position);
        console.log('- Salary:', profile.salary);
        console.log('- Employee Type:', profile.employee_type);
        console.log('- Employee Category:', profile.employee_category);
        console.log('- Allowances count:', profile.allowances ? profile.allowances.length : 'undefined');
        console.log('- Deductions count:', profile.deductions ? profile.deductions.length : 'undefined');
        console.log('- Bank accounts count:', profile.bank_accounts ? profile.bank_accounts.length : 'undefined');

        if (profile.allowances && profile.allowances.length > 0) {
          console.log('\nAllowances:');
          profile.allowances.forEach((allowance, index) => {
            console.log(`  ${index + 1}. ${allowance.allowance_name}: ${allowance.allowance_amount} (${allowance.is_percentage ? 'percentage' : 'fixed'})`);
          });
        }

        if (profile.deductions && profile.deductions.length > 0) {
          console.log('\nDeductions:');
          profile.deductions.forEach((deduction, index) => {
            console.log(`  ${index + 1}. ${deduction.deduction_name}: ${deduction.deduction_amount} (${deduction.is_percentage ? 'percentage' : 'fixed'})`);
          });
        }

        // Test the calculation functions
        const { calculateTotalAllowances, calculateTotalDeductions } = require('../hooks/use-employee-payroll');
        
        console.log('\n2. Testing calculation functions...');
        const totalAllowances = calculateTotalAllowances(profile.allowances || [], profile.salary || 0);
        const totalDeductions = calculateTotalDeductions(profile.deductions || [], profile.salary || 0, (profile.salary || 0) + totalAllowances);
        
        console.log(`Total Allowances: ${totalAllowances}`);
        console.log(`Total Deductions: ${totalDeductions}`);
        console.log(`Net Salary: ${(profile.salary || 0) + totalAllowances - totalDeductions}`);

      } else {
        console.log('❌ Profile is null');
      }
    } catch (error) {
      console.log('❌ Error getting profile:', error.message);
      console.log('Stack trace:', error.stack);
    }

    // Test the raw database queries
    console.log('\n3. Testing raw database queries...');
    
    try {
      console.log('Testing allowances query...');
      const allowances = await sql`
        SELECT * FROM allowance_assignments 
        WHERE user_id = ${user.id} AND is_active = TRUE
        ORDER BY created_at DESC
      `;
      console.log(`✅ Found ${allowances.length} allowances`);
      
      console.log('Testing deductions query...');
      const deductions = await sql`
        SELECT 
          da.*,
          pcm.name as component_name
        FROM deduction_approvals da
        LEFT JOIN payroll_components_master pcm ON da.component_id = pcm.id
        WHERE da.user_id = ${user.id}
        ORDER BY da.created_at DESC
      `;
      console.log(`✅ Found ${deductions.length} deductions`);
      
      console.log('Testing bank accounts query...');
      const bankAccounts = await sql`
        SELECT * FROM employee_bank_accounts 
        WHERE user_id = ${user.id} AND is_active = TRUE
        ORDER BY is_primary DESC, created_at DESC
      `;
      console.log(`✅ Found ${bankAccounts.length} bank accounts`);
      
    } catch (error) {
      console.log('❌ Error with raw queries:', error.message);
    }

  } catch (error) {
    console.error('❌ Error:', error.message);
  }
}

testPayrollAPIDirect();
