#!/usr/bin/env node

const { neon } = require('@neondatabase/serverless');
const bcrypt = require('bcryptjs');
require('dotenv').config({ path: '.env.local' });

async function fixPasswords() {
  console.log('Fixing passwords...');
  
  try {
    const sql = neon(process.env.DATABASE_URL);
    
    // Generate correct hash for admin123
    const hash = await bcrypt.hash('admin123', 12);
    console.log('Generated hash for admin123');
    
    // Update admin user specifically
    await sql`
      UPDATE users 
      SET password_hash = ${hash}
      WHERE email = '<EMAIL>'
    `;
    
    console.log('Updated admin user password');
    
    // Test the login
    const user = await sql`
      SELECT email, password_hash FROM users WHERE email = '<EMAIL>'
    `;
    
    if (user.length > 0) {
      const isValid = await bcrypt.compare('admin123', user[0].password_hash);
      console.log('Login test result:', isValid ? 'SUCCESS' : 'FAILED');
    }
    
  } catch (error) {
    console.error('Error:', error.message);
  }
}

fixPasswords();
