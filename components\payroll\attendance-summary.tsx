"use client"

import React, { useState, useEffect } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert'
import { Skeleton } from '@/components/ui/skeleton'
import { Progress } from '@/components/ui/progress'
import { 
  Calendar, 
  Clock, 
  Users, 
  CheckCircle, 
  XCircle, 
  AlertTriangle, 
  Coffee,
  Plane,
  RefreshCw,
  Calculator,
  TrendingUp,
  Info
} from 'lucide-react'
import { toast } from 'sonner'

interface AttendanceSummaryData {
  employee_id: string;
  employee_name: string;
  employee_email: string;
  pay_period_start: string;
  pay_period_end: string;
  fiscal_year: string;
  bs_month: number;
  bs_month_name: string;
  total_working_days: number;
  days_present: number;
  days_absent: number;
  days_late: number;
  days_half_day: number;
  days_on_leave: number;
  total_hours_worked: number;
  regular_hours: number;
  overtime_hours: number;
  attendance_percentage: number;
  daily_breakdown: {
    [date: string]: {
      status: "present" | "absent" | "late" | "half_day" | "on_leave";
      check_in_time?: string;
      check_out_time?: string;
      total_hours?: number;
      sessions_count: number;
    }
  };
}

interface WorkingDaysConfig {
  id: string;
  fiscal_year: string;
  bs_month: number;
  bs_month_name: string;
  total_days_in_month: number;
  working_days: number;
  public_holidays: number;
  weekend_days: number;
  late_penalty_type: 'none' | 'half_day' | 'custom';
  late_penalty_amount: number;
}

interface AttendanceSummaryProps {
  employeeId: string | null;
  payPeriodStart: string | null;
  payPeriodEnd: string | null;
  fiscalYear: string | null;
  bsMonth: number | null;
  onAttendanceDataChange?: (data: AttendanceSummaryData | null) => void;
  className?: string;
}

export function AttendanceSummary({
  employeeId,
  payPeriodStart,
  payPeriodEnd,
  fiscalYear,
  bsMonth,
  onAttendanceDataChange,
  className
}: AttendanceSummaryProps) {
  const [attendanceData, setAttendanceData] = useState<AttendanceSummaryData | null>(null)
  const [workingDaysConfig, setWorkingDaysConfig] = useState<WorkingDaysConfig | null>(null)
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)

  // Fetch attendance data when parameters change
  useEffect(() => {
    if (employeeId && payPeriodStart && payPeriodEnd) {
      fetchAttendanceData()
    } else {
      setAttendanceData(null)
      setWorkingDaysConfig(null)
      setError(null)
      onAttendanceDataChange?.(null)
    }
  }, [employeeId, payPeriodStart, payPeriodEnd, fiscalYear, bsMonth])

  const fetchAttendanceData = async () => {
    try {
      setLoading(true)
      setError(null)

      const params = new URLSearchParams({
        employee_id: employeeId!,
        start_date: payPeriodStart!,
        end_date: payPeriodEnd!,
      })

      if (fiscalYear) params.append('fiscal_year', fiscalYear)
      if (bsMonth) params.append('bs_month', bsMonth.toString())

      const response = await fetch(`/api/admin/payroll/attendance-summary?${params}`)
      const data = await response.json()

      if (data.success) {
        setAttendanceData(data.data)
        setWorkingDaysConfig(data.working_days_config)
        onAttendanceDataChange?.(data.data)
      } else {
        setError(data.error || 'Failed to fetch attendance data')
        toast.error('Failed to fetch attendance data')
      }
    } catch (error) {
      console.error('Error fetching attendance data:', error)
      setError('Error loading attendance data')
      toast.error('Error loading attendance data')
    } finally {
      setLoading(false)
    }
  }

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'present':
        return <CheckCircle className="h-4 w-4 text-green-600" />
      case 'absent':
        return <XCircle className="h-4 w-4 text-red-600" />
      case 'late':
        return <AlertTriangle className="h-4 w-4 text-yellow-600" />
      case 'half_day':
        return <Coffee className="h-4 w-4 text-blue-600" />
      case 'on_leave':
        return <Plane className="h-4 w-4 text-purple-600" />
      default:
        return <XCircle className="h-4 w-4 text-gray-400" />
    }
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'present':
        return 'bg-green-100 text-green-800 border-green-200'
      case 'absent':
        return 'bg-red-100 text-red-800 border-red-200'
      case 'late':
        return 'bg-yellow-100 text-yellow-800 border-yellow-200'
      case 'half_day':
        return 'bg-blue-100 text-blue-800 border-blue-200'
      case 'on_leave':
        return 'bg-purple-100 text-purple-800 border-purple-200'
      default:
        return 'bg-gray-100 text-gray-800 border-gray-200'
    }
  }

  const formatDate = (dateStr: string) => {
    return new Date(dateStr).toLocaleDateString('en-US', {
      month: 'short',
      day: 'numeric'
    })
  }

  const formatTime = (timeStr: string | undefined) => {
    if (!timeStr) return '--'
    return new Date(timeStr).toLocaleTimeString('en-US', {
      hour: '2-digit',
      minute: '2-digit',
      hour12: true
    })
  }

  if (!employeeId || !payPeriodStart || !payPeriodEnd) {
    return (
      <Card className={className}>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Calendar className="h-5 w-5" />
            Attendance Overview
          </CardTitle>
          <CardDescription>
            Select an employee and pay period to view attendance summary
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="text-center py-8 text-muted-foreground">
            <Users className="h-12 w-12 mx-auto mb-4 opacity-50" />
            <p>No employee or pay period selected</p>
            <p className="text-sm">Choose an employee and pay period to see attendance data</p>
          </div>
        </CardContent>
      </Card>
    )
  }

  if (loading) {
    return (
      <Card className={className}>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Calendar className="h-5 w-5" />
            Attendance Overview
          </CardTitle>
          <CardDescription>Loading attendance data...</CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            {[...Array(4)].map((_, i) => (
              <div key={i} className="space-y-2">
                <Skeleton className="h-4 w-20" />
                <Skeleton className="h-8 w-16" />
              </div>
            ))}
          </div>
          <Skeleton className="h-4 w-full" />
          <Skeleton className="h-20 w-full" />
        </CardContent>
      </Card>
    )
  }

  if (error) {
    return (
      <Card className={className}>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Calendar className="h-5 w-5" />
            Attendance Overview
          </CardTitle>
        </CardHeader>
        <CardContent>
          <Alert variant="destructive">
            <AlertTriangle className="h-4 w-4" />
            <AlertTitle>Error Loading Attendance Data</AlertTitle>
            <AlertDescription className="mt-2">
              {error}
              <Button 
                variant="outline" 
                size="sm" 
                onClick={fetchAttendanceData}
                className="mt-2"
              >
                <RefreshCw className="h-4 w-4 mr-2" />
                Retry
              </Button>
            </AlertDescription>
          </Alert>
        </CardContent>
      </Card>
    )
  }

  if (!attendanceData) {
    return (
      <Card className={className}>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Calendar className="h-5 w-5" />
            Attendance Overview
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-center py-8 text-muted-foreground">
            <AlertTriangle className="h-12 w-12 mx-auto mb-4 opacity-50" />
            <p>No attendance data found</p>
            <p className="text-sm">No attendance records for the selected period</p>
          </div>
        </CardContent>
      </Card>
    )
  }

  return (
    <Card className={className}>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Calendar className="h-5 w-5" />
          Attendance Overview
        </CardTitle>
        <CardDescription>
          {attendanceData.employee_name} • {formatDate(attendanceData.pay_period_start)} - {formatDate(attendanceData.pay_period_end)}
          {attendanceData.bs_month_name && (
            <span className="ml-2">• {attendanceData.bs_month_name} {attendanceData.fiscal_year}</span>
          )}
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-6">
        {/* Working Days Info */}
        {workingDaysConfig && (
          <Alert>
            <Info className="h-4 w-4" />
            <AlertTitle>Working Days Configuration</AlertTitle>
            <AlertDescription>
              {workingDaysConfig.working_days} working days configured for {workingDaysConfig.bs_month_name} {workingDaysConfig.fiscal_year}
              {workingDaysConfig.late_penalty_type !== 'none' && (
                <span className="ml-2">• Late penalty: {workingDaysConfig.late_penalty_type === 'half_day' ? 'Half day deduction' : `NPR ${workingDaysConfig.late_penalty_amount}`}</span>
              )}
            </AlertDescription>
          </Alert>
        )}

        {/* Attendance Statistics */}
        <div className="grid grid-cols-2 md:grid-cols-5 gap-4">
          <div className="text-center">
            <div className="flex items-center justify-center mb-2">
              <CheckCircle className="h-5 w-5 text-green-600" />
            </div>
            <div className="text-2xl font-bold text-green-600">{attendanceData.days_present}</div>
            <div className="text-sm text-muted-foreground">Present</div>
          </div>

          <div className="text-center">
            <div className="flex items-center justify-center mb-2">
              <XCircle className="h-5 w-5 text-red-600" />
            </div>
            <div className="text-2xl font-bold text-red-600">{attendanceData.days_absent}</div>
            <div className="text-sm text-muted-foreground">Absent</div>
          </div>

          <div className="text-center">
            <div className="flex items-center justify-center mb-2">
              <AlertTriangle className="h-5 w-5 text-yellow-600" />
            </div>
            <div className="text-2xl font-bold text-yellow-600">{attendanceData.days_late}</div>
            <div className="text-sm text-muted-foreground">Late</div>
          </div>

          <div className="text-center">
            <div className="flex items-center justify-center mb-2">
              <Coffee className="h-5 w-5 text-blue-600" />
            </div>
            <div className="text-2xl font-bold text-blue-600">{attendanceData.days_half_day}</div>
            <div className="text-sm text-muted-foreground">Half Day</div>
          </div>

          <div className="text-center">
            <div className="flex items-center justify-center mb-2">
              <Plane className="h-5 w-5 text-purple-600" />
            </div>
            <div className="text-2xl font-bold text-purple-600">{attendanceData.days_on_leave}</div>
            <div className="text-sm text-muted-foreground">On Leave</div>
          </div>
        </div>

        {/* Attendance Percentage */}
        <div className="space-y-2">
          <div className="flex items-center justify-between">
            <span className="text-sm font-medium">Attendance Percentage</span>
            <span className="text-sm font-bold">{attendanceData.attendance_percentage}%</span>
          </div>
          <Progress 
            value={attendanceData.attendance_percentage} 
            className="h-2"
          />
          <div className="text-xs text-muted-foreground">
            Based on {attendanceData.total_working_days} working days
          </div>
        </div>

        {/* Hours Summary */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div className="text-center p-3 bg-muted/50 rounded-lg">
            <Clock className="h-5 w-5 mx-auto mb-2 text-muted-foreground" />
            <div className="text-lg font-semibold">{attendanceData.total_hours_worked}h</div>
            <div className="text-sm text-muted-foreground">Total Hours</div>
          </div>

          <div className="text-center p-3 bg-muted/50 rounded-lg">
            <TrendingUp className="h-5 w-5 mx-auto mb-2 text-muted-foreground" />
            <div className="text-lg font-semibold">{attendanceData.regular_hours}h</div>
            <div className="text-sm text-muted-foreground">Regular Hours</div>
          </div>

          <div className="text-center p-3 bg-muted/50 rounded-lg">
            <Calculator className="h-5 w-5 mx-auto mb-2 text-muted-foreground" />
            <div className="text-lg font-semibold">{attendanceData.overtime_hours}h</div>
            <div className="text-sm text-muted-foreground">Overtime Hours</div>
          </div>
        </div>

        {/* Refresh Button */}
        <div className="flex justify-end">
          <Button variant="outline" size="sm" onClick={fetchAttendanceData}>
            <RefreshCw className="h-4 w-4 mr-2" />
            Refresh Data
          </Button>
        </div>
      </CardContent>
    </Card>
  )
}
