#!/usr/bin/env node

/**
 * Integration test script to verify all API endpoints are working properly
 * This script tests the actual HTTP endpoints to ensure the application works end-to-end
 */

const fetch = globalThis.fetch || require('node-fetch');

const BASE_URL = 'http://localhost:3000';

async function testApiIntegration() {
  console.log('🧪 Testing API Integration...\n');
  console.log('=' .repeat(60));
  
  let passedTests = 0;
  let totalTests = 0;
  
  function runTest(testName, testFunction) {
    totalTests++;
    console.log(`\n${totalTests}. ${testName}`);
    return testFunction()
      .then(result => {
        if (result) {
          console.log('   ✅ PASSED');
          passedTests++;
        } else {
          console.log('   ❌ FAILED');
        }
        return result;
      })
      .catch(error => {
        console.log(`   ❌ ERROR: ${error.message}`);
        return false;
      });
  }
  
  // Test 1: Check if dev server is running
  await runTest('Development Server Availability', async () => {
    try {
      const response = await fetch(`${BASE_URL}`, { 
        method: 'GET',
        timeout: 5000 
      });
      
      console.log(`   📊 Response status: ${response.status}`);
      return response.ok;
    } catch (error) {
      if (error.code === 'ECONNREFUSED') {
        console.log('   ❌ Development server is not running on port 3000');
        console.log('   💡 Please run: npm run dev');
        return false;
      }
      throw error;
    }
  });
  
  // Test 2: Test Tasks API
  await runTest('Tasks API Endpoint', async () => {
    try {
      const response = await fetch(`${BASE_URL}/api/tasks`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        },
        timeout: 10000
      });
      
      const data = await response.json();
      
      if (response.status === 401) {
        console.log('   ℹ️  Authentication required (expected for protected endpoint)');
        return true; // This is expected behavior
      }
      
      if (response.status === 500) {
        console.log('   ❌ Server error:', data.error || 'Unknown error');
        return false;
      }
      
      console.log(`   📊 Response status: ${response.status}`);
      
      if (response.ok && data.success) {
        console.log(`   📋 Found ${data.data?.tasks?.length || 0} tasks`);
        
        // Check if tasks have enhanced data
        const firstTask = data.data?.tasks?.[0];
        if (firstTask) {
          console.log(`   🔍 First task: "${firstTask.title}"`);
          console.log(`   📊 Assignments: ${firstTask.assignments?.length || 0}`);
          console.log(`   📊 Sub-tasks: ${firstTask.subtask_count || 0}`);
          console.log(`   📊 Attachments: ${firstTask.attachment_count || 0}`);
        }
      }
      
      return response.ok || response.status === 401; // 401 is acceptable
    } catch (error) {
      console.log('   ❌ Network error:', error.message);
      return false;
    }
  });
  
  // Test 3: Test specific task endpoint
  await runTest('Individual Task API Endpoint', async () => {
    try {
      // First get a task ID from the tasks list
      const tasksResponse = await fetch(`${BASE_URL}/api/tasks`, {
        method: 'GET',
        headers: { 'Content-Type': 'application/json' }
      });
      
      if (tasksResponse.status === 401) {
        console.log('   ℹ️  Authentication required (skipping individual task test)');
        return true;
      }
      
      const tasksData = await tasksResponse.json();
      const firstTask = tasksData.data?.tasks?.[0];
      
      if (!firstTask) {
        console.log('   ℹ️  No tasks available for testing');
        return true;
      }
      
      // Test individual task endpoint
      const response = await fetch(`${BASE_URL}/api/tasks/${firstTask.id}`, {
        method: 'GET',
        headers: { 'Content-Type': 'application/json' }
      });
      
      const data = await response.json();
      
      if (response.status === 401) {
        console.log('   ℹ️  Authentication required (expected)');
        return true;
      }
      
      console.log(`   📊 Response status: ${response.status}`);
      
      if (response.ok && data.success) {
        console.log(`   📋 Task: "${data.data.title}"`);
        console.log(`   📊 Status: ${data.data.status}`);
        console.log(`   📊 Priority: ${data.data.priority}`);
      }
      
      return response.ok || response.status === 401;
    } catch (error) {
      console.log('   ❌ Network error:', error.message);
      return false;
    }
  });
  
  // Test 4: Test task assignments endpoint
  await runTest('Task Assignments API Endpoint', async () => {
    try {
      // Use a known task ID for testing
      const testTaskId = '34fb6aaa-1bf0-477b-a89a-a8098e8839b2'; // From our test data
      
      const response = await fetch(`${BASE_URL}/api/tasks/${testTaskId}/assignments`, {
        method: 'GET',
        headers: { 'Content-Type': 'application/json' }
      });
      
      const data = await response.json();
      
      if (response.status === 401) {
        console.log('   ℹ️  Authentication required (expected)');
        return true;
      }
      
      console.log(`   📊 Response status: ${response.status}`);
      
      if (response.ok && data.success) {
        console.log(`   📋 Found ${data.data?.length || 0} assignments`);
      }
      
      return response.ok || response.status === 401;
    } catch (error) {
      console.log('   ❌ Network error:', error.message);
      return false;
    }
  });
  
  // Test 5: Test sub-tasks endpoint
  await runTest('Sub-tasks API Endpoint', async () => {
    try {
      const testTaskId = '34fb6aaa-1bf0-477b-a89a-a8098e8839b2'; // From our test data
      
      const response = await fetch(`${BASE_URL}/api/tasks/${testTaskId}/subtasks`, {
        method: 'GET',
        headers: { 'Content-Type': 'application/json' }
      });
      
      const data = await response.json();
      
      if (response.status === 401) {
        console.log('   ℹ️  Authentication required (expected)');
        return true;
      }
      
      console.log(`   📊 Response status: ${response.status}`);
      
      if (response.ok && data.success) {
        console.log(`   📋 Found ${data.data?.length || 0} sub-tasks`);
      }
      
      return response.ok || response.status === 401;
    } catch (error) {
      console.log('   ❌ Network error:', error.message);
      return false;
    }
  });
  
  // Test 6: Test attachments endpoint
  await runTest('Attachments API Endpoint', async () => {
    try {
      const testTaskId = '34fb6aaa-1bf0-477b-a89a-a8098e8839b2'; // From our test data
      
      const response = await fetch(`${BASE_URL}/api/tasks/${testTaskId}/attachments`, {
        method: 'GET',
        headers: { 'Content-Type': 'application/json' }
      });
      
      const data = await response.json();
      
      if (response.status === 401) {
        console.log('   ℹ️  Authentication required (expected)');
        return true;
      }
      
      console.log(`   📊 Response status: ${response.status}`);
      
      if (response.ok && data.success) {
        console.log(`   📋 Found ${data.data?.length || 0} attachments`);
      }
      
      return response.ok || response.status === 401;
    } catch (error) {
      console.log('   ❌ Network error:', error.message);
      return false;
    }
  });
  
  // Test 7: Test admin users endpoint (for user assignment dropdown)
  await runTest('Admin Users API Endpoint', async () => {
    try {
      const response = await fetch(`${BASE_URL}/api/admin/users`, {
        method: 'GET',
        headers: { 'Content-Type': 'application/json' }
      });
      
      const data = await response.json();
      
      if (response.status === 401 || response.status === 403) {
        console.log('   ℹ️  Authentication/Authorization required (expected)');
        return true;
      }
      
      console.log(`   📊 Response status: ${response.status}`);
      
      if (response.ok && data.success) {
        console.log(`   📋 Found ${data.data?.length || 0} users`);
      }
      
      return response.ok || response.status === 401 || response.status === 403;
    } catch (error) {
      console.log('   ❌ Network error:', error.message);
      return false;
    }
  });
  
  // Summary
  console.log('\n' + '=' .repeat(60));
  console.log('🎉 API Integration Test Summary');
  console.log('=' .repeat(60));
  console.log(`📊 Total Tests: ${totalTests}`);
  console.log(`✅ Passed: ${passedTests}`);
  console.log(`❌ Failed: ${totalTests - passedTests}`);
  console.log(`📈 Success Rate: ${Math.round((passedTests / totalTests) * 100)}%`);
  
  if (passedTests === totalTests) {
    console.log('\n🎉 All tests passed! The enhanced kanban board API is working correctly.');
  } else {
    console.log('\n⚠️  Some tests failed. Please check the issues above.');
  }
  
  console.log('\n📋 Next Steps:');
  console.log('   1. Open http://localhost:3000 in your browser');
  console.log('   2. Test the enhanced functionality manually');
  console.log('   3. Use the testing checklist in docs/testing-checklist.md');
  console.log('   4. Verify all features work as expected');
}

testApiIntegration();
