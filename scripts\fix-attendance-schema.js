#!/usr/bin/env node

/**
 * Migration script to fix attendance table schema
 * Adds missing columns: is_active, daily_sequence, entry_type
 * Fixes the "column 'is_active' does not exist" error
 */

require('dotenv').config({ path: '.env.local' });
const { neon } = require('@neondatabase/serverless');

async function fixAttendanceSchema() {
  try {
    const DATABASE_URL = process.env.DATABASE_URL;
    if (!DATABASE_URL) {
      throw new Error('DATABASE_URL not found in environment variables');
    }

    const sql = neon(DATABASE_URL);
    
    console.log('🔧 Fixing Attendance Table Schema');
    console.log('=====================================\n');
    
    // Step 1: Check current table structure
    console.log('1. Checking current table structure...');
    const currentColumns = await sql`
      SELECT 
          column_name, 
          data_type, 
          is_nullable,
          column_default
      FROM information_schema.columns 
      WHERE table_name = 'attendance' 
      ORDER BY ordinal_position
    `;
    
    console.log('Current columns:');
    currentColumns.forEach(col => {
      console.log(`  - ${col.column_name}: ${col.data_type} ${col.is_nullable === 'YES' ? '(nullable)' : '(not null)'}`);
    });
    
    // Step 2: Check which columns are missing
    const existingColumnNames = currentColumns.map(col => col.column_name);
    const requiredColumns = ['is_active', 'daily_sequence', 'entry_type'];
    const missingColumns = requiredColumns.filter(col => !existingColumnNames.includes(col));
    
    console.log(`\n2. Missing columns: ${missingColumns.length > 0 ? missingColumns.join(', ') : 'None'}`);
    
    if (missingColumns.length === 0) {
      console.log('✅ All required columns already exist!');
      return;
    }
    
    // Step 3: Add missing columns
    console.log('\n3. Adding missing columns...');
    
    if (missingColumns.includes('is_active')) {
      console.log('  Adding is_active column...');
      await sql`ALTER TABLE attendance ADD COLUMN is_active BOOLEAN DEFAULT FALSE`;
      console.log('  ✅ Added is_active column');
    }
    
    if (missingColumns.includes('daily_sequence')) {
      console.log('  Adding daily_sequence column...');
      await sql`ALTER TABLE attendance ADD COLUMN daily_sequence INTEGER DEFAULT 1`;
      console.log('  ✅ Added daily_sequence column');
    }
    
    if (missingColumns.includes('entry_type')) {
      console.log('  Adding entry_type column...');
      await sql`ALTER TABLE attendance ADD COLUMN entry_type VARCHAR(20) DEFAULT 'regular'`;
      console.log('  ✅ Added entry_type column');
    }

    // Check if hours_worked column exists (might be named total_hours instead)
    const hoursColumn = await sql`
      SELECT column_name
      FROM information_schema.columns
      WHERE table_name = 'attendance'
      AND column_name IN ('hours_worked', 'total_hours')
    `;

    const hasHoursWorked = hoursColumn.some(col => col.column_name === 'hours_worked');
    const hasTotalHours = hoursColumn.some(col => col.column_name === 'total_hours');

    if (!hasHoursWorked && hasTotalHours) {
      console.log('  Adding hours_worked column (total_hours exists but hours_worked is missing)...');
      await sql`ALTER TABLE attendance ADD COLUMN hours_worked DECIMAL(4,2)`;
      console.log('  ✅ Added hours_worked column');
    } else if (!hasHoursWorked && !hasTotalHours) {
      console.log('  Adding hours_worked column...');
      await sql`ALTER TABLE attendance ADD COLUMN hours_worked DECIMAL(4,2)`;
      console.log('  ✅ Added hours_worked column');
    }
    
    // Step 4: Update existing records
    console.log('\n4. Updating existing records with default values...');
    const updateResult = await sql`
      UPDATE attendance 
      SET 
          is_active = CASE 
              WHEN check_in_time IS NOT NULL AND check_out_time IS NULL THEN TRUE 
              ELSE FALSE 
          END,
          daily_sequence = COALESCE(daily_sequence, 1),
          entry_type = COALESCE(entry_type, 'regular')
      WHERE is_active IS NULL OR daily_sequence IS NULL OR entry_type IS NULL
    `;
    console.log(`  ✅ Updated ${updateResult.length || 0} existing records`);
    
    // Step 5: Handle unique constraint
    console.log('\n5. Checking unique constraints...');
    const constraints = await sql`
      SELECT constraint_name, constraint_type
      FROM information_schema.table_constraints 
      WHERE table_name = 'attendance' AND constraint_type = 'UNIQUE'
    `;
    
    // Find and drop the old unique constraint on (user_id, date)
    const oldConstraint = constraints.find(c => c.constraint_name.includes('user_id') || c.constraint_name.includes('date'));
    if (oldConstraint) {
      console.log(`  Dropping old unique constraint: ${oldConstraint.constraint_name}`);
      // Use dynamic SQL for constraint name
      await sql.query(`ALTER TABLE attendance DROP CONSTRAINT ${oldConstraint.constraint_name}`);
      console.log('  ✅ Dropped old unique constraint');
    }
    
    // Add new unique index for multiple sessions
    console.log('  Adding new unique index for multiple sessions...');
    try {
      await sql`
        CREATE UNIQUE INDEX IF NOT EXISTS idx_attendance_active_session 
        ON attendance (user_id, date, daily_sequence)
      `;
      console.log('  ✅ Added unique index for multiple sessions');
    } catch (error) {
      console.log('  ⚠️  Unique index may already exist:', error.message);
    }
    
    // Step 6: Add check constraint for entry_type
    console.log('\n6. Adding check constraint for entry_type...');
    try {
      await sql`
        ALTER TABLE attendance ADD CONSTRAINT attendance_entry_type_check 
        CHECK (entry_type IN ('regular', 'overtime', 'break', 'meeting', 'training'))
      `;
      console.log('  ✅ Added entry_type check constraint');
    } catch (error) {
      console.log('  ⚠️  Check constraint may already exist:', error.message);
    }
    
    // Step 7: Verify the changes
    console.log('\n7. Verifying changes...');
    const updatedColumns = await sql`
      SELECT 
          column_name, 
          data_type, 
          is_nullable,
          column_default
      FROM information_schema.columns 
      WHERE table_name = 'attendance'
      AND column_name IN ('is_active', 'daily_sequence', 'entry_type', 'check_in_time', 'check_out_time', 'hours_worked')
      ORDER BY column_name
    `;
    
    console.log('Updated schema:');
    updatedColumns.forEach(col => {
      console.log(`  ✅ ${col.column_name}: ${col.data_type} ${col.is_nullable === 'YES' ? '(nullable)' : '(not null)'} ${col.column_default ? `default: ${col.column_default}` : ''}`);
    });
    
    // Step 8: Test the schema
    console.log('\n8. Testing the new schema...');
    try {
      // Get a test user
      const testUser = await sql`SELECT id FROM users LIMIT 1`;
      if (testUser.length === 0) {
        console.log('  ⚠️  No users found for testing');
      } else {
        const userId = testUser[0].id;
        const today = new Date().toISOString().split('T')[0];
        
        // Test insert (will be rolled back)
        await sql`BEGIN`;
        
        const testInsert = await sql`
          INSERT INTO attendance (
              user_id, 
              date, 
              check_in_time, 
              status, 
              notes,
              entry_type, 
              daily_sequence, 
              is_active
          ) VALUES (
              ${userId},
              ${today},
              NOW(),
              'present',
              'Test insert after migration',
              'regular',
              1,
              TRUE
          )
          RETURNING id
        `;
        
        console.log(`  ✅ Test insert successful (ID: ${testInsert[0].id})`);
        
        // Rollback the test
        await sql`ROLLBACK`;
        console.log('  ✅ Test insert rolled back');
      }
    } catch (error) {
      console.log('  ❌ Test insert failed:', error.message);
      await sql`ROLLBACK`;
    }
    
    console.log('\n🎉 Migration completed successfully!');
    console.log('\nThe attendance table now supports:');
    console.log('  ✅ Multiple daily sessions (daily_sequence)');
    console.log('  ✅ Active session tracking (is_active)');
    console.log('  ✅ Entry type categorization (entry_type)');
    console.log('  ✅ Enhanced attendance management features');
    console.log('\n🚀 You can now test the Check In/Check Out functionality on the dashboard!');
    
  } catch (error) {
    console.error('❌ Migration failed:', error);
    console.error('\nPlease check:');
    console.error('  1. DATABASE_URL is correctly set in .env.local');
    console.error('  2. Database connection is working');
    console.error('  3. You have proper permissions to alter the table');
    process.exit(1);
  }
}

// Run the migration
if (require.main === module) {
  fixAttendanceSchema();
}

module.exports = { fixAttendanceSchema };
