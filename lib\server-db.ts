// Server-side only database operations
import { neon } from "@neondatabase/serverless"
import { randomBytes } from "crypto"

if (!process.env.DATABASE_URL) {
  throw new Error("DATABASE_URL environment variable is required")
}

// Configure Neon with timeout settings for better reliability
const sql = neon(process.env.DATABASE_URL, {
  fetchConnectionCache: true,
  fullResults: false,
  arrayMode: false,
})

// Types for our database
export type UserRole = "admin" | "hr_manager" | "manager" | "staff"
export type AttendanceStatus = "present" | "absent" | "late" | "half_day" | "on_leave"
export type TaskStatus = "todo" | "in_progress" | "completed" | "cancelled"
export type TaskPriority = "low" | "medium" | "high" | "urgent"
export type PayrollStatus = "draft" | "processed" | "paid"

export interface User {
  id: string
  email: string
  full_name: string
  role: UserRole

  // Basic employee information
  employee_id?: string
  department?: string
  position?: string
  phone?: string
  hire_date?: string

  // Employment details
  employment_type?: string
  employment_status?: string
  probation_period_months?: number
  confirmation_date?: string
  termination_date?: string
  termination_reason?: string

  // Salary and compensation
  salary?: number
  salary_currency?: string
  pay_grade?: string
  joining_bonus?: number

  // Personal information
  date_of_birth?: string
  gender?: string
  marital_status?: string
  nationality?: string

  // Identification
  citizenship_number?: string
  pan_number?: string
  passport_number?: string

  // Emergency contact
  emergency_contact_name?: string
  emergency_contact_phone?: string
  emergency_contact_relationship?: string

  // Bank details
  bank_name?: string
  bank_account_number?: string
  bank_branch?: string

  // System fields
  is_active: boolean
  email_verified: boolean
  last_login?: string
  created_at: string
  updated_at: string
}

export interface UserSession {
  id: string
  user_id: string
  session_token: string
  expires_at: string
  created_at: string
}

export interface Permission {
  id: string
  name: string
  description?: string
  resource: string
  action: string
  created_at: string
}

export interface Department {
  id: string
  name: string
  description?: string
  manager_id?: string
  budget?: number
  location?: string
  is_active: boolean
  created_at: string
  updated_at: string
}

export interface EmployeeAddress {
  id: string
  user_id: string
  address_type: string
  address_line_1: string
  address_line_2?: string
  city?: string
  district?: string
  province?: string
  postal_code?: string
  country?: string
  is_primary: boolean
  created_at: string
  updated_at: string
}

export interface Attendance {
  id: string
  user_id: string
  date: string
  check_in_time?: string
  check_out_time?: string
  break_start_time?: string
  break_end_time?: string
  total_hours: number
  overtime_hours: number
  status: AttendanceStatus
  notes?: string
  approved_by?: string
  approved_at?: string
  created_at: string
  updated_at: string
}

export interface Payroll {
  id: string
  user_id: string
  pay_period_start: string
  pay_period_end: string
  base_salary: number
  overtime_hours: number
  overtime_rate: number
  bonuses: number
  allowances: number
  deductions: number
  gross_pay: number
  tax_deductions: number
  provident_fund: number
  net_pay: number
  status: PayrollStatus
  processed_by?: string
  processed_at?: string
  created_at: string
  updated_at: string
}

// Simple session token generator
export function generateSessionToken(): string {
  return randomBytes(32).toString("hex")
}

// Generate unique employee ID
export async function generateEmployeeId(): Promise<string> {
  const currentYear = new Date().getFullYear()
  const prefix = `EMP${currentYear}`

  try {
    // Get the highest employee ID for the current year
    const result = await sql`
      SELECT employee_id FROM users
      WHERE employee_id LIKE ${prefix + '%'}
      ORDER BY employee_id DESC
      LIMIT 1
    `

    let nextNumber = 1
    if (result.length > 0 && result[0].employee_id) {
      const lastId = result[0].employee_id
      const lastNumber = parseInt(lastId.replace(prefix, ''))
      nextNumber = lastNumber + 1
    }

    // Format with leading zeros (e.g., EMP2024001)
    const employeeId = `${prefix}${nextNumber.toString().padStart(3, '0')}`

    // Double-check uniqueness
    const existingUser = await sql`
      SELECT id FROM users WHERE employee_id = ${employeeId}
    `

    if (existingUser.length > 0) {
      // If somehow it exists, try the next number
      return generateEmployeeId()
    }

    return employeeId
  } catch (error) {
    console.error("Error generating employee ID:", error)
    // Fallback to timestamp-based ID
    return `EMP${currentYear}${Date.now().toString().slice(-3)}`
  }
}

// File management utilities
export interface FileUploadData {
  userId: string
  fileType: 'profile_picture' | 'document' | 'contract' | 'signature'
  originalFilename: string
  fileContent: Buffer
  mimeType: string
  uploadedBy: string
  uploadIpAddress?: string
  uploadUserAgent?: string
}

export interface UserFile {
  id: string
  user_id: string
  file_type: string
  original_filename: string
  stored_filename: string
  file_path: string
  file_size: number
  mime_type: string
  file_extension: string
  is_validated: boolean
  validation_status: string
  validation_notes?: string
  validated_by?: string
  validated_at?: string
  is_public: boolean
  access_level: string
  download_count: number
  last_accessed_at?: string
  uploaded_by: string
  upload_ip_address?: string
  upload_user_agent?: string
  is_active: boolean
  created_at: string
  updated_at: string
}

// Generate unique filename
export function generateUniqueFilename(originalFilename: string): string {
  const timestamp = Date.now()
  const randomString = randomBytes(8).toString('hex')
  const extension = originalFilename.split('.').pop()
  return `${timestamp}_${randomString}.${extension}`
}

// Validate file type and size
export async function validateFileUpload(fileType: string, mimeType: string, fileSize: number, filename: string): Promise<{ valid: boolean; error?: string }> {
  try {
    const config = await sql`
      SELECT allowed_extensions, allowed_mime_types, max_file_size_mb
      FROM file_type_config
      WHERE file_type = ${fileType} AND is_active = true
    `

    if (config.length === 0) {
      return { valid: false, error: `File type '${fileType}' is not supported` }
    }

    const { allowed_extensions, allowed_mime_types, max_file_size_mb } = config[0]

    // Check file extension
    const extension = filename.split('.').pop()?.toLowerCase()
    if (!extension || !allowed_extensions.includes(extension)) {
      return { valid: false, error: `File extension '${extension}' is not allowed for ${fileType}. Allowed: ${allowed_extensions.join(', ')}` }
    }

    // Check MIME type
    if (!allowed_mime_types.includes(mimeType)) {
      return { valid: false, error: `File type '${mimeType}' is not allowed for ${fileType}. Allowed: ${allowed_mime_types.join(', ')}` }
    }

    // Check file size (convert MB to bytes)
    const maxSizeBytes = max_file_size_mb * 1024 * 1024
    if (fileSize > maxSizeBytes) {
      return { valid: false, error: `File size ${(fileSize / 1024 / 1024).toFixed(2)}MB exceeds maximum allowed size of ${max_file_size_mb}MB` }
    }

    return { valid: true }
  } catch (error) {
    console.error("Error validating file upload:", error)
    return { valid: false, error: "File validation failed" }
  }
}

// Server-side database operations
export const serverDb = {
  sql, // Export sql for direct queries if needed
  
  // User operations
  async getUserByEmail(email: string): Promise<User | null> {
    try {
      if (!email) {
        throw new Error("Email is required")
      }

      const result = await sql`
        SELECT * FROM users WHERE email = ${email} AND is_active = true
      `
      return result[0] || null
    } catch (error) {
      console.error("Error fetching user by email:", error)
      throw new Error("Failed to fetch user")
    }
  },

  async getUserById(id: string): Promise<User | null> {
    try {
      if (!id) {
        throw new Error("User ID is required")
      }

      const result = await sql`
        SELECT * FROM users WHERE id = ${id} AND is_active = true
      `
      return result[0] || null
    } catch (error) {
      console.error("Error fetching user by ID:", error)
      throw new Error("Failed to fetch user")
    }
  },

  async createUser(
    userData: Omit<User, "id" | "created_at" | "updated_at"> & { password_hash: string },
  ): Promise<User> {
    try {
      if (!userData.email || !userData.password_hash || !userData.full_name) {
        throw new Error("Email, password hash, and full name are required")
      }

      // Generate employee ID if not provided
      const employeeId = userData.employee_id || await generateEmployeeId()

      const result = await sql`
        INSERT INTO users (
          email, password_hash, full_name, role, employee_id, department, position,
          phone, hire_date, employment_type, employment_status, salary, salary_currency,
          date_of_birth, gender, marital_status, nationality, citizenship_number, pan_number,
          emergency_contact_name, emergency_contact_phone, emergency_contact_relationship,
          bank_name, bank_account_number, bank_branch, is_active, email_verified
        ) VALUES (
          ${userData.email}, ${userData.password_hash}, ${userData.full_name},
          ${userData.role}, ${employeeId}, ${userData.department || null},
          ${userData.position || null}, ${userData.phone || null}, ${userData.hire_date || null},
          ${userData.employment_type || 'full_time'}, ${userData.employment_status || 'active'},
          ${userData.salary || null}, ${userData.salary_currency || 'NPR'},
          ${userData.date_of_birth || null}, ${userData.gender || null},
          ${userData.marital_status || null}, ${userData.nationality || 'Nepali'},
          ${userData.citizenship_number || null}, ${userData.pan_number || null},
          ${userData.emergency_contact_name || null}, ${userData.emergency_contact_phone || null},
          ${userData.emergency_contact_relationship || null}, ${userData.bank_name || null},
          ${userData.bank_account_number || null}, ${userData.bank_branch || null},
          ${userData.is_active}, ${userData.email_verified}
        ) RETURNING *
      `
      return result[0]
    } catch (error) {
      console.error("Error creating user:", error)
      if (error.message?.includes("duplicate key")) {
        throw new Error("User with this email already exists")
      }
      throw new Error("Failed to create user")
    }
  },

  async updateUserLastLogin(userId: string): Promise<void> {
    try {
      if (!userId) {
        throw new Error("User ID is required")
      }

      await sql`
        UPDATE users SET last_login = NOW() WHERE id = ${userId}
      `
    } catch (error) {
      console.error("Error updating last login:", error)
      throw new Error("Failed to update last login")
    }
  },

  // Session operations
  async createSession(userId: string, sessionToken: string, expiresAt: Date): Promise<UserSession> {
    try {
      if (!userId || !sessionToken || !expiresAt) {
        throw new Error("User ID, session token, and expiration date are required")
      }

      const result = await sql`
        INSERT INTO user_sessions (user_id, session_token, expires_at)
        VALUES (${userId}, ${sessionToken}, ${expiresAt.toISOString()})
        RETURNING *
      `
      return result[0]
    } catch (error) {
      console.error("Error creating session:", error)
      throw new Error("Failed to create session")
    }
  },

  async getSessionByToken(sessionToken: string): Promise<UserSession | null> {
    try {
      if (!sessionToken) {
        return null
      }

      const result = await sql`
        SELECT * FROM user_sessions 
        WHERE session_token = ${sessionToken} AND expires_at > NOW()
      `
      return result[0] || null
    } catch (error) {
      console.error("Error fetching session:", error)
      return null
    }
  },

  async deleteSession(sessionToken: string): Promise<void> {
    try {
      if (!sessionToken) {
        return
      }

      await sql`
        DELETE FROM user_sessions WHERE session_token = ${sessionToken}
      `
    } catch (error) {
      console.error("Error deleting session:", error)
      // Don't throw as logout should still work
    }
  },

  async deleteExpiredSessions(): Promise<void> {
    try {
      await sql`
        DELETE FROM user_sessions WHERE expires_at <= NOW()
      `
    } catch (error) {
      console.error("Error deleting expired sessions:", error)
    }
  },

  // Permission operations
  async getUserPermissions(role: UserRole): Promise<Permission[]> {
    try {
      if (!role) {
        return []
      }

      const result = await sql`
        SELECT p.* FROM permissions p
        JOIN role_permissions rp ON p.id = rp.permission_id
        WHERE rp.role = ${role}
      `
      return result
    } catch (error) {
      console.error("Error fetching permissions:", error)
      return []
    }
  },

  // Department operations
  async getAllDepartments(): Promise<Department[]> {
    try {
      const result = await sql`
        SELECT d.*, u.full_name as manager_name
        FROM departments d
        LEFT JOIN users u ON d.manager_id = u.id
        WHERE d.is_active = true
        ORDER BY d.name
      `
      return result
    } catch (error) {
      console.error("Error fetching departments:", error)
      return []
    }
  },

  async getDepartmentById(id: string): Promise<Department | null> {
    try {
      const result = await sql`
        SELECT * FROM departments WHERE id = ${id} AND is_active = true
      `
      return result[0] || null
    } catch (error) {
      console.error("Error fetching department:", error)
      return null
    }
  },

  // User management operations
  async getAllUsers(filters?: { department?: string; role?: UserRole; is_active?: boolean }): Promise<User[]> {
    try {
      let query = sql`SELECT * FROM users WHERE 1=1`

      if (filters?.department) {
        query = sql`SELECT * FROM users WHERE department = ${filters.department}`
      }
      if (filters?.role) {
        query = sql`${query} AND role = ${filters.role}`
      }
      if (filters?.is_active !== undefined) {
        query = sql`${query} AND is_active = ${filters.is_active}`
      }

      query = sql`${query} ORDER BY created_at DESC`

      const result = await query
      return result
    } catch (error) {
      console.error("Error fetching users:", error)
      return []
    }
  },

  async updateUser(id: string, userData: Partial<User>): Promise<User | null> {
    try {
      if (!id) {
        throw new Error("User ID is required")
      }

      // Filter out undefined values and restricted fields
      const filteredData = Object.entries(userData).reduce((acc, [key, value]) => {
        if (key !== 'id' && key !== 'created_at' && key !== 'updated_at' && value !== undefined) {
          acc[key] = value
        }
        return acc
      }, {} as Record<string, any>)

      if (Object.keys(filteredData).length === 0) {
        throw new Error("No fields to update")
      }

      // Handle user status toggle (from PATCH endpoint)
      if (filteredData.is_active !== undefined && Object.keys(filteredData).length <= 3) {
        // Status update with optional employment_status and termination_date
        const result = await sql`
          UPDATE users
          SET is_active = ${filteredData.is_active},
              employment_status = ${filteredData.employment_status || (filteredData.is_active ? 'active' : 'terminated')},
              termination_date = ${filteredData.termination_date || (filteredData.is_active ? null : new Date().toISOString().split('T')[0])},
              updated_at = NOW()
          WHERE id = ${id}
          RETURNING *
        `
        return result[0] || null
      }

      // Handle full user profile update (from edit form)
      if (filteredData.email && filteredData.full_name) {
        const result = await sql`
          UPDATE users
          SET email = ${filteredData.email},
              full_name = ${filteredData.full_name},
              role = ${filteredData.role || 'staff'},
              employee_id = ${filteredData.employee_id || null},
              department = ${filteredData.department || null},
              position = ${filteredData.position || null},
              phone = ${filteredData.phone || null},
              salary = ${filteredData.salary || null},
              employment_type = ${filteredData.employment_type || 'full_time'},
              updated_at = NOW()
          WHERE id = ${id}
          RETURNING *
        `
        return result[0] || null
      }

      // Handle user deactivation (from DELETE endpoint)
      if (Object.keys(filteredData).length === 1 && filteredData.is_active === false) {
        const result = await sql`
          UPDATE users
          SET is_active = false,
              employment_status = 'terminated',
              termination_date = ${new Date().toISOString().split('T')[0]},
              updated_at = NOW()
          WHERE id = ${id}
          RETURNING *
        `
        return result[0] || null
      }

      // Handle single field updates for specific known fields
      if (Object.keys(filteredData).length === 1) {
        const key = Object.keys(filteredData)[0]
        const value = filteredData[key]

        switch (key) {
          case 'is_active':
            const result1 = await sql`
              UPDATE users SET is_active = ${value}, updated_at = NOW() WHERE id = ${id} RETURNING *
            `
            return result1[0] || null

          case 'employment_status':
            const result2 = await sql`
              UPDATE users SET employment_status = ${value}, updated_at = NOW() WHERE id = ${id} RETURNING *
            `
            return result2[0] || null

          case 'role':
            const result3 = await sql`
              UPDATE users SET role = ${value}, updated_at = NOW() WHERE id = ${id} RETURNING *
            `
            return result3[0] || null

          case 'department':
            const result4 = await sql`
              UPDATE users SET department = ${value}, updated_at = NOW() WHERE id = ${id} RETURNING *
            `
            return result4[0] || null

          default:
            throw new Error(`Single field update not supported for field: ${key}`)
        }
      }

      throw new Error("Invalid update data provided")
    } catch (error) {
      console.error("Error updating user:", error)
      throw new Error("Failed to update user")
    }
  },

  async getUserWithAddress(id: string): Promise<(User & { addresses?: EmployeeAddress[] }) | null> {
    try {
      // For admin interface, get user regardless of active status
      const result = await sql`
        SELECT * FROM users WHERE id = ${id}
      `
      const user = result[0] || null
      if (!user) return null

      const addresses = await sql`
        SELECT * FROM employee_addresses
        WHERE user_id = ${id}
        ORDER BY is_primary DESC, created_at DESC
      `

      return { ...user, addresses }
    } catch (error) {
      console.error("Error fetching user with address:", error)
      return null
    }
  },

  // Attendance clock functions
  async clockIn(userId: string, notes?: string): Promise<any> {
    try {
      if (!userId) {
        throw new Error("User ID is required")
      }

      const today = new Date().toISOString().split("T")[0]
      const now = new Date()

      // Check if user has an active session (checked in but not checked out)
      const activeSession = await sql`
        SELECT * FROM attendance
        WHERE user_id = ${userId}
        AND date = ${today}
        AND check_in_time IS NOT NULL
        AND check_out_time IS NULL
        ORDER BY created_at DESC
        LIMIT 1
      `

      if (activeSession.length > 0) {
        throw new Error("You must check out before checking in again")
      }

      // Check daily limits (max 5 check-ins per day)
      const todayEntries = await sql`
        SELECT COUNT(*) as count FROM attendance
        WHERE user_id = ${userId}
        AND date = ${today}
        AND check_in_time IS NOT NULL
      `

      const dailyCheckIns = Number(todayEntries[0]?.count || 0)
      if (dailyCheckIns >= 5) {
        throw new Error("Maximum 5 check-ins per day allowed")
      }

      // Determine status based on time (assuming 9 AM is standard start time)
      const checkInHour = now.getHours()
      const checkInMinute = now.getMinutes()
      const isLate = checkInHour > 9 || (checkInHour === 9 && checkInMinute > 15)
      const status: AttendanceStatus = isLate ? "late" : "present"

      // Create new attendance record
      const result = await sql`
        INSERT INTO attendance (
          user_id, date, check_in_time, status, notes
        )
        VALUES (
          ${userId}, ${today}, ${now.toISOString()}, ${status}, ${notes || null}
        )
        RETURNING *
      `
      return result[0] || null
    } catch (error) {
      console.error("Error clocking in:", error)
      throw new Error(error instanceof Error ? error.message : "Failed to clock in")
    }
  },

  async clockOut(userId: string, notes?: string): Promise<any> {
    try {
      if (!userId) {
        throw new Error("User ID is required")
      }

      const today = new Date().toISOString().split("T")[0]
      const now = new Date()

      // Find the active session (most recent check-in without check-out)
      const activeSession = await sql`
        SELECT * FROM attendance
        WHERE user_id = ${userId}
        AND date = ${today}
        AND check_in_time IS NOT NULL
        AND check_out_time IS NULL
        ORDER BY created_at DESC
        LIMIT 1
      `

      if (activeSession.length === 0) {
        throw new Error("No active session found. Please check in first")
      }

      const session = activeSession[0]

      // Check daily limits (max 5 check-outs per day)
      const todayCheckOuts = await sql`
        SELECT COUNT(*) as count FROM attendance
        WHERE user_id = ${userId}
        AND date = ${today}
        AND check_out_time IS NOT NULL
      `

      const dailyCheckOuts = Number(todayCheckOuts[0]?.count || 0)
      if (dailyCheckOuts >= 5) {
        throw new Error("Maximum 5 check-outs per day allowed")
      }

      // Update the active session with check-out time
      const result = await sql`
        UPDATE attendance
        SET check_out_time = ${now.toISOString()},
            notes = ${notes ? (session.notes ? `${session.notes}; ${notes}` : notes) : session.notes}
        WHERE id = ${session.id}
        RETURNING *
      `
      return result[0] || null
    } catch (error) {
      console.error("Error clocking out:", error)
      throw new Error(error instanceof Error ? error.message : "Failed to clock out")
    }
  },

  // User deletion operations
  async deleteUser(id: string): Promise<boolean> {
    try {
      if (!id) {
        throw new Error("User ID is required")
      }

      // Soft delete - just deactivate the user
      const result = await sql`
        UPDATE users
        SET is_active = false,
            employment_status = 'terminated',
            termination_date = ${new Date().toISOString().split('T')[0]},
            updated_at = NOW()
        WHERE id = ${id}
        RETURNING id
      `

      return result.length > 0
    } catch (error) {
      console.error("Error deleting user:", error)
      throw new Error("Failed to delete user")
    }
  },

  async hardDeleteUser(id: string): Promise<boolean> {
    try {
      if (!id) {
        throw new Error("User ID is required")
      }

      // Check if user exists and get their info for logging
      const userCheck = await sql`
        SELECT id, email, full_name FROM users WHERE id = ${id}
      `

      if (userCheck.length === 0) {
        throw new Error("User not found")
      }

      const user = userCheck[0]

      // Hard delete - completely remove user and all associated data
      // Note: Foreign key constraints with CASCADE will handle related data
      const result = await sql`
        DELETE FROM users WHERE id = ${id} RETURNING id
      `

      if (result.length > 0) {
        console.log(`Hard deleted user: ${user.email} (${user.full_name})`)
        return true
      }

      return false
    } catch (error) {
      console.error("Error hard deleting user:", error)
      throw new Error("Failed to permanently delete user")
    }
  },

  // File management operations
  async uploadFile(fileData: FileUploadData): Promise<UserFile> {
    try {
      const { userId, fileType, originalFilename, fileContent, mimeType, uploadedBy, uploadIpAddress, uploadUserAgent } = fileData

      // Validate file
      const validation = await validateFileUpload(fileType, mimeType, fileContent.length, originalFilename)
      if (!validation.valid) {
        throw new Error(validation.error)
      }

      // Generate unique filename
      const storedFilename = generateUniqueFilename(originalFilename)
      const filePath = `/uploads/${fileType}/${storedFilename}`
      const fileExtension = originalFilename.split('.').pop()?.toLowerCase() || ''

      // Generate content hash
      const contentHash = randomBytes(32).toString('hex') // Simplified hash for now

      // Insert file metadata
      const fileResult = await sql`
        INSERT INTO user_files (
          user_id, file_type, original_filename, stored_filename, file_path,
          file_size, mime_type, file_extension, uploaded_by, upload_ip_address, upload_user_agent
        ) VALUES (
          ${userId}, ${fileType}, ${originalFilename}, ${storedFilename}, ${filePath},
          ${fileContent.length}, ${mimeType}, ${fileExtension}, ${uploadedBy}, ${uploadIpAddress || null}, ${uploadUserAgent || null}
        ) RETURNING *
      `

      const file = fileResult[0]

      // Store file content
      await sql`
        INSERT INTO file_storage (file_id, file_content, content_hash)
        VALUES (${file.id}, ${fileContent}, ${contentHash})
      `

      // Log the upload
      await sql`
        INSERT INTO file_access_log (file_id, accessed_by, access_type, access_ip_address, user_agent)
        VALUES (${file.id}, ${uploadedBy}, 'upload', ${uploadIpAddress || null}, ${uploadUserAgent || null})
      `

      return file
    } catch (error) {
      console.error("Error uploading file:", error)
      throw new Error("Failed to upload file")
    }
  },

  async getUserFiles(userId: string, fileType?: string): Promise<UserFile[]> {
    try {
      let query = sql`
        SELECT * FROM user_files
        WHERE user_id = ${userId} AND is_active = true
      `

      if (fileType) {
        query = sql`
          SELECT * FROM user_files
          WHERE user_id = ${userId} AND file_type = ${fileType} AND is_active = true
        `
      }

      const result = await query
      return result
    } catch (error) {
      console.error("Error fetching user files:", error)
      throw new Error("Failed to fetch user files")
    }
  },

  async getFileContent(fileId: string, accessedBy: string, ipAddress?: string): Promise<{ file: UserFile; content: Buffer } | null> {
    try {
      // Get file metadata
      const fileResult = await sql`
        SELECT * FROM user_files WHERE id = ${fileId} AND is_active = true
      `

      if (fileResult.length === 0) {
        return null
      }

      const file = fileResult[0]

      // Get file content
      const contentResult = await sql`
        SELECT file_content FROM file_storage WHERE file_id = ${fileId}
      `

      if (contentResult.length === 0) {
        throw new Error("File content not found")
      }

      // Log the access
      await sql`
        INSERT INTO file_access_log (file_id, accessed_by, access_type, access_ip_address)
        VALUES (${fileId}, ${accessedBy}, 'download', ${ipAddress || null})
      `

      return {
        file,
        content: contentResult[0].file_content
      }
    } catch (error) {
      console.error("Error getting file content:", error)
      throw new Error("Failed to get file content")
    }
  },

  async deleteFile(fileId: string, deletedBy: string): Promise<boolean> {
    try {
      // Soft delete the file
      const result = await sql`
        UPDATE user_files
        SET is_active = false, updated_at = NOW()
        WHERE id = ${fileId}
        RETURNING id
      `

      if (result.length === 0) {
        return false
      }

      // Log the deletion
      await sql`
        INSERT INTO file_access_log (file_id, accessed_by, access_type)
        VALUES (${fileId}, ${deletedBy}, 'delete')
      `

      return true
    } catch (error) {
      console.error("Error deleting file:", error)
      throw new Error("Failed to delete file")
    }
  },

  // Health check
  async healthCheck(): Promise<boolean> {
    try {
      await sql`SELECT 1`
      return true
    } catch (error) {
      console.error("Database health check failed:", error)
      return false
    }
  },
}
