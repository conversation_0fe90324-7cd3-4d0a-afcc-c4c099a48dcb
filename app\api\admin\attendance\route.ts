import { type NextRequest, NextResponse } from "next/server"
import { AuthService } from "@/lib/auth-utils"
import { db } from "@/lib/neon"

export async function GET(request: NextRequest) {
  try {
    const sessionToken = request.cookies.get("session-token")?.value

    if (!sessionToken) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 })
    }

    const user = await AuthService.verifySession(sessionToken)

    if (!user || !["admin", "hr_manager", "manager"].includes(user.role)) {
      return NextResponse.json({ error: "Forbidden" }, { status: 403 })
    }

    const { searchParams } = new URL(request.url)
    const date = searchParams.get("date") || new Date().toISOString().split("T")[0]

    const attendance = await db.getAllAttendanceForDate(date)
    const stats = await db.getAttendanceStats()

    return NextResponse.json({
      success: true,
      attendance,
      stats,
      date,
    })
  } catch (error) {
    console.error("Admin attendance API error:", error)
    return NextResponse.json({ error: "Internal server error" }, { status: 500 })
  }
}

export async function POST(request: NextRequest) {
  try {
    const sessionToken = request.cookies.get("session-token")?.value

    if (!sessionToken) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 })
    }

    const user = await AuthService.verifySession(sessionToken)

    if (!user || !["admin", "hr_manager"].includes(user.role)) {
      return NextResponse.json({ error: "Forbidden" }, { status: 403 })
    }

    const data = await request.json()
    const { userId, date, checkInTime, checkOutTime, status, hoursWorked, notes } = data

    if (!userId || !date || !status) {
      return NextResponse.json({ error: "User ID, date, and status are required" }, { status: 400 })
    }

    const attendance = await db.createAttendanceRecord({
      userId,
      date,
      checkInTime,
      checkOutTime,
      status,
      hoursWorked,
      notes,
      createdBy: user.id,
    })

    return NextResponse.json({
      success: true,
      attendance,
      message: "Attendance record created successfully",
    })
  } catch (error) {
    console.error("Create attendance API error:", error)
    const message = error instanceof Error ? error.message : "Failed to create attendance record"
    return NextResponse.json({ error: message }, { status: 400 })
  }
}
