"use client"

import React, { useState, useEffect } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Checkbox } from "@/components/ui/checkbox"
import { Separator } from "@/components/ui/separator"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { Shield, Users, Settings, Eye, Edit, Trash2, FileText, BarChart3, UserCog } from "lucide-react"
import type { User, Permission } from "@/components/auth-provider"

interface RoleAssignmentProps {
  user: User
  onRoleChange: (newRole: string) => Promise<void>
}

interface RolePermissions {
  [key: string]: Permission[]
}

const roleDescriptions = {
  admin: "Full system access with all permissions",
  hr_manager: "Human resources management with user and payroll access",
  manager: "Department management with limited administrative access",
  staff: "Basic access to personal dashboard and attendance"
}

const roleIcons = {
  admin: Shield,
  hr_manager: User<PERSON><PERSON>,
  manager: Users,
  staff: Eye
}

export function RoleAssignment({ user, onRoleChange }: RoleAssignmentProps) {
  const [permissions, setPermissions] = useState<Permission[]>([])
  const [rolePermissions, setRolePermissions] = useState<RolePermissions>({})
  const [loading, setLoading] = useState(true)
  const [saving, setSaving] = useState(false)
  const [selectedRole, setSelectedRole] = useState(user.role)

  useEffect(() => {
    fetchPermissions()
  }, [])

  const fetchPermissions = async () => {
    try {
      setLoading(true)
      
      // Fetch all permissions
      const permissionsResponse = await fetch("/api/admin/permissions")
      const permissionsData = await permissionsResponse.json()
      
      if (permissionsData.success) {
        setPermissions(permissionsData.permissions)
        
        // Fetch role-specific permissions
        const roles = ["admin", "hr_manager", "manager", "staff"]
        const rolePermsMap: RolePermissions = {}
        
        for (const role of roles) {
          const roleResponse = await fetch(`/api/admin/permissions/role/${role}`)
          const roleData = await roleResponse.json()
          
          if (roleData.success) {
            rolePermsMap[role] = roleData.permissions
          }
        }
        
        setRolePermissions(rolePermsMap)
      }
    } catch (error) {
      console.error("Error fetching permissions:", error)
    } finally {
      setLoading(false)
    }
  }

  const handleRoleChange = async () => {
    if (selectedRole === user.role) return

    try {
      setSaving(true)
      await onRoleChange(selectedRole)
    } catch (error) {
      console.error("Error changing role:", error)
      setSelectedRole(user.role) // Reset on error
    } finally {
      setSaving(false)
    }
  }

  const getPermissionIcon = (resource: string) => {
    switch (resource) {
      case "users": return Users
      case "settings": return Settings
      case "reports": return BarChart3
      case "payroll": return FileText
      case "attendance": return Eye
      default: return Shield
    }
  }

  const groupPermissionsByResource = (perms: Permission[]) => {
    return perms.reduce((acc, perm) => {
      if (!acc[perm.resource]) {
        acc[perm.resource] = []
      }
      acc[perm.resource].push(perm)
      return acc
    }, {} as { [key: string]: Permission[] })
  }

  if (loading) {
    return (
      <Card>
        <CardContent className="py-6">
          <div className="text-center">Loading permissions...</div>
        </CardContent>
      </Card>
    )
  }

  const currentRolePermissions = rolePermissions[user.role] || []
  const selectedRolePermissions = rolePermissions[selectedRole] || []
  const groupedCurrentPerms = groupPermissionsByResource(currentRolePermissions)
  const groupedSelectedPerms = groupPermissionsByResource(selectedRolePermissions)

  return (
    <div className="space-y-6">
      {/* Current Role */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Shield className="w-5 h-5" />
            Current Role Assignment
          </CardTitle>
          <CardDescription>
            User's current role and associated permissions
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="flex items-center gap-4">
            <div className="flex items-center gap-2">
              {React.createElement(roleIcons[user.role as keyof typeof roleIcons] || Shield, {
                className: "w-5 h-5"
              })}
              <Badge className="text-sm px-3 py-1">
                {user.role.replace("_", " ").toUpperCase()}
              </Badge>
            </div>
            <div className="text-sm text-gray-600">
              {roleDescriptions[user.role as keyof typeof roleDescriptions]}
            </div>
          </div>

          <Separator />

          <div>
            <h4 className="font-medium mb-3">Current Permissions</h4>
            <div className="space-y-3">
              {Object.entries(groupedCurrentPerms).map(([resource, perms]) => {
                const Icon = getPermissionIcon(resource)
                return (
                  <div key={resource} className="flex items-start gap-3">
                    <Icon className="w-4 h-4 mt-1 text-gray-500" />
                    <div className="flex-1">
                      <div className="font-medium capitalize">{resource}</div>
                      <div className="flex flex-wrap gap-1 mt-1">
                        {perms.map((perm) => (
                          <Badge key={perm.id} variant="outline" className="text-xs">
                            {perm.action}
                          </Badge>
                        ))}
                      </div>
                    </div>
                  </div>
                )
              })}
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Role Change */}
      <Card>
        <CardHeader>
          <CardTitle>Change Role</CardTitle>
          <CardDescription>
            Assign a new role to this user. This will change their permissions immediately.
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="flex items-center gap-4">
            <div className="flex-1">
              <Select value={selectedRole} onValueChange={setSelectedRole}>
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="staff">Staff</SelectItem>
                  <SelectItem value="manager">Manager</SelectItem>
                  <SelectItem value="hr_manager">HR Manager</SelectItem>
                  <SelectItem value="admin">Admin</SelectItem>
                </SelectContent>
              </Select>
            </div>
            <Button 
              onClick={handleRoleChange}
              disabled={selectedRole === user.role || saving}
            >
              {saving ? "Updating..." : "Update Role"}
            </Button>
          </div>

          {selectedRole !== user.role && (
            <Alert>
              <Shield className="h-4 w-4" />
              <AlertDescription>
                Changing the role will immediately update the user's permissions. 
                The user may need to log out and log back in to see the changes.
              </AlertDescription>
            </Alert>
          )}

          {selectedRole !== user.role && (
            <>
              <Separator />
              <div>
                <h4 className="font-medium mb-3">New Role Permissions Preview</h4>
                <div className="text-sm text-gray-600 mb-3">
                  {roleDescriptions[selectedRole as keyof typeof roleDescriptions]}
                </div>
                <div className="space-y-3">
                  {Object.entries(groupedSelectedPerms).map(([resource, perms]) => {
                    const Icon = getPermissionIcon(resource)
                    return (
                      <div key={resource} className="flex items-start gap-3">
                        <Icon className="w-4 h-4 mt-1 text-gray-500" />
                        <div className="flex-1">
                          <div className="font-medium capitalize">{resource}</div>
                          <div className="flex flex-wrap gap-1 mt-1">
                            {perms.map((perm) => (
                              <Badge key={perm.id} variant="outline" className="text-xs">
                                {perm.action}
                              </Badge>
                            ))}
                          </div>
                        </div>
                      </div>
                    )
                  })}
                </div>
              </div>
            </>
          )}
        </CardContent>
      </Card>

      {/* Role Comparison */}
      <Card>
        <CardHeader>
          <CardTitle>Role Comparison</CardTitle>
          <CardDescription>
            Compare permissions across different roles
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="overflow-x-auto">
            <table className="w-full">
              <thead>
                <tr className="border-b">
                  <th className="text-left p-2">Permission</th>
                  <th className="text-center p-2">Staff</th>
                  <th className="text-center p-2">Manager</th>
                  <th className="text-center p-2">HR Manager</th>
                  <th className="text-center p-2">Admin</th>
                </tr>
              </thead>
              <tbody>
                {permissions.map((permission) => (
                  <tr key={permission.id} className="border-b">
                    <td className="p-2">
                      <div className="font-medium">{permission.name}</div>
                      <div className="text-sm text-gray-500">{permission.description}</div>
                    </td>
                    {["staff", "manager", "hr_manager", "admin"].map((role) => {
                      const hasPermission = rolePermissions[role]?.some(p => p.id === permission.id)
                      return (
                        <td key={role} className="text-center p-2">
                          <Checkbox checked={hasPermission} disabled />
                        </td>
                      )
                    })}
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
