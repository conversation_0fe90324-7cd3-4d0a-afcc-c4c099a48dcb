import { NextRequest, NextResponse } from "next/server"
import { AuthService } from "@/lib/auth-utils"
import { serverDb } from "@/lib/server-db"
import { z } from "zod"

// Validation schema for status updates
const statusUpdateSchema = z.object({
  status: z.enum(["todo", "in_progress", "completed", "cancelled"]),
  position: z.number().int().min(0).optional(),
})

// PATCH /api/tasks/[id]/status - Update task status (optimized for drag-and-drop)
export async function PATCH(request: NextRequest, { params }: { params: { id: string } }) {
  try {
    const sessionToken = request.cookies.get("session-token")?.value

    if (!sessionToken) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 })
    }

    const user = await AuthService.verifySession(sessionToken)

    if (!user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 })
    }

    const { id } = params

    // Parse and validate request body
    const body = await request.json()
    const { status, position } = statusUpdateSchema.parse(body)

    // Check if task exists and user has permission
    const existingTaskResult = await serverDb.sql`
      SELECT * FROM tasks WHERE id = ${id}
    `

    if (existingTaskResult.length === 0) {
      return NextResponse.json({ error: "Task not found" }, { status: 404 })
    }

    const existingTask = existingTaskResult[0]

    // Check permissions
    const canEdit = 
      ["admin", "manager"].includes(user.role) ||
      existingTask.assigned_to === user.id ||
      existingTask.assigned_by === user.id

    if (!canEdit) {
      return NextResponse.json({ error: "Access denied" }, { status: 403 })
    }

    // Start transaction for position updates
    await serverDb.sql`BEGIN`

    try {
      // If position is specified, we need to reorder tasks in the target column
      if (position !== undefined) {
        // First, make room for the new task by incrementing positions
        await serverDb.sql`
          UPDATE tasks 
          SET position = position + 1 
          WHERE status = ${status} AND position >= ${position} AND id != ${id}
        `
      }

      // Determine the new position if not specified
      let finalPosition = position
      if (finalPosition === undefined) {
        const maxPositionResult = await serverDb.sql`
          SELECT COALESCE(MAX(position), 0) + 1 as next_position
          FROM tasks
          WHERE status = ${status}
        `
        finalPosition = maxPositionResult[0]?.next_position || 1
      }

      // Update the task status and position
      const updateResult = await serverDb.sql`
        UPDATE tasks 
        SET 
          status = ${status},
          position = ${finalPosition},
          completed_at = CASE 
            WHEN ${status} = 'completed' AND status != 'completed' THEN NOW()
            WHEN ${status} != 'completed' THEN NULL
            ELSE completed_at
          END,
          updated_at = NOW()
        WHERE id = ${id}
        RETURNING *
      `

      // Clean up positions in the old column (remove gaps)
      if (existingTask.status !== status) {
        await serverDb.sql`
          UPDATE tasks 
          SET position = (
            SELECT ROW_NUMBER() OVER (ORDER BY position, created_at)
            FROM tasks t2 
            WHERE t2.status = ${existingTask.status} AND t2.id = tasks.id
          )
          WHERE status = ${existingTask.status}
        `
      }

      // Commit transaction
      await serverDb.sql`COMMIT`

      // Get the complete updated task data
      const completeTaskResult = await serverDb.sql`
        SELECT 
          t.*,
          assigned_user.full_name as assigned_to_name,
          assigned_user.email as assigned_to_email,
          created_user.full_name as created_by_name,
          created_user.email as created_by_email,
          p.name as project_name,
          p.color as project_color
        FROM tasks t
        LEFT JOIN users assigned_user ON t.assigned_to = assigned_user.id
        LEFT JOIN users created_user ON t.assigned_by = created_user.id
        LEFT JOIN task_projects p ON t.project_id = p.id
        WHERE t.id = ${id}
      `

      return NextResponse.json({
        success: true,
        data: completeTaskResult[0],
        message: "Task status updated successfully"
      })

    } catch (error) {
      // Rollback transaction on error
      await serverDb.sql`ROLLBACK`
      throw error
    }

  } catch (error) {
    console.error("Update task status error:", error)
    
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: "Invalid status data", details: error.errors },
        { status: 400 }
      )
    }

    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    )
  }
}
