// Enhanced Allowance Management Interface
// Integrates with Phase 1 allowance APIs for comprehensive allowance management

"use client"

import React, { useState, useEffect } from 'react'
import {
  Plus,
  Edit,
  Trash2,
  DollarSign,
  Users,
  TrendingUp,
  Award,
  Search,
  Filter,
  CheckCircle,
  Clock,
  AlertCircle,
  Eye,
  Settings,
  Download
} from 'lucide-react'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar'
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog'
import { CurrencyDisplay } from '@/components/ui/currency-display'
import { toast } from '@/hooks/use-toast'

interface AllowanceComponent {
  id: string
  name: string
  code: string
  category: string
  calculation_type: string
  fixed_amount?: number
  percentage?: number
  percentage_base?: string
  is_taxable: boolean
  is_statutory: boolean
  description?: string
  is_active: boolean
  effective_from: string
  effective_to?: string
}

interface EmployeeAllowanceAssignment {
  assignment_id: string
  user_id: string
  component_id: string
  is_active: boolean
  effective_from: string
  effective_to?: string
  override_amount?: number
  override_percentage?: number
  assigned_by: string
  approved_by?: string
  approval_date?: string
  notes?: string
  created_at: string
  name: string
  code: string
  category: string
  calculation_type: string
  fixed_amount?: number
  percentage?: number
  percentage_base?: string
  description?: string
  employee_name: string
  assigned_by_name?: string
  approved_by_name?: string
}

interface Employee {
  id: string
  full_name: string
  employee_id: string
  department: string
  position: string
  salary: number
  employment_status: string
}

export function EnhancedAllowanceManagement() {
  const [allowanceComponents, setAllowanceComponents] = useState<AllowanceComponent[]>([])
  const [employeeAssignments, setEmployeeAssignments] = useState<EmployeeAllowanceAssignment[]>([])
  const [employees, setEmployees] = useState<Employee[]>([])
  const [loading, setLoading] = useState(true)
  const [searchTerm, setSearchTerm] = useState('')
  const [categoryFilter, setCategoryFilter] = useState('all')
  const [activeTab, setActiveTab] = useState('assignments')
  
  // Dialog states
  const [showAssignDialog, setShowAssignDialog] = useState(false)
  const [showComponentDialog, setShowComponentDialog] = useState(false)
  const [editingAssignment, setEditingAssignment] = useState<EmployeeAllowanceAssignment | null>(null)
  const [editingComponent, setEditingComponent] = useState<AllowanceComponent | null>(null)
  
  // Form states
  const [assignmentForm, setAssignmentForm] = useState({
    userId: '',
    componentId: '',
    effectiveFrom: new Date().toISOString().split('T')[0],
    effectiveTo: '',
    overrideAmount: '',
    overridePercentage: '',
    notes: '',
    requiresApproval: false
  })

  const [componentForm, setComponentForm] = useState({
    name: '',
    code: '',
    category: 'company_policy',
    calculation_type: 'fixed',
    fixed_amount: '',
    percentage: '',
    percentage_base: 'base_salary',
    is_taxable: false,
    is_statutory: false,
    description: ''
  })

  useEffect(() => {
    loadAllowanceData()
  }, [])

  const loadAllowanceData = async () => {
    try {
      setLoading(true)
      
      // Load available allowance components
      const componentsResponse = await fetch('/api/admin/payroll/allowances?action=available_allowances')
      const componentsData = await componentsResponse.json()
      
      if (componentsData.success) {
        setAllowanceComponents(componentsData.data)
      }

      // Load all employee assignments
      const assignmentsResponse = await fetch('/api/admin/payroll/allowances?action=all_assignments&limit=100')
      const assignmentsData = await assignmentsResponse.json()
      
      if (assignmentsData.success) {
        setEmployeeAssignments(assignmentsData.data.assignments)
      }

      // Load employees for assignment dropdown
      const employeesResponse = await fetch('/api/admin/payroll/bulk-process?action=eligible_employees')
      const employeesData = await employeesResponse.json()
      
      if (employeesData.success) {
        setEmployees(employeesData.data.employees)
      }

    } catch (error) {
      console.error('Error loading allowance data:', error)
      toast({
        title: "Error",
        description: "Failed to load allowance data",
        variant: "destructive"
      })
    } finally {
      setLoading(false)
    }
  }

  const handleCreateAssignment = async () => {
    try {
      const response = await fetch('/api/admin/payroll/allowances', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          userId: assignmentForm.userId,
          componentId: assignmentForm.componentId,
          effectiveFrom: assignmentForm.effectiveFrom,
          effectiveTo: assignmentForm.effectiveTo || null,
          overrideAmount: assignmentForm.overrideAmount ? parseFloat(assignmentForm.overrideAmount) : null,
          overridePercentage: assignmentForm.overridePercentage ? parseFloat(assignmentForm.overridePercentage) : null,
          notes: assignmentForm.notes,
          requiresApproval: assignmentForm.requiresApproval
        })
      })

      const data = await response.json()

      if (data.success) {
        toast({
          title: "Success",
          description: data.message
        })
        setShowAssignDialog(false)
        resetAssignmentForm()
        loadAllowanceData()
      } else {
        throw new Error(data.error)
      }
    } catch (error) {
      console.error('Error creating assignment:', error)
      toast({
        title: "Error",
        description: "Failed to create allowance assignment",
        variant: "destructive"
      })
    }
  }

  const handleApproveAssignment = async (assignmentId: string) => {
    try {
      const response = await fetch('/api/admin/payroll/allowances', {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          assignmentId,
          action: 'approve'
        })
      })

      const data = await response.json()

      if (data.success) {
        toast({
          title: "Success",
          description: "Allowance assignment approved"
        })
        loadAllowanceData()
      } else {
        throw new Error(data.error)
      }
    } catch (error) {
      console.error('Error approving assignment:', error)
      toast({
        title: "Error",
        description: "Failed to approve assignment",
        variant: "destructive"
      })
    }
  }

  const handleDeleteAssignment = async (assignmentId: string) => {
    try {
      const response = await fetch(`/api/admin/payroll/allowances?assignmentId=${assignmentId}`, {
        method: 'DELETE'
      })

      const data = await response.json()

      if (data.success) {
        toast({
          title: "Success",
          description: "Allowance assignment removed"
        })
        loadAllowanceData()
      } else {
        throw new Error(data.error)
      }
    } catch (error) {
      console.error('Error deleting assignment:', error)
      toast({
        title: "Error",
        description: "Failed to remove assignment",
        variant: "destructive"
      })
    }
  }

  const resetAssignmentForm = () => {
    setAssignmentForm({
      userId: '',
      componentId: '',
      effectiveFrom: new Date().toISOString().split('T')[0],
      effectiveTo: '',
      overrideAmount: '',
      overridePercentage: '',
      notes: '',
      requiresApproval: false
    })
  }

  const filteredAssignments = employeeAssignments.filter(assignment => {
    const matchesSearch = assignment.employee_name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         assignment.name.toLowerCase().includes(searchTerm.toLowerCase())
    const matchesCategory = categoryFilter === 'all' || assignment.category === categoryFilter
    return matchesSearch && matchesCategory
  })

  const getStatusBadge = (assignment: EmployeeAllowanceAssignment) => {
    if (!assignment.approved_by) {
      return <Badge variant="outline">Pending Approval</Badge>
    }
    if (!assignment.is_active) {
      return <Badge variant="secondary">Inactive</Badge>
    }
    return <Badge variant="default">Active</Badge>
  }

  const calculateAmount = (assignment: EmployeeAllowanceAssignment, employee?: Employee) => {
    if (assignment.override_amount) {
      return assignment.override_amount
    }
    
    if (assignment.calculation_type === 'fixed' && assignment.fixed_amount) {
      return assignment.fixed_amount
    }
    
    if (assignment.calculation_type === 'percentage' && assignment.percentage && employee) {
      return (employee.salary * assignment.percentage) / 100
    }
    
    return 0
  }

  if (loading) {
    return <div className="flex items-center justify-center h-64">Loading allowance data...</div>
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold">Allowance Management</h1>
          <p className="text-muted-foreground">Manage employee allowances and assignments</p>
        </div>
        <div className="flex items-center space-x-2">
          <Button onClick={() => setShowAssignDialog(true)}>
            <Plus className="h-4 w-4 mr-2" />
            Assign Allowance
          </Button>
          <Button variant="outline">
            <Download className="h-4 w-4 mr-2" />
            Export
          </Button>
        </div>
      </div>

      {/* Summary Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Allowances</CardTitle>
            <Award className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{allowanceComponents.length}</div>
            <p className="text-xs text-muted-foreground">Available allowance types</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Active Assignments</CardTitle>
            <Users className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{employeeAssignments.filter(a => a.is_active).length}</div>
            <p className="text-xs text-muted-foreground">Current active assignments</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Pending Approvals</CardTitle>
            <Clock className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{employeeAssignments.filter(a => !a.approved_by).length}</div>
            <p className="text-xs text-muted-foreground">Awaiting approval</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Monthly Total</CardTitle>
            <DollarSign className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              <CurrencyDisplay 
                amount={employeeAssignments
                  .filter(a => a.is_active && a.approved_by)
                  .reduce((sum, a) => {
                    const employee = employees.find(e => e.id === a.user_id)
                    return sum + calculateAmount(a, employee)
                  }, 0)
                } 
              />
            </div>
            <p className="text-xs text-muted-foreground">Total monthly allowances</p>
          </CardContent>
        </Card>
      </div>

      {/* Main Content */}
      <Tabs value={activeTab} onValueChange={setActiveTab}>
        <TabsList>
          <TabsTrigger value="assignments">Employee Assignments</TabsTrigger>
          <TabsTrigger value="components">Allowance Types</TabsTrigger>
        </TabsList>

        <TabsContent value="assignments" className="space-y-4">
          {/* Search and Filters */}
          <div className="flex items-center space-x-4">
            <div className="relative flex-1">
              <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
              <Input
                placeholder="Search employees or allowances..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-8"
              />
            </div>
            <Select value={categoryFilter} onValueChange={setCategoryFilter}>
              <SelectTrigger className="w-48">
                <SelectValue placeholder="Filter by category" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Categories</SelectItem>
                <SelectItem value="company_policy">Company Policy</SelectItem>
                <SelectItem value="statutory">Statutory</SelectItem>
                <SelectItem value="custom">Custom</SelectItem>
              </SelectContent>
            </Select>
          </div>

          {/* Assignments Table */}
          <Card>
            <CardContent className="p-0">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Employee</TableHead>
                    <TableHead>Allowance</TableHead>
                    <TableHead>Type</TableHead>
                    <TableHead>Amount</TableHead>
                    <TableHead>Status</TableHead>
                    <TableHead>Effective From</TableHead>
                    <TableHead>Actions</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {filteredAssignments.map((assignment) => {
                    const employee = employees.find(e => e.id === assignment.user_id)
                    const amount = calculateAmount(assignment, employee)
                    
                    return (
                      <TableRow key={assignment.assignment_id}>
                        <TableCell>
                          <div className="flex items-center space-x-2">
                            <Avatar className="h-8 w-8">
                              <AvatarFallback>
                                {assignment.employee_name.split(' ').map(n => n[0]).join('')}
                              </AvatarFallback>
                            </Avatar>
                            <div>
                              <div className="font-medium">{assignment.employee_name}</div>
                              <div className="text-sm text-muted-foreground">{employee?.department}</div>
                            </div>
                          </div>
                        </TableCell>
                        <TableCell>
                          <div>
                            <div className="font-medium">{assignment.name}</div>
                            <div className="text-sm text-muted-foreground">{assignment.code}</div>
                          </div>
                        </TableCell>
                        <TableCell>
                          <Badge variant="outline">{assignment.category}</Badge>
                        </TableCell>
                        <TableCell>
                          <CurrencyDisplay amount={amount} />
                          {assignment.calculation_type === 'percentage' && (
                            <div className="text-sm text-muted-foreground">
                              {assignment.percentage}% of {assignment.percentage_base}
                            </div>
                          )}
                        </TableCell>
                        <TableCell>{getStatusBadge(assignment)}</TableCell>
                        <TableCell>{new Date(assignment.effective_from).toLocaleDateString()}</TableCell>
                        <TableCell>
                          <div className="flex items-center space-x-2">
                            {!assignment.approved_by && (
                              <Button
                                size="sm"
                                onClick={() => handleApproveAssignment(assignment.assignment_id)}
                              >
                                <CheckCircle className="h-4 w-4" />
                              </Button>
                            )}
                            <Button
                              size="sm"
                              variant="outline"
                              onClick={() => setEditingAssignment(assignment)}
                            >
                              <Edit className="h-4 w-4" />
                            </Button>
                            <Button
                              size="sm"
                              variant="outline"
                              onClick={() => handleDeleteAssignment(assignment.assignment_id)}
                            >
                              <Trash2 className="h-4 w-4" />
                            </Button>
                          </div>
                        </TableCell>
                      </TableRow>
                    )
                  })}
                </TableBody>
              </Table>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="components" className="space-y-4">
          {/* Components Table */}
          <Card>
            <CardHeader>
              <CardTitle>Available Allowance Types</CardTitle>
              <CardDescription>Configure allowance types and their calculation methods</CardDescription>
            </CardHeader>
            <CardContent className="p-0">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Name</TableHead>
                    <TableHead>Code</TableHead>
                    <TableHead>Category</TableHead>
                    <TableHead>Calculation</TableHead>
                    <TableHead>Amount</TableHead>
                    <TableHead>Status</TableHead>
                    <TableHead>Actions</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {allowanceComponents.map((component) => (
                    <TableRow key={component.id}>
                      <TableCell>
                        <div>
                          <div className="font-medium">{component.name}</div>
                          <div className="text-sm text-muted-foreground">{component.description}</div>
                        </div>
                      </TableCell>
                      <TableCell>{component.code}</TableCell>
                      <TableCell>
                        <Badge variant="outline">{component.category}</Badge>
                      </TableCell>
                      <TableCell>{component.calculation_type}</TableCell>
                      <TableCell>
                        {component.calculation_type === 'fixed' && component.fixed_amount && (
                          <CurrencyDisplay amount={component.fixed_amount} />
                        )}
                        {component.calculation_type === 'percentage' && component.percentage && (
                          <span>{component.percentage}%</span>
                        )}
                      </TableCell>
                      <TableCell>
                        <Badge variant={component.is_active ? "default" : "secondary"}>
                          {component.is_active ? "Active" : "Inactive"}
                        </Badge>
                      </TableCell>
                      <TableCell>
                        <div className="flex items-center space-x-2">
                          <Button
                            size="sm"
                            variant="outline"
                            onClick={() => setEditingComponent(component)}
                          >
                            <Edit className="h-4 w-4" />
                          </Button>
                        </div>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>

      {/* Assignment Dialog */}
      <Dialog open={showAssignDialog} onOpenChange={setShowAssignDialog}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Assign Allowance</DialogTitle>
            <DialogDescription>Assign an allowance to an employee</DialogDescription>
          </DialogHeader>
          <div className="space-y-4">
            <div>
              <Label htmlFor="employee">Employee</Label>
              <Select value={assignmentForm.userId} onValueChange={(value) => 
                setAssignmentForm(prev => ({ ...prev, userId: value }))
              }>
                <SelectTrigger>
                  <SelectValue placeholder="Select employee" />
                </SelectTrigger>
                <SelectContent>
                  {employees.map((employee) => (
                    <SelectItem key={employee.id} value={employee.id}>
                      {employee.full_name} - {employee.department}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
            
            <div>
              <Label htmlFor="allowance">Allowance Type</Label>
              <Select value={assignmentForm.componentId} onValueChange={(value) => 
                setAssignmentForm(prev => ({ ...prev, componentId: value }))
              }>
                <SelectTrigger>
                  <SelectValue placeholder="Select allowance" />
                </SelectTrigger>
                <SelectContent>
                  {allowanceComponents.map((component) => (
                    <SelectItem key={component.id} value={component.id}>
                      {component.name} ({component.code})
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div className="grid grid-cols-2 gap-4">
              <div>
                <Label htmlFor="effectiveFrom">Effective From</Label>
                <Input
                  id="effectiveFrom"
                  type="date"
                  value={assignmentForm.effectiveFrom}
                  onChange={(e) => setAssignmentForm(prev => ({ ...prev, effectiveFrom: e.target.value }))}
                />
              </div>
              <div>
                <Label htmlFor="effectiveTo">Effective To (Optional)</Label>
                <Input
                  id="effectiveTo"
                  type="date"
                  value={assignmentForm.effectiveTo}
                  onChange={(e) => setAssignmentForm(prev => ({ ...prev, effectiveTo: e.target.value }))}
                />
              </div>
            </div>

            <div className="grid grid-cols-2 gap-4">
              <div>
                <Label htmlFor="overrideAmount">Override Amount (Optional)</Label>
                <Input
                  id="overrideAmount"
                  type="number"
                  placeholder="Override fixed amount"
                  value={assignmentForm.overrideAmount}
                  onChange={(e) => setAssignmentForm(prev => ({ ...prev, overrideAmount: e.target.value }))}
                />
              </div>
              <div>
                <Label htmlFor="overridePercentage">Override Percentage (Optional)</Label>
                <Input
                  id="overridePercentage"
                  type="number"
                  placeholder="Override percentage"
                  value={assignmentForm.overridePercentage}
                  onChange={(e) => setAssignmentForm(prev => ({ ...prev, overridePercentage: e.target.value }))}
                />
              </div>
            </div>

            <div>
              <Label htmlFor="notes">Notes</Label>
              <Input
                id="notes"
                placeholder="Assignment notes"
                value={assignmentForm.notes}
                onChange={(e) => setAssignmentForm(prev => ({ ...prev, notes: e.target.value }))}
              />
            </div>

            <div className="flex items-center space-x-2">
              <input
                type="checkbox"
                id="requiresApproval"
                checked={assignmentForm.requiresApproval}
                onChange={(e) => setAssignmentForm(prev => ({ ...prev, requiresApproval: e.target.checked }))}
              />
              <Label htmlFor="requiresApproval">Requires approval</Label>
            </div>
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setShowAssignDialog(false)}>
              Cancel
            </Button>
            <Button onClick={handleCreateAssignment}>
              Assign Allowance
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  )
}
