import { createClient } from "@supabase/supabase-js"

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!
const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!

export const supabase = createClient(supabaseUrl, supabaseAnonKey)

// Types for our database
export type UserRole = "admin" | "hr_manager" | "manager" | "staff"
export type AttendanceStatus = "present" | "absent" | "late" | "half_day" | "on_leave"
export type TaskStatus = "todo" | "in_progress" | "completed" | "cancelled"
export type TaskPriority = "low" | "medium" | "high" | "urgent"
export type PayrollStatus = "draft" | "processed" | "paid"

export interface User {
  id: string
  email: string
  full_name: string
  role: UserRole
  department?: string
  position?: string
  phone?: string
  hire_date?: string
  salary?: number
  is_active: boolean
  created_at: string
  updated_at: string
}

export interface Attendance {
  id: string
  user_id: string
  date: string
  check_in_time?: string
  check_out_time?: string
  status: AttendanceStatus
  hours_worked?: number
  notes?: string
  created_by?: string
  created_at: string
  updated_at: string
}

export interface Task {
  id: string
  title: string
  description?: string
  assigned_to?: string
  assigned_by: string
  status: TaskStatus
  priority: TaskPriority
  due_date?: string
  completed_at?: string
  created_at: string
  updated_at: string
}

export interface Payroll {
  id: string
  user_id: string
  pay_period_start: string
  pay_period_end: string
  base_salary: number
  overtime_hours: number
  overtime_rate: number
  bonuses: number
  deductions: number
  gross_pay: number
  tax_deductions: number
  net_pay: number
  status: PayrollStatus
  processed_by?: string
  processed_at?: string
  created_at: string
}

export interface Permission {
  id: string
  name: string
  description?: string
  resource: string
  action: string
  created_at: string
}
