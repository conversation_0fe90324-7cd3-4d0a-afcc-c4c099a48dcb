import { type NextRequest, NextResponse } from "next/server"
import { AuthService } from "@/lib/auth-utils"
import { serverDb } from "@/lib/server-db"

// GET /api/admin/users/[id] - Get user details
export async function GET(request: NextRequest, { params }: { params: { id: string } }) {
  try {
    const sessionToken = request.cookies.get("session-token")?.value

    if (!sessionToken) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 })
    }

    const currentUser = await AuthService.verifySession(sessionToken)

    if (!currentUser || !["admin", "hr_manager"].includes(currentUser.role)) {
      return NextResponse.json({ error: "Forbidden" }, { status: 403 })
    }

    const user = await serverDb.getUserWithAddress(params.id)

    if (!user) {
      return NextResponse.json({ error: "User not found" }, { status: 404 })
    }

    // Remove sensitive information
    const { password_hash, ...userWithoutPassword } = user as any
    
    return NextResponse.json(userWithoutPassword)
  } catch (error) {
    console.error("Get user API error:", error)
    return NextResponse.json({ error: "Internal server error" }, { status: 500 })
  }
}

// PUT /api/admin/users/[id] - Update user
export async function PUT(request: NextRequest, { params }: { params: { id: string } }) {
  try {
    const sessionToken = request.cookies.get("session-token")?.value

    if (!sessionToken) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 })
    }

    const currentUser = await AuthService.verifySession(sessionToken)

    if (!currentUser || !["admin", "hr_manager"].includes(currentUser.role)) {
      return NextResponse.json({ error: "Forbidden" }, { status: 403 })
    }

    const updateData = await request.json()

    // Remove fields that shouldn't be updated via this endpoint
    const { id, password_hash, created_at, updated_at, ...allowedFields } = updateData

    const updatedUser = await serverDb.updateUser(params.id, allowedFields)

    if (!updatedUser) {
      return NextResponse.json({ error: "User not found" }, { status: 404 })
    }

    // Remove sensitive information
    const { password_hash: _, ...userWithoutPassword } = updatedUser as any
    
    return NextResponse.json(userWithoutPassword)
  } catch (error) {
    console.error("Update user API error:", error)
    return NextResponse.json({ error: "Internal server error" }, { status: 500 })
  }
}

// DELETE /api/admin/users/[id] - Deactivate user (soft delete)
export async function DELETE(request: NextRequest, { params }: { params: { id: string } }) {
  try {
    const sessionToken = request.cookies.get("session-token")?.value

    if (!sessionToken) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 })
    }

    const currentUser = await AuthService.verifySession(sessionToken)

    if (!currentUser || currentUser.role !== "admin") {
      return NextResponse.json({ error: "Forbidden" }, { status: 403 })
    }

    // Prevent admin from deactivating themselves
    if (currentUser.id === params.id) {
      return NextResponse.json({ error: "Cannot deactivate your own account" }, { status: 400 })
    }

    const updatedUser = await serverDb.updateUser(params.id, { 
      is_active: false,
      employment_status: 'terminated',
      termination_date: new Date().toISOString().split('T')[0]
    })

    if (!updatedUser) {
      return NextResponse.json({ error: "User not found" }, { status: 404 })
    }

    return NextResponse.json({ message: "User deactivated successfully" })
  } catch (error) {
    console.error("Delete user API error:", error)
    return NextResponse.json({ error: "Internal server error" }, { status: 500 })
  }
}

// PATCH /api/admin/users/[id] - Toggle user status
export async function PATCH(request: NextRequest, { params }: { params: { id: string } }) {
  try {
    const sessionToken = request.cookies.get("session-token")?.value

    if (!sessionToken) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 })
    }

    const currentUser = await AuthService.verifySession(sessionToken)

    if (!currentUser || !["admin", "hr_manager"].includes(currentUser.role)) {
      return NextResponse.json({ error: "Forbidden" }, { status: 403 })
    }

    // Prevent admin from deactivating themselves
    if (currentUser.id === params.id) {
      return NextResponse.json({ error: "Cannot change your own status" }, { status: 400 })
    }

    const { is_active } = await request.json()

    if (typeof is_active !== 'boolean') {
      return NextResponse.json({ error: "is_active must be a boolean" }, { status: 400 })
    }

    const updateData: any = { is_active }

    // If deactivating, set employment status and termination date
    if (!is_active) {
      updateData.employment_status = 'terminated'
      updateData.termination_date = new Date().toISOString().split('T')[0]
    } else {
      // If reactivating, clear termination data
      updateData.employment_status = 'active'
      updateData.termination_date = null
    }

    const updatedUser = await serverDb.updateUser(params.id, updateData)

    if (!updatedUser) {
      return NextResponse.json({ error: "User not found" }, { status: 404 })
    }

    // Remove sensitive information
    const { password_hash: _, ...userWithoutPassword } = updatedUser as any

    return NextResponse.json({
      message: `User ${is_active ? 'activated' : 'deactivated'} successfully`,
      user: userWithoutPassword
    })
  } catch (error) {
    console.error("Toggle user status API error:", error)
    return NextResponse.json({ error: "Internal server error" }, { status: 500 })
  }
}
