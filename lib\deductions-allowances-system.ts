// Flexible Deductions & Allowances System
// Phase 2: Core Payroll Engine Development - Deductions & Allowances

import { db } from './neon';
import { nepalConfig } from './nepal-config';

export interface DeductionAllowanceComponent {
  id?: string;
  name: string;
  code: string;
  type: 'deduction' | 'allowance';
  category: 'statutory' | 'voluntary' | 'company_policy' | 'custom';
  calculationType: 'fixed' | 'percentage' | 'formula' | 'conditional';
  
  // Calculation parameters
  fixedAmount?: number;
  percentage?: number;
  percentageBase?: 'base_salary' | 'gross_pay' | 'net_pay' | 'total_earnings';
  formula?: string;
  conditions?: ComponentCondition[];
  
  // Tax and compliance
  isTaxable: boolean;
  isStatutory: boolean;
  affectsProvidentFund: boolean;
  affectsGratuity: boolean;
  
  // Applicability
  applicableToPayStructures: string[];
  applicableToEmployeeCategories: string[];
  applicableToDepartments: string[];
  
  // Limits and validations
  minimumAmount?: number;
  maximumAmount?: number;
  minimumSalaryThreshold?: number;
  maximumSalaryThreshold?: number;
  
  // Metadata
  description?: string;
  isActive: boolean;
  effectiveFrom: string;
  effectiveTo?: string;
  createdBy: string;
  approvedBy?: string;
}

export interface ComponentCondition {
  field: string;
  operator: 'equals' | 'greater_than' | 'less_than' | 'between' | 'in' | 'not_in';
  value: any;
  logicalOperator?: 'AND' | 'OR';
}

export interface EmployeeComponentAssignment {
  id?: string;
  userId: string;
  componentId: string;
  isActive: boolean;
  effectiveFrom: string;
  effectiveTo?: string;
  
  // Override values
  overrideAmount?: number;
  overridePercentage?: number;
  overrideConditions?: ComponentCondition[];
  
  // Approval
  assignedBy: string;
  approvedBy?: string;
  approvalDate?: string;
  notes?: string;
}

export interface ComponentCalculationInput {
  userId: string;
  payrollPeriod: {
    start: string;
    end: string;
  };
  salaryData: {
    baseSalary: number;
    grossPay: number;
    totalEarnings: number;
    regularHours: number;
    overtimeHours: number;
  };
  employeeData: {
    category: string;
    department: string;
    payStructureType: string;
    hireDate: string;
    isActive: boolean;
  };
  attendanceData: {
    workingDays: number;
    presentDays: number;
    attendanceRate: number;
  };
}

export interface ComponentCalculationResult {
  componentId: string;
  componentName: string;
  componentCode: string;
  type: 'deduction' | 'allowance';
  category: string;
  
  // Calculation details
  calculationType: string;
  calculationBase: number;
  calculationRate?: number;
  calculatedAmount: number;
  finalAmount: number;
  
  // Applied conditions
  conditionsApplied: string[];
  limitsApplied: string[];
  
  // Tax impact
  isTaxable: boolean;
  taxableAmount: number;
  
  // Metadata
  calculatedAt: string;
  calculationNotes: string[];
}

export class DeductionsAllowancesSystem {
  private nepalConfig = nepalConfig;

  /**
   * Create a new deduction/allowance component
   */
  async createComponent(component: Omit<DeductionAllowanceComponent, 'id'>): Promise<DeductionAllowanceComponent> {
    try {
      // Validate component
      this.validateComponent(component);

      // Check for duplicate codes
      const existing = await this.getComponentByCode(component.code);
      if (existing) {
        throw new Error(`Component with code '${component.code}' already exists`);
      }

      // Insert into database
      const result = await db.sql`
        INSERT INTO payroll_components_master (
          name, code, type, category, calculation_type, fixed_amount, percentage,
          percentage_base, formula, conditions, is_taxable, is_statutory,
          affects_provident_fund, affects_gratuity, applicable_to_pay_structures,
          applicable_to_employee_categories, applicable_to_departments,
          minimum_amount, maximum_amount, minimum_salary_threshold, maximum_salary_threshold,
          description, is_active, effective_from, effective_to, created_by, approved_by
        )
        VALUES (
          ${component.name}, ${component.code}, ${component.type}, ${component.category},
          ${component.calculationType}, ${component.fixedAmount || null}, ${component.percentage || null},
          ${component.percentageBase || null}, ${component.formula || null}, 
          ${JSON.stringify(component.conditions || [])}, ${component.isTaxable}, ${component.isStatutory},
          ${component.affectsProvidentFund}, ${component.affectsGratuity},
          ${JSON.stringify(component.applicableToPayStructures)},
          ${JSON.stringify(component.applicableToEmployeeCategories)},
          ${JSON.stringify(component.applicableToDepartments)},
          ${component.minimumAmount || null}, ${component.maximumAmount || null},
          ${component.minimumSalaryThreshold || null}, ${component.maximumSalaryThreshold || null},
          ${component.description || null}, ${component.isActive}, ${component.effectiveFrom},
          ${component.effectiveTo || null}, ${component.createdBy}, ${component.approvedBy || null}
        )
        RETURNING *
      `;

      return this.mapDatabaseToComponent(result[0]);
    } catch (error) {
      console.error('Error creating component:', error);
      throw new Error(`Failed to create component: ${error.message}`);
    }
  }

  /**
   * Assign component to employee
   */
  async assignComponentToEmployee(assignment: Omit<EmployeeComponentAssignment, 'id'>): Promise<EmployeeComponentAssignment> {
    try {
      // Validate assignment
      await this.validateComponentAssignment(assignment);

      // Check for existing active assignment
      const existing = await this.getActiveComponentAssignment(assignment.userId, assignment.componentId);
      if (existing) {
        // Deactivate existing assignment
        await this.deactivateComponentAssignment(existing.id!);
      }

      // Insert new assignment
      const result = await db.sql`
        INSERT INTO employee_component_assignments (
          user_id, component_id, is_active, effective_from, effective_to,
          override_amount, override_percentage, override_conditions,
          assigned_by, approved_by, approval_date, notes
        )
        VALUES (
          ${assignment.userId}, ${assignment.componentId}, ${assignment.isActive},
          ${assignment.effectiveFrom}, ${assignment.effectiveTo || null},
          ${assignment.overrideAmount || null}, ${assignment.overridePercentage || null},
          ${JSON.stringify(assignment.overrideConditions || [])},
          ${assignment.assignedBy}, ${assignment.approvedBy || null},
          ${assignment.approvalDate || null}, ${assignment.notes || null}
        )
        RETURNING *
      `;

      return this.mapDatabaseToAssignment(result[0]);
    } catch (error) {
      console.error('Error assigning component:', error);
      throw new Error(`Failed to assign component: ${error.message}`);
    }
  }

  /**
   * Calculate all applicable deductions and allowances for an employee
   */
  async calculateEmployeeComponents(input: ComponentCalculationInput): Promise<{
    allowances: ComponentCalculationResult[];
    deductions: ComponentCalculationResult[];
    summary: {
      totalAllowances: number;
      totalDeductions: number;
      taxableAllowances: number;
      taxableDeductions: number;
      statutoryDeductions: number;
    };
  }> {
    try {
      // Get all active component assignments for the employee
      const assignments = await this.getActiveEmployeeAssignments(input.userId);

      const allowances: ComponentCalculationResult[] = [];
      const deductions: ComponentCalculationResult[] = [];

      // Calculate each component
      for (const assignment of assignments) {
        const component = await this.getComponentById(assignment.componentId);
        if (!component || !component.isActive) continue;

        // Check if component is applicable for this period
        if (!this.isComponentApplicable(component, input)) continue;

        // Calculate component amount
        const result = await this.calculateComponentAmount(component, assignment, input);
        
        if (component.type === 'allowance') {
          allowances.push(result);
        } else {
          deductions.push(result);
        }
      }

      // Calculate summary
      const summary = this.calculateComponentSummary(allowances, deductions);

      return { allowances, deductions, summary };
    } catch (error) {
      console.error('Error calculating employee components:', error);
      throw new Error(`Failed to calculate components: ${error.message}`);
    }
  }

  /**
   * Calculate individual component amount
   */
  private async calculateComponentAmount(
    component: DeductionAllowanceComponent,
    assignment: EmployeeComponentAssignment,
    input: ComponentCalculationInput
  ): Promise<ComponentCalculationResult> {
    let calculatedAmount = 0;
    let calculationBase = 0;
    let calculationRate = 0;
    const calculationNotes: string[] = [];
    const conditionsApplied: string[] = [];
    const limitsApplied: string[] = [];

    // Determine calculation base
    switch (component.percentageBase || 'base_salary') {
      case 'base_salary':
        calculationBase = input.salaryData.baseSalary;
        break;
      case 'gross_pay':
        calculationBase = input.salaryData.grossPay;
        break;
      case 'total_earnings':
        calculationBase = input.salaryData.totalEarnings;
        break;
      default:
        calculationBase = input.salaryData.baseSalary;
    }

    // Calculate amount based on type
    switch (component.calculationType) {
      case 'fixed':
        calculatedAmount = assignment.overrideAmount || component.fixedAmount || 0;
        calculationNotes.push(`Fixed amount: ${calculatedAmount}`);
        break;

      case 'percentage':
        calculationRate = assignment.overridePercentage || component.percentage || 0;
        calculatedAmount = (calculationBase * calculationRate) / 100;
        calculationNotes.push(`${calculationRate}% of ${component.percentageBase}: ${calculatedAmount}`);
        break;

      case 'formula':
        calculatedAmount = this.evaluateFormula(component.formula || '', input);
        calculationNotes.push(`Formula calculation: ${calculatedAmount}`);
        break;

      case 'conditional':
        const conditionResult = this.evaluateConditions(component.conditions || [], input);
        if (conditionResult.isApplicable) {
          calculatedAmount = conditionResult.amount;
          conditionsApplied.push(...conditionResult.appliedConditions);
        }
        break;
    }

    // Apply limits
    let finalAmount = calculatedAmount;
    
    if (component.minimumAmount && finalAmount < component.minimumAmount) {
      finalAmount = component.minimumAmount;
      limitsApplied.push(`Minimum limit applied: ${component.minimumAmount}`);
    }
    
    if (component.maximumAmount && finalAmount > component.maximumAmount) {
      finalAmount = component.maximumAmount;
      limitsApplied.push(`Maximum limit applied: ${component.maximumAmount}`);
    }

    // Check salary thresholds
    if (component.minimumSalaryThreshold && input.salaryData.baseSalary < component.minimumSalaryThreshold) {
      finalAmount = 0;
      limitsApplied.push(`Below minimum salary threshold: ${component.minimumSalaryThreshold}`);
    }

    if (component.maximumSalaryThreshold && input.salaryData.baseSalary > component.maximumSalaryThreshold) {
      finalAmount = 0;
      limitsApplied.push(`Above maximum salary threshold: ${component.maximumSalaryThreshold}`);
    }

    // Calculate taxable amount
    const taxableAmount = component.isTaxable ? finalAmount : 0;

    return {
      componentId: component.id!,
      componentName: component.name,
      componentCode: component.code,
      type: component.type,
      category: component.category,
      calculationType: component.calculationType,
      calculationBase,
      calculationRate,
      calculatedAmount: Math.round(calculatedAmount * 100) / 100,
      finalAmount: Math.round(finalAmount * 100) / 100,
      conditionsApplied,
      limitsApplied,
      isTaxable: component.isTaxable,
      taxableAmount: Math.round(taxableAmount * 100) / 100,
      calculatedAt: new Date().toISOString(),
      calculationNotes
    };
  }

  /**
   * Check if component is applicable for the given input
   */
  private isComponentApplicable(component: DeductionAllowanceComponent, input: ComponentCalculationInput): boolean {
    // Check pay structure applicability
    if (component.applicableToPayStructures.length > 0 && 
        !component.applicableToPayStructures.includes(input.employeeData.payStructureType)) {
      return false;
    }

    // Check employee category applicability
    if (component.applicableToEmployeeCategories.length > 0 && 
        !component.applicableToEmployeeCategories.includes(input.employeeData.category)) {
      return false;
    }

    // Check department applicability
    if (component.applicableToDepartments.length > 0 && 
        !component.applicableToDepartments.includes(input.employeeData.department)) {
      return false;
    }

    // Check effective dates
    const periodStart = new Date(input.payrollPeriod.start);
    const effectiveFrom = new Date(component.effectiveFrom);
    const effectiveTo = component.effectiveTo ? new Date(component.effectiveTo) : null;

    if (periodStart < effectiveFrom) return false;
    if (effectiveTo && periodStart > effectiveTo) return false;

    return true;
  }

  /**
   * Evaluate formula-based calculations
   */
  private evaluateFormula(formula: string, input: ComponentCalculationInput): number {
    try {
      // Simple formula evaluation - in production, use a proper expression evaluator
      // This is a simplified version for demonstration
      let result = formula;
      
      // Replace variables with actual values
      result = result.replace(/\{base_salary\}/g, input.salaryData.baseSalary.toString());
      result = result.replace(/\{gross_pay\}/g, input.salaryData.grossPay.toString());
      result = result.replace(/\{working_days\}/g, input.attendanceData.workingDays.toString());
      result = result.replace(/\{attendance_rate\}/g, input.attendanceData.attendanceRate.toString());
      
      // Evaluate the expression (WARNING: In production, use a safe expression evaluator)
      return eval(result) || 0;
    } catch (error) {
      console.error('Error evaluating formula:', error);
      return 0;
    }
  }

  /**
   * Evaluate conditional calculations
   */
  private evaluateConditions(conditions: ComponentCondition[], input: ComponentCalculationInput): {
    isApplicable: boolean;
    amount: number;
    appliedConditions: string[];
  } {
    // Simplified condition evaluation
    // In production, implement a proper condition engine
    return {
      isApplicable: true,
      amount: 0,
      appliedConditions: []
    };
  }

  /**
   * Calculate component summary
   */
  private calculateComponentSummary(allowances: ComponentCalculationResult[], deductions: ComponentCalculationResult[]): {
    totalAllowances: number;
    totalDeductions: number;
    taxableAllowances: number;
    taxableDeductions: number;
    statutoryDeductions: number;
  } {
    const totalAllowances = allowances.reduce((sum, a) => sum + a.finalAmount, 0);
    const totalDeductions = deductions.reduce((sum, d) => sum + d.finalAmount, 0);
    const taxableAllowances = allowances.reduce((sum, a) => sum + a.taxableAmount, 0);
    const taxableDeductions = deductions.reduce((sum, d) => sum + d.taxableAmount, 0);
    const statutoryDeductions = deductions
      .filter(d => d.category === 'statutory')
      .reduce((sum, d) => sum + d.finalAmount, 0);

    return {
      totalAllowances: Math.round(totalAllowances * 100) / 100,
      totalDeductions: Math.round(totalDeductions * 100) / 100,
      taxableAllowances: Math.round(taxableAllowances * 100) / 100,
      taxableDeductions: Math.round(taxableDeductions * 100) / 100,
      statutoryDeductions: Math.round(statutoryDeductions * 100) / 100
    };
  }

  // Helper methods for database operations
  private async getComponentByCode(code: string): Promise<DeductionAllowanceComponent | null> {
    // Implementation would query database
    return null;
  }

  private async getComponentById(id: string): Promise<DeductionAllowanceComponent | null> {
    // Implementation would query database
    return null;
  }

  private async getActiveEmployeeAssignments(userId: string): Promise<EmployeeComponentAssignment[]> {
    // Implementation would query database
    return [];
  }

  private async getActiveComponentAssignment(userId: string, componentId: string): Promise<EmployeeComponentAssignment | null> {
    // Implementation would query database
    return null;
  }

  private async deactivateComponentAssignment(id: string): Promise<void> {
    // Implementation would update database
  }

  private validateComponent(component: DeductionAllowanceComponent): void {
    if (!component.name) throw new Error('Component name is required');
    if (!component.code) throw new Error('Component code is required');
    if (!component.type) throw new Error('Component type is required');
  }

  private async validateComponentAssignment(assignment: EmployeeComponentAssignment): Promise<void> {
    if (!assignment.userId) throw new Error('User ID is required');
    if (!assignment.componentId) throw new Error('Component ID is required');
  }

  private mapDatabaseToComponent(row: any): DeductionAllowanceComponent {
    // Map database row to component object
    return {} as DeductionAllowanceComponent;
  }

  private mapDatabaseToAssignment(row: any): EmployeeComponentAssignment {
    // Map database row to assignment object
    return {} as EmployeeComponentAssignment;
  }
}

// Export singleton instance
export const deductionsAllowancesSystem = new DeductionsAllowancesSystem();
