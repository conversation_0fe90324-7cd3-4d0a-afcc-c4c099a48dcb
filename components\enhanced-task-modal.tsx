"use client"

import { useEffe<PERSON>, useState } from "react"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>alog<PERSON>eader, DialogTitle } from "@/components/ui/dialog"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Textarea } from "@/components/ui/textarea"
import { Label } from "@/components/ui/label"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { UserSelector } from "@/components/user-selector"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Badge } from "@/components/ui/badge"
import { Avatar, AvatarFallback } from "@/components/ui/avatar"
import { useForm } from "react-hook-form"
import { zodResolver } from "@hookform/resolvers/zod"
import { z } from "zod"
import { useCreateTask, useUpdateTask } from "@/hooks/use-tasks"
import { useProjectOptions } from "@/hooks/use-projects"
import { useActiveEmployees } from "@/hooks/use-employees"
import { Loader2, Calendar, User, Flag, Folder, Plus, X, Upload, Users, CheckSquare, Crown } from "lucide-react"
import { toast } from "@/hooks/use-toast"

// Form validation schema
const taskFormSchema = z.object({
  title: z.string().min(1, "Title is required").max(255, "Title too long"),
  description: z.string().optional(),
  priority: z.enum(["low", "medium", "high", "urgent"]).default("medium"),
  due_date: z.string().optional(),
  estimated_hours: z.coerce.number().positive().optional(),
  project_id: z.string().optional(),
  assigned_to: z.string().optional(),
})

type TaskFormData = z.infer<typeof taskFormSchema>

// Sub-task interface
interface SubTask {
  id?: string
  title: string
  description?: string
  assigned_to?: string
}

// File attachment interface
interface FileAttachment {
  id?: string
  file: File
  name: string
  size: number
  type: string
}

interface Task {
  id: string
  title: string
  description?: string
  priority: "low" | "medium" | "high" | "urgent"
  due_date?: string
  estimated_hours?: number
  assigned_to?: string
  assigned_to_name?: string
}

interface EnhancedTaskModalProps {
  isOpen: boolean
  onClose: () => void
  task?: Task | null
  currentUserId?: string
}

export function EnhancedTaskModal({ isOpen, onClose, task, currentUserId }: EnhancedTaskModalProps) {
  const createTaskMutation = useCreateTask()
  const updateTaskMutation = useUpdateTask()
  const projectOptions = useProjectOptions()
  const { data: employeesResponse } = useActiveEmployees()
  const employees = employeesResponse?.users || []

  // Multi-user assignment state
  const [selectedUsers, setSelectedUsers] = useState<string[]>([])
  const [primaryUser, setPrimaryUser] = useState<string>("")

  // Sub-tasks state
  const [subTasks, setSubTasks] = useState<SubTask[]>([])
  const [isAddingSubTask, setIsAddingSubTask] = useState(false)
  const [newSubTaskTitle, setNewSubTaskTitle] = useState("")
  const [newSubTaskDescription, setNewSubTaskDescription] = useState("")
  const [newSubTaskAssignee, setNewSubTaskAssignee] = useState("no-assignee")

  // File attachments state
  const [attachments, setAttachments] = useState<FileAttachment[]>([])

  const {
    register,
    handleSubmit,
    reset,
    setValue,
    watch,
    formState: { errors, isSubmitting },
  } = useForm<TaskFormData>({
    resolver: zodResolver(taskFormSchema),
    defaultValues: {
      title: "",
      description: "",
      priority: "medium",
      due_date: "",
      estimated_hours: undefined,
      project_id: "",
    },
  })

  const isEditing = !!task

  // Reset form when modal opens/closes or task changes
  useEffect(() => {
    if (isOpen) {
      if (task) {
        // Editing existing task
        reset({
          title: task.title,
          description: task.description || "",
          priority: task.priority,
          due_date: task.due_date ? task.due_date.split('T')[0] : "", // Format for date input
          estimated_hours: task.estimated_hours,
        })
        // Reset other states for editing
        setSelectedUsers(task.assigned_to ? [task.assigned_to] : [])
        setPrimaryUser(task.assigned_to || "")
        setSubTasks([])
        setAttachments([])
      } else {
        // Creating new task
        reset({
          title: "",
          description: "",
          priority: "medium",
          due_date: "",
          estimated_hours: undefined,
        })
        // Reset all states for new task
        setSelectedUsers(currentUserId ? [currentUserId] : [])
        setPrimaryUser(currentUserId || "")
        setSubTasks([])
        setAttachments([])
        setIsAddingSubTask(false)
        setNewSubTaskTitle("")
        setNewSubTaskDescription("")
        setNewSubTaskAssignee("no-assignee")
      }
    }
  }, [isOpen, task, reset, currentUserId])

  // Sub-task management functions
  const handleAddSubTask = () => {
    if (!newSubTaskTitle.trim()) return

    const newSubTask: SubTask = {
      id: `temp-${Date.now()}`,
      title: newSubTaskTitle.trim(),
      description: newSubTaskDescription.trim() || undefined,
      assigned_to: newSubTaskAssignee && newSubTaskAssignee !== "no-assignee" ? newSubTaskAssignee : undefined,
    }

    setSubTasks(prev => [...prev, newSubTask])
    setNewSubTaskTitle("")
    setNewSubTaskDescription("")
    setNewSubTaskAssignee("no-assignee")
    setIsAddingSubTask(false)
  }

  const handleRemoveSubTask = (index: number) => {
    setSubTasks(prev => prev.filter((_, i) => i !== index))
  }

  // File attachment management functions
  const handleFileUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const files = event.target.files
    if (!files) return

    const newAttachments: FileAttachment[] = Array.from(files).map(file => ({
      id: `temp-${Date.now()}-${file.name}`,
      file,
      name: file.name,
      size: file.size,
      type: file.type,
    }))

    setAttachments(prev => [...prev, ...newAttachments])
    // Reset the input
    event.target.value = ""
  }

  const handleRemoveAttachment = (index: number) => {
    setAttachments(prev => prev.filter((_, i) => i !== index))
  }

  // User assignment management functions
  const handleAddUser = (userId: string) => {
    if (!selectedUsers.includes(userId)) {
      const newUsers = [...selectedUsers, userId]
      setSelectedUsers(newUsers)
      if (!primaryUser) {
        setPrimaryUser(userId)
      }
    }
  }

  const handleRemoveUser = (userId: string) => {
    const newUsers = selectedUsers.filter(id => id !== userId)
    setSelectedUsers(newUsers)
    if (primaryUser === userId) {
      setPrimaryUser(newUsers[0] || "")
    }
  }

  const handleSetPrimary = (userId: string) => {
    setPrimaryUser(userId)
  }

  const onSubmit = async (data: TaskFormData) => {
    try {
      const taskData = {
        title: data.title,
        description: data.description,
        priority: data.priority,
        due_date: data.due_date ? new Date(data.due_date).toISOString() : undefined,
        estimated_hours: data.estimated_hours,
        assigned_to: primaryUser || selectedUsers[0] || currentUserId, // Primary assignee
        assigned_users: selectedUsers.length > 0 ? selectedUsers : undefined, // Multi-user assignment
        primary_assignee: primaryUser || selectedUsers[0] || currentUserId,
      }

      console.log("=== ENHANCED TASK CREATION DEBUG ===")
      console.log("Task Data:", JSON.stringify(taskData, null, 2))
      console.log("Selected Users:", selectedUsers)
      console.log("Primary User:", primaryUser)
      console.log("Sub-tasks:", subTasks)
      console.log("Attachments:", attachments.map(a => ({ name: a.name, size: a.size, type: a.type })))
      console.log("====================================")

      let createdTask
      if (isEditing && task) {
        createdTask = await updateTaskMutation.mutateAsync({ id: task.id, data: taskData })
      } else {
        createdTask = await createTaskMutation.mutateAsync(taskData)
      }

      // TODO: After task creation, we would need to:
      // 1. Create sub-tasks if any
      // 2. Upload attachments if any
      // 3. Set up multi-user assignments if different from single assignment

      // For now, we'll show a warning if there are sub-tasks or attachments
      if (subTasks.length > 0 || attachments.length > 0) {
        toast({
          title: "Task Created",
          description: `Task created successfully. Note: Sub-tasks (${subTasks.length}) and attachments (${attachments.length}) are not yet implemented in the API.`,
          variant: "default",
        })
      }

      onClose()
    } catch (error) {
      // Error handling is done in the mutation hooks
      console.error("Task submission error:", error)
    } finally {
      // Ensure form is not stuck in submitting state
    }
  }

  const handleClose = () => {
    if (!isSubmitting) {
      onClose()
    }
  }

  const priorityColors = {
    low: "text-blue-600 bg-blue-50 border-blue-200",
    medium: "text-orange-600 bg-orange-50 border-orange-200",
    high: "text-red-600 bg-red-50 border-red-200",
    urgent: "text-purple-600 bg-purple-50 border-purple-200",
  }

  const watchedPriority = watch("priority")

  return (
    <Dialog open={isOpen} onOpenChange={handleClose}>
      <DialogContent className="sm:max-w-[800px] w-[95vw] max-h-[95vh] h-[95vh] overflow-hidden flex flex-col p-0">
        <DialogHeader className="flex-shrink-0 px-6 pt-6 pb-4 border-b">
          <DialogTitle className="flex items-center gap-2">
            <Flag className="h-5 w-5" />
            {isEditing ? "Edit Task" : "Create New Task"}
          </DialogTitle>
        </DialogHeader>

        <form onSubmit={handleSubmit(onSubmit)} className="flex-1 flex flex-col overflow-hidden">
          <div className="flex-1 overflow-hidden min-h-0 px-6">
            <Tabs defaultValue="basic" className="h-full flex flex-col">
              <TabsList className="grid w-full grid-cols-4 my-4 flex-shrink-0">
                <TabsTrigger value="basic">Basic Info</TabsTrigger>
                <TabsTrigger value="assignments">Assignments</TabsTrigger>
                <TabsTrigger value="subtasks">Sub-tasks</TabsTrigger>
                <TabsTrigger value="attachments">Attachments</TabsTrigger>
              </TabsList>

              <div className="flex-1 overflow-hidden min-h-0">
                <TabsContent value="basic" className="h-full data-[state=active]:flex data-[state=active]:flex-col">
                  <div className="flex-1 overflow-y-auto space-y-6 pr-2">
                    {/* Title */}
                    <div className="space-y-2">
                      <Label htmlFor="title" className="text-sm font-medium">
                        Title *
                      </Label>
                      <Input
                        id="title"
                        {...register("title")}
                        placeholder="Enter task title..."
                        className={errors.title ? "border-red-500" : ""}
                        disabled={isSubmitting}
                      />
                      {errors.title && (
                        <p className="text-sm text-red-600">{errors.title.message}</p>
                      )}
                    </div>

          {/* Description */}
          <div className="space-y-2">
            <Label htmlFor="description" className="text-sm font-medium">
              Description
            </Label>
            <Textarea
              id="description"
              {...register("description")}
              placeholder="Enter task description..."
              rows={3}
              className="resize-none"
              disabled={isSubmitting}
            />
            {errors.description && (
              <p className="text-sm text-red-600">{errors.description.message}</p>
            )}
          </div>

          {/* Priority and Due Date Row */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {/* Priority */}
            <div className="space-y-2">
              <Label htmlFor="priority" className="text-sm font-medium">
                Priority
              </Label>
              <Select
                value={watchedPriority}
                onValueChange={(value) => setValue("priority", value as any)}
                disabled={isSubmitting}
              >
                <SelectTrigger className={`${priorityColors[watchedPriority]} border`}>
                  <SelectValue placeholder="Select priority" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="low" className="text-blue-600">
                    <div className="flex items-center gap-2">
                      <div className="w-2 h-2 rounded-full bg-blue-500"></div>
                      Low
                    </div>
                  </SelectItem>
                  <SelectItem value="medium" className="text-orange-600">
                    <div className="flex items-center gap-2">
                      <div className="w-2 h-2 rounded-full bg-orange-500"></div>
                      Medium
                    </div>
                  </SelectItem>
                  <SelectItem value="high" className="text-red-600">
                    <div className="flex items-center gap-2">
                      <div className="w-2 h-2 rounded-full bg-red-500"></div>
                      High
                    </div>
                  </SelectItem>
                  <SelectItem value="urgent" className="text-purple-600">
                    <div className="flex items-center gap-2">
                      <div className="w-2 h-2 rounded-full bg-purple-500"></div>
                      Urgent
                    </div>
                  </SelectItem>
                </SelectContent>
              </Select>
            </div>

            {/* Due Date */}
            <div className="space-y-2">
              <Label htmlFor="due_date" className="text-sm font-medium flex items-center gap-1">
                <Calendar className="h-4 w-4" />
                Due Date
              </Label>
              <Input
                id="due_date"
                type="date"
                {...register("due_date")}
                disabled={isSubmitting}
                min={new Date().toISOString().split('T')[0]} // Prevent past dates
              />
              {errors.due_date && (
                <p className="text-sm text-red-600">{errors.due_date.message}</p>
              )}
            </div>
          </div>

          {/* Project and Estimated Hours Row */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {/* Project */}
            <div className="space-y-2">
              <Label htmlFor="project_id" className="text-sm font-medium flex items-center gap-1">
                <Folder className="h-4 w-4" />
                Project
              </Label>
              <Select
                value={watch("project_id") || "no-project"}
                onValueChange={(value) => setValue("project_id", value === "no-project" ? undefined : value)}
                disabled={isSubmitting}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Select project (optional)" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="no-project">No project</SelectItem>
                  {projectOptions.map((project) => (
                    <SelectItem key={project.value} value={project.value}>
                      <div className="flex items-center gap-2">
                        <div
                          className="w-3 h-3 rounded-full"
                          style={{ backgroundColor: project.color }}
                        />
                        {project.label}
                      </div>
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            {/* Estimated Hours */}
            <div className="space-y-2">
              <Label htmlFor="estimated_hours" className="text-sm font-medium">
                Estimated Hours
              </Label>
              <Input
                id="estimated_hours"
                type="number"
                step="0.5"
                min="0"
                max="999"
                {...register("estimated_hours")}
                placeholder="e.g., 8.5"
                disabled={isSubmitting}
              />
              {errors.estimated_hours && (
                <p className="text-sm text-red-600">{errors.estimated_hours.message}</p>
              )}
            </div>
          </div>
        </div>
      </TabsContent>

      {/* Assignments Tab */}
      <TabsContent value="assignments" className="h-full data-[state=active]:flex data-[state=active]:flex-col">
                  <div className="flex-1 overflow-y-auto space-y-4 pr-2">
                    <Card>
                      <CardHeader>
                        <CardTitle className="flex items-center gap-2">
                          <Users className="h-5 w-5" />
                          Task Assignments
                        </CardTitle>
                      </CardHeader>
                      <CardContent className="space-y-4">
                      {/* User Selection */}
                      <div className="space-y-2">
                        <Label className="text-sm font-medium">Add Assignee</Label>
                        <Select
                          value=""
                          onValueChange={handleAddUser}
                          disabled={isSubmitting}
                        >
                          <SelectTrigger>
                            <SelectValue placeholder="Select user to assign..." />
                          </SelectTrigger>
                          <SelectContent>
                            {employees
                              .filter(emp => !selectedUsers.includes(emp.id))
                              .map((employee) => (
                                <SelectItem key={employee.id} value={employee.id}>
                                  <div className="flex items-center gap-2">
                                    <Avatar className="h-6 w-6">
                                      <AvatarFallback className="text-xs">
                                        {employee.full_name.split(' ').map(n => n[0]).join('')}
                                      </AvatarFallback>
                                    </Avatar>
                                    <div>
                                      <div className="font-medium">{employee.full_name}</div>
                                      <div className="text-xs text-muted-foreground">{employee.email}</div>
                                    </div>
                                  </div>
                                </SelectItem>
                              ))}
                          </SelectContent>
                        </Select>
                      </div>

                      {/* Selected Users */}
                      {selectedUsers.length > 0 && (
                        <div className="space-y-2">
                          <Label className="text-sm font-medium">Assigned Users</Label>
                          <div className="space-y-2">
                            {selectedUsers.map((userId) => {
                              const employee = employees.find(emp => emp.id === userId)
                              const isPrimary = primaryUser === userId

                              return (
                                <div key={userId} className="flex items-center justify-between p-2 border rounded-lg">
                                  <div className="flex items-center gap-2">
                                    <Avatar className="h-6 w-6">
                                      <AvatarFallback className="text-xs">
                                        {employee?.full_name.split(' ').map(n => n[0]).join('') || 'U'}
                                      </AvatarFallback>
                                    </Avatar>
                                    <div>
                                      <div className="flex items-center gap-2">
                                        <span className="text-sm font-medium">
                                          {employee?.full_name || `User ${userId}`}
                                        </span>
                                        {isPrimary && (
                                          <Badge variant="secondary" className="text-xs">
                                            <Crown className="h-3 w-3 mr-1" />
                                            Primary
                                          </Badge>
                                        )}
                                      </div>
                                      {employee?.email && (
                                        <p className="text-xs text-muted-foreground">{employee.email}</p>
                                      )}
                                    </div>
                                  </div>

                                  <div className="flex items-center gap-1">
                                    {!isPrimary && selectedUsers.length > 1 && (
                                      <Button
                                        size="sm"
                                        variant="ghost"
                                        onClick={() => handleSetPrimary(userId)}
                                        className="h-6 w-6 p-0"
                                        title="Set as primary assignee"
                                        type="button"
                                      >
                                        <Crown className="h-3 w-3" />
                                      </Button>
                                    )}
                                    <Button
                                      size="sm"
                                      variant="ghost"
                                      onClick={() => handleRemoveUser(userId)}
                                      className="h-6 w-6 p-0 text-red-500 hover:text-red-700"
                                      title="Remove user"
                                      type="button"
                                    >
                                      <X className="h-3 w-3" />
                                    </Button>
                                  </div>
                                </div>
                              )
                            })}
                          </div>
                        </div>
                      )}

                      {selectedUsers.length === 0 && (
                        <div className="text-center py-8 text-muted-foreground">
                          <Users className="h-12 w-12 mx-auto mb-4 opacity-50" />
                          <p>No users assigned</p>
                          <p className="text-sm">Select users from the dropdown above</p>
                        </div>
                      )}
                      </CardContent>
                    </Card>
                  </div>
      </TabsContent>

      {/* Sub-tasks Tab */}
      <TabsContent value="subtasks" className="h-full data-[state=active]:flex data-[state=active]:flex-col">
                  <div className="flex-1 overflow-y-auto space-y-4 pr-2">
                    <Card>
                      <CardHeader>
                        <CardTitle className="flex items-center gap-2">
                          <CheckSquare className="h-5 w-5" />
                          Sub-tasks
                        </CardTitle>
                      </CardHeader>
                    <CardContent className="space-y-4">
                      {/* Add Sub-task Button */}
                      {!isAddingSubTask && (
                        <Button
                          type="button"
                          variant="outline"
                          onClick={() => setIsAddingSubTask(true)}
                          className="w-full"
                          disabled={isSubmitting}
                        >
                          <Plus className="h-4 w-4 mr-2" />
                          Add Sub-task
                        </Button>
                      )}

                      {/* Add Sub-task Form */}
                      {isAddingSubTask && (
                        <Card className="border-dashed">
                          <CardContent className="pt-4 space-y-3">
                            <div className="space-y-2">
                              <Label className="text-sm font-medium">Sub-task Title *</Label>
                              <Input
                                value={newSubTaskTitle}
                                onChange={(e) => setNewSubTaskTitle(e.target.value)}
                                placeholder="Enter sub-task title..."
                                disabled={isSubmitting}
                              />
                            </div>
                            <div className="space-y-2">
                              <Label className="text-sm font-medium">Description</Label>
                              <Textarea
                                value={newSubTaskDescription}
                                onChange={(e) => setNewSubTaskDescription(e.target.value)}
                                placeholder="Enter sub-task description..."
                                rows={2}
                                className="resize-none"
                                disabled={isSubmitting}
                              />
                            </div>
                            <div className="space-y-2">
                              <Label className="text-sm font-medium">Assign to</Label>
                              <Select
                                value={newSubTaskAssignee}
                                onValueChange={setNewSubTaskAssignee}
                                disabled={isSubmitting}
                              >
                                <SelectTrigger>
                                  <SelectValue placeholder="Select assignee (optional)" />
                                </SelectTrigger>
                                <SelectContent>
                                  <SelectItem value="no-assignee">No assignee</SelectItem>
                                  {employees.map((employee) => (
                                    <SelectItem key={employee.id} value={employee.id}>
                                      {employee.full_name}
                                    </SelectItem>
                                  ))}
                                </SelectContent>
                              </Select>
                            </div>
                            <div className="flex justify-end gap-2">
                              <Button
                                type="button"
                                variant="outline"
                                onClick={() => {
                                  setIsAddingSubTask(false)
                                  setNewSubTaskTitle("")
                                  setNewSubTaskDescription("")
                                  setNewSubTaskAssignee("no-assignee")
                                }}
                                disabled={isSubmitting}
                              >
                                Cancel
                              </Button>
                              <Button
                                type="button"
                                onClick={handleAddSubTask}
                                disabled={!newSubTaskTitle.trim() || isSubmitting}
                              >
                                Add Sub-task
                              </Button>
                            </div>
                          </CardContent>
                        </Card>
                      )}

                      {/* Sub-tasks List */}
                      {subTasks.length > 0 && (
                        <div className="space-y-2">
                          <Label className="text-sm font-medium">Sub-tasks ({subTasks.length})</Label>
                          <div className="space-y-2">
                            {subTasks.map((subTask, index) => {
                              const assignee = subTask.assigned_to ? employees.find(emp => emp.id === subTask.assigned_to) : null

                              return (
                                <div key={subTask.id || index} className="flex items-start justify-between p-3 border rounded-lg">
                                  <div className="flex-1">
                                    <div className="flex items-center gap-2">
                                      <span className="font-medium">{subTask.title}</span>
                                    </div>
                                    {subTask.description && (
                                      <p className="text-sm text-muted-foreground mt-1">{subTask.description}</p>
                                    )}
                                    {assignee && (
                                      <div className="flex items-center gap-2 mt-2">
                                        <Avatar className="h-5 w-5">
                                          <AvatarFallback className="text-xs">
                                            {assignee.full_name.split(' ').map(n => n[0]).join('')}
                                          </AvatarFallback>
                                        </Avatar>
                                        <span className="text-xs text-muted-foreground">Assigned to {assignee.full_name}</span>
                                      </div>
                                    )}
                                  </div>
                                  <Button
                                    type="button"
                                    size="sm"
                                    variant="ghost"
                                    onClick={() => handleRemoveSubTask(index)}
                                    className="text-red-500 hover:text-red-700"
                                    disabled={isSubmitting}
                                  >
                                    <X className="h-4 w-4" />
                                  </Button>
                                </div>
                              )
                            })}
                          </div>
                        </div>
                      )}

                      {subTasks.length === 0 && !isAddingSubTask && (
                        <div className="text-center py-8 text-muted-foreground">
                          <CheckSquare className="h-12 w-12 mx-auto mb-4 opacity-50" />
                          <p>No sub-tasks added</p>
                          <p className="text-sm">Click "Add Sub-task" to create your first sub-task</p>
                        </div>
                      )}
                    </CardContent>
                  </Card>
                  </div>
      </TabsContent>

      {/* Attachments Tab */}
      <TabsContent value="attachments" className="h-full data-[state=active]:flex data-[state=active]:flex-col">
                  <div className="flex-1 overflow-y-auto space-y-4 pr-2">
                    <Card>
                      <CardHeader>
                        <CardTitle className="flex items-center gap-2">
                          <Upload className="h-5 w-5" />
                          File Attachments
                      </CardTitle>
                    </CardHeader>
                    <CardContent className="space-y-4">
                      {/* File Upload */}
                      <div className="space-y-2">
                        <Label className="text-sm font-medium">Upload Files</Label>
                        <div className="border-2 border-dashed border-border rounded-lg p-6 text-center">
                          <Upload className="h-8 w-8 mx-auto mb-2 text-muted-foreground" />
                          <p className="text-sm text-muted-foreground mb-2">
                            Drag and drop files here, or click to select
                          </p>
                          <Input
                            type="file"
                            multiple
                            onChange={handleFileUpload}
                            className="hidden"
                            id="file-upload"
                            disabled={isSubmitting}
                          />
                          <Button
                            type="button"
                            variant="outline"
                            onClick={() => document.getElementById('file-upload')?.click()}
                            disabled={isSubmitting}
                          >
                            Select Files
                          </Button>
                        </div>
                      </div>

                      {/* Attachments List */}
                      {attachments.length > 0 && (
                        <div className="space-y-2">
                          <Label className="text-sm font-medium">Attached Files ({attachments.length})</Label>
                          <div className="space-y-2">
                            {attachments.map((attachment, index) => (
                              <div key={attachment.id || index} className="flex items-center justify-between p-3 border rounded-lg">
                                <div className="flex items-center gap-3">
                                  <div className="p-2 bg-muted rounded">
                                    <Upload className="h-4 w-4 text-muted-foreground" />
                                  </div>
                                  <div>
                                    <div className="font-medium text-sm">{attachment.name}</div>
                                    <div className="text-xs text-muted-foreground">
                                      {(attachment.size / 1024).toFixed(1)} KB • {attachment.type || 'Unknown type'}
                                    </div>
                                  </div>
                                </div>
                                <Button
                                  type="button"
                                  size="sm"
                                  variant="ghost"
                                  onClick={() => handleRemoveAttachment(index)}
                                  className="text-red-500 hover:text-red-700"
                                  disabled={isSubmitting}
                                >
                                  <X className="h-4 w-4" />
                                </Button>
                              </div>
                            ))}
                          </div>
                        </div>
                      )}

                      {attachments.length === 0 && (
                        <div className="text-center py-8 text-muted-foreground">
                          <Upload className="h-12 w-12 mx-auto mb-4 opacity-50" />
                          <p>No files attached</p>
                          <p className="text-sm">Upload files to attach them to this task</p>
                        </div>
                      )}
                    </CardContent>
                  </Card>
                  </div>
                </TabsContent>
              </div>
            </Tabs>
          </div>

          {/* Form Actions */}
          <div className="flex-shrink-0 flex justify-end gap-3 px-6 py-4 border-t bg-background">
            <Button
              type="button"
              variant="outline"
              onClick={handleClose}
              disabled={isSubmitting}
            >
              Cancel
            </Button>
            <Button
              type="submit"
              disabled={isSubmitting}
              className="min-w-[100px]"
            >
              {isSubmitting ? (
                <>
                  <Loader2 className="h-4 w-4 animate-spin mr-2" />
                  {isEditing ? "Updating..." : "Creating..."}
                </>
              ) : (
                isEditing ? "Update Task" : "Create Task"
              )}
            </Button>
          </div>
        </form>
      </DialogContent>
    </Dialog>
  )
}
