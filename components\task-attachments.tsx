"use client"

import { useState, useRef } from "react"
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Avatar, AvatarFallback } from "@/components/ui/avatar"
import { useTaskAttachments, useUploadAttachment, useDeleteAttachment, formatFileSize, getFileIcon, isImageFile } from "@/hooks/use-attachments"
import { useAuth } from "@/components/auth-provider"
import { formatDistanceToNow } from "date-fns"
import { 
  Paperclip, 
  Upload, 
  Loader2, 
  Trash2, 
  Download,
  Eye,
  MoreHorizontal,
  X
} from "lucide-react"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog"

interface TaskAttachmentsProps {
  taskId: string
}

interface Attachment {
  id: string
  filename: string
  original_filename: string
  file_path: string
  file_size: number
  mime_type: string
  uploaded_by: string
  uploaded_by_name: string
  created_at: string
}

export function TaskAttachments({ taskId }: TaskAttachmentsProps) {
  const { user } = useAuth()
  const fileInputRef = useRef<HTMLInputElement>(null)
  const [previewFile, setPreviewFile] = useState<Attachment | null>(null)
  const [isDragOver, setIsDragOver] = useState(false)
  
  const { data: attachmentsResponse, isLoading } = useTaskAttachments(taskId)
  const uploadMutation = useUploadAttachment(taskId)
  const deleteMutation = useDeleteAttachment(taskId)

  const attachments = attachmentsResponse?.data || []

  const handleFileSelect = (files: FileList | null) => {
    if (!files || files.length === 0) return

    const file = files[0]
    uploadMutation.mutate(file)
  }

  const handleFileInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    handleFileSelect(e.target.files)
    // Reset input value to allow selecting the same file again
    e.target.value = ""
  }

  const handleDrop = (e: React.DragEvent) => {
    e.preventDefault()
    setIsDragOver(false)
    handleFileSelect(e.dataTransfer.files)
  }

  const handleDragOver = (e: React.DragEvent) => {
    e.preventDefault()
    setIsDragOver(true)
  }

  const handleDragLeave = (e: React.DragEvent) => {
    e.preventDefault()
    setIsDragOver(false)
  }

  const handleDeleteAttachment = async (attachmentId: string) => {
    if (confirm("Are you sure you want to delete this attachment?")) {
      deleteMutation.mutate(attachmentId)
    }
  }

  const handleDownload = (attachment: Attachment) => {
    const link = document.createElement("a")
    link.href = attachment.file_path
    link.download = attachment.original_filename
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
  }

  const handlePreview = (attachment: Attachment) => {
    if (isImageFile(attachment.mime_type)) {
      setPreviewFile(attachment)
    } else {
      // For non-images, just download
      handleDownload(attachment)
    }
  }

  const getInitials = (name: string) => {
    return name
      .split(" ")
      .map(n => n[0])
      .join("")
      .toUpperCase()
      .slice(0, 2)
  }

  if (isLoading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Paperclip className="h-5 w-5" />
            Attachments
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex items-center justify-center py-8">
            <Loader2 className="h-6 w-6 animate-spin" />
          </div>
        </CardContent>
      </Card>
    )
  }

  return (
    <>
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Paperclip className="h-5 w-5" />
            Attachments ({attachments.length})
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          {/* Upload Area */}
          <div
            className={`border-2 border-dashed rounded-lg p-6 text-center transition-colors ${
              isDragOver 
                ? "border-blue-500 bg-blue-50 dark:bg-blue-950/20" 
                : "border-gray-300 dark:border-gray-600"
            } ${uploadMutation.isPending ? "opacity-50 pointer-events-none" : ""}`}
            onDrop={handleDrop}
            onDragOver={handleDragOver}
            onDragLeave={handleDragLeave}
          >
            <input
              ref={fileInputRef}
              type="file"
              onChange={handleFileInputChange}
              className="hidden"
              disabled={uploadMutation.isPending}
            />
            
            {uploadMutation.isPending ? (
              <div className="flex items-center justify-center gap-2">
                <Loader2 className="h-5 w-5 animate-spin" />
                <span>Uploading...</span>
              </div>
            ) : (
              <div className="space-y-2">
                <Upload className="h-8 w-8 mx-auto text-gray-400" />
                <div>
                  <p className="text-sm font-medium">Drop files here or click to upload</p>
                  <p className="text-xs text-gray-500">
                    Supports images, PDF, documents, and more (max 10MB)
                  </p>
                </div>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => fileInputRef.current?.click()}
                >
                  Choose File
                </Button>
              </div>
            )}
          </div>

          {/* Attachments List */}
          <div className="space-y-3">
            {attachments.length === 0 ? (
              <div className="text-center py-8 text-gray-500 dark:text-gray-400">
                <Paperclip className="h-12 w-12 mx-auto mb-3 opacity-50" />
                <p>No attachments yet</p>
                <p className="text-sm">Upload files to share with your team</p>
              </div>
            ) : (
              attachments.map((attachment: Attachment) => (
                <div key={attachment.id} className="flex items-center gap-3 p-3 border rounded-lg group hover:bg-gray-50 dark:hover:bg-gray-800">
                  {/* File Icon */}
                  <div className="text-2xl flex-shrink-0">
                    {getFileIcon(attachment.mime_type)}
                  </div>

                  {/* File Info */}
                  <div className="flex-1 min-w-0">
                    <div className="flex items-center gap-2 mb-1">
                      <p className="font-medium text-sm truncate">
                        {attachment.original_filename}
                      </p>
                      {isImageFile(attachment.mime_type) && (
                        <Badge variant="secondary" className="text-xs">
                          Image
                        </Badge>
                      )}
                    </div>
                    <div className="flex items-center gap-2 text-xs text-gray-500">
                      <span>{formatFileSize(attachment.file_size)}</span>
                      <span>•</span>
                      <div className="flex items-center gap-1">
                        <Avatar className="h-4 w-4">
                          <AvatarFallback className="text-xs">
                            {getInitials(attachment.uploaded_by_name)}
                          </AvatarFallback>
                        </Avatar>
                        <span>{attachment.uploaded_by_name}</span>
                      </div>
                      <span>•</span>
                      <span>{formatDistanceToNow(new Date(attachment.created_at), { addSuffix: true })}</span>
                    </div>
                  </div>

                  {/* Actions */}
                  <div className="flex items-center gap-1 opacity-0 group-hover:opacity-100 transition-opacity">
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => handlePreview(attachment)}
                    >
                      {isImageFile(attachment.mime_type) ? (
                        <Eye className="h-4 w-4" />
                      ) : (
                        <Download className="h-4 w-4" />
                      )}
                    </Button>
                    
                    {(user?.id === attachment.uploaded_by || ["admin", "manager"].includes(user?.role || "")) && (
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => handleDeleteAttachment(attachment.id)}
                        className="text-red-600 hover:text-red-700"
                      >
                        <Trash2 className="h-4 w-4" />
                      </Button>
                    )}
                  </div>
                </div>
              ))
            )}
          </div>
        </CardContent>
      </Card>

      {/* Image Preview Dialog */}
      <Dialog open={!!previewFile} onOpenChange={() => setPreviewFile(null)}>
        <DialogContent className="max-w-4xl max-h-[90vh]">
          <DialogHeader>
            <DialogTitle className="flex items-center justify-between">
              <span>{previewFile?.original_filename}</span>
              <Button
                variant="ghost"
                size="sm"
                onClick={() => setPreviewFile(null)}
              >
                <X className="h-4 w-4" />
              </Button>
            </DialogTitle>
          </DialogHeader>
          {previewFile && (
            <div className="flex justify-center">
              <img
                src={previewFile.file_path}
                alt={previewFile.original_filename}
                className="max-w-full max-h-[70vh] object-contain"
              />
            </div>
          )}
        </DialogContent>
      </Dialog>
    </>
  )
}
