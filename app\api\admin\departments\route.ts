import { type NextRequest, NextResponse } from "next/server"
import { AuthService } from "@/lib/auth-utils"
import { serverDb } from "@/lib/server-db"

export async function GET(request: NextRequest) {
  try {
    const sessionToken = request.cookies.get("session-token")?.value

    if (!sessionToken) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 })
    }

    const user = await AuthService.verifySession(sessionToken)

    if (!user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 })
    }

    const departments = await serverDb.getAllDepartments()

    return NextResponse.json({
      success: true,
      departments,
    })
  } catch (error) {
    console.error("Departments API error:", error)
    return NextResponse.json({ error: "Internal server error" }, { status: 500 })
  }
}

export async function POST(request: NextRequest) {
  try {
    const sessionToken = request.cookies.get("session-token")?.value

    if (!sessionToken) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 })
    }

    const currentUser = await AuthService.verifySession(sessionToken)

    if (!currentUser || !["admin", "hr_manager"].includes(currentUser.role)) {
      return NextResponse.json({ error: "Forbidden" }, { status: 403 })
    }

    const { name, description, manager_id, budget, location } = await request.json()

    if (!name) {
      return NextResponse.json({ error: "Department name is required" }, { status: 400 })
    }

    const result = await serverDb.sql`
      INSERT INTO departments (name, description, manager_id, budget, location)
      VALUES (${name}, ${description || null}, ${manager_id || null}, ${budget || null}, ${location || null})
      RETURNING *
    `

    return NextResponse.json({
      success: true,
      department: result[0],
      message: "Department created successfully"
    })
  } catch (error) {
    console.error("Create department API error:", error)
    if (error.message?.includes("duplicate key")) {
      return NextResponse.json({ error: "Department with this name already exists" }, { status: 400 })
    }
    return NextResponse.json({ error: "Internal server error" }, { status: 500 })
  }
}
