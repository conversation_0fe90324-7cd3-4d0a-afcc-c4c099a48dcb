#!/usr/bin/env node

/**
 * Test script to debug login authentication
 */

const { neon } = require('@neondatabase/serverless');
const bcrypt = require('bcryptjs');
require('dotenv').config({ path: '.env.local' });

async function testLogin() {
  console.log('🔐 Testing login authentication...\n');
  
  if (!process.env.DATABASE_URL) {
    console.error('❌ ERROR: DATABASE_URL environment variable is not set');
    process.exit(1);
  }
  
  try {
    const sql = neon(process.env.DATABASE_URL);
    
    const testEmail = '<EMAIL>';
    const testPassword = 'admin123';
    
    console.log(`🔄 Testing login for: ${testEmail}`);
    console.log(`🔄 Using password: ${testPassword}\n`);
    
    // Step 1: Check if user exists
    console.log('Step 1: Checking if user exists...');
    const userCheck = await sql`
      SELECT email, full_name, role, is_active, email_verified, password_hash
      FROM users 
      WHERE email = ${testEmail.toLowerCase().trim()}
    `;
    
    if (userCheck.length === 0) {
      console.log('❌ User not found in database');
      return;
    }
    
    const user = userCheck[0];
    console.log('✅ User found:');
    console.log(`   Email: ${user.email}`);
    console.log(`   Name: ${user.full_name}`);
    console.log(`   Role: ${user.role}`);
    console.log(`   Active: ${user.is_active}`);
    console.log(`   Email Verified: ${user.email_verified}`);
    console.log(`   Has Password Hash: ${user.password_hash ? 'Yes' : 'No'}`);
    console.log(`   Password Hash Length: ${user.password_hash ? user.password_hash.length : 0}`);
    console.log();
    
    // Step 2: Test password verification
    console.log('Step 2: Testing password verification...');
    
    if (!user.password_hash) {
      console.log('❌ No password hash found for user');
      return;
    }
    
    console.log(`🔄 Comparing password "${testPassword}" with stored hash...`);
    
    try {
      const isValid = await bcrypt.compare(testPassword, user.password_hash);
      console.log(`✅ Password verification result: ${isValid ? 'VALID' : 'INVALID'}`);
      
      if (!isValid) {
        console.log('\n🔍 Debugging password hash...');
        console.log(`Stored hash: ${user.password_hash.substring(0, 20)}...`);
        
        // Test if the hash was created correctly
        console.log('🔄 Testing hash creation...');
        const testHash = await bcrypt.hash(testPassword, 12);
        console.log(`New test hash: ${testHash.substring(0, 20)}...`);
        
        const testVerify = await bcrypt.compare(testPassword, testHash);
        console.log(`Test hash verification: ${testVerify ? 'VALID' : 'INVALID'}`);
      }
      
    } catch (error) {
      console.log('❌ Error during password verification:', error.message);
    }
    
    // Step 3: Test the actual login flow
    console.log('\nStep 3: Testing complete login flow...');
    
    // Simulate the AuthService.login method
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(testEmail)) {
      console.log('❌ Invalid email format');
      return;
    }
    
    // Check active status
    if (!user.is_active) {
      console.log('❌ User account is not active');
      return;
    }
    
    console.log('✅ All login checks passed');
    
    // Step 4: Check database table structure
    console.log('\nStep 4: Checking database table structure...');
    const tableInfo = await sql`
      SELECT column_name, data_type, is_nullable
      FROM information_schema.columns
      WHERE table_name = 'users' AND table_schema = 'public'
      ORDER BY ordinal_position
    `;
    
    console.log('📊 Users table structure:');
    tableInfo.forEach(col => {
      console.log(`   - ${col.column_name}: ${col.data_type} (${col.is_nullable === 'YES' ? 'nullable' : 'not null'})`);
    });
    
  } catch (error) {
    console.error('❌ Test failed:', error.message);
    console.error('Full error:', error);
  }
}

testLogin();
