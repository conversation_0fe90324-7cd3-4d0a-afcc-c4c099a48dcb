"use client"

import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON> } from "lucide-react"

interface Task {
  id: string
  title: string
  description?: string
  status: "todo" | "in_progress" | "completed" | "cancelled"
  priority: "low" | "medium" | "high" | "urgent"
  assigned_to?: string
  assigned_by: string
  due_date?: string
  project_id?: string
  created_at: string
  updated_at: string
  // Extended fields from joins
  assigned_to_name?: string
  assigned_to_email?: string
  project_name?: string
  project_color?: string
}

interface HighPriorityRemindersProps {
  tasks?: Task[]
  onTaskClick?: (taskId: string) => void
}

export function HighPriorityReminders({ tasks = [], onTaskClick = () => {} }: HighPriorityRemindersProps) {
  // Ensure tasks is an array before filtering
  const tasksArray = Array.isArray(tasks) ? tasks : []

  // Filter high priority and urgent tasks that are not completed
  const highPriorityTasks = tasksArray.filter((task) =>
    (task.priority === "high" || task.priority === "urgent") &&
    task.status !== "completed" &&
    task.status !== "cancelled"
  )

  if (highPriorityTasks.length === 0) {
    return (
      <Card className="mb-4 border-gray-200 dark:border-gray-700">
        <CardHeader className="py-2 px-4">
          <CardTitle className="text-sm font-medium flex items-center gap-2 text-gray-600 dark:text-gray-400">
            <Bell className="h-4 w-4" />
            High Priority Reminders
          </CardTitle>
        </CardHeader>
        <CardContent className="py-0 px-4 pb-2">
          <div className="text-sm text-gray-500 dark:text-gray-400 text-center py-4">
            No high priority tasks at the moment
          </div>
        </CardContent>
      </Card>
    )
  }

  return (
    <Card className="mb-4 border-exobank-red/20 bg-red-50 dark:bg-red-900/10 dark:border-red-900/30">
      <CardHeader className="py-2 px-4">
        <div className="flex items-center justify-between">
          <CardTitle className="text-sm font-medium flex items-center gap-2 text-exobank-red dark:text-red-300">
            <AlertTriangle className="h-4 w-4" />
            High Priority Reminders
          </CardTitle>
          <Badge className="bg-exobank-red text-white">{highPriorityTasks.length}</Badge>
        </div>
      </CardHeader>
      <CardContent className="py-0 px-4 pb-2">
        <div className="space-y-2">
          {highPriorityTasks.slice(0, 3).map((task) => {
            const isOverdue = task.due_date && new Date(task.due_date) < new Date()
            const dueDateText = task.due_date
              ? new Date(task.due_date).toLocaleDateString()
              : "No due date"

            return (
              <div
                key={task.id}
                className="flex items-center justify-between p-2 bg-white dark:bg-gray-800 rounded-md border border-red-100 dark:border-red-900/30 cursor-pointer hover:bg-red-50 dark:hover:bg-red-900/20 transition-colors"
                onClick={() => onTaskClick(task.id)}
              >
                <div className="flex items-center gap-2 flex-1 min-w-0">
                  <div className="flex items-center gap-1">
                    {task.priority === "urgent" ? (
                      <AlertTriangle className="h-3 w-3 text-purple-600 dark:text-purple-400" />
                    ) : (
                      <Bell className="h-3 w-3 text-exobank-red dark:text-red-400" />
                    )}
                    <Badge
                      variant={task.priority === "urgent" ? "destructive" : "secondary"}
                      className="text-xs px-1 py-0"
                    >
                      {task.priority}
                    </Badge>
                  </div>
                  <span className="text-sm font-medium dark:text-gray-200 truncate">
                    {task.title}
                  </span>
                </div>
                <div className="flex items-center text-xs text-gray-500 dark:text-gray-400 ml-2">
                  <Clock className={`h-3 w-3 mr-1 ${isOverdue ? 'text-red-500' : ''}`} />
                  <span className={isOverdue ? 'text-red-500 font-medium' : ''}>
                    {isOverdue ? 'Overdue' : dueDateText}
                  </span>
                </div>
              </div>
            )
          })}

          {highPriorityTasks.length > 3 && (
            <div className="text-center text-xs text-exobank-red dark:text-red-400 pt-1">
              +{highPriorityTasks.length - 3} more high priority tasks
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  )
}
