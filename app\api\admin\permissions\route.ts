import { type NextRequest, NextResponse } from "next/server"
import { AuthService } from "@/lib/auth-utils"
import { serverDb } from "@/lib/server-db"

export async function GET(request: NextRequest) {
  try {
    const sessionToken = request.cookies.get("session-token")?.value

    if (!sessionToken) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 })
    }

    const user = await AuthService.verifySession(sessionToken)

    if (!user || !["admin", "hr_manager"].includes(user.role)) {
      return NextResponse.json({ error: "Forbidden" }, { status: 403 })
    }

    const permissions = await serverDb.sql`
      SELECT * FROM permissions 
      ORDER BY resource, action
    `

    return NextResponse.json({
      success: true,
      permissions,
    })
  } catch (error) {
    console.error("Permissions API error:", error)
    return NextResponse.json({ error: "Internal server error" }, { status: 500 })
  }
}
