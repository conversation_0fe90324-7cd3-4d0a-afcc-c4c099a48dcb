#!/usr/bin/env node

// Test the tasks API endpoint directly
const http = require('http');

async function testAPIEndpoint() {
  console.log('🔍 TESTING TASKS API ENDPOINT DIRECTLY');
  console.log('======================================\n');

  // Test if server is running
  const options = {
    hostname: 'localhost',
    port: 3000,
    path: '/api/tasks',
    method: 'GET',
    headers: {
      'Content-Type': 'application/json',
    }
  };

  return new Promise((resolve, reject) => {
    const req = http.request(options, (res) => {
      console.log(`Status Code: ${res.statusCode}`);
      console.log(`Headers:`, res.headers);

      let data = '';
      res.on('data', (chunk) => {
        data += chunk;
      });

      res.on('end', () => {
        console.log('\nResponse Body:');
        try {
          const jsonData = JSON.parse(data);
          console.log(JSON.stringify(jsonData, null, 2));
          resolve(jsonData);
        } catch (error) {
          console.log('Raw response:', data);
          resolve(data);
        }
      });
    });

    req.on('error', (error) => {
      console.error('Request failed:', error.message);
      reject(error);
    });

    req.setTimeout(5000, () => {
      console.error('Request timeout');
      req.destroy();
      reject(new Error('Request timeout'));
    });

    req.end();
  });
}

testAPIEndpoint()
  .then((result) => {
    console.log('\n✅ API test completed');
  })
  .catch((error) => {
    console.error('\n❌ API test failed:', error.message);
    console.log('\n📋 This suggests the development server is not running properly');
    console.log('📋 Let\'s check if there are any compilation errors');
  });
