"use client"

import { useState, useEffect } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Avatar, AvatarFallback } from "@/components/ui/avatar"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON>, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog"
import { Label } from "@/components/ui/label"
import { Separator } from "@/components/ui/separator"
import { UserSelector } from "@/components/user-selector"
import { 
  Users, 
  Plus, 
  X, 
  Crown, 
  UserCheck,
  AlertCircle,
  Loader2
} from "lucide-react"
import { useUpdateTaskAssignments } from "@/hooks/use-tasks"
import { useAuth } from "@/components/auth-provider"
import { toast } from "@/hooks/use-toast"
import { formatDistanceToNow } from "date-fns"

interface TaskAssignment {
  id: string
  full_name: string
  email: string
  employee_id?: string
  department?: string
  position?: string
  is_primary: boolean
  assigned_at?: string
  assigned_by_name?: string
}

interface MultiUserAssignmentProps {
  taskId: string
  assignments: TaskAssignment[]
  canEdit?: boolean
  onAssignmentsChange?: () => void
}

export function MultiUserAssignment({ 
  taskId, 
  assignments, 
  canEdit = false,
  onAssignmentsChange 
}: MultiUserAssignmentProps) {
  const { user } = useAuth()
  const [isDialogOpen, setIsDialogOpen] = useState(false)
  const [selectedUsers, setSelectedUsers] = useState<string[]>([])
  const [primaryUser, setPrimaryUser] = useState<string>("")
  const [isSubmitting, setIsSubmitting] = useState(false)
  
  const updateAssignmentsMutation = useUpdateTaskAssignments()

  // Initialize form with current assignments
  useEffect(() => {
    if (isDialogOpen) {
      const userIds = assignments.map(a => a.id)
      const primary = assignments.find(a => a.is_primary)?.id || userIds[0] || ""
      setSelectedUsers(userIds)
      setPrimaryUser(primary)
    }
  }, [assignments, isDialogOpen])

  const handleAddUser = (userId: string | undefined) => {
    if (!userId || selectedUsers.includes(userId)) return
    
    const newUsers = [...selectedUsers, userId]
    setSelectedUsers(newUsers)
    
    // Set as primary if it's the first user
    if (newUsers.length === 1) {
      setPrimaryUser(userId)
    }
  }

  const handleRemoveUser = (userId: string) => {
    const newUsers = selectedUsers.filter(id => id !== userId)
    setSelectedUsers(newUsers)
    
    // If removing primary user, set new primary
    if (primaryUser === userId && newUsers.length > 0) {
      setPrimaryUser(newUsers[0])
    } else if (newUsers.length === 0) {
      setPrimaryUser("")
    }
  }

  const handleSetPrimary = (userId: string) => {
    setPrimaryUser(userId)
  }

  const handleSaveAssignments = async () => {
    if (selectedUsers.length === 0) {
      toast({
        title: "Error",
        description: "At least one user must be assigned to the task",
        variant: "destructive",
      })
      return
    }

    try {
      setIsSubmitting(true)

      const assignmentData = {
        user_ids: selectedUsers,
        is_primary: primaryUser || selectedUsers[0]
      }

      console.log("=== ASSIGNMENT SAVE DEBUG ===")
      console.log("Task ID:", taskId)
      console.log("Selected Users:", selectedUsers)
      console.log("Primary User:", primaryUser || selectedUsers[0])
      console.log("Assignment Data:", JSON.stringify(assignmentData, null, 2))
      console.log("Current User:", user)
      console.log("============================")

      const result = await updateAssignmentsMutation.mutateAsync({
        taskId,
        data: assignmentData
      })

      console.log("Assignment save result:", result)

      setIsDialogOpen(false)
      onAssignmentsChange?.()

      toast({
        title: "Success",
        description: "Task assignments updated successfully",
      })
    } catch (error) {
      console.error("=== ASSIGNMENT SAVE ERROR ===")
      console.error("Error object:", error)
      console.error("Error message:", error instanceof Error ? error.message : "Unknown error")
      console.error("Error stack:", error instanceof Error ? error.stack : "No stack")
      console.error("=============================")

      toast({
        title: "Error",
        description: error instanceof Error ? error.message : "Failed to update task assignments",
        variant: "destructive",
      })
    } finally {
      setIsSubmitting(false)
    }
  }

  const handleCancel = () => {
    setIsDialogOpen(false)
    // Reset form
    const userIds = assignments.map(a => a.id)
    const primary = assignments.find(a => a.is_primary)?.id || userIds[0] || ""
    setSelectedUsers(userIds)
    setPrimaryUser(primary)
  }

  return (
    <Card>
      <CardHeader>
        <div className="flex items-center justify-between">
          <CardTitle className="flex items-center gap-2">
            <Users className="h-5 w-5" />
            Assigned Users ({assignments.length})
          </CardTitle>
          {canEdit && (
            <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
              <DialogTrigger asChild>
                <Button size="sm" variant="outline">
                  <Plus className="h-4 w-4 mr-1" />
                  Manage
                </Button>
              </DialogTrigger>
              <DialogContent className="sm:max-w-[500px]">
                <DialogHeader>
                  <DialogTitle>Manage Task Assignments</DialogTitle>
                </DialogHeader>
                
                <div className="space-y-4">
                  {/* Add User Section */}
                  <div className="space-y-2">
                    <Label>Add User to Task</Label>
                    <UserSelector
                      value=""
                      onValueChange={handleAddUser}
                      placeholder="Select user to add..."
                      allowUnassigned={false}
                      filterByRole={[]}
                    />
                  </div>

                  <Separator />

                  {/* Current Assignments */}
                  <div className="space-y-3">
                    <Label>Current Assignments ({selectedUsers.length})</Label>
                    
                    {selectedUsers.length === 0 ? (
                      <div className="text-center py-6 text-gray-500">
                        <AlertCircle className="h-8 w-8 mx-auto mb-2 opacity-50" />
                        <p className="text-sm">No users assigned</p>
                        <p className="text-xs">Add at least one user to the task</p>
                      </div>
                    ) : (
                      <div className="space-y-2 max-h-60 overflow-y-auto">
                        {selectedUsers.map((userId) => {
                          const assignment = assignments.find(a => a.id === userId)
                          const isPrimary = primaryUser === userId
                          
                          return (
                            <div key={userId} className="flex items-center justify-between p-2 border rounded-lg">
                              <div className="flex items-center gap-2">
                                <Avatar className="h-6 w-6">
                                  <AvatarFallback className="text-xs">
                                    {assignment?.full_name.split(' ').map(n => n[0]).join('') || 'U'}
                                  </AvatarFallback>
                                </Avatar>
                                <div>
                                  <div className="flex items-center gap-2">
                                    <span className="text-sm font-medium">
                                      {assignment?.full_name || `User ${userId}`}
                                    </span>
                                    {isPrimary && (
                                      <Badge variant="secondary" className="text-xs">
                                        <Crown className="h-3 w-3 mr-1" />
                                        Primary
                                      </Badge>
                                    )}
                                  </div>
                                  {assignment?.email && (
                                    <p className="text-xs text-gray-500">{assignment.email}</p>
                                  )}
                                </div>
                              </div>
                              
                              <div className="flex items-center gap-1">
                                {!isPrimary && selectedUsers.length > 1 && (
                                  <Button
                                    size="sm"
                                    variant="ghost"
                                    onClick={() => handleSetPrimary(userId)}
                                    className="h-6 w-6 p-0"
                                    title="Set as primary assignee"
                                  >
                                    <Crown className="h-3 w-3" />
                                  </Button>
                                )}
                                <Button
                                  size="sm"
                                  variant="ghost"
                                  onClick={() => handleRemoveUser(userId)}
                                  className="h-6 w-6 p-0 text-red-500 hover:text-red-700"
                                  title="Remove user"
                                >
                                  <X className="h-3 w-3" />
                                </Button>
                              </div>
                            </div>
                          )
                        })}
                      </div>
                    )}
                  </div>

                  {/* Action Buttons */}
                  <div className="flex justify-end gap-2 pt-4">
                    <Button variant="outline" onClick={handleCancel} disabled={isSubmitting}>
                      Cancel
                    </Button>
                    <Button onClick={handleSaveAssignments} disabled={isSubmitting || selectedUsers.length === 0}>
                      {isSubmitting && <Loader2 className="h-4 w-4 mr-2 animate-spin" />}
                      Save Assignments
                    </Button>
                  </div>
                </div>
              </DialogContent>
            </Dialog>
          )}
        </div>
      </CardHeader>
      
      <CardContent>
        {assignments.length > 0 ? (
          <div className="space-y-3">
            {assignments.map((assignment) => (
              <div key={assignment.id} className="flex items-center justify-between p-3 border rounded-lg">
                <div className="flex items-center gap-3">
                  <Avatar className="h-8 w-8">
                    <AvatarFallback>
                      {assignment.full_name.split(' ').map(n => n[0]).join('')}
                    </AvatarFallback>
                  </Avatar>
                  <div>
                    <div className="flex items-center gap-2">
                      <span className="font-medium">{assignment.full_name}</span>
                      {assignment.is_primary && (
                        <Badge variant="secondary" className="text-xs">
                          <Crown className="h-3 w-3 mr-1" />
                          Primary
                        </Badge>
                      )}
                    </div>
                    <p className="text-sm text-gray-500">{assignment.email}</p>
                    {assignment.department && (
                      <p className="text-xs text-gray-400">{assignment.department}</p>
                    )}
                  </div>
                </div>
                {assignment.assigned_at && (
                  <div className="text-xs text-gray-400">
                    Assigned {formatDistanceToNow(new Date(assignment.assigned_at), { addSuffix: true })}
                  </div>
                )}
              </div>
            ))}
          </div>
        ) : (
          <div className="text-center py-8 text-gray-500">
            <Users className="h-12 w-12 mx-auto mb-4 opacity-50" />
            <p>No users assigned to this task</p>
            {canEdit && (
              <p className="text-sm mt-2">Click "Manage" to assign users</p>
            )}
          </div>
        )}
      </CardContent>
    </Card>
  )
}
