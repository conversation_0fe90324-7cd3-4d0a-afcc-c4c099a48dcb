// Labor Law Compliance Dashboard
// Phase 3: Nepal Localization Implementation - Labor Law Compliance

"use client"

import React, { useState, useEffect } from 'react'
import { Shield, AlertTriangle, CheckCircle, XCircle, Clock, Users, FileText, TrendingUp } from 'lucide-react'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { Progress } from '@/components/ui/progress'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { 
  laborLawComplianceChecker, 
  ComplianceReport, 
  ComplianceCheck, 
  LaborLawRule 
} from '@/lib/labor-law-compliance'
import { NepaliDateRangePicker } from './nepali-date-range-picker'
import { cn } from '@/lib/utils'

interface ComplianceDashboardProps {
  className?: string
}

interface ComplianceReportCardProps {
  report: ComplianceReport
  onViewDetails?: () => void
  className?: string
}

interface ComplianceCheckItemProps {
  check: ComplianceCheck
  className?: string
}

export function ComplianceDashboard({ className }: ComplianceDashboardProps) {
  const [dashboardData, setDashboardData] = useState<any>(null)
  const [selectedPeriod, setSelectedPeriod] = useState({
    from: new Date(new Date().getFullYear(), new Date().getMonth(), 1),
    to: new Date()
  })
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    loadDashboardData()
  }, [selectedPeriod])

  const loadDashboardData = async () => {
    if (!selectedPeriod.from || !selectedPeriod.to) return

    setLoading(true)
    try {
      const data = await laborLawComplianceChecker.getComplianceDashboard(
        selectedPeriod.from.toISOString().split('T')[0],
        selectedPeriod.to.toISOString().split('T')[0]
      )
      setDashboardData(data)
    } catch (error) {
      console.error('Error loading dashboard data:', error)
    } finally {
      setLoading(false)
    }
  }

  const getComplianceColor = (percentage: number) => {
    if (percentage >= 95) return 'text-green-600'
    if (percentage >= 85) return 'text-yellow-600'
    return 'text-red-600'
  }

  const getComplianceStatus = (percentage: number) => {
    if (percentage >= 95) return { label: 'Excellent', variant: 'default' as const }
    if (percentage >= 85) return { label: 'Good', variant: 'secondary' as const }
    if (percentage >= 70) return { label: 'Needs Attention', variant: 'destructive' as const }
    return { label: 'Critical', variant: 'destructive' as const }
  }

  if (loading) {
    return (
      <Card className={className}>
        <CardContent className="p-6">
          <div className="flex items-center justify-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
          </div>
        </CardContent>
      </Card>
    )
  }

  return (
    <div className={cn("space-y-6", className)}>
      {/* Header */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <CardTitle className="flex items-center gap-2">
              <Shield className="h-5 w-5" />
              Labor Law Compliance Dashboard
            </CardTitle>
            
            <div className="w-96">
              <NepaliDateRangePicker
                value={selectedPeriod}
                onChange={(range) => range && setSelectedPeriod(range)}
                showPresets={true}
                showPayrollPeriodPresets={true}
              />
            </div>
          </div>
        </CardHeader>
      </Card>

      {/* Overview Cards */}
      {dashboardData && (
        <>
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            <Card>
              <CardContent className="p-4">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-muted-foreground">Overall Compliance</p>
                    <p className={cn("text-2xl font-bold", getComplianceColor(dashboardData.overallCompliance))}>
                      {dashboardData.overallCompliance}%
                    </p>
                  </div>
                  <Shield className={cn("h-8 w-8", getComplianceColor(dashboardData.overallCompliance))} />
                </div>
                <div className="mt-2">
                  <Progress value={dashboardData.overallCompliance} className="h-2" />
                </div>
                <div className="mt-2">
                  <Badge variant={getComplianceStatus(dashboardData.overallCompliance).variant}>
                    {getComplianceStatus(dashboardData.overallCompliance).label}
                  </Badge>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="p-4">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-muted-foreground">Total Employees</p>
                    <p className="text-2xl font-bold">{dashboardData.totalEmployees}</p>
                  </div>
                  <Users className="h-8 w-8 text-blue-600" />
                </div>
                <div className="mt-2 text-sm text-muted-foreground">
                  Monitored for compliance
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="p-4">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-muted-foreground">Compliant</p>
                    <p className="text-2xl font-bold text-green-600">{dashboardData.compliantEmployees}</p>
                  </div>
                  <CheckCircle className="h-8 w-8 text-green-600" />
                </div>
                <div className="mt-2 text-sm text-muted-foreground">
                  {((dashboardData.compliantEmployees / dashboardData.totalEmployees) * 100).toFixed(1)}% of total
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="p-4">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-muted-foreground">Non-Compliant</p>
                    <p className="text-2xl font-bold text-red-600">{dashboardData.nonCompliantEmployees}</p>
                  </div>
                  <XCircle className="h-8 w-8 text-red-600" />
                </div>
                <div className="mt-2 text-sm text-muted-foreground">
                  Require immediate attention
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Compliance by Category */}
          <Card>
            <CardHeader>
              <CardTitle>Compliance by Category</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                {Object.entries(dashboardData.complianceByCategory).map(([category, percentage]) => (
                  <div key={category} className="space-y-2">
                    <div className="flex justify-between text-sm">
                      <span className="font-medium capitalize">{category.replace('_', ' ')}</span>
                      <span className={getComplianceColor(percentage as number)}>{percentage}%</span>
                    </div>
                    <Progress value={percentage as number} className="h-2" />
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>

          {/* Top Violations */}
          <Card>
            <CardHeader>
              <CardTitle>Top Compliance Violations</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                {dashboardData.topViolations.map((violation, index) => (
                  <div key={index} className="flex items-center justify-between p-3 bg-muted rounded-lg">
                    <div className="flex items-center gap-3">
                      <div className="flex items-center justify-center w-8 h-8 bg-red-100 text-red-600 rounded-full text-sm font-bold">
                        {index + 1}
                      </div>
                      <div>
                        <div className="font-medium">{violation.rule}</div>
                        <div className="text-sm text-muted-foreground">
                          {violation.count} violation{violation.count !== 1 ? 's' : ''}
                        </div>
                      </div>
                    </div>
                    <Badge variant="destructive">{violation.count}</Badge>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </>
      )}
    </div>
  )
}

export function ComplianceReportCard({ report, onViewDetails, className }: ComplianceReportCardProps) {
  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'compliant':
        return <CheckCircle className="h-5 w-5 text-green-600" />
      case 'non_compliant':
        return <XCircle className="h-5 w-5 text-red-600" />
      case 'warning':
        return <AlertTriangle className="h-5 w-5 text-yellow-600" />
      default:
        return <Clock className="h-5 w-5 text-gray-600" />
    }
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'compliant': return 'border-green-200 bg-green-50'
      case 'non_compliant': return 'border-red-200 bg-red-50'
      case 'warning': return 'border-yellow-200 bg-yellow-50'
      default: return 'border-gray-200 bg-gray-50'
    }
  }

  return (
    <Card className={cn(getStatusColor(report.overallStatus), className)}>
      <CardHeader className="pb-3">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            {getStatusIcon(report.overallStatus)}
            <CardTitle className="text-lg">Compliance Report</CardTitle>
          </div>
          <Badge variant={report.overallStatus === 'compliant' ? 'default' : 'destructive'}>
            {report.overallStatus.replace('_', ' ').toUpperCase()}
          </Badge>
        </div>
      </CardHeader>
      
      <CardContent className="space-y-4">
        <div className="grid grid-cols-3 gap-4 text-sm">
          <div className="text-center">
            <div className="text-2xl font-bold text-green-600">{report.compliantChecks}</div>
            <div className="text-muted-foreground">Compliant</div>
          </div>
          <div className="text-center">
            <div className="text-2xl font-bold text-yellow-600">{report.warningChecks}</div>
            <div className="text-muted-foreground">Warnings</div>
          </div>
          <div className="text-center">
            <div className="text-2xl font-bold text-red-600">{report.nonCompliantChecks}</div>
            <div className="text-muted-foreground">Violations</div>
          </div>
        </div>

        <div className="space-y-2">
          <div className="text-sm font-medium">Period Summary</div>
          <div className="text-xs text-muted-foreground">
            {new Date(report.periodStart).toLocaleDateString()} - {new Date(report.periodEnd).toLocaleDateString()}
          </div>
          
          <div className="grid grid-cols-2 gap-2 text-xs">
            <div>Working Hours: {report.summary.workingHours.weeklyHours.toFixed(1)}h</div>
            <div>Overtime: {report.summary.overtime.weeklyOvertimeHours.toFixed(1)}h</div>
          </div>
        </div>

        {onViewDetails && (
          <Button variant="outline" size="sm" onClick={onViewDetails} className="w-full">
            <FileText className="h-4 w-4 mr-1" />
            View Details
          </Button>
        )}
      </CardContent>
    </Card>
  )
}

export function ComplianceCheckItem({ check, className }: ComplianceCheckItemProps) {
  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'compliant':
        return <CheckCircle className="h-4 w-4 text-green-600" />
      case 'non_compliant':
        return <XCircle className="h-4 w-4 text-red-600" />
      case 'warning':
        return <AlertTriangle className="h-4 w-4 text-yellow-600" />
      default:
        return <Clock className="h-4 w-4 text-gray-600" />
    }
  }

  const getSeverityColor = (severity: string) => {
    switch (severity) {
      case 'critical': return 'text-red-600'
      case 'high': return 'text-orange-600'
      case 'medium': return 'text-yellow-600'
      case 'low': return 'text-blue-600'
      default: return 'text-gray-600'
    }
  }

  return (
    <Card className={className}>
      <CardContent className="p-4">
        <div className="flex items-start gap-3">
          {getStatusIcon(check.status)}
          
          <div className="flex-1 space-y-2">
            <div className="flex items-center justify-between">
              <h4 className="font-medium">{check.ruleName}</h4>
              <Badge variant="outline" className={getSeverityColor(check.severity)}>
                {check.severity}
              </Badge>
            </div>
            
            <p className="text-sm text-muted-foreground">{check.message}</p>
            
            {check.actualValue !== undefined && check.requiredValue !== undefined && (
              <div className="text-xs text-muted-foreground">
                Actual: {check.actualValue} {check.unit} | Required: {check.requiredValue} {check.unit}
              </div>
            )}
            
            {check.recommendations && check.recommendations.length > 0 && (
              <div className="space-y-1">
                <div className="text-xs font-medium">Recommendations:</div>
                <ul className="text-xs text-muted-foreground space-y-1">
                  {check.recommendations.map((rec, index) => (
                    <li key={index} className="flex items-start gap-1">
                      <span>•</span>
                      <span>{rec}</span>
                    </li>
                  ))}
                </ul>
              </div>
            )}
          </div>
        </div>
      </CardContent>
    </Card>
  )
}

export function ComplianceAlerts({ checks }: { checks: ComplianceCheck[] }) {
  const criticalChecks = checks.filter(c => c.severity === 'critical' && c.status === 'non_compliant')
  const highChecks = checks.filter(c => c.severity === 'high' && c.status === 'non_compliant')

  if (criticalChecks.length === 0 && highChecks.length === 0) {
    return null
  }

  return (
    <div className="space-y-4">
      {criticalChecks.length > 0 && (
        <Alert variant="destructive">
          <XCircle className="h-4 w-4" />
          <AlertTitle>Critical Compliance Violations</AlertTitle>
          <AlertDescription>
            {criticalChecks.length} critical violation{criticalChecks.length !== 1 ? 's' : ''} require immediate attention.
          </AlertDescription>
        </Alert>
      )}
      
      {highChecks.length > 0 && (
        <Alert>
          <AlertTriangle className="h-4 w-4" />
          <AlertTitle>High Priority Violations</AlertTitle>
          <AlertDescription>
            {highChecks.length} high priority violation{highChecks.length !== 1 ? 's' : ''} should be addressed soon.
          </AlertDescription>
        </Alert>
      )}
    </div>
  )
}

export default ComplianceDashboard
