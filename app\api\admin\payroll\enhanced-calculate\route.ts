// Enhanced Payroll Calculation API Endpoints
// Admin endpoints for automatic payroll calculation with attendance integration

import { NextRequest, NextResponse } from 'next/server';
import { enhancedPayrollCalculator } from '@/lib/enhanced-payroll-calculator';
import { AuthService } from '@/lib/auth-utils';
import { db } from '@/lib/neon';

// GET - Get attendance summary or existing payroll calculations
export async function GET(request: NextRequest) {
  try {
    const sessionToken = request.cookies.get("session-token")?.value;

    if (!sessionToken) {
      return NextResponse.json({
        success: false,
        error: 'Unauthorized'
      }, { status: 401 });
    }

    const user = await AuthService.verifySession(sessionToken);

    if (!user || (user.role !== 'admin' && user.role !== 'hr_manager')) {
      return NextResponse.json({
        success: false,
        error: 'Insufficient permissions'
      }, { status: 403 });
    }

    const { searchParams } = new URL(request.url);
    const action = searchParams.get('action');
    const userId = searchParams.get('userId');
    const year = parseInt(searchParams.get('year') || new Date().getFullYear().toString());
    const month = parseInt(searchParams.get('month') || (new Date().getMonth() + 1).toString());

    if (action === 'attendance_summary' && userId) {
      // Get attendance summary for specific employee
      const attendanceSummary = await enhancedPayrollCalculator.getAttendanceSummary(userId, year, month);
      return NextResponse.json({
        success: true,
        data: attendanceSummary
      });
    } else if (action === 'preview' && userId) {
      // Preview payroll calculation for specific employee
      const calculation = await enhancedPayrollCalculator.calculateEmployeePayroll(userId, year, month);
      return NextResponse.json({
        success: true,
        data: calculation
      });
    } else if (action === 'bulk_preview') {
      // Preview bulk payroll calculation for all employees
      const calculations = await enhancedPayrollCalculator.calculateBulkPayroll(year, month);
      return NextResponse.json({
        success: true,
        data: calculations
      });
    } else if (action === 'existing') {
      // Get existing payroll calculations
      const existingPayrolls = await db.sql`
        SELECT 
          mps.*,
          u.full_name,
          u.email,
          u.department
        FROM monthly_payroll_summary mps
        JOIN users u ON mps.user_id = u.id
        WHERE mps.fiscal_year = ${year.toString()}
          AND mps.bs_month = ${year.toString() + '-' + month.toString().padStart(2, '0')}
        ORDER BY u.full_name
      `;
      
      return NextResponse.json({
        success: true,
        data: existingPayrolls
      });
    } else {
      return NextResponse.json({
        success: false,
        error: 'Invalid action parameter'
      }, { status: 400 });
    }

  } catch (error) {
    console.error('Error in enhanced payroll calculation GET:', error);
    return NextResponse.json({
      success: false,
      error: error.message || 'Internal server error'
    }, { status: 500 });
  }
}

// POST - Calculate and save payroll
export async function POST(request: NextRequest) {
  try {
    const sessionToken = request.cookies.get("session-token")?.value;

    if (!sessionToken) {
      return NextResponse.json({
        success: false,
        error: 'Unauthorized'
      }, { status: 401 });
    }

    const user = await AuthService.verifySession(sessionToken);

    if (!user || (user.role !== 'admin' && user.role !== 'hr_manager')) {
      return NextResponse.json({
        success: false,
        error: 'Insufficient permissions'
      }, { status: 403 });
    }

    const body = await request.json();
    const { action, userId, year, month, calculations } = body;

    if (action === 'calculate_single') {
      // Calculate and save payroll for single employee
      if (!userId || !year || !month) {
        return NextResponse.json({
          success: false,
          error: 'User ID, year, and month are required'
        }, { status: 400 });
      }

      const calculation = await enhancedPayrollCalculator.calculateEmployeePayroll(userId, year, month);
      const payrollId = await enhancedPayrollCalculator.savePayrollCalculation(calculation);

      return NextResponse.json({
        success: true,
        data: {
          payrollId,
          calculation
        },
        message: 'Payroll calculated and saved successfully'
      });

    } else if (action === 'calculate_bulk') {
      // Calculate and save payroll for all employees
      if (!year || !month) {
        return NextResponse.json({
          success: false,
          error: 'Year and month are required'
        }, { status: 400 });
      }

      const calculations = await enhancedPayrollCalculator.calculateBulkPayroll(year, month);
      const savedPayrolls = [];

      for (const calculation of calculations) {
        try {
          const payrollId = await enhancedPayrollCalculator.savePayrollCalculation(calculation);
          savedPayrolls.push({ payrollId, userId: calculation.user_id });
        } catch (error) {
          console.error(`Error saving payroll for user ${calculation.user_id}:`, error);
          // Continue with other calculations
        }
      }

      return NextResponse.json({
        success: true,
        data: {
          totalCalculated: calculations.length,
          totalSaved: savedPayrolls.length,
          savedPayrolls,
          calculations
        },
        message: `Bulk payroll calculated: ${calculations.length} employees, ${savedPayrolls.length} saved successfully`
      });

    } else if (action === 'save_calculations') {
      // Save pre-calculated payroll data
      if (!calculations || !Array.isArray(calculations)) {
        return NextResponse.json({
          success: false,
          error: 'Calculations array is required'
        }, { status: 400 });
      }

      const savedPayrolls = [];

      for (const calculation of calculations) {
        try {
          const payrollId = await enhancedPayrollCalculator.savePayrollCalculation(calculation);
          savedPayrolls.push({ payrollId, userId: calculation.user_id });
        } catch (error) {
          console.error(`Error saving payroll for user ${calculation.user_id}:`, error);
          // Continue with other calculations
        }
      }

      return NextResponse.json({
        success: true,
        data: {
          totalCalculations: calculations.length,
          totalSaved: savedPayrolls.length,
          savedPayrolls
        },
        message: `Payroll calculations saved: ${savedPayrolls.length} out of ${calculations.length} successfully`
      });

    } else {
      return NextResponse.json({
        success: false,
        error: 'Invalid action'
      }, { status: 400 });
    }

  } catch (error) {
    console.error('Error in enhanced payroll calculation POST:', error);
    return NextResponse.json({
      success: false,
      error: error.message || 'Internal server error'
    }, { status: 500 });
  }
}

// PUT - Update payroll calculation status
export async function PUT(request: NextRequest) {
  try {
    const sessionToken = request.cookies.get("session-token")?.value;

    if (!sessionToken) {
      return NextResponse.json({
        success: false,
        error: 'Unauthorized'
      }, { status: 401 });
    }

    const user = await AuthService.verifySession(sessionToken);

    if (!user || (user.role !== 'admin' && user.role !== 'hr_manager')) {
      return NextResponse.json({
        success: false,
        error: 'Insufficient permissions'
      }, { status: 403 });
    }

    const body = await request.json();
    const { action, payrollIds, status } = body;

    if (action === 'update_status') {
      if (!payrollIds || !Array.isArray(payrollIds) || !status) {
        return NextResponse.json({
          success: false,
          error: 'Payroll IDs array and status are required'
        }, { status: 400 });
      }

      // Validate status
      if (!['draft', 'calculated', 'approved', 'processed'].includes(status)) {
        return NextResponse.json({
          success: false,
          error: 'Invalid status'
        }, { status: 400 });
      }

      // Update status for multiple payroll records
      const results = [];
      
      for (const payrollId of payrollIds) {
        try {
          let updateQuery = `
            UPDATE monthly_payroll_summary 
            SET status = $1, updated_at = NOW()
          `;
          const params = [status];
          
          if (status === 'approved') {
            updateQuery += `, approved_by = $2, approved_at = NOW()`;
            params.push(user.id);
          } else if (status === 'processed') {
            updateQuery += `, processed_by = $2, processed_at = NOW()`;
            params.push(user.id);
          }
          
          updateQuery += ` WHERE id = $${params.length + 1}`;
          params.push(payrollId);
          
          await db.sql([updateQuery, ...params]);
          results.push({ payrollId, success: true });
        } catch (error) {
          console.error(`Error updating payroll ${payrollId}:`, error);
          results.push({ payrollId, success: false, error: error.message });
        }
      }

      const successful = results.filter(r => r.success).length;

      return NextResponse.json({
        success: true,
        data: {
          totalUpdated: successful,
          totalRequested: payrollIds.length,
          results
        },
        message: `Payroll status updated: ${successful} out of ${payrollIds.length} records`
      });

    } else {
      return NextResponse.json({
        success: false,
        error: 'Invalid action'
      }, { status: 400 });
    }

  } catch (error) {
    console.error('Error in enhanced payroll calculation PUT:', error);
    return NextResponse.json({
      success: false,
      error: 'Internal server error'
    }, { status: 500 });
  }
}
