-- ============================================================================
-- FINAL DATABASE FIX FOR ATTENDANCE TABLE SCHEMA
-- Execute these commands ONE BY ONE in your Neon SQL console
-- ============================================================================

-- STEP 1: Check current table structure
-- This will show you what columns exist and their types
SELECT 
    column_name, 
    data_type, 
    is_nullable,
    column_default
FROM information_schema.columns 
WHERE table_name = 'attendance' 
ORDER BY column_name;

-- Expected problematic columns: check_in_time and check_out_time as 'time without time zone'

-- ============================================================================
-- STEP 2: Check for existing data (important for migration strategy)
-- ============================================================================
SELECT COUNT(*) as total_records FROM attendance;

-- If you see any records, we'll need to migrate the data
-- If 0 records, we can use the simpler approach

-- ============================================================================
-- STEP 3A: SIMPLE APPROACH (if no data exists - COUNT = 0)
-- ============================================================================
-- If you have 0 records, run these commands:

-- Drop the table and recreate with correct schema
-- DROP TABLE IF EXISTS attendance;

-- CREATE TABLE attendance (
--     id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
--     user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
--     date DATE NOT NULL,
--     check_in_time TIMESTAMP WITH TIME ZONE,
--     check_out_time TIMESTAMP WITH TIME ZONE,
--     status VARCHAR(20) NOT NULL DEFAULT 'present' CHECK (status IN ('present', 'absent', 'late', 'half_day', 'on_leave')),
--     hours_worked DECIMAL(4,2),
--     notes TEXT,
--     created_by UUID REFERENCES users(id),
--     created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
--     updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
--     UNIQUE(user_id, date)
-- );

-- ============================================================================
-- STEP 3B: MIGRATION APPROACH (if data exists - COUNT > 0)
-- ============================================================================
-- If you have existing data, follow these steps carefully:

-- Step 3B.1: Add new columns with correct type
ALTER TABLE attendance ADD COLUMN IF NOT EXISTS check_in_time_new TIMESTAMP WITH TIME ZONE;
ALTER TABLE attendance ADD COLUMN IF NOT EXISTS check_out_time_new TIMESTAMP WITH TIME ZONE;

-- Step 3B.2: Verify new columns were created
SELECT column_name, data_type 
FROM information_schema.columns 
WHERE table_name = 'attendance' 
AND column_name LIKE '%_new';

-- Step 3B.3: Migrate existing data using proper PostgreSQL syntax
-- This converts TIME + DATE to TIMESTAMP WITH TIME ZONE
UPDATE attendance 
SET 
    check_in_time_new = CASE 
        WHEN check_in_time IS NOT NULL THEN 
            (date::text || ' ' || check_in_time::text)::TIMESTAMP WITH TIME ZONE
        ELSE NULL 
    END,
    check_out_time_new = CASE 
        WHEN check_out_time IS NOT NULL THEN 
            (date::text || ' ' || check_out_time::text)::TIMESTAMP WITH TIME ZONE
        ELSE NULL 
    END
WHERE check_in_time_new IS NULL OR check_out_time_new IS NULL;

-- Step 3B.4: Verify data migration
SELECT 
    id,
    date,
    check_in_time as old_check_in,
    check_in_time_new as new_check_in,
    check_out_time as old_check_out,
    check_out_time_new as new_check_out
FROM attendance 
LIMIT 5;

-- Step 3B.5: Drop old columns
ALTER TABLE attendance DROP COLUMN IF EXISTS check_in_time;
ALTER TABLE attendance DROP COLUMN IF EXISTS check_out_time;

-- Step 3B.6: Rename new columns
ALTER TABLE attendance RENAME COLUMN check_in_time_new TO check_in_time;
ALTER TABLE attendance RENAME COLUMN check_out_time_new TO check_out_time;

-- ============================================================================
-- STEP 4: VERIFY THE FIX
-- ============================================================================
-- Check that both columns are now TIMESTAMP WITH TIME ZONE
SELECT 
    column_name, 
    data_type, 
    is_nullable
FROM information_schema.columns 
WHERE table_name = 'attendance' 
AND column_name IN ('check_in_time', 'check_out_time')
ORDER BY column_name;

-- Expected result:
-- check_in_time   | timestamp with time zone | YES
-- check_out_time  | timestamp with time zone | YES

-- ============================================================================
-- STEP 5: TEST THE FIX
-- ============================================================================
-- Get a valid user ID for testing
SELECT id, email FROM users LIMIT 1;

-- Test inserting a timestamp (replace 'USER_ID_HERE' with actual user ID)
-- INSERT INTO attendance (user_id, date, check_in_time, status) 
-- VALUES ('USER_ID_HERE', CURRENT_DATE, NOW(), 'present');

-- Verify the test record
-- SELECT * FROM attendance WHERE date = CURRENT_DATE ORDER BY created_at DESC LIMIT 1;

-- Clean up test record
-- DELETE FROM attendance WHERE date = CURRENT_DATE AND status = 'present';

-- ============================================================================
-- STEP 6: FINAL VERIFICATION
-- ============================================================================
-- Check the complete table structure
SELECT 
    column_name, 
    data_type, 
    is_nullable,
    column_default
FROM information_schema.columns 
WHERE table_name = 'attendance' 
ORDER BY ordinal_position;

-- ============================================================================
-- TROUBLESHOOTING
-- ============================================================================
-- If you encounter errors:

-- 1. Check if attendance table exists:
-- SELECT * FROM information_schema.tables WHERE table_name = 'attendance';

-- 2. Check for any remaining temp columns:
-- SELECT column_name FROM information_schema.columns 
-- WHERE table_name = 'attendance' AND column_name LIKE '%temp%';

-- 3. If you need to start completely over:
-- DROP TABLE IF EXISTS attendance CASCADE;
-- -- Then run the CREATE TABLE statement from STEP 3A

-- ============================================================================
-- SUCCESS CRITERIA
-- ============================================================================
-- After successful completion:
-- ✅ check_in_time should be 'timestamp with time zone'
-- ✅ check_out_time should be 'timestamp with time zone'  
-- ✅ No more "invalid input syntax for type time" errors
-- ✅ Clock-in/out functionality should work in the application
