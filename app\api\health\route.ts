import { NextResponse } from "next/server"
import { serverDb } from "@/lib/server-db"

export async function GET() {
  try {
    // Test database connection
    const isHealthy = await serverDb.healthCheck()
    
    if (isHealthy) {
      return NextResponse.json({
        status: "healthy",
        database: "connected",
        timestamp: new Date().toISOString()
      })
    } else {
      return NextResponse.json({
        status: "unhealthy",
        database: "disconnected",
        timestamp: new Date().toISOString()
      }, { status: 503 })
    }
  } catch (error) {
    console.error("Health check error:", error)
    return NextResponse.json({
      status: "error",
      database: "error",
      error: error.message,
      timestamp: new Date().toISOString()
    }, { status: 500 })
  }
}
