#!/usr/bin/env node

require('dotenv').config({ path: '.env.local' });
const { neon } = require('@neondatabase/serverless');

async function testUsersAPI() {
  console.log('🎯 TESTING USERS API ENDPOINT');
  console.log('=============================');
  
  if (!process.env.DATABASE_URL) {
    console.error('❌ DATABASE_URL environment variable is not set');
    process.exit(1);
  }
  
  try {
    const sql = neon(process.env.DATABASE_URL);
    
    // Test 1: Direct database query
    console.log('🔄 Testing direct database query...');
    const directUsers = await sql`
      SELECT id, email, full_name, role, department, is_active
      FROM users 
      WHERE is_active = true
      ORDER BY full_name
    `;
    console.log(`✅ Direct query found ${directUsers.length} active users`);
    
    // Show sample users
    console.log('\n📋 Sample users from database:');
    directUsers.slice(0, 5).forEach(user => {
      console.log(`   • ${user.full_name} (${user.email}) - ${user.role}`);
    });
    
    // Test 2: Check if the new API endpoint file exists
    console.log('\n🔄 Checking API endpoint file...');
    const fs = require('fs');
    const apiPath = 'app/api/users/route.ts';
    
    if (fs.existsSync(apiPath)) {
      console.log('✅ /api/users endpoint file exists');
      
      // Check the content
      const content = fs.readFileSync(apiPath, 'utf8');
      if (content.includes('serverDb.sql.unsafe')) {
        console.log('✅ Endpoint uses correct database query method');
      } else {
        console.log('⚠️  Endpoint may have query issues');
      }
    } else {
      console.log('❌ /api/users endpoint file missing');
    }
    
    // Test 3: Check UserSelector component
    console.log('\n🔄 Checking UserSelector component...');
    const userSelectorPath = 'components/user-selector.tsx';
    
    if (fs.existsSync(userSelectorPath)) {
      const content = fs.readFileSync(userSelectorPath, 'utf8');
      if (content.includes('"/api/users"')) {
        console.log('✅ UserSelector calls /api/users endpoint');
      } else {
        console.log('❌ UserSelector not calling /api/users endpoint');
      }
    }
    
    // Test 4: Check TaskModal component
    console.log('\n🔄 Checking TaskModal component...');
    const taskModalPath = 'components/task-modal.tsx';
    
    if (fs.existsSync(taskModalPath)) {
      const content = fs.readFileSync(taskModalPath, 'utf8');
      if (content.includes('useActiveEmployees')) {
        console.log('✅ TaskModal uses useActiveEmployees hook');
        
        // Check if it calls the right endpoint
        const employeesHookPath = 'hooks/use-employees.ts';
        if (fs.existsSync(employeesHookPath)) {
          const hookContent = fs.readFileSync(employeesHookPath, 'utf8');
          if (hookContent.includes('/api/admin/users')) {
            console.log('✅ useActiveEmployees calls /api/admin/users endpoint');
          } else {
            console.log('❌ useActiveEmployees not calling correct endpoint');
          }
        }
      } else {
        console.log('❌ TaskModal not using useActiveEmployees hook');
      }
    }
    
    console.log('\n🎯 DIAGNOSIS:');
    console.log('=============');
    
    if (directUsers.length >= 9) {
      console.log(`✅ Database has ${directUsers.length} active users (expected 9+)`);
    } else {
      console.log(`⚠️  Database has only ${directUsers.length} active users (expected 9+)`);
    }
    
    console.log('\n📋 RECOMMENDATIONS:');
    console.log('1. The new /api/users endpoint should fix UserSelector issues');
    console.log('2. TaskModal should use useActiveEmployees which calls /api/admin/users');
    console.log('3. Both endpoints should now return all 9 active users');
    console.log('4. Test the application to verify task assignment shows all users');
    
  } catch (error) {
    console.error('❌ Test failed:', error.message);
    process.exit(1);
  }
}

testUsersAPI();
