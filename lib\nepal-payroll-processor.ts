// Nepal-Compliant Payroll Processor
// Complete payroll processing system compliant with Nepal employment laws

import { db } from './neon';
import { nepalTaxCalculator, NepalTaxCalculation, NepalPayrollCompliance } from './nepal-tax-calculator';
import { enhancedPayrollCalculator } from './enhanced-payroll-calculator';
import { enhancedAttendancePayrollIntegration, EnhancedAttendanceSummary, PayrollComponentCalculation } from './enhanced-attendance-payroll-integration';

export interface NepalPayrollRecord {
  id: string;
  user_id: string;
  employee_name: string;
  employee_code: string;
  department: string;
  position: string;
  
  // Payroll period
  fiscal_year: string;
  bs_month: string;
  ad_month_start: string;
  ad_month_end: string;
  payroll_period: string;
  
  // Basic salary information
  base_salary: number;
  daily_rate: number;
  hourly_rate: number;
  
  // Attendance summary
  total_working_days: number;
  days_present: number;
  days_absent: number;
  days_late: number;
  days_half_day: number;
  days_on_leave: number;
  total_hours_worked: number;
  regular_hours: number;
  overtime_hours: number;
  
  // Earnings
  basic_pay: number;
  overtime_pay: number;
  attendance_bonus: number;
  festival_bonus: number;
  total_allowances: number;
  gross_earnings: number;
  
  // Statutory deductions
  provident_fund_employee: number;
  provident_fund_employer: number;
  social_security_fund_employee: number;
  social_security_fund_employer: number;
  income_tax: number;
  
  // Other deductions
  other_deductions: number;
  late_penalty: number;
  advance_recovery: number;
  loan_deduction: number;
  
  // Final calculations
  total_deductions: number;
  net_pay: number;
  
  // Nepal compliance
  tax_calculation: NepalTaxCalculation;
  compliance_details: NepalPayrollCompliance;
  compliance_status: 'compliant' | 'non_compliant';
  compliance_notes: string[];
  
  // Processing information
  status: 'draft' | 'calculated' | 'approved' | 'processed' | 'paid';
  calculated_at: string;
  calculated_by: string;
  approved_at?: string;
  approved_by?: string;
  processed_at?: string;
  processed_by?: string;
  
  created_at: string;
  updated_at: string;
}

export interface PayrollSummary {
  fiscal_year: string;
  month: string;
  total_employees: number;
  total_gross_pay: number;
  total_net_pay: number;
  total_tax: number;
  total_pf: number;
  total_ssf: number;
  compliance_rate: number;
  processing_status: {
    draft: number;
    calculated: number;
    approved: number;
    processed: number;
    paid: number;
  };
}

export class NepalPayrollProcessor {
  
  // Process monthly payroll for all employees
  async processMonthlyPayroll(year: number, month: number, processedBy: string): Promise<{
    success: boolean;
    processed_count: number;
    failed_count: number;
    payroll_records: NepalPayrollRecord[];
    summary: PayrollSummary;
  }> {
    try {
      // Get fiscal year and period information
      const fiscalYear = nepalTaxCalculator.getCurrentFiscalYear();
      const payrollPeriod = nepalTaxCalculator.getMonthlyPayrollPeriod(year, month);
      
      // Get all active employees
      const employees = await db.sql`
        SELECT 
          id, full_name, email, department, position, salary, employee_type,
          hire_date, citizenship_number, pan_number
        FROM users 
        WHERE role != 'admin' AND employment_status = 'active'
        ORDER BY full_name
      `;
      
      const payrollRecords: NepalPayrollRecord[] = [];
      let processedCount = 0;
      let failedCount = 0;
      
      for (const employee of employees) {
        try {
          const payrollRecord = await this.processEmployeePayroll(
            employee.id,
            year,
            month,
            fiscalYear,
            payrollPeriod,
            processedBy
          );
          
          payrollRecords.push(payrollRecord);
          processedCount++;
        } catch (error) {
          console.error(`Failed to process payroll for employee ${employee.id}:`, error);
          failedCount++;
        }
      }
      
      // Generate summary
      const summary = this.generatePayrollSummary(payrollRecords, fiscalYear.fiscal_year, `${year}-${month.toString().padStart(2, '0')}`);
      
      return {
        success: true,
        processed_count: processedCount,
        failed_count: failedCount,
        payroll_records: payrollRecords,
        summary
      };
      
    } catch (error) {
      console.error('Error processing monthly payroll:', error);
      throw new Error('Failed to process monthly payroll');
    }
  }
  
  // Process payroll for individual employee
  async processEmployeePayroll(
    userId: string,
    year: number,
    month: number,
    fiscalYear: any,
    payrollPeriod: any,
    processedBy: string
  ): Promise<NepalPayrollRecord> {
    try {
      // Get employee details
      const employee = await db.sql`
        SELECT 
          id, full_name, email, department, position, salary, employee_type,
          hire_date, citizenship_number, pan_number
        FROM users 
        WHERE id = ${userId}
      `;
      
      if (employee.length === 0) {
        throw new Error('Employee not found');
      }
      
      const emp = employee[0];
      
      // Get enhanced attendance summary with payroll integration
      const attendanceSummary = await enhancedAttendancePayrollIntegration.getEnhancedAttendanceSummary(userId, year, month);
      
      // Calculate basic pay components
      const baseSalary = emp.salary || 0;
      const dailyRate = baseSalary / attendanceSummary.total_working_days;
      const hourlyRate = dailyRate / 8; // 8 hours per day

      // Calculate years of service
      const hireDate = new Date(emp.hire_date);
      const currentDate = new Date();
      const yearsOfService = Math.floor((currentDate.getTime() - hireDate.getTime()) / (365.25 * 24 * 60 * 60 * 1000));

      // Calculate basic pay based on attendance (including paid leave)
      const basicPay = (attendanceSummary.days_present + attendanceSummary.days_on_leave) * dailyRate;

      // Calculate overtime pay using enhanced calculation
      const overtimePay = attendanceSummary.overtime_hours * hourlyRate * attendanceSummary.overtime_rate_multiplier;

      // Get payroll components (allowances and deductions) using new system
      const payrollComponents = await enhancedAttendancePayrollIntegration.calculatePayrollComponents(
        userId,
        attendanceSummary,
        baseSalary
      );
      
      // Calculate allowances from payroll components
      const allowanceComponents = payrollComponents.filter(c => c.component_type === 'allowance' && c.is_applicable);
      const totalAllowances = allowanceComponents.reduce((sum, comp) => sum + comp.calculated_amount, 0);

      // Extract specific allowances for reporting
      const attendanceBonus = allowanceComponents.find(c => c.component_code === 'ATTENDANCE_BONUS_AUTO')?.calculated_amount || 0;
      const festivalBonus = allowanceComponents.find(c => c.component_code === 'FESTIVAL_BONUS')?.calculated_amount || 0;
      
      // Calculate gross earnings
      const grossEarnings = basicPay + overtimePay + totalAllowances;
      
      // Calculate Nepal-compliant payroll components
      const complianceDetails = nepalTaxCalculator.calculatePayrollCompliance(
        baseSalary,
        payrollPeriod.working_days,
        attendanceSummary.days_present,
        attendanceSummary.regular_hours,
        attendanceSummary.overtime_hours,
        yearsOfService
      );
      
      // Calculate annual income for tax calculation
      const annualIncome = grossEarnings * 12;
      const taxCalculation = nepalTaxCalculator.calculateIncomeTax(annualIncome);
      
      // Calculate deductions from payroll components
      const deductionComponents = payrollComponents.filter(c => c.component_type === 'deduction' && c.is_applicable);
      const totalOtherDeductions = deductionComponents.reduce((sum, comp) => sum + comp.calculated_amount, 0);

      // Extract specific deductions for reporting
      const latePenalty = deductionComponents.find(c => c.component_code === 'LATE_PENALTY_AUTO')?.calculated_amount || 0;
      const loanDeduction = deductionComponents.find(c => c.component_code === 'LOAN_DEDUCTION')?.calculated_amount || 0;
      const advanceRecovery = deductionComponents.find(c => c.component_code === 'ADVANCE_DEDUCTION')?.calculated_amount || 0;
      
      // Calculate total deductions
      const totalDeductions = 
        complianceDetails.provident_fund.employee_contribution +
        complianceDetails.social_security_fund.employee_contribution +
        taxCalculation.monthly_tax +
        totalOtherDeductions +
        latePenalty;
      
      // Calculate net pay
      const netPay = grossEarnings - totalDeductions;
      
      // Validate compliance
      const complianceValidation = nepalTaxCalculator.validatePayrollCompliance({
        monthly_salary: baseSalary,
        provident_fund: complianceDetails.provident_fund.employee_contribution,
        overtime_hours: attendanceSummary.overtime_hours,
        total_hours: attendanceSummary.total_hours_worked,
        festival_bonus: festivalBonus,
        years_of_service: yearsOfService,
        gratuity: complianceDetails.gratuity
      });
      
      // Create payroll record
      const payrollRecord: NepalPayrollRecord = {
        id: '', // Will be set when saved to database
        user_id: userId,
        employee_name: emp.full_name,
        employee_code: emp.email.split('@')[0], // Use email prefix as employee code
        department: emp.department || 'Not Assigned',
        position: emp.position || 'Not Assigned',
        
        fiscal_year: fiscalYear.fiscal_year,
        bs_month: nepalTaxCalculator.convertADToBS(`${year}-${month.toString().padStart(2, '0')}-01`).substring(0, 7),
        ad_month_start: payrollPeriod.ad_start_date,
        ad_month_end: payrollPeriod.ad_end_date,
        payroll_period: `${year}-${month.toString().padStart(2, '0')}`,
        
        base_salary: baseSalary,
        daily_rate: dailyRate,
        hourly_rate: hourlyRate,
        
        total_working_days: attendanceSummary.total_working_days,
        days_present: attendanceSummary.days_present,
        days_absent: attendanceSummary.days_absent,
        days_late: attendanceSummary.days_late,
        days_half_day: attendanceSummary.days_half_day,
        days_on_leave: attendanceSummary.days_on_leave,
        total_hours_worked: attendanceSummary.total_hours_worked,
        regular_hours: attendanceSummary.regular_hours,
        overtime_hours: attendanceSummary.overtime_hours,
        
        basic_pay: basicPay,
        overtime_pay: overtimePay,
        attendance_bonus: attendanceBonus,
        festival_bonus: festivalBonus,
        total_allowances: totalAllowances,
        gross_earnings: grossEarnings,
        
        provident_fund_employee: complianceDetails.provident_fund.employee_contribution,
        provident_fund_employer: complianceDetails.provident_fund.employer_contribution,
        social_security_fund_employee: complianceDetails.social_security_fund.employee_contribution,
        social_security_fund_employer: complianceDetails.social_security_fund.employer_contribution,
        income_tax: taxCalculation.monthly_tax,
        
        other_deductions: totalOtherDeductions,
        late_penalty: latePenalty,
        advance_recovery: advanceRecovery,
        loan_deduction: loanDeduction,

        total_deductions: totalDeductions,
        net_pay: netPay,
        
        tax_calculation: taxCalculation,
        compliance_details: complianceDetails,
        compliance_status: complianceValidation.is_compliant ? 'compliant' : 'non_compliant',
        compliance_notes: [...complianceValidation.violations, ...complianceValidation.recommendations],
        
        status: 'calculated',
        calculated_at: new Date().toISOString(),
        calculated_by: processedBy,
        
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      };
      
      // Save to database
      const savedRecord = await this.savePayrollRecord(payrollRecord);
      payrollRecord.id = savedRecord.id;

      // Also save to monthly payroll summary table
      await this.saveMonthlyPayrollSummary(payrollRecord, attendanceSummary, processedBy);

      return payrollRecord;
      
    } catch (error) {
      console.error('Error processing employee payroll:', error);
      throw new Error(`Failed to process payroll for employee ${userId}`);
    }
  }
  
  // Save payroll record to database
  async savePayrollRecord(record: NepalPayrollRecord): Promise<{ id: string }> {
    try {
      const result = await db.sql`
        INSERT INTO monthly_payroll_summary (
          user_id, fiscal_year, bs_month, ad_month_start, ad_month_end,
          total_working_days, days_present, days_absent, days_late, days_half_day, days_on_leave,
          total_hours_worked, regular_hours, overtime_hours,
          base_salary, overtime_pay, total_allowances, total_deductions,
          gross_pay, tax_deductions, provident_fund, net_pay,
          attendance_bonus, late_penalty,
          status, calculated_at, calculated_by
        ) VALUES (
          ${record.user_id}, ${record.fiscal_year}, ${record.bs_month},
          ${record.ad_month_start}, ${record.ad_month_end},
          ${record.total_working_days}, ${record.days_present}, ${record.days_absent},
          ${record.days_late}, ${record.days_half_day}, ${record.days_on_leave},
          ${record.total_hours_worked}, ${record.regular_hours}, ${record.overtime_hours},
          ${record.base_salary}, ${record.overtime_pay}, ${record.total_allowances},
          ${record.total_deductions}, ${record.gross_earnings}, ${record.income_tax},
          ${record.provident_fund_employee}, ${record.net_pay}, ${record.attendance_bonus},
          ${record.late_penalty}, ${record.status}, ${record.calculated_at}, ${record.calculated_by}
        ) RETURNING id
      `;
      
      return { id: result[0].id };
      
    } catch (error) {
      console.error('Error saving payroll record:', error);
      throw new Error('Failed to save payroll record');
    }
  }
  
  // Generate payroll summary
  private generatePayrollSummary(records: NepalPayrollRecord[], fiscalYear: string, month: string): PayrollSummary {
    const totalEmployees = records.length;
    const totalGrossPay = records.reduce((sum, record) => sum + record.gross_earnings, 0);
    const totalNetPay = records.reduce((sum, record) => sum + record.net_pay, 0);
    const totalTax = records.reduce((sum, record) => sum + record.income_tax, 0);
    const totalPF = records.reduce((sum, record) => sum + record.provident_fund_employee, 0);
    const totalSSF = records.reduce((sum, record) => sum + record.social_security_fund_employee, 0);
    
    const compliantRecords = records.filter(record => record.compliance_status === 'compliant').length;
    const complianceRate = totalEmployees > 0 ? (compliantRecords / totalEmployees) * 100 : 0;
    
    const statusCounts = records.reduce((counts, record) => {
      counts[record.status] = (counts[record.status] || 0) + 1;
      return counts;
    }, {} as any);
    
    return {
      fiscal_year: fiscalYear,
      month: month,
      total_employees: totalEmployees,
      total_gross_pay: totalGrossPay,
      total_net_pay: totalNetPay,
      total_tax: totalTax,
      total_pf: totalPF,
      total_ssf: totalSSF,
      compliance_rate: complianceRate,
      processing_status: {
        draft: statusCounts.draft || 0,
        calculated: statusCounts.calculated || 0,
        approved: statusCounts.approved || 0,
        processed: statusCounts.processed || 0,
        paid: statusCounts.paid || 0
      }
    };
  }
  
  // Update payroll status
  async updatePayrollStatus(
    payrollIds: string[],
    status: 'approved' | 'processed' | 'paid',
    updatedBy: string
  ): Promise<{ updated: number; failed: number }> {
    try {
      let updated = 0;
      let failed = 0;
      
      for (const payrollId of payrollIds) {
        try {
          let updateQuery = `
            UPDATE monthly_payroll_summary 
            SET status = $1, updated_at = NOW()
          `;
          const params = [status];
          
          if (status === 'approved') {
            updateQuery += `, approved_by = $2, approved_at = NOW()`;
            params.push(updatedBy);
          } else if (status === 'processed') {
            updateQuery += `, processed_by = $2, processed_at = NOW()`;
            params.push(updatedBy);
          }
          
          updateQuery += ` WHERE id = $${params.length + 1}`;
          params.push(payrollId);
          
          await db.sql([updateQuery, ...params]);
          updated++;
        } catch (error) {
          console.error(`Error updating payroll ${payrollId}:`, error);
          failed++;
        }
      }
      
      return { updated, failed };

    } catch (error) {
      console.error('Error updating payroll status:', error);
      throw new Error('Failed to update payroll status');
    }
  }

  // Save monthly payroll summary to the new table
  async saveMonthlyPayrollSummary(
    payrollRecord: NepalPayrollRecord,
    attendanceSummary: EnhancedAttendanceSummary,
    processedBy: string
  ): Promise<void> {
    try {
      await db.sql`
        INSERT INTO monthly_payroll_summary (
          user_id, fiscal_year, bs_month, ad_month_start, ad_month_end,
          total_working_days, days_present, days_absent, days_late,
          days_half_day, days_on_leave, total_hours_worked, regular_hours,
          overtime_hours, base_salary, overtime_pay, total_allowances,
          total_deductions, gross_pay, tax_deductions, provident_fund,
          net_pay, attendance_bonus, late_penalty, status, calculated_at,
          calculated_by
        ) VALUES (
          ${payrollRecord.user_id}, ${payrollRecord.fiscal_year}, ${payrollRecord.bs_month},
          ${payrollRecord.ad_month_start}, ${payrollRecord.ad_month_end},
          ${attendanceSummary.total_working_days}, ${attendanceSummary.days_present},
          ${attendanceSummary.days_absent}, ${attendanceSummary.days_late},
          ${attendanceSummary.days_half_day}, ${attendanceSummary.days_on_leave},
          ${attendanceSummary.total_hours_worked}, ${attendanceSummary.regular_hours},
          ${attendanceSummary.overtime_hours}, ${payrollRecord.base_salary},
          ${payrollRecord.overtime_pay}, ${payrollRecord.total_allowances},
          ${payrollRecord.total_deductions}, ${payrollRecord.gross_earnings},
          ${payrollRecord.income_tax}, ${payrollRecord.provident_fund_employee},
          ${payrollRecord.net_pay}, ${payrollRecord.attendance_bonus},
          ${payrollRecord.late_penalty}, ${payrollRecord.status}, NOW(), ${processedBy}
        )
        ON CONFLICT (user_id, fiscal_year, bs_month)
        DO UPDATE SET
          total_working_days = EXCLUDED.total_working_days,
          days_present = EXCLUDED.days_present,
          days_absent = EXCLUDED.days_absent,
          days_late = EXCLUDED.days_late,
          days_half_day = EXCLUDED.days_half_day,
          days_on_leave = EXCLUDED.days_on_leave,
          total_hours_worked = EXCLUDED.total_hours_worked,
          regular_hours = EXCLUDED.regular_hours,
          overtime_hours = EXCLUDED.overtime_hours,
          base_salary = EXCLUDED.base_salary,
          overtime_pay = EXCLUDED.overtime_pay,
          total_allowances = EXCLUDED.total_allowances,
          total_deductions = EXCLUDED.total_deductions,
          gross_pay = EXCLUDED.gross_pay,
          tax_deductions = EXCLUDED.tax_deductions,
          provident_fund = EXCLUDED.provident_fund,
          net_pay = EXCLUDED.net_pay,
          attendance_bonus = EXCLUDED.attendance_bonus,
          late_penalty = EXCLUDED.late_penalty,
          status = EXCLUDED.status,
          calculated_at = EXCLUDED.calculated_at,
          calculated_by = EXCLUDED.calculated_by,
          updated_at = NOW()
      `;
    } catch (error) {
      console.error('Error saving monthly payroll summary:', error);
      throw new Error('Failed to save monthly payroll summary');
    }
  }
}

export const nepalPayrollProcessor = new NepalPayrollProcessor();
