#!/usr/bin/env node

// Test script to verify the kanban board fix
// This simulates the data processing logic to ensure it handles edge cases

console.log('🧪 TESTING KANBAN BOARD FIX');
console.log('============================\n');

// Simulate the defensive programming logic from the fixed kanban board
function processTasksResponse(tasksResponse) {
  // Handle different possible response structures
  if (!tasksResponse) return []

  // If tasksResponse is already an array (direct response)
  if (Array.isArray(tasksResponse)) return tasksResponse

  // If tasksResponse has data.tasks structure (API wrapper)
  if (tasksResponse.data && Array.isArray(tasksResponse.data.tasks)) {
    return tasksResponse.data.tasks
  }

  // If tasksResponse has tasks directly
  if (Array.isArray(tasksResponse.tasks)) {
    return tasksResponse.tasks
  }

  // If tasksResponse.data is an array
  if (Array.isArray(tasksResponse.data)) {
    return tasksResponse.data
  }

  // Fallback to empty array
  return []
}

// Test cases
const testCases = [
  {
    name: "Normal API response",
    input: {
      success: true,
      data: {
        tasks: [
          { id: "1", title: "Task 1", status: "todo" },
          { id: "2", title: "Task 2", status: "in_progress" },
          { id: "3", title: "Task 3", status: "completed" }
        ],
        pagination: { page: 1, limit: 20 }
      }
    },
    expected: 3
  },
  {
    name: "Undefined response",
    input: undefined,
    expected: 0
  },
  {
    name: "Null response",
    input: null,
    expected: 0
  },
  {
    name: "Empty object",
    input: {},
    expected: 0
  },
  {
    name: "Response with null data",
    input: { data: null },
    expected: 0
  },
  {
    name: "Response with undefined tasks",
    input: { data: { tasks: undefined } },
    expected: 0
  },
  {
    name: "Response with null tasks",
    input: { data: { tasks: null } },
    expected: 0
  },
  {
    name: "Response with non-array tasks",
    input: { data: { tasks: "not an array" } },
    expected: 0
  },
  {
    name: "Direct array response",
    input: [
      { id: "1", title: "Task 1", status: "todo" },
      { id: "2", title: "Task 2", status: "in_progress" }
    ],
    expected: 2
  },
  {
    name: "Response with tasks at root level",
    input: {
      tasks: [
        { id: "1", title: "Task 1", status: "todo" }
      ]
    },
    expected: 1
  },
  {
    name: "Response with data as array",
    input: {
      data: [
        { id: "1", title: "Task 1", status: "todo" },
        { id: "2", title: "Task 2", status: "completed" }
      ]
    },
    expected: 2
  }
]

// Run tests
let passed = 0
let failed = 0

testCases.forEach((testCase, index) => {
  console.log(`🔄 Test ${index + 1}: ${testCase.name}`)
  
  try {
    const result = processTasksResponse(testCase.input)
    
    // Check if result is an array
    if (!Array.isArray(result)) {
      console.log(`❌ FAILED: Result is not an array (got ${typeof result})`)
      failed++
      return
    }
    
    // Check if length matches expected
    if (result.length !== testCase.expected) {
      console.log(`❌ FAILED: Expected ${testCase.expected} tasks, got ${result.length}`)
      failed++
      return
    }
    
    // Test that filter method works (this was the original error)
    try {
      const todoTasks = result.filter(task => task.status === "todo")
      const inProgressTasks = result.filter(task => task.status === "in_progress")
      const completedTasks = result.filter(task => task.status === "completed")
      
      console.log(`✅ PASSED: Got ${result.length} tasks, filter method works`)
      console.log(`   - Todo: ${todoTasks.length}, In Progress: ${inProgressTasks.length}, Completed: ${completedTasks.length}`)
      passed++
    } catch (filterError) {
      console.log(`❌ FAILED: Filter method error: ${filterError.message}`)
      failed++
    }
    
  } catch (error) {
    console.log(`❌ FAILED: ${error.message}`)
    failed++
  }
  
  console.log('')
})

// Summary
console.log('📊 TEST SUMMARY:')
console.log(`✅ Passed: ${passed}`)
console.log(`❌ Failed: ${failed}`)
console.log(`📈 Success Rate: ${Math.round((passed / (passed + failed)) * 100)}%`)

if (failed === 0) {
  console.log('\n🎉 ALL TESTS PASSED! The kanban board fix should work correctly.')
} else {
  console.log('\n⚠️ Some tests failed. The fix may need additional work.')
}
