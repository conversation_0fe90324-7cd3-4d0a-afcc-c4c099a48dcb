"use client"

import { useState, useEffect } from "react"
import { Check, ChevronsUpDown, User, Search } from "lucide-react"
import { cn } from "@/lib/utils"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Avatar, AvatarFallback } from "@/components/ui/avatar"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Label } from "@/components/ui/label"
import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
  CommandList,
} from "@/components/ui/command"
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover"

interface User {
  id: string
  name?: string
  full_name?: string
  email?: string
  role?: string
  is_active?: boolean
  avatar?: string
}

// Legacy interface for backward compatibility
interface LegacyUserSelectorProps {
  users: User[]
  selectedUser: string | null
  onSelectUser: (userId: string | null) => void
  className?: string
}

// Enhanced interface for new functionality
interface EnhancedUserSelectorProps {
  value?: string
  onValueChange: (value: string | undefined) => void
  placeholder?: string
  disabled?: boolean
  allowUnassigned?: boolean
  filterByRole?: string[]
  mode?: "enhanced" | "legacy"
}

type UserSelectorProps = LegacyUserSelectorProps | EnhancedUserSelectorProps

function isEnhancedProps(props: UserSelectorProps): props is EnhancedUserSelectorProps {
  return 'onValueChange' in props
}

export function UserSelector(props: UserSelectorProps) {
  // Legacy mode for backward compatibility
  if (!isEnhancedProps(props)) {
    const { users, selectedUser, onSelectUser, className = "" } = props
    return (
      <div className={`flex items-center gap-2 ${className}`}>
        <Label htmlFor="user-filter" className="text-sm whitespace-nowrap text-teal-700">
          Team Member:
        </Label>
        <Select value={selectedUser || "all"} onValueChange={(value) => onSelectUser(value === "all" ? null : value)}>
          <SelectTrigger id="user-filter" className="w-[140px]">
            <SelectValue placeholder="All Users" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="all">All Users</SelectItem>
            {users.map((user) => (
              <SelectItem key={user.id} value={user.id}>
                {user.name || user.full_name}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
      </div>
    )
  }

  // Enhanced mode with search and better UX
  const {
    value,
    onValueChange,
    placeholder = "Select user...",
    disabled = false,
    allowUnassigned = true,
    filterByRole = [],
  } = props

  const [open, setOpen] = useState(false)
  const [users, setUsers] = useState<User[]>([])
  const [loading, setLoading] = useState(false)
  const [searchQuery, setSearchQuery] = useState("")

  // Fetch users
  useEffect(() => {
    const fetchUsers = async () => {
      setLoading(true)
      try {
        const response = await fetch("/api/users", {
          credentials: "include",
        })

        if (response.ok) {
          const data = await response.json()
          let filteredUsers = data.data || []

          // Filter by role if specified
          if (filterByRole.length > 0) {
            filteredUsers = filteredUsers.filter((user: User) =>
              filterByRole.includes(user.role || "")
            )
          }

          // Only show active users
          filteredUsers = filteredUsers.filter((user: User) => user.is_active !== false)

          setUsers(filteredUsers)
        }
      } catch (error) {
        console.error("Failed to fetch users:", error)
      } finally {
        setLoading(false)
      }
    }

    if (open) {
      fetchUsers()
    }
  }, [open, filterByRole])

  // Filter users based on search query
  const filteredUsers = users.filter(user => {
    const name = user.full_name || user.name || ""
    const email = user.email || ""
    const query = searchQuery.toLowerCase()
    return name.toLowerCase().includes(query) || email.toLowerCase().includes(query)
  })

  const selectedUser = users.find(user => user.id === value)

  const getInitials = (name: string) => {
    return name
      .split(" ")
      .map(n => n[0])
      .join("")
      .toUpperCase()
      .slice(0, 2)
  }

  const handleSelect = (userId: string | undefined) => {
    onValueChange(userId)
    setOpen(false)
  }

  return (
    <Popover open={open} onOpenChange={setOpen}>
      <PopoverTrigger asChild>
        <Button
          variant="outline"
          role="combobox"
          aria-expanded={open}
          className="w-full justify-between"
          disabled={disabled}
        >
          {selectedUser ? (
            <div className="flex items-center gap-2">
              <Avatar className="h-6 w-6">
                <AvatarFallback className="text-xs">
                  {getInitials(selectedUser.full_name || selectedUser.name || "U")}
                </AvatarFallback>
              </Avatar>
              <span className="truncate">{selectedUser.full_name || selectedUser.name}</span>
            </div>
          ) : (
            <span className="text-muted-foreground">{placeholder}</span>
          )}
          <ChevronsUpDown className="ml-2 h-4 w-4 shrink-0 opacity-50" />
        </Button>
      </PopoverTrigger>
      <PopoverContent className="w-full p-0" align="start">
        <Command>
          <div className="flex items-center border-b px-3">
            <Search className="mr-2 h-4 w-4 shrink-0 opacity-50" />
            <Input
              placeholder="Search users..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="border-0 focus-visible:ring-0 focus-visible:ring-offset-0"
            />
          </div>
          <CommandList>
            {loading ? (
              <div className="p-4 text-center text-sm text-muted-foreground">
                Loading users...
              </div>
            ) : (
              <>
                {allowUnassigned && (
                  <CommandGroup>
                    <CommandItem
                      value="unassigned"
                      onSelect={() => handleSelect(undefined)}
                    >
                      <div className="flex items-center gap-2">
                        <div className="h-6 w-6 rounded-full bg-gray-200 dark:bg-gray-700 flex items-center justify-center">
                          <User className="h-3 w-3 text-gray-500" />
                        </div>
                        <span>Unassigned</span>
                      </div>
                      <Check
                        className={cn(
                          "ml-auto h-4 w-4",
                          !value ? "opacity-100" : "opacity-0"
                        )}
                      />
                    </CommandItem>
                  </CommandGroup>
                )}

                {filteredUsers.length > 0 ? (
                  <CommandGroup heading="Users">
                    {filteredUsers.map((user) => (
                      <CommandItem
                        key={user.id}
                        value={user.id}
                        onSelect={() => handleSelect(user.id)}
                      >
                        <div className="flex items-center gap-2">
                          <Avatar className="h-6 w-6">
                            <AvatarFallback className="text-xs">
                              {getInitials(user.full_name || user.name || "U")}
                            </AvatarFallback>
                          </Avatar>
                          <div className="flex flex-col">
                            <span className="text-sm font-medium">
                              {user.full_name || user.name}
                            </span>
                            {user.email && (
                              <span className="text-xs text-muted-foreground">
                                {user.email}
                              </span>
                            )}
                          </div>
                        </div>
                        <Check
                          className={cn(
                            "ml-auto h-4 w-4",
                            value === user.id ? "opacity-100" : "opacity-0"
                          )}
                        />
                      </CommandItem>
                    ))}
                  </CommandGroup>
                ) : (
                  !loading && (
                    <CommandEmpty>
                      {searchQuery ? "No users found." : "No users available."}
                    </CommandEmpty>
                  )
                )}
              </>
            )}
          </CommandList>
        </Command>
      </PopoverContent>
    </Popover>
  )
}
