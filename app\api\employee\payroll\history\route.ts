// Employee Payroll History API Endpoint
// Phase 2: Core Payroll Engine Development - API Endpoints

import { NextRequest, NextResponse } from 'next/server';
import { db } from '@/lib/neon';
import { nepalConfig } from '@/lib/nepal-config';

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const userId = searchParams.get('userId');
    const limit = parseInt(searchParams.get('limit') || '10');
    const offset = parseInt(searchParams.get('offset') || '0');
    const fiscalYear = searchParams.get('fiscalYear');
    const status = searchParams.get('status');

    if (!userId) {
      return NextResponse.json(
        { success: false, error: 'User ID is required' },
        { status: 400 }
      );
    }

    // Build query conditions
    let whereConditions = 'WHERE p.user_id = $1';
    const queryParams = [userId];
    let paramIndex = 2;

    if (fiscalYear) {
      whereConditions += ` AND p.nepali_fiscal_year = $${paramIndex}`;
      queryParams.push(fiscalYear);
      paramIndex++;
    }

    if (status) {
      whereConditions += ` AND p.status = $${paramIndex}`;
      queryParams.push(status);
      paramIndex++;
    }

    // Get payroll history
    const payrollHistory = await db.sql`
      SELECT 
        p.*,
        u.full_name,
        u.email,
        u.department,
        u.position
      FROM payroll p
      JOIN users u ON p.user_id = u.id
      ${db.sql.raw(whereConditions)}
      ORDER BY p.pay_period_start DESC
      LIMIT ${limit}
      OFFSET ${offset}
    `;

    // Get total count for pagination
    const countResult = await db.sql`
      SELECT COUNT(*) as total
      FROM payroll p
      ${db.sql.raw(whereConditions)}
    `;

    const totalCount = parseInt(countResult[0].total);

    // Format payroll history with Nepal-specific formatting
    const formattedHistory = payrollHistory.map(record => ({
      id: record.id,
      payPeriod: {
        start: record.pay_period_start,
        end: record.pay_period_end,
        bsStart: record.bs_pay_period_start,
        bsEnd: record.bs_pay_period_end,
        fiscalYear: record.nepali_fiscal_year
      },
      salary: {
        baseSalary: nepalConfig.formatCurrency(record.base_salary),
        grossPay: nepalConfig.formatCurrency(record.gross_pay),
        netPay: nepalConfig.formatCurrency(record.net_pay),
        totalDeductions: nepalConfig.formatCurrency(record.deductions),
        totalBonuses: nepalConfig.formatCurrency(record.bonuses)
      },
      attendance: {
        workingDays: record.working_days,
        totalHours: record.total_attendance_hours,
        regularHours: record.regular_hours,
        overtimeHours: record.overtime_hours_calculated,
        attendanceBonus: nepalConfig.formatCurrency(record.attendance_bonus),
        latePenalty: nepalConfig.formatCurrency(record.late_penalty)
      },
      deductions: {
        providentFund: nepalConfig.formatCurrency(record.provident_fund),
        incomeTax: nepalConfig.formatCurrency(record.tax_deductions),
        insurance: nepalConfig.formatCurrency(record.insurance_deduction),
        other: nepalConfig.formatCurrency(record.deductions - record.provident_fund - record.tax_deductions - record.insurance_deduction)
      },
      status: record.status,
      processedAt: record.processed_at,
      processedBy: record.processed_by,
      paidAt: record.paid_at,
      notes: record.notes
    }));

    return NextResponse.json({
      success: true,
      data: {
        payrollHistory: formattedHistory,
        pagination: {
          total: totalCount,
          limit,
          offset,
          hasMore: offset + limit < totalCount
        }
      }
    });

  } catch (error) {
    console.error('Error fetching payroll history:', error);
    return NextResponse.json(
      { 
        success: false, 
        error: 'Failed to fetch payroll history',
        details: error.message 
      },
      { status: 500 }
    );
  }
}

// Get payroll summary statistics
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { userId, fiscalYear } = body;

    if (!userId) {
      return NextResponse.json(
        { success: false, error: 'User ID is required' },
        { status: 400 }
      );
    }

    // Get current fiscal year if not provided
    const targetFiscalYear = fiscalYear || nepalConfig.getCurrentFiscalYear();

    // Get payroll summary for the fiscal year
    const summaryResult = await db.sql`
      SELECT 
        COUNT(*) as total_payrolls,
        SUM(gross_pay) as total_gross_pay,
        SUM(net_pay) as total_net_pay,
        SUM(deductions) as total_deductions,
        SUM(bonuses) as total_bonuses,
        SUM(provident_fund) as total_provident_fund,
        SUM(tax_deductions) as total_tax_deductions,
        SUM(working_days) as total_working_days,
        SUM(total_attendance_hours) as total_hours_worked,
        SUM(overtime_hours_calculated) as total_overtime_hours,
        SUM(attendance_bonus) as total_attendance_bonus,
        SUM(late_penalty) as total_late_penalty,
        AVG(gross_pay) as avg_gross_pay,
        AVG(net_pay) as avg_net_pay
      FROM payroll
      WHERE user_id = ${userId}
        AND nepali_fiscal_year = ${targetFiscalYear}
        AND status IN ('processed', 'paid')
    `;

    const summary = summaryResult[0];

    // Get monthly breakdown
    const monthlyBreakdown = await db.sql`
      SELECT 
        bs_pay_period_start,
        bs_pay_period_end,
        gross_pay,
        net_pay,
        deductions,
        bonuses,
        status,
        processed_at
      FROM payroll
      WHERE user_id = ${userId}
        AND nepali_fiscal_year = ${targetFiscalYear}
      ORDER BY pay_period_start ASC
    `;

    // Format summary with Nepal currency
    const formattedSummary = {
      fiscalYear: targetFiscalYear,
      totalPayrolls: parseInt(summary.total_payrolls || 0),
      earnings: {
        totalGrossPay: nepalConfig.formatCurrency(summary.total_gross_pay || 0),
        totalNetPay: nepalConfig.formatCurrency(summary.total_net_pay || 0),
        averageGrossPay: nepalConfig.formatCurrency(summary.avg_gross_pay || 0),
        averageNetPay: nepalConfig.formatCurrency(summary.avg_net_pay || 0),
        totalBonuses: nepalConfig.formatCurrency(summary.total_bonuses || 0)
      },
      deductions: {
        totalDeductions: nepalConfig.formatCurrency(summary.total_deductions || 0),
        totalProvidentFund: nepalConfig.formatCurrency(summary.total_provident_fund || 0),
        totalTaxDeductions: nepalConfig.formatCurrency(summary.total_tax_deductions || 0)
      },
      attendance: {
        totalWorkingDays: parseInt(summary.total_working_days || 0),
        totalHoursWorked: parseFloat(summary.total_hours_worked || 0),
        totalOvertimeHours: parseFloat(summary.total_overtime_hours || 0),
        totalAttendanceBonus: nepalConfig.formatCurrency(summary.total_attendance_bonus || 0),
        totalLatePenalty: nepalConfig.formatCurrency(summary.total_late_penalty || 0)
      },
      monthlyBreakdown: monthlyBreakdown.map(month => ({
        period: {
          bsStart: month.bs_pay_period_start,
          bsEnd: month.bs_pay_period_end
        },
        grossPay: nepalConfig.formatCurrency(month.gross_pay),
        netPay: nepalConfig.formatCurrency(month.net_pay),
        deductions: nepalConfig.formatCurrency(month.deductions),
        bonuses: nepalConfig.formatCurrency(month.bonuses),
        status: month.status,
        processedAt: month.processed_at
      }))
    };

    return NextResponse.json({
      success: true,
      data: formattedSummary
    });

  } catch (error) {
    console.error('Error fetching payroll summary:', error);
    return NextResponse.json(
      { 
        success: false, 
        error: 'Failed to fetch payroll summary',
        details: error.message 
      },
      { status: 500 }
    );
  }
}
