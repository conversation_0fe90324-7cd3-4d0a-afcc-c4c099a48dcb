-- Migration script to add missing columns to attendance table
-- This fixes the "column 'is_active' does not exist" error and related issues

-- First, check current table structure
SELECT 
    column_name, 
    data_type, 
    is_nullable,
    column_default
FROM information_schema.columns 
WHERE table_name = 'attendance' 
ORDER BY ordinal_position;

-- Add missing columns for enhanced attendance system
DO $$
BEGIN
    -- Add is_active column if it doesn't exist
    IF NOT EXISTS (
        SELECT FROM information_schema.columns 
        WHERE table_name = 'attendance' 
        AND column_name = 'is_active'
    ) THEN
        ALTER TABLE attendance ADD COLUMN is_active BOOLEAN DEFAULT FALSE;
        RAISE NOTICE 'Added is_active column';
    ELSE
        RAISE NOTICE 'is_active column already exists';
    END IF;

    -- Add daily_sequence column if it doesn't exist
    IF NOT EXISTS (
        SELECT FROM information_schema.columns 
        WHERE table_name = 'attendance' 
        AND column_name = 'daily_sequence'
    ) THEN
        ALTER TABLE attendance ADD COLUMN daily_sequence INTEGER DEFAULT 1;
        RAISE NOTICE 'Added daily_sequence column';
    ELSE
        RAISE NOTICE 'daily_sequence column already exists';
    END IF;

    -- Add entry_type column if it doesn't exist
    IF NOT EXISTS (
        SELECT FROM information_schema.columns 
        WHERE table_name = 'attendance' 
        AND column_name = 'entry_type'
    ) THEN
        ALTER TABLE attendance ADD COLUMN entry_type VARCHAR(20) DEFAULT 'regular';
        RAISE NOTICE 'Added entry_type column';
    ELSE
        RAISE NOTICE 'entry_type column already exists';
    END IF;

    -- Update existing records to have proper default values
    UPDATE attendance 
    SET 
        is_active = CASE 
            WHEN check_in_time IS NOT NULL AND check_out_time IS NULL THEN TRUE 
            ELSE FALSE 
        END,
        daily_sequence = COALESCE(daily_sequence, 1),
        entry_type = COALESCE(entry_type, 'regular')
    WHERE is_active IS NULL OR daily_sequence IS NULL OR entry_type IS NULL;

    RAISE NOTICE 'Updated existing records with default values';
END $$;

-- Remove the UNIQUE constraint on (user_id, date) to allow multiple daily sessions
DO $$
BEGIN
    -- Check if the constraint exists and drop it
    IF EXISTS (
        SELECT FROM information_schema.table_constraints 
        WHERE table_name = 'attendance' 
        AND constraint_type = 'UNIQUE'
        AND constraint_name LIKE '%user_id%date%'
    ) THEN
        -- Find the exact constraint name
        DECLARE
            constraint_name_var TEXT;
        BEGIN
            SELECT constraint_name INTO constraint_name_var
            FROM information_schema.table_constraints 
            WHERE table_name = 'attendance' 
            AND constraint_type = 'UNIQUE'
            AND constraint_name LIKE '%user_id%date%'
            LIMIT 1;
            
            IF constraint_name_var IS NOT NULL THEN
                EXECUTE 'ALTER TABLE attendance DROP CONSTRAINT ' || constraint_name_var;
                RAISE NOTICE 'Dropped unique constraint: %', constraint_name_var;
            END IF;
        END;
    ELSE
        RAISE NOTICE 'No unique constraint found on (user_id, date)';
    END IF;
END $$;

-- Add a new unique constraint that allows multiple sessions per day
-- but prevents duplicate active sessions
CREATE UNIQUE INDEX IF NOT EXISTS idx_attendance_active_session 
ON attendance (user_id, date, daily_sequence);

-- Add check constraint for entry_type
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT FROM information_schema.check_constraints 
        WHERE constraint_name = 'attendance_entry_type_check'
    ) THEN
        ALTER TABLE attendance ADD CONSTRAINT attendance_entry_type_check 
        CHECK (entry_type IN ('regular', 'overtime', 'break', 'meeting', 'training'));
        RAISE NOTICE 'Added entry_type check constraint';
    ELSE
        RAISE NOTICE 'entry_type check constraint already exists';
    END IF;
END $$;

-- Verify the changes
SELECT 
    column_name, 
    data_type, 
    is_nullable,
    column_default
FROM information_schema.columns 
WHERE table_name = 'attendance' 
AND column_name IN ('is_active', 'daily_sequence', 'entry_type', 'check_in_time', 'check_out_time')
ORDER BY column_name;

-- Show table constraints
SELECT 
    constraint_name,
    constraint_type
FROM information_schema.table_constraints 
WHERE table_name = 'attendance'
ORDER BY constraint_type, constraint_name;

-- Test the new schema with a sample insert (will be rolled back)
DO $$
BEGIN
    -- This is just a test - we'll rollback
    SAVEPOINT test_insert;
    
    INSERT INTO attendance (
        user_id, 
        date, 
        check_in_time, 
        status, 
        notes,
        entry_type, 
        daily_sequence, 
        is_active
    ) VALUES (
        (SELECT id FROM users LIMIT 1),
        CURRENT_DATE,
        NOW(),
        'present',
        'Test insert after migration',
        'regular',
        1,
        TRUE
    );
    
    RAISE NOTICE 'Test insert successful - schema is working correctly';
    
    -- Rollback the test insert
    ROLLBACK TO test_insert;
    RAISE NOTICE 'Test insert rolled back';
END $$;

RAISE NOTICE 'Migration completed successfully!';
RAISE NOTICE 'The attendance table now supports:';
RAISE NOTICE '- Multiple daily sessions (daily_sequence)';
RAISE NOTICE '- Active session tracking (is_active)';
RAISE NOTICE '- Entry type categorization (entry_type)';
RAISE NOTICE '- Enhanced attendance management features';
