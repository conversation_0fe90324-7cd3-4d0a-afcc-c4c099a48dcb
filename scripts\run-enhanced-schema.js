const { neon } = require('@neondatabase/serverless');
const fs = require('fs');

async function runSchemaEnhancement() {
  try {
    const sql = neon(process.env.DATABASE_URL);
    
    console.log('🔄 Running enhanced employee payroll schema...');
    
    // Read the enhanced schema
    const schemaSQL = fs.readFileSync('./scripts/02-enhanced-employee-payroll-schema.sql', 'utf8');
    
    // Split by semicolon and filter out empty statements
    const statements = schemaSQL
      .split(';')
      .map(stmt => stmt.trim())
      .filter(stmt => stmt.length > 0 && !stmt.startsWith('--'));
    
    console.log(`Found ${statements.length} SQL statements to execute`);
    
    let successCount = 0;
    let skipCount = 0;
    let errorCount = 0;
    
    for (let i = 0; i < statements.length; i++) {
      const statement = statements[i];
      if (statement) {
        try {
          // Use template literal to execute the statement
          await sql([statement]);
          successCount++;
          console.log(`✅ Statement ${i + 1}/${statements.length} executed successfully`);
        } catch (error) {
          if (error.message.includes('already exists') || 
              error.message.includes('duplicate') ||
              error.message.includes('does not exist') ||
              error.message.includes('column') && error.message.includes('already exists')) {
            skipCount++;
            console.log(`⚠️  Statement ${i + 1}: ${error.message.split('\n')[0]} (skipped)`);
          } else {
            errorCount++;
            console.error(`❌ Statement ${i + 1} failed: ${error.message.split('\n')[0]}`);
            // Don't exit on error, continue with other statements
          }
        }
      }
    }
    
    console.log(`\n📊 Schema enhancement summary:`);
    console.log(`   ✅ Successful: ${successCount}`);
    console.log(`   ⚠️  Skipped: ${skipCount}`);
    console.log(`   ❌ Errors: ${errorCount}`);
    
    // Verify the enhancements
    console.log('\n🔍 Verifying schema enhancements...');
    
    try {
      // Check new tables
      const tables = await sql`
        SELECT table_name 
        FROM information_schema.tables 
        WHERE table_name IN ('employee_bank_accounts', 'employee_addresses', 'employee_emergency_contacts', 'payroll_audit_trail', 'deduction_approvals', 'allowance_assignments', 'monthly_payroll_summary')
        ORDER BY table_name
      `;
      
      console.log('✅ New tables created:', tables.map(t => t.table_name));
      
      // Check enhanced users table
      const userColumns = await sql`
        SELECT column_name 
        FROM information_schema.columns 
        WHERE table_name = 'users' AND column_name IN ('employee_type', 'employee_category', 'tax_identification_number', 'citizenship_number', 'pan_number')
        ORDER BY column_name
      `;
      
      console.log('✅ Enhanced user columns:', userColumns.map(c => c.column_name));
      
      // Check if payroll_components_master table exists
      const componentTable = await sql`
        SELECT table_name 
        FROM information_schema.tables 
        WHERE table_name = 'payroll_components_master'
      `;
      
      if (componentTable.length > 0) {
        // Check default components
        const components = await sql`
          SELECT name, code, type 
          FROM payroll_components_master 
          WHERE code IN ('TRAVEL_ALLOW', 'PHONE_ALLOW', 'INCOME_TAX', 'PF_DEDUCTION')
          ORDER BY type, name
        `;
        
        console.log('✅ Default payroll components:', components);
      } else {
        console.log('⚠️  payroll_components_master table not found - may need to run base schema first');
      }
      
    } catch (verifyError) {
      console.error('❌ Verification failed:', verifyError.message);
    }
    
    console.log('\n🎉 Enhanced employee payroll schema process completed!');
    
  } catch (error) {
    console.error('❌ Schema enhancement failed:', error);
    process.exit(1);
  }
}

runSchemaEnhancement();
