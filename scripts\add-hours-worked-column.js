#!/usr/bin/env node

/**
 * Simple script to add the missing hours_worked column
 */

require('dotenv').config({ path: '.env.local' });
const { neon } = require('@neondatabase/serverless');

async function addHoursWorkedColumn() {
  try {
    const DATABASE_URL = process.env.DATABASE_URL;
    if (!DATABASE_URL) {
      throw new Error('DATABASE_URL not found in environment variables');
    }

    const sql = neon(DATABASE_URL);
    
    console.log('🔧 Adding hours_worked Column');
    console.log('==============================\n');
    
    // Check if hours_worked column exists
    console.log('1. Checking if hours_worked column exists...');
    const columns = await sql`
      SELECT column_name 
      FROM information_schema.columns 
      WHERE table_name = 'attendance' 
      AND column_name = 'hours_worked'
    `;
    
    if (columns.length > 0) {
      console.log('✅ hours_worked column already exists!');
      return;
    }
    
    console.log('❌ hours_worked column does not exist, adding it...');
    
    // Add the hours_worked column
    await sql`ALTER TABLE attendance ADD COLUMN hours_worked DECIMAL(4,2)`;
    console.log('✅ Added hours_worked column');
    
    // Verify the column was added
    const verification = await sql`
      SELECT column_name, data_type, is_nullable
      FROM information_schema.columns 
      WHERE table_name = 'attendance' 
      AND column_name = 'hours_worked'
    `;
    
    if (verification.length > 0) {
      console.log('✅ Verification successful:');
      console.log(`   Column: ${verification[0].column_name}`);
      console.log(`   Type: ${verification[0].data_type}`);
      console.log(`   Nullable: ${verification[0].is_nullable}`);
    } else {
      console.log('❌ Verification failed - column not found');
    }
    
    // Test insert and update
    console.log('\n2. Testing the column with sample data...');
    
    const users = await sql`SELECT id FROM users LIMIT 1`;
    if (users.length === 0) {
      console.log('⚠️  No users found for testing');
      return;
    }
    
    const testUserId = users[0].id;
    const today = new Date().toISOString().split('T')[0];
    
    // Clean up any existing test data
    await sql`DELETE FROM attendance WHERE user_id = ${testUserId} AND date = ${today} AND notes LIKE '%HOURS_TEST%'`;
    
    // Test insert
    const testInsert = await sql`
      INSERT INTO attendance (
        user_id, 
        date, 
        check_in_time, 
        status, 
        notes,
        entry_type, 
        daily_sequence, 
        is_active,
        hours_worked
      ) VALUES (
        ${testUserId},
        ${today},
        NOW(),
        'present',
        'HOURS_TEST - Insert verification',
        'regular',
        1,
        TRUE,
        1.5
      )
      RETURNING id, hours_worked
    `;
    
    console.log('✅ Test insert successful!');
    console.log(`   Record ID: ${testInsert[0].id}`);
    console.log(`   hours_worked: ${testInsert[0].hours_worked}`);
    
    // Test update
    const testUpdate = await sql`
      UPDATE attendance 
      SET 
        check_out_time = NOW(),
        hours_worked = 2.5,
        is_active = FALSE
      WHERE id = ${testInsert[0].id}
      RETURNING id, hours_worked, is_active
    `;
    
    console.log('✅ Test update successful!');
    console.log(`   hours_worked after update: ${testUpdate[0].hours_worked}`);
    console.log(`   is_active: ${testUpdate[0].is_active}`);
    
    // Clean up test data
    await sql`DELETE FROM attendance WHERE id = ${testInsert[0].id}`;
    console.log('✅ Test data cleaned up');
    
    console.log('\n🎉 hours_worked column added successfully!');
    console.log('The attendance system should now work correctly.');
    
  } catch (error) {
    console.error('❌ Failed to add hours_worked column:', error);
    process.exit(1);
  }
}

// Run the script
if (require.main === module) {
  addHoursWorkedColumn();
}

module.exports = { addHoursWorkedColumn };
