#!/usr/bin/env node

/**
 * Simple script to create sample users for testing
 * This script creates users directly without complex SQL file parsing
 */

const { neon } = require('@neondatabase/serverless');
const bcrypt = require('bcryptjs');
require('dotenv').config({ path: '.env.local' });

async function createSampleUsers() {
  console.log('👥 Creating sample users for testing...\n');
  
  if (!process.env.DATABASE_URL) {
    console.error('❌ ERROR: DATABASE_URL environment variable is not set');
    console.log('📝 Please update your .env.local file with your Neon connection string');
    process.exit(1);
  }
  
  try {
    const sql = neon(process.env.DATABASE_URL);
    
    // Test connection
    console.log('🔄 Testing database connection...');
    await sql`SELECT 1`;
    console.log('✅ Database connection successful!\n');
    
    // Hash password for all users
    console.log('🔄 Hashing password...');
    const passwordHash = await bcrypt.hash('admin123', 12);
    console.log('✅ Password hashed\n');
    
    // Check if users table exists
    console.log('🔄 Checking if users table exists...');
    const tableCheck = await sql`
      SELECT EXISTS (
        SELECT FROM information_schema.tables 
        WHERE table_schema = 'public' 
        AND table_name = 'users'
      );
    `;
    
    if (!tableCheck[0].exists) {
      console.log('❌ Users table does not exist. Please run the database setup first.');
      console.log('💡 Try running: npm run setup:user-management');
      process.exit(1);
    }
    console.log('✅ Users table exists\n');
    
    // Sample users data
    const sampleUsers = [
      {
        email: '<EMAIL>',
        full_name: 'Rajesh Kumar Sharma',
        role: 'admin',
        employee_id: 'EMP001',
        department: 'Information Technology',
        position: 'System Administrator',
        phone: '+977-9841234567',
        salary: 120000.00,
        employment_type: 'full_time',
        employment_status: 'active',
        hire_date: '2020-01-15',
        is_active: true,
        email_verified: true
      },
      {
        email: '<EMAIL>',
        full_name: 'Sunita Devi Thapa',
        role: 'hr_manager',
        employee_id: 'EMP002',
        department: 'Human Resources',
        position: 'HR Manager',
        phone: '+977-9851234567',
        salary: 95000.00,
        employment_type: 'full_time',
        employment_status: 'active',
        hire_date: '2019-06-01',
        is_active: true,
        email_verified: true
      },
      {
        email: '<EMAIL>',
        full_name: 'Prakash Bahadur Rana',
        role: 'manager',
        employee_id: 'EMP003',
        department: 'Operations',
        position: 'Operations Manager',
        phone: '+977-**********',
        salary: 85000.00,
        employment_type: 'full_time',
        employment_status: 'active',
        hire_date: '2018-03-10',
        is_active: true,
        email_verified: true
      },
      {
        email: '<EMAIL>',
        full_name: 'Anita Kumari Shrestha',
        role: 'staff',
        employee_id: 'EMP004',
        department: 'Finance & Accounting',
        position: 'Senior Accountant',
        phone: '+977-**********',
        salary: 65000.00,
        employment_type: 'full_time',
        employment_status: 'active',
        hire_date: '2021-08-15',
        is_active: true,
        email_verified: true
      }
    ];
    
    console.log('🔄 Creating sample users...');
    
    for (const userData of sampleUsers) {
      try {
        // Check if user already exists
        const existingUser = await sql`
          SELECT email FROM users WHERE email = ${userData.email}
        `;
        
        if (existingUser.length > 0) {
          console.log(`ℹ️  User ${userData.email} already exists, skipping...`);
          continue;
        }
        
        // Create user
        await sql`
          INSERT INTO users (
            email, password_hash, full_name, role, employee_id, department, position,
            phone, salary, employment_type, employment_status, hire_date, 
            is_active, email_verified
          ) VALUES (
            ${userData.email}, ${passwordHash}, ${userData.full_name}, ${userData.role},
            ${userData.employee_id}, ${userData.department}, ${userData.position},
            ${userData.phone}, ${userData.salary}, ${userData.employment_type}, 
            ${userData.employment_status}, ${userData.hire_date},
            ${userData.is_active}, ${userData.email_verified}
          )
        `;
        
        console.log(`✅ Created user: ${userData.email} (${userData.role})`);
        
      } catch (error) {
        if (error.message.includes('duplicate key')) {
          console.log(`ℹ️  User ${userData.email} already exists`);
        } else {
          console.error(`❌ Error creating user ${userData.email}:`, error.message);
        }
      }
    }
    
    // Verify users were created
    console.log('\n🔍 Verifying created users...');
    const users = await sql`
      SELECT email, role, full_name, is_active 
      FROM users 
      WHERE email IN ('<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>')
      ORDER BY role
    `;
    
    console.log(`📊 Found ${users.length} test users:`);
    users.forEach(user => {
      console.log(`   - ${user.email} (${user.role}) - ${user.full_name} ${user.is_active ? '✅' : '❌'}`);
    });
    
    console.log('\n🎉 Sample users created successfully!');
    console.log('\n📋 Login Credentials (all use password: admin123):');
    users.forEach(user => {
      console.log(`   ${user.email} / admin123 (${user.role})`);
    });
    
    console.log('\n🚀 Next steps:');
    console.log('1. Start your development server: npm run dev');
    console.log('2. Navigate to /auth/login');
    console.log('3. Login with any of the credentials above');
    console.log('4. Navigate to /admin for the admin dashboard');
    
  } catch (error) {
    console.error('❌ Error:', error.message);
    console.error('Full error:', error);
    process.exit(1);
  }
}

// Run the script
if (require.main === module) {
  createSampleUsers();
}

module.exports = { createSampleUsers };
