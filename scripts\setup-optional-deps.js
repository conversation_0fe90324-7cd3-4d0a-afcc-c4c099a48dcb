#!/usr/bin/env node

/**
 * Setup script for optional dependencies
 * This script helps install optional packages based on user preferences
 */

const { execSync } = require('child_process')
const fs = require('fs')
const path = require('path')

console.log('🚀 Nepal Payroll Management System - Optional Dependencies Setup\n')

// Check if package.json exists
const packageJsonPath = path.join(process.cwd(), 'package.json')
if (!fs.existsSync(packageJsonPath)) {
  console.error('❌ package.json not found. Please run this script from the project root.')
  process.exit(1)
}

// Read package.json
const packageJson = JSON.parse(fs.readFileSync(packageJsonPath, 'utf8'))

// Optional dependencies with descriptions
const optionalDeps = {
  '@sentry/nextjs': {
    description: 'Error tracking and performance monitoring',
    envVar: 'SENTRY_DSN',
    recommended: true
  },
  'next-pwa': {
    description: 'Progressive Web App features',
    envVar: 'ENABLE_PWA',
    recommended: false
  },
  '@next/bundle-analyzer': {
    description: 'Bundle size analysis',
    envVar: 'ANALYZE',
    recommended: false
  },
  'husky': {
    description: 'Git hooks for code quality',
    envVar: null,
    recommended: true
  },
  'lint-staged': {
    description: 'Run linters on staged files',
    envVar: null,
    recommended: true
  }
}

// Check which dependencies are already installed
const installedDeps = {
  ...packageJson.dependencies || {},
  ...packageJson.devDependencies || {}
}

console.log('📦 Checking optional dependencies...\n')

const toInstall = []
const alreadyInstalled = []

Object.entries(optionalDeps).forEach(([dep, info]) => {
  if (installedDeps[dep]) {
    alreadyInstalled.push(dep)
    console.log(`✅ ${dep} - Already installed`)
  } else {
    toInstall.push({ dep, ...info })
    console.log(`❌ ${dep} - Not installed (${info.description})`)
  }
})

if (alreadyInstalled.length > 0) {
  console.log(`\n✅ Already installed: ${alreadyInstalled.length} packages`)
}

if (toInstall.length === 0) {
  console.log('\n🎉 All optional dependencies are already installed!')
  process.exit(0)
}

console.log(`\n📋 Available for installation: ${toInstall.length} packages\n`)

// Show installation options
toInstall.forEach(({ dep, description, recommended }, index) => {
  const status = recommended ? '(Recommended)' : '(Optional)'
  console.log(`${index + 1}. ${dep} ${status}`)
  console.log(`   ${description}\n`)
})

// Interactive installation (simplified for this example)
console.log('💡 Installation Options:')
console.log('1. Install all recommended packages')
console.log('2. Install all packages')
console.log('3. Skip installation (you can install manually later)')

// For this example, we'll provide manual installation commands
console.log('\n📝 Manual Installation Commands:')
console.log('\nRecommended packages:')
const recommended = toInstall.filter(({ recommended }) => recommended)
if (recommended.length > 0) {
  const recommendedPackages = recommended.map(({ dep }) => dep).join(' ')
  console.log(`npm install ${recommendedPackages}`)
  console.log(`# or`)
  console.log(`pnpm add ${recommendedPackages}`)
  console.log(`# or`)
  console.log(`yarn add ${recommendedPackages}`)
}

console.log('\nAll optional packages:')
const allPackages = toInstall.map(({ dep }) => dep).join(' ')
console.log(`npm install ${allPackages}`)
console.log(`# or`)
console.log(`pnpm add ${allPackages}`)
console.log(`# or`)
console.log(`yarn add ${allPackages}`)

console.log('\n🔧 Environment Configuration:')
console.log('After installing packages, you can enable features by setting environment variables:')
toInstall.forEach(({ dep, envVar }) => {
  if (envVar) {
    console.log(`- ${dep}: Set ${envVar} in your .env file`)
  }
})

console.log('\n📖 For more information, see:')
console.log('- .env.example for environment variable examples')
console.log('- docs/deployment-guide.md for production setup')
console.log('- next.config.js for configuration details')

console.log('\n✨ The project will work without these optional dependencies!')
console.log('You can install them later when needed.')
