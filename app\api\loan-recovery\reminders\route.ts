import { NextRequest, NextResponse } from "next/server"
import { AuthService } from "@/lib/auth-utils"
import { serverDb } from "@/lib/server-db"
import { z } from "zod"

// Validation schema for reminder creation
const createReminderSchema = z.object({
  loan_id: z.string().uuid("Invalid loan ID"),
  title: z.string().min(1, "Title is required").max(255, "Title too long"),
  reminder_date: z.string().regex(/^\d{4}-\d{2}-\d{2}$/, "Invalid date format"),
  reminder_date_bs: z.string().optional(),
})

// Validation schema for reminder updates
const updateReminderSchema = z.object({
  title: z.string().min(1, "Title is required").max(255, "Title too long").optional(),
  reminder_date: z.string().regex(/^\d{4}-\d{2}-\d{2}$/, "Invalid date format").optional(),
  reminder_date_bs: z.string().optional(),
  status: z.enum(["pending", "completed", "cancelled"]).optional(),
})

export async function GET(request: NextRequest) {
  try {
    const sessionToken = request.cookies.get("session-token")?.value

    if (!sessionToken) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 })
    }

    const user = await AuthService.verifySession(sessionToken)

    if (!user || !["admin", "hr_manager"].includes(user.role)) {
      return NextResponse.json({ error: "Forbidden" }, { status: 403 })
    }

    const { searchParams } = new URL(request.url)
    const loan_id = searchParams.get("loan_id")
    const status = searchParams.get("status")
    const upcoming = searchParams.get("upcoming") === "true"
    const limit = parseInt(searchParams.get("limit") || "50")
    const offset = parseInt(searchParams.get("offset") || "0")

    let whereConditions = []
    let params = []

    if (loan_id) {
      whereConditions.push(`lr.loan_id = $${params.length + 1}`)
      params.push(loan_id)
    }

    if (status) {
      whereConditions.push(`lr.status = $${params.length + 1}`)
      params.push(status)
    }

    if (upcoming) {
      whereConditions.push("lr.reminder_date >= CURRENT_DATE")
      whereConditions.push("lr.status = 'pending'")
    }

    const whereClause = whereConditions.length > 0 
      ? `WHERE ${whereConditions.join(" AND ")}`
      : ""

    const reminders = await serverDb.sql`
      SELECT 
        lr.*,
        u.full_name as created_by_name,
        uc.full_name as completed_by_name,
        c.name as customer_name,
        lrec.loan_amount,
        lrec.current_stage
      FROM loan_reminders lr
      LEFT JOIN users u ON lr.created_by = u.id
      LEFT JOIN users uc ON lr.completed_by = uc.id
      LEFT JOIN loan_records lrec ON lr.loan_id = lrec.id
      LEFT JOIN loan_recovery_customers c ON lrec.customer_id = c.id
      ${whereClause ? serverDb.sql([whereClause], ...params) : serverDb.sql``}
      ORDER BY lr.reminder_date ASC, lr.created_at DESC
      LIMIT ${limit} OFFSET ${offset}
    `

    return NextResponse.json({
      success: true,
      reminders,
    })
  } catch (error) {
    console.error("Reminders API error:", error)
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    )
  }
}

export async function POST(request: NextRequest) {
  try {
    const sessionToken = request.cookies.get("session-token")?.value

    if (!sessionToken) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 })
    }

    const user = await AuthService.verifySession(sessionToken)

    if (!user || !["admin", "hr_manager"].includes(user.role)) {
      return NextResponse.json({ error: "Forbidden" }, { status: 403 })
    }

    const body = await request.json()
    const validatedData = createReminderSchema.parse(body)

    // Verify loan exists
    const loan = await serverDb.sql`
      SELECT id FROM loan_records WHERE id = ${validatedData.loan_id}
    `

    if (loan.length === 0) {
      return NextResponse.json(
        { error: "Loan not found" },
        { status: 400 }
      )
    }

    const reminder = await serverDb.sql`
      INSERT INTO loan_reminders (
        loan_id, title, reminder_date, reminder_date_bs, created_by
      )
      VALUES (
        ${validatedData.loan_id},
        ${validatedData.title},
        ${validatedData.reminder_date},
        ${validatedData.reminder_date_bs || null},
        ${user.id}
      )
      RETURNING *
    `

    // Get reminder with user details
    const reminderWithDetails = await serverDb.sql`
      SELECT 
        lr.*,
        u.full_name as created_by_name
      FROM loan_reminders lr
      LEFT JOIN users u ON lr.created_by = u.id
      WHERE lr.id = ${reminder[0].id}
    `

    return NextResponse.json({
      success: true,
      reminder: reminderWithDetails[0],
    })
  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: "Validation error", details: error.errors },
        { status: 400 }
      )
    }

    console.error("Create reminder API error:", error)
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    )
  }
}
