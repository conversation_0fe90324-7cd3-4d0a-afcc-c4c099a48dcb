import { NextRequest, NextResponse } from "next/server"
import { AuthService } from "@/lib/auth-utils"
import { serverDb } from "@/lib/server-db"
import { z } from "zod"

// Validation schema for customer creation
const createCustomerSchema = z.object({
  name: z.string().min(1, "Name is required").max(255, "Name too long"),
  phone: z.string().min(1, "Phone is required").max(20, "Phone too long"),
  email: z.string().email("Invalid email").nullish(),
  address: z.string().nullish(),
  employee_id: z.string().nullish(),
})

// Validation schema for customer updates
const updateCustomerSchema = z.object({
  name: z.string().min(1, "Name is required").max(255, "Name too long").optional(),
  phone: z.string().min(1, "Phone is required").max(20, "Phone too long").optional(),
  email: z.string().email("Invalid email").nullish(),
  address: z.string().nullish(),
  employee_id: z.string().nullish(),
})

export async function GET(request: NextRequest) {
  try {
    const sessionToken = request.cookies.get("session-token")?.value

    if (!sessionToken) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 })
    }

    const user = await AuthService.verifySession(sessionToken)

    if (!user || !["admin", "hr_manager"].includes(user.role)) {
      return NextResponse.json({ error: "Forbidden" }, { status: 403 })
    }

    const { searchParams } = new URL(request.url)
    const search = searchParams.get("search")
    const limit = parseInt(searchParams.get("limit") || "50")
    const offset = parseInt(searchParams.get("offset") || "0")

    let customers
    if (search) {
      customers = await serverDb.sql`
        SELECT 
          c.*,
          COUNT(lr.id) as loan_count,
          COALESCE(SUM(lr.loan_amount - lr.amount_paid), 0) as total_outstanding
        FROM loan_recovery_customers c
        LEFT JOIN loan_records lr ON c.id = lr.customer_id
        WHERE 
          c.name ILIKE ${`%${search}%`} OR 
          c.phone ILIKE ${`%${search}%`} OR 
          c.email ILIKE ${`%${search}%`}
        GROUP BY c.id
        ORDER BY c.name
        LIMIT ${limit} OFFSET ${offset}
      `
    } else {
      customers = await serverDb.sql`
        SELECT 
          c.*,
          COUNT(lr.id) as loan_count,
          COALESCE(SUM(lr.loan_amount - lr.amount_paid), 0) as total_outstanding
        FROM loan_recovery_customers c
        LEFT JOIN loan_records lr ON c.id = lr.customer_id
        GROUP BY c.id
        ORDER BY c.name
        LIMIT ${limit} OFFSET ${offset}
      `
    }

    return NextResponse.json({
      success: true,
      customers,
    })
  } catch (error) {
    console.error("Customers API error:", error)
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    )
  }
}

export async function POST(request: NextRequest) {
  try {
    const sessionToken = request.cookies.get("session-token")?.value

    if (!sessionToken) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 })
    }

    const user = await AuthService.verifySession(sessionToken)

    if (!user || !["admin", "hr_manager"].includes(user.role)) {
      return NextResponse.json({ error: "Forbidden" }, { status: 403 })
    }

    const body = await request.json()
    const validatedData = createCustomerSchema.parse(body)

    // Check if phone number already exists
    const existingCustomer = await serverDb.sql`
      SELECT id FROM loan_recovery_customers 
      WHERE phone = ${validatedData.phone}
    `

    if (existingCustomer.length > 0) {
      return NextResponse.json(
        { error: "Customer with this phone number already exists" },
        { status: 400 }
      )
    }

    const customer = await serverDb.sql`
      INSERT INTO loan_recovery_customers (
        name, phone, email, address, employee_id, created_by
      )
      VALUES (
        ${validatedData.name},
        ${validatedData.phone},
        ${validatedData.email || null},
        ${validatedData.address || null},
        ${validatedData.employee_id || null},
        ${user.id}
      )
      RETURNING *
    `

    return NextResponse.json({
      success: true,
      customer: customer[0],
    })
  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: "Validation error", details: error.errors },
        { status: 400 }
      )
    }

    console.error("Create customer API error:", error)
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    )
  }
}
