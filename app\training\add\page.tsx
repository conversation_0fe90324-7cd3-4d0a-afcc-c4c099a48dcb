"use client"

import type React from "react"

import { useState } from "react"
import { useRouter } from "next/navigation"
import { <PERSON>pp<PERSON>eader } from "@/components/app-header"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Switch } from "@/components/ui/switch"
import { ChatbotButton } from "@/components/chatbot-button"
import { ArrowLeft, Save, BookOpen, Clock, FileText, Tag, Upload, Plus } from "lucide-react"

export default function AddTrainingPage() {
  const router = useRouter()
  const [formData, setFormData] = useState({
    title: "",
    description: "",
    category: "product",
    difficulty: "beginner",
    duration: "",
    modules: "1",
    featured: false,
  })

  const [modules, setModules] = useState([{ title: "", description: "", duration: "" }])

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target
    setFormData((prev) => ({ ...prev, [name]: value }))
  }

  const handleSelectChange = (name: string, value: string) => {
    setFormData((prev) => ({ ...prev, [name]: value }))
  }

  const handleSwitchChange = (checked: boolean) => {
    setFormData((prev) => ({ ...prev, featured: checked }))
  }

  const handleModuleChange = (index: number, field: string, value: string) => {
    const updatedModules = [...modules]
    updatedModules[index] = { ...updatedModules[index], [field]: value }
    setModules(updatedModules)
  }

  const addModule = () => {
    setModules([...modules, { title: "", description: "", duration: "" }])
  }

  const removeModule = (index: number) => {
    const updatedModules = [...modules]
    updatedModules.splice(index, 1)
    setModules(updatedModules)
  }

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault()
    // In a real app, this would save the training course to a database
    console.log("Training data:", { ...formData, modules })

    // Navigate back to training page
    router.push("/training")
  }

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900 flex flex-col">
      <AppHeader />

      <div className="p-4">
        <div className="flex items-center gap-2 mb-4">
          <Button variant="ghost" size="icon" onClick={() => router.back()}>
            <ArrowLeft className="h-5 w-5" />
          </Button>
          <h2 className="text-xl font-semibold text-teal-800 dark:text-teal-300">Add New Training Course</h2>
        </div>

        <Card className="dark:bg-gray-800">
          <CardHeader>
            <CardTitle className="text-teal-800 dark:text-teal-300">Course Information</CardTitle>
            <CardDescription>Enter the details of the new training course</CardDescription>
          </CardHeader>
          <form onSubmit={handleSubmit}>
            <CardContent className="space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2 md:col-span-2">
                  <Label htmlFor="title" className="text-teal-700 dark:text-teal-400">
                    Course Title
                  </Label>
                  <div className="relative">
                    <BookOpen className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                    <Input
                      id="title"
                      name="title"
                      placeholder="e.g. Advanced Sales Techniques"
                      className="pl-10"
                      value={formData.title}
                      onChange={handleChange}
                      required
                    />
                  </div>
                </div>
                <div className="space-y-2 md:col-span-2">
                  <Label htmlFor="description" className="text-teal-700 dark:text-teal-400">
                    Course Description
                  </Label>
                  <Textarea
                    id="description"
                    name="description"
                    placeholder="Provide a detailed description of the course..."
                    rows={3}
                    value={formData.description}
                    onChange={handleChange}
                    required
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="category" className="text-teal-700 dark:text-teal-400">
                    Category
                  </Label>
                  <div className="relative">
                    <Tag className="absolute left-3 top-3 text-gray-400 h-4 w-4 z-10" />
                    <Select value={formData.category} onValueChange={(value) => handleSelectChange("category", value)}>
                      <SelectTrigger className="pl-10">
                        <SelectValue placeholder="Select category" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="product">Product Knowledge</SelectItem>
                        <SelectItem value="sales">Sales</SelectItem>
                        <SelectItem value="client">Client Management</SelectItem>
                        <SelectItem value="marketing">Marketing</SelectItem>
                        <SelectItem value="technical">Technical</SelectItem>
                        <SelectItem value="compliance">Compliance</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                </div>
                <div className="space-y-2">
                  <Label htmlFor="difficulty" className="text-teal-700 dark:text-teal-400">
                    Difficulty Level
                  </Label>
                  <div className="relative">
                    <FileText className="absolute left-3 top-3 text-gray-400 h-4 w-4 z-10" />
                    <Select
                      value={formData.difficulty}
                      onValueChange={(value) => handleSelectChange("difficulty", value)}
                    >
                      <SelectTrigger className="pl-10">
                        <SelectValue placeholder="Select difficulty" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="beginner">Beginner</SelectItem>
                        <SelectItem value="intermediate">Intermediate</SelectItem>
                        <SelectItem value="advanced">Advanced</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                </div>
                <div className="space-y-2">
                  <Label htmlFor="duration" className="text-teal-700 dark:text-teal-400">
                    Total Duration
                  </Label>
                  <div className="relative">
                    <Clock className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                    <Input
                      id="duration"
                      name="duration"
                      placeholder="e.g. 2 hours"
                      className="pl-10"
                      value={formData.duration}
                      onChange={handleChange}
                      required
                    />
                  </div>
                </div>
                <div className="space-y-2">
                  <Label htmlFor="featured" className="text-teal-700 dark:text-teal-400">
                    Featured Course
                  </Label>
                  <div className="flex items-center gap-2">
                    <Switch checked={formData.featured} onCheckedChange={handleSwitchChange} />
                    <span className="text-sm text-gray-600 dark:text-gray-300">
                      {formData.featured ? "This course will be featured" : "This course will not be featured"}
                    </span>
                  </div>
                </div>
              </div>

              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <Label className="text-teal-700 dark:text-teal-400 text-base">Course Modules</Label>
                  <Button type="button" variant="outline" size="sm" onClick={addModule}>
                    <Plus className="h-4 w-4 mr-1" /> Add Module
                  </Button>
                </div>

                {modules.map((module, index) => (
                  <Card key={index} className="dark:bg-gray-700">
                    <CardHeader className="p-4 pb-2">
                      <div className="flex items-center justify-between">
                        <CardTitle className="text-sm text-teal-800 dark:text-teal-300">Module {index + 1}</CardTitle>
                        {modules.length > 1 && (
                          <Button
                            type="button"
                            variant="ghost"
                            size="sm"
                            className="h-8 text-red-500"
                            onClick={() => removeModule(index)}
                          >
                            Remove
                          </Button>
                        )}
                      </div>
                    </CardHeader>
                    <CardContent className="p-4 pt-2">
                      <div className="space-y-3">
                        <div className="space-y-1">
                          <Label htmlFor={`module-title-${index}`} className="text-sm text-teal-700 dark:text-teal-400">
                            Title
                          </Label>
                          <Input
                            id={`module-title-${index}`}
                            value={module.title}
                            onChange={(e) => handleModuleChange(index, "title", e.target.value)}
                            placeholder="Module title"
                            required
                          />
                        </div>
                        <div className="space-y-1">
                          <Label
                            htmlFor={`module-description-${index}`}
                            className="text-sm text-teal-700 dark:text-teal-400"
                          >
                            Description
                          </Label>
                          <Textarea
                            id={`module-description-${index}`}
                            value={module.description}
                            onChange={(e) => handleModuleChange(index, "description", e.target.value)}
                            placeholder="Module description"
                            rows={2}
                          />
                        </div>
                        <div className="space-y-1">
                          <Label
                            htmlFor={`module-duration-${index}`}
                            className="text-sm text-teal-700 dark:text-teal-400"
                          >
                            Duration
                          </Label>
                          <Input
                            id={`module-duration-${index}`}
                            value={module.duration}
                            onChange={(e) => handleModuleChange(index, "duration", e.target.value)}
                            placeholder="e.g. 30 minutes"
                          />
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                ))}
              </div>

              <div className="space-y-2">
                <Label className="text-teal-700 dark:text-teal-400">Course Materials</Label>
                <div className="border-2 border-dashed border-gray-300 dark:border-gray-600 rounded-lg p-6 text-center">
                  <Upload className="h-8 w-8 mx-auto text-gray-400" />
                  <p className="mt-2 text-sm text-gray-500 dark:text-gray-400">
                    Drag and drop files here, or click to select files
                  </p>
                  <p className="text-xs text-gray-400 dark:text-gray-500 mt-1">
                    Supports: PDF, PPTX, DOCX, MP4, MP3 (max 100MB)
                  </p>
                  <Button type="button" variant="outline" size="sm" className="mt-4">
                    Upload Files
                  </Button>
                </div>
              </div>
            </CardContent>
            <CardFooter className="flex justify-between">
              <Button variant="outline" type="button" onClick={() => router.back()}>
                Cancel
              </Button>
              <Button type="submit" className="bg-exobank-green hover:bg-exobank-green/90 text-white">
                <Save className="h-4 w-4 mr-2" /> Create Course
              </Button>
            </CardFooter>
          </form>
        </Card>
      </div>

      <ChatbotButton />
    </div>
  )
}
