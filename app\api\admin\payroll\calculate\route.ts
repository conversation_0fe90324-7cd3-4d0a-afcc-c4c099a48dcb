// Admin Payroll Calculation API Endpoint
// Phase 2: Core Payroll Engine Development - API Endpoints

import { NextRequest, NextResponse } from 'next/server';
import { db } from '@/lib/neon';
import { PayrollEngine, PayrollInput } from '@/lib/payroll-engine';
import { attendancePayrollProcessor } from '@/lib/attendance-payroll-processor';
import { deductionsAllowancesSystem } from '@/lib/deductions-allowances-system';
import { payStructureManager } from '@/lib/pay-structure-manager';

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { userId, periodStart, periodEnd, payStructureOverride, componentsOverride } = body;

    // Validate required fields
    if (!userId || !periodStart || !periodEnd) {
      return NextResponse.json(
        { success: false, error: 'User ID, period start, and period end are required' },
        { status: 400 }
      );
    }

    // Validate date format and range
    const startDate = new Date(periodStart);
    const endDate = new Date(periodEnd);
    
    if (isNaN(startDate.getTime()) || isNaN(endDate.getTime())) {
      return NextResponse.json(
        { success: false, error: 'Invalid date format' },
        { status: 400 }
      );
    }

    if (startDate >= endDate) {
      return NextResponse.json(
        { success: false, error: 'Period start date must be before end date' },
        { status: 400 }
      );
    }

    // Get employee data
    const employee = await db.getUserById(userId);
    if (!employee) {
      return NextResponse.json(
        { success: false, error: 'Employee not found' },
        { status: 404 }
      );
    }

    // Get employee pay structure
    const payStructure = payStructureOverride || await payStructureManager.getActivePayStructure(userId, periodStart);
    if (!payStructure) {
      return NextResponse.json(
        { success: false, error: 'No active pay structure found for employee' },
        { status: 404 }
      );
    }

    // Process attendance data for payroll period
    const attendanceData = await attendancePayrollProcessor.processAttendanceForPayroll(
      userId,
      periodStart,
      periodEnd
    );

    // Calculate deductions and allowances
    const componentsInput = {
      userId,
      payrollPeriod: { start: periodStart, end: periodEnd },
      salaryData: {
        baseSalary: payStructure.baseSalary,
        grossPay: payStructure.baseSalary, // Will be recalculated
        totalEarnings: payStructure.baseSalary,
        regularHours: attendanceData.periodSummary.regularHours,
        overtimeHours: attendanceData.periodSummary.overtimeHours
      },
      employeeData: {
        category: employee.employee_category || 'regular',
        department: employee.department || '',
        payStructureType: payStructure.type,
        hireDate: employee.hire_date || employee.created_at,
        isActive: employee.is_active
      },
      attendanceData: {
        workingDays: attendanceData.periodSummary.workingDays,
        presentDays: attendanceData.periodSummary.presentDays,
        attendanceRate: attendanceData.periodSummary.attendanceRate
      }
    };

    const components = componentsOverride || await deductionsAllowancesSystem.calculateEmployeeComponents(componentsInput);

    // Prepare payroll input
    const payrollInput: PayrollInput = {
      userId,
      periodStart,
      periodEnd,
      attendanceData: attendanceData.dailyAttendance.map(day => ({
        date: day.date,
        totalHours: day.totalHours,
        regularHours: day.regularHours,
        overtimeHours: day.overtimeHours,
        status: day.status,
        lateMinutes: day.lateMinutes,
        earlyDeparture: day.earlyDeparture,
        sessionCount: day.sessionCount
      })),
      employeeData: {
        id: employee.id,
        fullName: employee.full_name,
        email: employee.email,
        department: employee.department,
        position: employee.position,
        hireDate: employee.hire_date || employee.created_at,
        isActive: employee.is_active
      },
      payStructure: {
        type: payStructure.type,
        baseSalary: payStructure.baseSalary,
        hourlyRate: payStructure.hourlyRate,
        dailyRate: payStructure.dailyRate,
        overtimeMultiplier: payStructure.overtimeMultiplier,
        providentFundPercentage: payStructure.providentFundPercentage,
        effectiveFrom: payStructure.effectiveFrom,
        effectiveTo: payStructure.effectiveTo
      },
      deductions: components.deductions.map(d => ({
        id: d.componentId,
        name: d.componentName,
        type: d.calculationType === 'fixed' ? 'fixed' : 'percentage',
        amount: d.finalAmount,
        isPercentage: d.calculationType === 'percentage',
        percentageBase: 'base_salary',
        isTaxable: d.isTaxable,
        isStatutory: d.category === 'statutory'
      })),
      allowances: components.allowances.map(a => ({
        id: a.componentId,
        name: a.componentName,
        type: a.calculationType === 'fixed' ? 'fixed' : 'percentage',
        amount: a.finalAmount,
        isPercentage: a.calculationType === 'percentage',
        percentageBase: 'base_salary',
        isTaxable: a.isTaxable
      }))
    };

    // Calculate payroll
    const payrollResult = await PayrollEngine.calculatePayroll(payrollInput);

    // Return comprehensive result
    return NextResponse.json({
      success: true,
      data: {
        payrollCalculation: payrollResult,
        attendanceData: attendanceData,
        components: components,
        payStructure: payStructure,
        employee: {
          id: employee.id,
          fullName: employee.full_name,
          email: employee.email,
          department: employee.department,
          position: employee.position
        }
      }
    });

  } catch (error) {
    console.error('Error calculating payroll:', error);
    return NextResponse.json(
      { 
        success: false, 
        error: 'Failed to calculate payroll',
        details: error.message 
      },
      { status: 500 }
    );
  }
}

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const userId = searchParams.get('userId');
    const periodStart = searchParams.get('periodStart');
    const periodEnd = searchParams.get('periodEnd');

    if (!userId || !periodStart || !periodEnd) {
      return NextResponse.json(
        { success: false, error: 'User ID, period start, and period end are required' },
        { status: 400 }
      );
    }

    // Get existing payroll calculation if available
    const existingPayroll = await db.sql`
      SELECT * FROM payroll
      WHERE user_id = ${userId}
        AND pay_period_start = ${periodStart}
        AND pay_period_end = ${periodEnd}
      ORDER BY created_at DESC
      LIMIT 1
    `;

    if (existingPayroll.length > 0) {
      return NextResponse.json({
        success: true,
        data: {
          payrollCalculation: existingPayroll[0],
          isExisting: true
        }
      });
    }

    // If no existing calculation, return empty result
    return NextResponse.json({
      success: true,
      data: {
        payrollCalculation: null,
        isExisting: false
      }
    });

  } catch (error) {
    console.error('Error fetching payroll calculation:', error);
    return NextResponse.json(
      { 
        success: false, 
        error: 'Failed to fetch payroll calculation',
        details: error.message 
      },
      { status: 500 }
    );
  }
}
